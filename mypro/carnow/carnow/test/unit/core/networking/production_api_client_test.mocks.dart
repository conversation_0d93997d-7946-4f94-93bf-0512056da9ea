// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in carnow/test/unit/core/networking/production_api_client_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i12;

import 'package:carnow/core/auth/auth_models.dart' as _i10;
import 'package:carnow/core/auth/unified_auth_provider.dart' as _i16;
import 'package:dio/dio.dart' as _i7;
import 'package:dio/src/adapter.dart' as _i3;
import 'package:dio/src/cancel_token.dart' as _i13;
import 'package:dio/src/dio_mixin.dart' as _i5;
import 'package:dio/src/headers.dart' as _i8;
import 'package:dio/src/options.dart' as _i2;
import 'package:dio/src/redirect_record.dart' as _i14;
import 'package:dio/src/response.dart' as _i6;
import 'package:dio/src/transformer.dart' as _i4;
import 'package:flutter/foundation.dart' as _i11;
import 'package:flutter_riverpod/flutter_riverpod.dart' as _i9;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i15;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeBaseOptions_0 extends _i1.SmartFake implements _i2.BaseOptions {
  _FakeBaseOptions_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeHttpClientAdapter_1 extends _i1.SmartFake
    implements _i3.HttpClientAdapter {
  _FakeHttpClientAdapter_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTransformer_2 extends _i1.SmartFake implements _i4.Transformer {
  _FakeTransformer_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInterceptors_3 extends _i1.SmartFake implements _i5.Interceptors {
  _FakeInterceptors_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResponse_4<T1> extends _i1.SmartFake implements _i6.Response<T1> {
  _FakeResponse_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDio_5 extends _i1.SmartFake implements _i7.Dio {
  _FakeDio_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRequestOptions_6 extends _i1.SmartFake
    implements _i2.RequestOptions {
  _FakeRequestOptions_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeHeaders_7 extends _i1.SmartFake implements _i8.Headers {
  _FakeHeaders_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUri_8 extends _i1.SmartFake implements Uri {
  _FakeUri_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAutoDisposeNotifierProviderRef_9<T> extends _i1.SmartFake
    implements _i9.AutoDisposeNotifierProviderRef<T> {
  _FakeAutoDisposeNotifierProviderRef_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeAuthState_10 extends _i1.SmartFake implements _i10.AuthState {
  _FakeAuthState_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);

  @override
  String toString({
    _i11.DiagnosticLevel? minLevel = _i11.DiagnosticLevel.info,
  }) => super.toString();
}

class _FakeAuthResult_11 extends _i1.SmartFake implements _i10.AuthResult {
  _FakeAuthResult_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);

  @override
  String toString({
    _i11.DiagnosticLevel? minLevel = _i11.DiagnosticLevel.info,
  }) => super.toString();
}

/// A class which mocks [Dio].
///
/// See the documentation for Mockito's code generation for more information.
class MockDio extends _i1.Mock implements _i7.Dio {
  MockDio() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.BaseOptions get options =>
      (super.noSuchMethod(
            Invocation.getter(#options),
            returnValue: _FakeBaseOptions_0(this, Invocation.getter(#options)),
          )
          as _i2.BaseOptions);

  @override
  _i3.HttpClientAdapter get httpClientAdapter =>
      (super.noSuchMethod(
            Invocation.getter(#httpClientAdapter),
            returnValue: _FakeHttpClientAdapter_1(
              this,
              Invocation.getter(#httpClientAdapter),
            ),
          )
          as _i3.HttpClientAdapter);

  @override
  _i4.Transformer get transformer =>
      (super.noSuchMethod(
            Invocation.getter(#transformer),
            returnValue: _FakeTransformer_2(
              this,
              Invocation.getter(#transformer),
            ),
          )
          as _i4.Transformer);

  @override
  _i5.Interceptors get interceptors =>
      (super.noSuchMethod(
            Invocation.getter(#interceptors),
            returnValue: _FakeInterceptors_3(
              this,
              Invocation.getter(#interceptors),
            ),
          )
          as _i5.Interceptors);

  @override
  set options(_i2.BaseOptions? _options) => super.noSuchMethod(
    Invocation.setter(#options, _options),
    returnValueForMissingStub: null,
  );

  @override
  set httpClientAdapter(_i3.HttpClientAdapter? _httpClientAdapter) =>
      super.noSuchMethod(
        Invocation.setter(#httpClientAdapter, _httpClientAdapter),
        returnValueForMissingStub: null,
      );

  @override
  set transformer(_i4.Transformer? _transformer) => super.noSuchMethod(
    Invocation.setter(#transformer, _transformer),
    returnValueForMissingStub: null,
  );

  @override
  void close({bool? force = false}) => super.noSuchMethod(
    Invocation.method(#close, [], {#force: force}),
    returnValueForMissingStub: null,
  );

  @override
  _i12.Future<_i6.Response<T>> head<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #head,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #head,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> headUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #headUri,
              [uri],
              {#data: data, #options: options, #cancelToken: cancelToken},
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #headUri,
                  [uri],
                  {#data: data, #options: options, #cancelToken: cancelToken},
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> get<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #get,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #get,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> getUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUri,
              [uri],
              {
                #data: data,
                #options: options,
                #cancelToken: cancelToken,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #getUri,
                  [uri],
                  {
                    #data: data,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> post<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #post,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #post,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> postUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #postUri,
              [uri],
              {
                #data: data,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #postUri,
                  [uri],
                  {
                    #data: data,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> put<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #put,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #put,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> putUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #putUri,
              [uri],
              {
                #data: data,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #putUri,
                  [uri],
                  {
                    #data: data,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> patch<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #patch,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #patch,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> patchUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #patchUri,
              [uri],
              {
                #data: data,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #patchUri,
                  [uri],
                  {
                    #data: data,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> delete<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #delete,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #delete,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> deleteUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i13.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteUri,
              [uri],
              {#data: data, #options: options, #cancelToken: cancelToken},
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #deleteUri,
                  [uri],
                  {#data: data, #options: options, #cancelToken: cancelToken},
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<dynamic>> download(
    String? urlPath,
    dynamic savePath, {
    _i2.ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    _i13.CancelToken? cancelToken,
    bool? deleteOnError = true,
    _i2.FileAccessMode? fileAccessMode = _i2.FileAccessMode.write,
    String? lengthHeader = 'content-length',
    Object? data,
    _i2.Options? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #download,
              [urlPath, savePath],
              {
                #onReceiveProgress: onReceiveProgress,
                #queryParameters: queryParameters,
                #cancelToken: cancelToken,
                #deleteOnError: deleteOnError,
                #fileAccessMode: fileAccessMode,
                #lengthHeader: lengthHeader,
                #data: data,
                #options: options,
              },
            ),
            returnValue: _i12.Future<_i6.Response<dynamic>>.value(
              _FakeResponse_4<dynamic>(
                this,
                Invocation.method(
                  #download,
                  [urlPath, savePath],
                  {
                    #onReceiveProgress: onReceiveProgress,
                    #queryParameters: queryParameters,
                    #cancelToken: cancelToken,
                    #deleteOnError: deleteOnError,
                    #fileAccessMode: fileAccessMode,
                    #lengthHeader: lengthHeader,
                    #data: data,
                    #options: options,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<dynamic>>);

  @override
  _i12.Future<_i6.Response<dynamic>> downloadUri(
    Uri? uri,
    dynamic savePath, {
    _i2.ProgressCallback? onReceiveProgress,
    _i13.CancelToken? cancelToken,
    bool? deleteOnError = true,
    _i2.FileAccessMode? fileAccessMode = _i2.FileAccessMode.write,
    String? lengthHeader = 'content-length',
    Object? data,
    _i2.Options? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #downloadUri,
              [uri, savePath],
              {
                #onReceiveProgress: onReceiveProgress,
                #cancelToken: cancelToken,
                #deleteOnError: deleteOnError,
                #fileAccessMode: fileAccessMode,
                #lengthHeader: lengthHeader,
                #data: data,
                #options: options,
              },
            ),
            returnValue: _i12.Future<_i6.Response<dynamic>>.value(
              _FakeResponse_4<dynamic>(
                this,
                Invocation.method(
                  #downloadUri,
                  [uri, savePath],
                  {
                    #onReceiveProgress: onReceiveProgress,
                    #cancelToken: cancelToken,
                    #deleteOnError: deleteOnError,
                    #fileAccessMode: fileAccessMode,
                    #lengthHeader: lengthHeader,
                    #data: data,
                    #options: options,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<dynamic>>);

  @override
  _i12.Future<_i6.Response<T>> request<T>(
    String? url, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i13.CancelToken? cancelToken,
    _i2.Options? options,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #request,
              [url],
              {
                #data: data,
                #queryParameters: queryParameters,
                #cancelToken: cancelToken,
                #options: options,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #request,
                  [url],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #cancelToken: cancelToken,
                    #options: options,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> requestUri<T>(
    Uri? uri, {
    Object? data,
    _i13.CancelToken? cancelToken,
    _i2.Options? options,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #requestUri,
              [uri],
              {
                #data: data,
                #cancelToken: cancelToken,
                #options: options,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #requestUri,
                  [uri],
                  {
                    #data: data,
                    #cancelToken: cancelToken,
                    #options: options,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i12.Future<_i6.Response<T>> fetch<T>(_i2.RequestOptions? requestOptions) =>
      (super.noSuchMethod(
            Invocation.method(#fetch, [requestOptions]),
            returnValue: _i12.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(#fetch, [requestOptions]),
              ),
            ),
          )
          as _i12.Future<_i6.Response<T>>);

  @override
  _i7.Dio clone({
    _i2.BaseOptions? options,
    _i5.Interceptors? interceptors,
    _i3.HttpClientAdapter? httpClientAdapter,
    _i4.Transformer? transformer,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#clone, [], {
              #options: options,
              #interceptors: interceptors,
              #httpClientAdapter: httpClientAdapter,
              #transformer: transformer,
            }),
            returnValue: _FakeDio_5(
              this,
              Invocation.method(#clone, [], {
                #options: options,
                #interceptors: interceptors,
                #httpClientAdapter: httpClientAdapter,
                #transformer: transformer,
              }),
            ),
          )
          as _i7.Dio);
}

/// A class which mocks [Response].
///
/// See the documentation for Mockito's code generation for more information.
class MockResponse<T> extends _i1.Mock implements _i6.Response<T> {
  MockResponse() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.RequestOptions get requestOptions =>
      (super.noSuchMethod(
            Invocation.getter(#requestOptions),
            returnValue: _FakeRequestOptions_6(
              this,
              Invocation.getter(#requestOptions),
            ),
          )
          as _i2.RequestOptions);

  @override
  _i8.Headers get headers =>
      (super.noSuchMethod(
            Invocation.getter(#headers),
            returnValue: _FakeHeaders_7(this, Invocation.getter(#headers)),
          )
          as _i8.Headers);

  @override
  bool get isRedirect =>
      (super.noSuchMethod(Invocation.getter(#isRedirect), returnValue: false)
          as bool);

  @override
  List<_i14.RedirectRecord> get redirects =>
      (super.noSuchMethod(
            Invocation.getter(#redirects),
            returnValue: <_i14.RedirectRecord>[],
          )
          as List<_i14.RedirectRecord>);

  @override
  Map<String, dynamic> get extra =>
      (super.noSuchMethod(
            Invocation.getter(#extra),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Uri get realUri =>
      (super.noSuchMethod(
            Invocation.getter(#realUri),
            returnValue: _FakeUri_8(this, Invocation.getter(#realUri)),
          )
          as Uri);

  @override
  set data(T? _data) => super.noSuchMethod(
    Invocation.setter(#data, _data),
    returnValueForMissingStub: null,
  );

  @override
  set requestOptions(_i2.RequestOptions? _requestOptions) => super.noSuchMethod(
    Invocation.setter(#requestOptions, _requestOptions),
    returnValueForMissingStub: null,
  );

  @override
  set statusCode(int? _statusCode) => super.noSuchMethod(
    Invocation.setter(#statusCode, _statusCode),
    returnValueForMissingStub: null,
  );

  @override
  set statusMessage(String? _statusMessage) => super.noSuchMethod(
    Invocation.setter(#statusMessage, _statusMessage),
    returnValueForMissingStub: null,
  );

  @override
  set headers(_i8.Headers? _headers) => super.noSuchMethod(
    Invocation.setter(#headers, _headers),
    returnValueForMissingStub: null,
  );

  @override
  set isRedirect(bool? _isRedirect) => super.noSuchMethod(
    Invocation.setter(#isRedirect, _isRedirect),
    returnValueForMissingStub: null,
  );

  @override
  set redirects(List<_i14.RedirectRecord>? _redirects) => super.noSuchMethod(
    Invocation.setter(#redirects, _redirects),
    returnValueForMissingStub: null,
  );

  @override
  set extra(Map<String, dynamic>? _extra) => super.noSuchMethod(
    Invocation.setter(#extra, _extra),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [RequestOptions].
///
/// See the documentation for Mockito's code generation for more information.
class MockRequestOptions extends _i1.Mock implements _i2.RequestOptions {
  MockRequestOptions() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get path =>
      (super.noSuchMethod(
            Invocation.getter(#path),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.getter(#path),
            ),
          )
          as String);

  @override
  Uri get uri =>
      (super.noSuchMethod(
            Invocation.getter(#uri),
            returnValue: _FakeUri_8(this, Invocation.getter(#uri)),
          )
          as Uri);

  @override
  set sourceStackTrace(StackTrace? _sourceStackTrace) => super.noSuchMethod(
    Invocation.setter(#sourceStackTrace, _sourceStackTrace),
    returnValueForMissingStub: null,
  );

  @override
  set data(dynamic _data) => super.noSuchMethod(
    Invocation.setter(#data, _data),
    returnValueForMissingStub: null,
  );

  @override
  set path(String? _path) => super.noSuchMethod(
    Invocation.setter(#path, _path),
    returnValueForMissingStub: null,
  );

  @override
  set cancelToken(_i13.CancelToken? _cancelToken) => super.noSuchMethod(
    Invocation.setter(#cancelToken, _cancelToken),
    returnValueForMissingStub: null,
  );

  @override
  set onReceiveProgress(_i2.ProgressCallback? _onReceiveProgress) =>
      super.noSuchMethod(
        Invocation.setter(#onReceiveProgress, _onReceiveProgress),
        returnValueForMissingStub: null,
      );

  @override
  set onSendProgress(_i2.ProgressCallback? _onSendProgress) =>
      super.noSuchMethod(
        Invocation.setter(#onSendProgress, _onSendProgress),
        returnValueForMissingStub: null,
      );

  @override
  String get method =>
      (super.noSuchMethod(
            Invocation.getter(#method),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.getter(#method),
            ),
          )
          as String);

  @override
  bool get preserveHeaderCase =>
      (super.noSuchMethod(
            Invocation.getter(#preserveHeaderCase),
            returnValue: false,
          )
          as bool);

  @override
  _i2.ResponseType get responseType =>
      (super.noSuchMethod(
            Invocation.getter(#responseType),
            returnValue: _i2.ResponseType.json,
          )
          as _i2.ResponseType);

  @override
  _i2.ValidateStatus get validateStatus =>
      (super.noSuchMethod(
            Invocation.getter(#validateStatus),
            returnValue: (int? status) => false,
          )
          as _i2.ValidateStatus);

  @override
  bool get receiveDataWhenStatusError =>
      (super.noSuchMethod(
            Invocation.getter(#receiveDataWhenStatusError),
            returnValue: false,
          )
          as bool);

  @override
  Map<String, dynamic> get extra =>
      (super.noSuchMethod(
            Invocation.getter(#extra),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  bool get followRedirects =>
      (super.noSuchMethod(
            Invocation.getter(#followRedirects),
            returnValue: false,
          )
          as bool);

  @override
  int get maxRedirects =>
      (super.noSuchMethod(Invocation.getter(#maxRedirects), returnValue: 0)
          as int);

  @override
  bool get persistentConnection =>
      (super.noSuchMethod(
            Invocation.getter(#persistentConnection),
            returnValue: false,
          )
          as bool);

  @override
  _i2.ListFormat get listFormat =>
      (super.noSuchMethod(
            Invocation.getter(#listFormat),
            returnValue: _i2.ListFormat.csv,
          )
          as _i2.ListFormat);

  @override
  Map<String, dynamic> get headers =>
      (super.noSuchMethod(
            Invocation.getter(#headers),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  set method(String? _method) => super.noSuchMethod(
    Invocation.setter(#method, _method),
    returnValueForMissingStub: null,
  );

  @override
  set preserveHeaderCase(bool? _preserveHeaderCase) => super.noSuchMethod(
    Invocation.setter(#preserveHeaderCase, _preserveHeaderCase),
    returnValueForMissingStub: null,
  );

  @override
  set responseType(_i2.ResponseType? _responseType) => super.noSuchMethod(
    Invocation.setter(#responseType, _responseType),
    returnValueForMissingStub: null,
  );

  @override
  set validateStatus(_i2.ValidateStatus? _validateStatus) => super.noSuchMethod(
    Invocation.setter(#validateStatus, _validateStatus),
    returnValueForMissingStub: null,
  );

  @override
  set receiveDataWhenStatusError(bool? _receiveDataWhenStatusError) =>
      super.noSuchMethod(
        Invocation.setter(
          #receiveDataWhenStatusError,
          _receiveDataWhenStatusError,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set extra(Map<String, dynamic>? _extra) => super.noSuchMethod(
    Invocation.setter(#extra, _extra),
    returnValueForMissingStub: null,
  );

  @override
  set followRedirects(bool? _followRedirects) => super.noSuchMethod(
    Invocation.setter(#followRedirects, _followRedirects),
    returnValueForMissingStub: null,
  );

  @override
  set maxRedirects(int? _maxRedirects) => super.noSuchMethod(
    Invocation.setter(#maxRedirects, _maxRedirects),
    returnValueForMissingStub: null,
  );

  @override
  set persistentConnection(bool? _persistentConnection) => super.noSuchMethod(
    Invocation.setter(#persistentConnection, _persistentConnection),
    returnValueForMissingStub: null,
  );

  @override
  set requestEncoder(_i2.RequestEncoder? _requestEncoder) => super.noSuchMethod(
    Invocation.setter(#requestEncoder, _requestEncoder),
    returnValueForMissingStub: null,
  );

  @override
  set responseDecoder(_i2.ResponseDecoder? _responseDecoder) =>
      super.noSuchMethod(
        Invocation.setter(#responseDecoder, _responseDecoder),
        returnValueForMissingStub: null,
      );

  @override
  set listFormat(_i2.ListFormat? _listFormat) => super.noSuchMethod(
    Invocation.setter(#listFormat, _listFormat),
    returnValueForMissingStub: null,
  );

  @override
  set headers(Map<String, dynamic>? headers) => super.noSuchMethod(
    Invocation.setter(#headers, headers),
    returnValueForMissingStub: null,
  );

  @override
  set sendTimeout(Duration? value) => super.noSuchMethod(
    Invocation.setter(#sendTimeout, value),
    returnValueForMissingStub: null,
  );

  @override
  set receiveTimeout(Duration? value) => super.noSuchMethod(
    Invocation.setter(#receiveTimeout, value),
    returnValueForMissingStub: null,
  );

  @override
  set contentType(String? contentType) => super.noSuchMethod(
    Invocation.setter(#contentType, contentType),
    returnValueForMissingStub: null,
  );

  @override
  Map<String, dynamic> get queryParameters =>
      (super.noSuchMethod(
            Invocation.getter(#queryParameters),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  String get baseUrl =>
      (super.noSuchMethod(
            Invocation.getter(#baseUrl),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.getter(#baseUrl),
            ),
          )
          as String);

  @override
  set queryParameters(Map<String, dynamic>? _queryParameters) =>
      super.noSuchMethod(
        Invocation.setter(#queryParameters, _queryParameters),
        returnValueForMissingStub: null,
      );

  @override
  set baseUrl(String? value) => super.noSuchMethod(
    Invocation.setter(#baseUrl, value),
    returnValueForMissingStub: null,
  );

  @override
  set connectTimeout(Duration? value) => super.noSuchMethod(
    Invocation.setter(#connectTimeout, value),
    returnValueForMissingStub: null,
  );

  @override
  _i2.RequestOptions copyWith({
    String? method,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    Duration? connectTimeout,
    dynamic data,
    String? path,
    Map<String, dynamic>? queryParameters,
    String? baseUrl,
    _i2.ProgressCallback? onReceiveProgress,
    _i2.ProgressCallback? onSendProgress,
    _i13.CancelToken? cancelToken,
    Map<String, dynamic>? extra,
    Map<String, dynamic>? headers,
    bool? preserveHeaderCase,
    _i2.ResponseType? responseType,
    String? contentType,
    _i2.ValidateStatus? validateStatus,
    bool? receiveDataWhenStatusError,
    bool? followRedirects,
    int? maxRedirects,
    bool? persistentConnection,
    _i2.RequestEncoder? requestEncoder,
    _i2.ResponseDecoder? responseDecoder,
    _i2.ListFormat? listFormat,
    bool? setRequestContentTypeWhenNoPayload,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#copyWith, [], {
              #method: method,
              #sendTimeout: sendTimeout,
              #receiveTimeout: receiveTimeout,
              #connectTimeout: connectTimeout,
              #data: data,
              #path: path,
              #queryParameters: queryParameters,
              #baseUrl: baseUrl,
              #onReceiveProgress: onReceiveProgress,
              #onSendProgress: onSendProgress,
              #cancelToken: cancelToken,
              #extra: extra,
              #headers: headers,
              #preserveHeaderCase: preserveHeaderCase,
              #responseType: responseType,
              #contentType: contentType,
              #validateStatus: validateStatus,
              #receiveDataWhenStatusError: receiveDataWhenStatusError,
              #followRedirects: followRedirects,
              #maxRedirects: maxRedirects,
              #persistentConnection: persistentConnection,
              #requestEncoder: requestEncoder,
              #responseDecoder: responseDecoder,
              #listFormat: listFormat,
              #setRequestContentTypeWhenNoPayload:
                  setRequestContentTypeWhenNoPayload,
            }),
            returnValue: _FakeRequestOptions_6(
              this,
              Invocation.method(#copyWith, [], {
                #method: method,
                #sendTimeout: sendTimeout,
                #receiveTimeout: receiveTimeout,
                #connectTimeout: connectTimeout,
                #data: data,
                #path: path,
                #queryParameters: queryParameters,
                #baseUrl: baseUrl,
                #onReceiveProgress: onReceiveProgress,
                #onSendProgress: onSendProgress,
                #cancelToken: cancelToken,
                #extra: extra,
                #headers: headers,
                #preserveHeaderCase: preserveHeaderCase,
                #responseType: responseType,
                #contentType: contentType,
                #validateStatus: validateStatus,
                #receiveDataWhenStatusError: receiveDataWhenStatusError,
                #followRedirects: followRedirects,
                #maxRedirects: maxRedirects,
                #persistentConnection: persistentConnection,
                #requestEncoder: requestEncoder,
                #responseDecoder: responseDecoder,
                #listFormat: listFormat,
                #setRequestContentTypeWhenNoPayload:
                    setRequestContentTypeWhenNoPayload,
              }),
            ),
          )
          as _i2.RequestOptions);
}

/// A class which mocks [UnifiedAuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockUnifiedAuthProvider extends _i1.Mock
    implements _i16.UnifiedAuthProvider {
  MockUnifiedAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isReady =>
      (super.noSuchMethod(Invocation.getter(#isReady), returnValue: false)
          as bool);

  @override
  bool get isInitializing =>
      (super.noSuchMethod(
            Invocation.getter(#isInitializing),
            returnValue: false,
          )
          as bool);

  @override
  _i16.AuthInitializationPhase get initializationPhase =>
      (super.noSuchMethod(
            Invocation.getter(#initializationPhase),
            returnValue: _i16.AuthInitializationPhase.notStarted,
          )
          as _i16.AuthInitializationPhase);

  @override
  bool get isAuthenticated =>
      (super.noSuchMethod(
            Invocation.getter(#isAuthenticated),
            returnValue: false,
          )
          as bool);

  @override
  _i9.AutoDisposeNotifierProviderRef<_i10.AuthState> get ref =>
      (super.noSuchMethod(
            Invocation.getter(#ref),
            returnValue: _FakeAutoDisposeNotifierProviderRef_9<_i10.AuthState>(
              this,
              Invocation.getter(#ref),
            ),
          )
          as _i9.AutoDisposeNotifierProviderRef<_i10.AuthState>);

  @override
  _i10.AuthState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeAuthState_10(this, Invocation.getter(#state)),
          )
          as _i10.AuthState);

  @override
  set state(_i10.AuthState? value) => super.noSuchMethod(
    Invocation.setter(#state, value),
    returnValueForMissingStub: null,
  );

  @override
  _i10.AuthState build() =>
      (super.noSuchMethod(
            Invocation.method(#build, []),
            returnValue: _FakeAuthState_10(this, Invocation.method(#build, [])),
          )
          as _i10.AuthState);

  @override
  _i12.Future<_i10.AuthResult> signInWithEmail({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmail, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i12.Future<_i10.AuthResult>.value(
              _FakeAuthResult_11(
                this,
                Invocation.method(#signInWithEmail, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i12.Future<_i10.AuthResult>);

  @override
  _i12.Future<_i10.AuthResult> signInWithGoogle() =>
      (super.noSuchMethod(
            Invocation.method(#signInWithGoogle, []),
            returnValue: _i12.Future<_i10.AuthResult>.value(
              _FakeAuthResult_11(
                this,
                Invocation.method(#signInWithGoogle, []),
              ),
            ),
          )
          as _i12.Future<_i10.AuthResult>);

  @override
  _i12.Future<_i10.AuthResult> signUp({
    required String? firstName,
    required String? lastName,
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUp, [], {
              #firstName: firstName,
              #lastName: lastName,
              #email: email,
              #password: password,
            }),
            returnValue: _i12.Future<_i10.AuthResult>.value(
              _FakeAuthResult_11(
                this,
                Invocation.method(#signUp, [], {
                  #firstName: firstName,
                  #lastName: lastName,
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i12.Future<_i10.AuthResult>);

  @override
  _i12.Future<_i10.AuthResult> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i12.Future<_i10.AuthResult>.value(
              _FakeAuthResult_11(this, Invocation.method(#signOut, [])),
            ),
          )
          as _i12.Future<_i10.AuthResult>);

  @override
  _i12.Future<_i10.AuthResult> resetPassword(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [email]),
            returnValue: _i12.Future<_i10.AuthResult>.value(
              _FakeAuthResult_11(
                this,
                Invocation.method(#resetPassword, [email]),
              ),
            ),
          )
          as _i12.Future<_i10.AuthResult>);

  @override
  _i12.Future<_i10.AuthResult> updatePassword({
    required String? newPassword,
    String? accessToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #newPassword: newPassword,
              #accessToken: accessToken,
            }),
            returnValue: _i12.Future<_i10.AuthResult>.value(
              _FakeAuthResult_11(
                this,
                Invocation.method(#updatePassword, [], {
                  #newPassword: newPassword,
                  #accessToken: accessToken,
                }),
              ),
            ),
          )
          as _i12.Future<_i10.AuthResult>);

  @override
  _i12.Future<_i10.AuthResult> resendVerificationEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#resendVerificationEmail, [email]),
            returnValue: _i12.Future<_i10.AuthResult>.value(
              _FakeAuthResult_11(
                this,
                Invocation.method(#resendVerificationEmail, [email]),
              ),
            ),
          )
          as _i12.Future<_i10.AuthResult>);

  @override
  _i12.Future<bool> validateSession() =>
      (super.noSuchMethod(
            Invocation.method(#validateSession, []),
            returnValue: _i12.Future<bool>.value(false),
          )
          as _i12.Future<bool>);

  @override
  _i12.Future<void> retryInitialization() =>
      (super.noSuchMethod(
            Invocation.method(#retryInitialization, []),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void listenSelf(
    void Function(_i10.AuthState?, _i10.AuthState)? listener, {
    void Function(Object, StackTrace)? onError,
  }) => super.noSuchMethod(
    Invocation.method(#listenSelf, [listener], {#onError: onError}),
    returnValueForMissingStub: null,
  );

  @override
  bool updateShouldNotify(_i10.AuthState? previous, _i10.AuthState? next) =>
      (super.noSuchMethod(
            Invocation.method(#updateShouldNotify, [previous, next]),
            returnValue: false,
          )
          as bool);
}
