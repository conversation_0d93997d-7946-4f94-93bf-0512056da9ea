// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in carnow/test/unit/core/auth/production_google_auth_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:convert' as _i7;
import 'dart:typed_data' as _i8;

import 'package:google_sign_in/google_sign_in.dart' as _i2;
import 'package:google_sign_in_platform_interface/google_sign_in_platform_interface.dart'
    as _i4;
import 'package:http/http.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGoogleSignInAuthentication_0 extends _i1.SmartFake
    implements _i2.GoogleSignInAuthentication {
  _FakeGoogleSignInAuthentication_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResponse_1 extends _i1.SmartFake implements _i3.Response {
  _FakeResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeStreamedResponse_2 extends _i1.SmartFake
    implements _i3.StreamedResponse {
  _FakeStreamedResponse_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GoogleSignIn].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleSignIn extends _i1.Mock implements _i2.GoogleSignIn {
  MockGoogleSignIn() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.SignInOption get signInOption =>
      (super.noSuchMethod(
            Invocation.getter(#signInOption),
            returnValue: _i4.SignInOption.standard,
          )
          as _i4.SignInOption);

  @override
  List<String> get scopes =>
      (super.noSuchMethod(Invocation.getter(#scopes), returnValue: <String>[])
          as List<String>);

  @override
  bool get forceCodeForRefreshToken =>
      (super.noSuchMethod(
            Invocation.getter(#forceCodeForRefreshToken),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Stream<_i2.GoogleSignInAccount?> get onCurrentUserChanged =>
      (super.noSuchMethod(
            Invocation.getter(#onCurrentUserChanged),
            returnValue: _i5.Stream<_i2.GoogleSignInAccount?>.empty(),
          )
          as _i5.Stream<_i2.GoogleSignInAccount?>);

  @override
  _i5.Future<_i2.GoogleSignInAccount?> signInSilently({
    bool? suppressErrors = true,
    bool? reAuthenticate = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInSilently, [], {
              #suppressErrors: suppressErrors,
              #reAuthenticate: reAuthenticate,
            }),
            returnValue: _i5.Future<_i2.GoogleSignInAccount?>.value(),
          )
          as _i5.Future<_i2.GoogleSignInAccount?>);

  @override
  _i5.Future<bool> isSignedIn() =>
      (super.noSuchMethod(
            Invocation.method(#isSignedIn, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<_i2.GoogleSignInAccount?> signIn() =>
      (super.noSuchMethod(
            Invocation.method(#signIn, []),
            returnValue: _i5.Future<_i2.GoogleSignInAccount?>.value(),
          )
          as _i5.Future<_i2.GoogleSignInAccount?>);

  @override
  _i5.Future<_i2.GoogleSignInAccount?> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i5.Future<_i2.GoogleSignInAccount?>.value(),
          )
          as _i5.Future<_i2.GoogleSignInAccount?>);

  @override
  _i5.Future<_i2.GoogleSignInAccount?> disconnect() =>
      (super.noSuchMethod(
            Invocation.method(#disconnect, []),
            returnValue: _i5.Future<_i2.GoogleSignInAccount?>.value(),
          )
          as _i5.Future<_i2.GoogleSignInAccount?>);

  @override
  _i5.Future<bool> requestScopes(List<String>? scopes) =>
      (super.noSuchMethod(
            Invocation.method(#requestScopes, [scopes]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canAccessScopes(
    List<String>? scopes, {
    String? accessToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #canAccessScopes,
              [scopes],
              {#accessToken: accessToken},
            ),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);
}

/// A class which mocks [GoogleSignInAccount].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockGoogleSignInAccount extends _i1.Mock
    implements _i2.GoogleSignInAccount {
  MockGoogleSignInAccount() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get email =>
      (super.noSuchMethod(
            Invocation.getter(#email),
            returnValue: _i6.dummyValue<String>(
              this,
              Invocation.getter(#email),
            ),
          )
          as String);

  @override
  String get id =>
      (super.noSuchMethod(
            Invocation.getter(#id),
            returnValue: _i6.dummyValue<String>(this, Invocation.getter(#id)),
          )
          as String);

  @override
  _i5.Future<_i2.GoogleSignInAuthentication> get authentication =>
      (super.noSuchMethod(
            Invocation.getter(#authentication),
            returnValue: _i5.Future<_i2.GoogleSignInAuthentication>.value(
              _FakeGoogleSignInAuthentication_0(
                this,
                Invocation.getter(#authentication),
              ),
            ),
          )
          as _i5.Future<_i2.GoogleSignInAuthentication>);

  @override
  _i5.Future<Map<String, String>> get authHeaders =>
      (super.noSuchMethod(
            Invocation.getter(#authHeaders),
            returnValue: _i5.Future<Map<String, String>>.value(
              <String, String>{},
            ),
          )
          as _i5.Future<Map<String, String>>);

  @override
  _i5.Future<void> clearAuthCache() =>
      (super.noSuchMethod(
            Invocation.method(#clearAuthCache, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [GoogleSignInAuthentication].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleSignInAuthentication extends _i1.Mock
    implements _i2.GoogleSignInAuthentication {
  MockGoogleSignInAuthentication() {
    _i1.throwOnMissingStub(this);
  }
}

/// A class which mocks [Client].
///
/// See the documentation for Mockito's code generation for more information.
class MockClient extends _i1.Mock implements _i3.Client {
  MockClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i3.Response> head(Uri? url, {Map<String, String>? headers}) =>
      (super.noSuchMethod(
            Invocation.method(#head, [url], {#headers: headers}),
            returnValue: _i5.Future<_i3.Response>.value(
              _FakeResponse_1(
                this,
                Invocation.method(#head, [url], {#headers: headers}),
              ),
            ),
          )
          as _i5.Future<_i3.Response>);

  @override
  _i5.Future<_i3.Response> get(Uri? url, {Map<String, String>? headers}) =>
      (super.noSuchMethod(
            Invocation.method(#get, [url], {#headers: headers}),
            returnValue: _i5.Future<_i3.Response>.value(
              _FakeResponse_1(
                this,
                Invocation.method(#get, [url], {#headers: headers}),
              ),
            ),
          )
          as _i5.Future<_i3.Response>);

  @override
  _i5.Future<_i3.Response> post(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i7.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #post,
              [url],
              {#headers: headers, #body: body, #encoding: encoding},
            ),
            returnValue: _i5.Future<_i3.Response>.value(
              _FakeResponse_1(
                this,
                Invocation.method(
                  #post,
                  [url],
                  {#headers: headers, #body: body, #encoding: encoding},
                ),
              ),
            ),
          )
          as _i5.Future<_i3.Response>);

  @override
  _i5.Future<_i3.Response> put(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i7.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #put,
              [url],
              {#headers: headers, #body: body, #encoding: encoding},
            ),
            returnValue: _i5.Future<_i3.Response>.value(
              _FakeResponse_1(
                this,
                Invocation.method(
                  #put,
                  [url],
                  {#headers: headers, #body: body, #encoding: encoding},
                ),
              ),
            ),
          )
          as _i5.Future<_i3.Response>);

  @override
  _i5.Future<_i3.Response> patch(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i7.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #patch,
              [url],
              {#headers: headers, #body: body, #encoding: encoding},
            ),
            returnValue: _i5.Future<_i3.Response>.value(
              _FakeResponse_1(
                this,
                Invocation.method(
                  #patch,
                  [url],
                  {#headers: headers, #body: body, #encoding: encoding},
                ),
              ),
            ),
          )
          as _i5.Future<_i3.Response>);

  @override
  _i5.Future<_i3.Response> delete(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i7.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #delete,
              [url],
              {#headers: headers, #body: body, #encoding: encoding},
            ),
            returnValue: _i5.Future<_i3.Response>.value(
              _FakeResponse_1(
                this,
                Invocation.method(
                  #delete,
                  [url],
                  {#headers: headers, #body: body, #encoding: encoding},
                ),
              ),
            ),
          )
          as _i5.Future<_i3.Response>);

  @override
  _i5.Future<String> read(Uri? url, {Map<String, String>? headers}) =>
      (super.noSuchMethod(
            Invocation.method(#read, [url], {#headers: headers}),
            returnValue: _i5.Future<String>.value(
              _i6.dummyValue<String>(
                this,
                Invocation.method(#read, [url], {#headers: headers}),
              ),
            ),
          )
          as _i5.Future<String>);

  @override
  _i5.Future<_i8.Uint8List> readBytes(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#readBytes, [url], {#headers: headers}),
            returnValue: _i5.Future<_i8.Uint8List>.value(_i8.Uint8List(0)),
          )
          as _i5.Future<_i8.Uint8List>);

  @override
  _i5.Future<_i3.StreamedResponse> send(_i3.BaseRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#send, [request]),
            returnValue: _i5.Future<_i3.StreamedResponse>.value(
              _FakeStreamedResponse_2(
                this,
                Invocation.method(#send, [request]),
              ),
            ),
          )
          as _i5.Future<_i3.StreamedResponse>);

  @override
  void close() => super.noSuchMethod(
    Invocation.method(#close, []),
    returnValueForMissingStub: null,
  );
}
