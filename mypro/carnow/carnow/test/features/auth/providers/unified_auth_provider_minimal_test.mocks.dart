// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in carnow/test/features/auth/providers/unified_auth_provider_minimal_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:carnow/core/auth/enhanced_secure_token_storage.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [EnhancedSecureTokenStorage].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockEnhancedSecureTokenStorage extends _i1.Mock
    implements _i2.EnhancedSecureTokenStorage {
  MockEnhancedSecureTokenStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isEncrypted =>
      (super.noSuchMethod(Invocation.getter(#isEncrypted), returnValue: false)
          as bool);

  @override
  _i3.Future<bool> get supportsBiometrics =>
      (super.noSuchMethod(
            Invocation.getter(#supportsBiometrics),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<void> storeToken(String? token, {DateTime? expiryDate}) =>
      (super.noSuchMethod(
            Invocation.method(#storeToken, [token], {#expiryDate: expiryDate}),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> storeRefreshToken(
    String? refreshToken, {
    DateTime? expiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #storeRefreshToken,
              [refreshToken],
              {#expiryDate: expiryDate},
            ),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> storeSessionData(Map<String, dynamic>? sessionData) =>
      (super.noSuchMethod(
            Invocation.method(#storeSessionData, [sessionData]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> storeTokenExpiry(DateTime? expiryDate) =>
      (super.noSuchMethod(
            Invocation.method(#storeTokenExpiry, [expiryDate]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<String?> getToken() =>
      (super.noSuchMethod(
            Invocation.method(#getToken, []),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<String?> getRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshToken, []),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<Map<String, dynamic>> getSessionData() =>
      (super.noSuchMethod(
            Invocation.method(#getSessionData, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<DateTime?> getTokenExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTokenExpiry, []),
            returnValue: _i3.Future<DateTime?>.value(),
          )
          as _i3.Future<DateTime?>);

  @override
  _i3.Future<bool> hasToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> hasRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasRefreshToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> willTokenExpireWithin(Duration? duration) =>
      (super.noSuchMethod(
            Invocation.method(#willTokenExpireWithin, [duration]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<DateTime?> getRefreshTokenExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshTokenExpiry, []),
            returnValue: _i3.Future<DateTime?>.value(),
          )
          as _i3.Future<DateTime?>);

  @override
  _i3.Future<bool> hasValidToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> hasValidRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidRefreshToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> isTokenExpired() =>
      (super.noSuchMethod(
            Invocation.method(#isTokenExpired, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> isTokenValid(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#isTokenValid, [token]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<Duration?> getTimeUntilExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTimeUntilExpiry, []),
            returnValue: _i3.Future<Duration?>.value(),
          )
          as _i3.Future<Duration?>);

  @override
  _i3.Future<void> clearAllData() =>
      (super.noSuchMethod(
            Invocation.method(#clearAllData, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> clearCorruptedData() =>
      (super.noSuchMethod(
            Invocation.method(#clearCorruptedData, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> clearExpiredTokens() =>
      (super.noSuchMethod(
            Invocation.method(#clearExpiredTokens, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updateTokenExpiry(DateTime? newExpiryDate) =>
      (super.noSuchMethod(
            Invocation.method(#updateTokenExpiry, [newExpiryDate]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> rotateTokens({
    required String? newToken,
    String? newRefreshToken,
    DateTime? newExpiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#rotateTokens, [], {
              #newToken: newToken,
              #newRefreshToken: newRefreshToken,
              #newExpiryDate: newExpiryDate,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<bool> validateStorageIntegrity() =>
      (super.noSuchMethod(
            Invocation.method(#validateStorageIntegrity, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<Map<String, dynamic>> getStorageMetadata() =>
      (super.noSuchMethod(
            Invocation.method(#getStorageMetadata, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);
}

/// A class which mocks [EnhancedSecureTokenStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockUnifiedAuthEnhancedSecureTokenStorage extends _i1.Mock
    implements _i2.EnhancedSecureTokenStorage {
  MockUnifiedAuthEnhancedSecureTokenStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isEncrypted =>
      (super.noSuchMethod(Invocation.getter(#isEncrypted), returnValue: false)
          as bool);

  @override
  _i3.Future<bool> get supportsBiometrics =>
      (super.noSuchMethod(
            Invocation.getter(#supportsBiometrics),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<void> storeToken(String? token, {DateTime? expiryDate}) =>
      (super.noSuchMethod(
            Invocation.method(#storeToken, [token], {#expiryDate: expiryDate}),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> storeRefreshToken(
    String? refreshToken, {
    DateTime? expiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #storeRefreshToken,
              [refreshToken],
              {#expiryDate: expiryDate},
            ),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> storeSessionData(Map<String, dynamic>? sessionData) =>
      (super.noSuchMethod(
            Invocation.method(#storeSessionData, [sessionData]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> storeTokenExpiry(DateTime? expiryDate) =>
      (super.noSuchMethod(
            Invocation.method(#storeTokenExpiry, [expiryDate]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<String?> getToken() =>
      (super.noSuchMethod(
            Invocation.method(#getToken, []),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<String?> getRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshToken, []),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<Map<String, dynamic>> getSessionData() =>
      (super.noSuchMethod(
            Invocation.method(#getSessionData, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<DateTime?> getTokenExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTokenExpiry, []),
            returnValue: _i3.Future<DateTime?>.value(),
          )
          as _i3.Future<DateTime?>);

  @override
  _i3.Future<bool> hasToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> hasRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasRefreshToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> willTokenExpireWithin(Duration? duration) =>
      (super.noSuchMethod(
            Invocation.method(#willTokenExpireWithin, [duration]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<DateTime?> getRefreshTokenExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshTokenExpiry, []),
            returnValue: _i3.Future<DateTime?>.value(),
          )
          as _i3.Future<DateTime?>);

  @override
  _i3.Future<bool> hasValidToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> hasValidRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidRefreshToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> isTokenExpired() =>
      (super.noSuchMethod(
            Invocation.method(#isTokenExpired, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> isTokenValid(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#isTokenValid, [token]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<Duration?> getTimeUntilExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTimeUntilExpiry, []),
            returnValue: _i3.Future<Duration?>.value(),
          )
          as _i3.Future<Duration?>);

  @override
  _i3.Future<void> clearAllData() =>
      (super.noSuchMethod(
            Invocation.method(#clearAllData, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> clearCorruptedData() =>
      (super.noSuchMethod(
            Invocation.method(#clearCorruptedData, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> clearExpiredTokens() =>
      (super.noSuchMethod(
            Invocation.method(#clearExpiredTokens, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updateTokenExpiry(DateTime? newExpiryDate) =>
      (super.noSuchMethod(
            Invocation.method(#updateTokenExpiry, [newExpiryDate]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> rotateTokens({
    required String? newToken,
    String? newRefreshToken,
    DateTime? newExpiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#rotateTokens, [], {
              #newToken: newToken,
              #newRefreshToken: newRefreshToken,
              #newExpiryDate: newExpiryDate,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<bool> validateStorageIntegrity() =>
      (super.noSuchMethod(
            Invocation.method(#validateStorageIntegrity, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<Map<String, dynamic>> getStorageMetadata() =>
      (super.noSuchMethod(
            Invocation.method(#getStorageMetadata, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);
}
