// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in carnow/test/features/auth/services/session_management_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:carnow/core/auth/enhanced_secure_token_storage.dart' as _i7;
import 'package:carnow/core/error/app_error.dart' as _i3;
import 'package:carnow/core/error/retry_service.dart' as _i8;
import 'package:carnow/core/models/api_response.dart' as _i2;
import 'package:carnow/core/networking/simple_api_client.dart' as _i5;
import 'package:dio/dio.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiResponse_0<T1> extends _i1.SmartFake
    implements _i2.ApiResponse<T1> {
  _FakeApiResponse_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAppResult_1<T1> extends _i1.SmartFake implements _i3.AppResult<T1> {
  _FakeAppResult_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFuture_2<T1> extends _i1.SmartFake implements _i4.Future<T1> {
  _FakeFuture_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SimpleApiClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSimpleApiClient extends _i1.Mock implements _i5.SimpleApiClient {
  MockSimpleApiClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.ApiResponse<T>> get<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #get,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #get,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> getApi<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getApi,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #getApi,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> postApi<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #postApi,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #postApi,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> putApi<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #putApi,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #putApi,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> deleteApi<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteApi,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #deleteApi,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> post<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #post,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #post,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> put<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #put,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #put,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> delete<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #delete,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #delete,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> patch<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #patch,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #patch,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> uploadFormData<T>(
    String? endpoint,
    _i6.FormData? formData, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #uploadFormData,
              [endpoint, formData],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #uploadFormData,
                  [endpoint, formData],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);
}

/// A class which mocks [EnhancedSecureTokenStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockEnhancedSecureTokenStorage extends _i1.Mock
    implements _i7.EnhancedSecureTokenStorage {
  MockEnhancedSecureTokenStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isEncrypted =>
      (super.noSuchMethod(Invocation.getter(#isEncrypted), returnValue: false)
          as bool);

  @override
  _i4.Future<bool> get supportsBiometrics =>
      (super.noSuchMethod(
            Invocation.getter(#supportsBiometrics),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<void> storeToken(String? token, {DateTime? expiryDate}) =>
      (super.noSuchMethod(
            Invocation.method(#storeToken, [token], {#expiryDate: expiryDate}),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> storeRefreshToken(
    String? refreshToken, {
    DateTime? expiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #storeRefreshToken,
              [refreshToken],
              {#expiryDate: expiryDate},
            ),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> storeSessionData(Map<String, dynamic>? sessionData) =>
      (super.noSuchMethod(
            Invocation.method(#storeSessionData, [sessionData]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> storeTokenExpiry(DateTime? expiryDate) =>
      (super.noSuchMethod(
            Invocation.method(#storeTokenExpiry, [expiryDate]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<String?> getToken() =>
      (super.noSuchMethod(
            Invocation.method(#getToken, []),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<String?> getRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshToken, []),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<Map<String, dynamic>> getSessionData() =>
      (super.noSuchMethod(
            Invocation.method(#getSessionData, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<DateTime?> getTokenExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTokenExpiry, []),
            returnValue: _i4.Future<DateTime?>.value(),
          )
          as _i4.Future<DateTime?>);

  @override
  _i4.Future<bool> hasToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasToken, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> hasRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasRefreshToken, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> willTokenExpireWithin(Duration? duration) =>
      (super.noSuchMethod(
            Invocation.method(#willTokenExpireWithin, [duration]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<DateTime?> getRefreshTokenExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshTokenExpiry, []),
            returnValue: _i4.Future<DateTime?>.value(),
          )
          as _i4.Future<DateTime?>);

  @override
  _i4.Future<bool> hasValidToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidToken, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> hasValidRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidRefreshToken, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> isTokenExpired() =>
      (super.noSuchMethod(
            Invocation.method(#isTokenExpired, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> isTokenValid(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#isTokenValid, [token]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<Duration?> getTimeUntilExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTimeUntilExpiry, []),
            returnValue: _i4.Future<Duration?>.value(),
          )
          as _i4.Future<Duration?>);

  @override
  _i4.Future<void> clearAllData() =>
      (super.noSuchMethod(
            Invocation.method(#clearAllData, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearCorruptedData() =>
      (super.noSuchMethod(
            Invocation.method(#clearCorruptedData, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearExpiredTokens() =>
      (super.noSuchMethod(
            Invocation.method(#clearExpiredTokens, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateTokenExpiry(DateTime? newExpiryDate) =>
      (super.noSuchMethod(
            Invocation.method(#updateTokenExpiry, [newExpiryDate]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> rotateTokens({
    required String? newToken,
    String? newRefreshToken,
    DateTime? newExpiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#rotateTokens, [], {
              #newToken: newToken,
              #newRefreshToken: newRefreshToken,
              #newExpiryDate: newExpiryDate,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> validateStorageIntegrity() =>
      (super.noSuchMethod(
            Invocation.method(#validateStorageIntegrity, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<Map<String, dynamic>> getStorageMetadata() =>
      (super.noSuchMethod(
            Invocation.method(#getStorageMetadata, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);
}

/// A class which mocks [RetryService].
///
/// See the documentation for Mockito's code generation for more information.
class MockRetryService extends _i1.Mock implements _i8.RetryService {
  MockRetryService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i3.AppResult<T>> executeWithRetry<T>({
    required _i4.Future<T> Function()? operation,
    required String? operationName,
    _i8.RetryConfig? config = const _i8.RetryConfig(),
    Map<String, dynamic>? metadata,
    void Function(int, _i3.AppError)? onRetry,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#executeWithRetry, [], {
              #operation: operation,
              #operationName: operationName,
              #config: config,
              #metadata: metadata,
              #onRetry: onRetry,
            }),
            returnValue: _i4.Future<_i3.AppResult<T>>.value(
              _FakeAppResult_1<T>(
                this,
                Invocation.method(#executeWithRetry, [], {
                  #operation: operation,
                  #operationName: operationName,
                  #config: config,
                  #metadata: metadata,
                  #onRetry: onRetry,
                }),
              ),
            ),
          )
          as _i4.Future<_i3.AppResult<T>>);

  @override
  _i4.Future<_i3.AppResult<T>> executeWithTimeoutAndRetry<T>({
    required _i4.Future<T> Function()? operation,
    required String? operationName,
    required Duration? timeout,
    _i8.RetryConfig? config = const _i8.RetryConfig(),
    Map<String, dynamic>? metadata,
    void Function(int, _i3.AppError)? onRetry,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#executeWithTimeoutAndRetry, [], {
              #operation: operation,
              #operationName: operationName,
              #timeout: timeout,
              #config: config,
              #metadata: metadata,
              #onRetry: onRetry,
            }),
            returnValue: _i4.Future<_i3.AppResult<T>>.value(
              _FakeAppResult_1<T>(
                this,
                Invocation.method(#executeWithTimeoutAndRetry, [], {
                  #operation: operation,
                  #operationName: operationName,
                  #timeout: timeout,
                  #config: config,
                  #metadata: metadata,
                  #onRetry: onRetry,
                }),
              ),
            ),
          )
          as _i4.Future<_i3.AppResult<T>>);

  @override
  _i4.Future<T> Function() createRetryWrapper<T>({
    required _i4.Future<T> Function()? operation,
    required String? operationName,
    _i8.RetryConfig? config = const _i8.RetryConfig(),
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createRetryWrapper, [], {
              #operation: operation,
              #operationName: operationName,
              #config: config,
              #metadata: metadata,
            }),
            returnValue: () =>
                _i9.ifNotNull(
                  _i9.dummyValueOrNull<T>(
                    this,
                    Invocation.method(#createRetryWrapper, [], {
                      #operation: operation,
                      #operationName: operationName,
                      #config: config,
                      #metadata: metadata,
                    }),
                  ),
                  (T v) => _i4.Future<T>.value(v),
                ) ??
                _FakeFuture_2<T>(
                  this,
                  Invocation.method(#createRetryWrapper, [], {
                    #operation: operation,
                    #operationName: operationName,
                    #config: config,
                    #metadata: metadata,
                  }),
                ),
          )
          as _i4.Future<T> Function());
}

/// A class which mocks [EnhancedSecureTokenStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockSessionEnhancedSecureTokenStorage extends _i1.Mock
    implements _i7.EnhancedSecureTokenStorage {
  MockSessionEnhancedSecureTokenStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isEncrypted =>
      (super.noSuchMethod(Invocation.getter(#isEncrypted), returnValue: false)
          as bool);

  @override
  _i4.Future<bool> get supportsBiometrics =>
      (super.noSuchMethod(
            Invocation.getter(#supportsBiometrics),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<void> storeToken(String? token, {DateTime? expiryDate}) =>
      (super.noSuchMethod(
            Invocation.method(#storeToken, [token], {#expiryDate: expiryDate}),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> storeRefreshToken(
    String? refreshToken, {
    DateTime? expiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #storeRefreshToken,
              [refreshToken],
              {#expiryDate: expiryDate},
            ),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> storeSessionData(Map<String, dynamic>? sessionData) =>
      (super.noSuchMethod(
            Invocation.method(#storeSessionData, [sessionData]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> storeTokenExpiry(DateTime? expiryDate) =>
      (super.noSuchMethod(
            Invocation.method(#storeTokenExpiry, [expiryDate]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<String?> getToken() =>
      (super.noSuchMethod(
            Invocation.method(#getToken, []),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<String?> getRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshToken, []),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<Map<String, dynamic>> getSessionData() =>
      (super.noSuchMethod(
            Invocation.method(#getSessionData, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<DateTime?> getTokenExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTokenExpiry, []),
            returnValue: _i4.Future<DateTime?>.value(),
          )
          as _i4.Future<DateTime?>);

  @override
  _i4.Future<bool> hasToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasToken, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> hasRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasRefreshToken, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> willTokenExpireWithin(Duration? duration) =>
      (super.noSuchMethod(
            Invocation.method(#willTokenExpireWithin, [duration]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<DateTime?> getRefreshTokenExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshTokenExpiry, []),
            returnValue: _i4.Future<DateTime?>.value(),
          )
          as _i4.Future<DateTime?>);

  @override
  _i4.Future<bool> hasValidToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidToken, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> hasValidRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidRefreshToken, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> isTokenExpired() =>
      (super.noSuchMethod(
            Invocation.method(#isTokenExpired, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> isTokenValid(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#isTokenValid, [token]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<Duration?> getTimeUntilExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTimeUntilExpiry, []),
            returnValue: _i4.Future<Duration?>.value(),
          )
          as _i4.Future<Duration?>);

  @override
  _i4.Future<void> clearAllData() =>
      (super.noSuchMethod(
            Invocation.method(#clearAllData, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearCorruptedData() =>
      (super.noSuchMethod(
            Invocation.method(#clearCorruptedData, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearExpiredTokens() =>
      (super.noSuchMethod(
            Invocation.method(#clearExpiredTokens, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateTokenExpiry(DateTime? newExpiryDate) =>
      (super.noSuchMethod(
            Invocation.method(#updateTokenExpiry, [newExpiryDate]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> rotateTokens({
    required String? newToken,
    String? newRefreshToken,
    DateTime? newExpiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#rotateTokens, [], {
              #newToken: newToken,
              #newRefreshToken: newRefreshToken,
              #newExpiryDate: newExpiryDate,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> validateStorageIntegrity() =>
      (super.noSuchMethod(
            Invocation.method(#validateStorageIntegrity, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<Map<String, dynamic>> getStorageMetadata() =>
      (super.noSuchMethod(
            Invocation.method(#getStorageMetadata, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);
}
