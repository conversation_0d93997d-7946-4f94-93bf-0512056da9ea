// Mocks generated by Mockito 5.4.6 from annotations
// in carnow/test/core/services/subscription_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:carnow/core/models/api_response.dart' as _i2;
import 'package:carnow/core/networking/simple_api_client.dart' as _i3;
import 'package:dio/dio.dart' as _i5;
import 'package:logger/src/log_level.dart' as _i7;
import 'package:logger/src/logger.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiResponse_0<T1> extends _i1.SmartFake
    implements _i2.ApiResponse<T1> {
  _FakeApiResponse_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SimpleApiClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSimpleApiClient extends _i1.Mock implements _i3.SimpleApiClient {
  MockSimpleApiClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.ApiResponse<T>> get<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #get,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #get,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> getApi<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getApi,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #getApi,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> postApi<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #postApi,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #postApi,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> putApi<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #putApi,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #putApi,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> deleteApi<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteApi,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #deleteApi,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> post<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #post,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #post,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> put<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #put,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #put,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> delete<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #delete,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #delete,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> patch<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #patch,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #patch,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> uploadFormData<T>(
    String? endpoint,
    _i5.FormData? formData, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #uploadFormData,
              [endpoint, formData],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #uploadFormData,
                  [endpoint, formData],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.ApiResponse<T>>);
}

/// A class which mocks [Logger].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogger extends _i1.Mock implements _i6.Logger {
  MockLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> get init =>
      (super.noSuchMethod(
            Invocation.getter(#init),
            returnValue: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void v(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #v,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void t(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #t,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void d(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #d,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void i(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #i,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void w(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #w,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void e(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #e,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void wtf(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #wtf,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void f(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #f,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void log(
    _i7.Level? level,
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #log,
      [level, message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  bool isClosed() =>
      (super.noSuchMethod(Invocation.method(#isClosed, []), returnValue: false)
          as bool);

  @override
  _i4.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
