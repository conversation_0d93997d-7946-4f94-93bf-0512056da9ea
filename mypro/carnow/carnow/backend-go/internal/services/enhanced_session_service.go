package services

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// EnhancedSessionService handles advanced session management
type EnhancedSessionService struct {
	config *config.Config
	db     *gorm.DB
	logger *zap.Logger
}

// SessionStatus represents session status
type SessionStatus string

const (
	SessionStatusActive    SessionStatus = "active"
	SessionStatusExpired   SessionStatus = "expired"
	SessionStatusRevoked   SessionStatus = "revoked"
	SessionStatusSuspended SessionStatus = "suspended"
)

// DeviceType represents device type
type DeviceType string

const (
	DeviceTypeMobile  DeviceType = "mobile"
	DeviceTypeDesktop DeviceType = "desktop"
	DeviceTypeTablet  DeviceType = "tablet"
	DeviceTypeUnknown DeviceType = "unknown"
)

// UserSession represents an enhanced user session
type UserSession struct {
	ID                string        `json:"id" gorm:"primaryKey"`
	UserID            string        `json:"user_id" gorm:"not null;index"`
	SessionToken      string        `json:"-" gorm:"not null;uniqueIndex"` // Never expose
	SessionTokenHash  string        `json:"-" gorm:"not null"`
	RefreshToken      string        `json:"-" gorm:"column:refresh_token"` // Never expose
	RefreshTokenHash  string        `json:"-" gorm:"column:refresh_token_hash"`
	DeviceFingerprint string        `json:"device_fingerprint" gorm:"not null"`
	DeviceType        DeviceType    `json:"device_type" gorm:"not null"`
	DeviceName        string        `json:"device_name"`
	IPAddress         string        `json:"ip_address" gorm:"not null"`
	UserAgent         string        `json:"user_agent"`
	Location          string        `json:"location"`
	Status            SessionStatus `json:"status" gorm:"default:active"`
	LastActivity      time.Time     `json:"last_activity" gorm:"autoUpdateTime"`
	ExpiresAt         time.Time     `json:"expires_at" gorm:"not null"`
	CreatedAt         time.Time     `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt         time.Time     `json:"updated_at" gorm:"autoUpdateTime"`

	// Security fields
	LoginAttempts      int  `json:"login_attempts" gorm:"default:0"`
	SuspiciousActivity bool `json:"suspicious_activity" gorm:"default:false"`
	TwoFactorVerified  bool `json:"two_factor_verified" gorm:"default:false"`

	// Metadata
	Metadata map[string]interface{} `json:"metadata" gorm:"serializer:json"`
}

// SessionActivity represents session activity log
type SessionActivity struct {
	ID        string                 `json:"id" gorm:"primaryKey"`
	SessionID string                 `json:"session_id" gorm:"not null;index"`
	UserID    string                 `json:"user_id" gorm:"not null;index"`
	Action    string                 `json:"action" gorm:"not null"`
	IPAddress string                 `json:"ip_address"`
	UserAgent string                 `json:"user_agent"`
	Metadata  map[string]interface{} `json:"metadata" gorm:"serializer:json"`
	CreatedAt time.Time              `json:"created_at" gorm:"autoCreateTime"`
}

// SessionConfig represents session configuration
type SessionConfig struct {
	MaxConcurrentSessions int           `json:"max_concurrent_sessions"`
	SessionTimeout        time.Duration `json:"session_timeout"`
	RefreshTokenTimeout   time.Duration `json:"refresh_token_timeout"`
	InactivityTimeout     time.Duration `json:"inactivity_timeout"`
	RequireMFA            bool          `json:"require_mfa"`
	AllowMultipleDevices  bool          `json:"allow_multiple_devices"`
	TrackLocation         bool          `json:"track_location"`
}

// DeviceInfo represents device information
type DeviceInfo struct {
	Fingerprint string     `json:"fingerprint"`
	Type        DeviceType `json:"type"`
	Name        string     `json:"name"`
	UserAgent   string     `json:"user_agent"`
	IPAddress   string     `json:"ip_address"`
	Location    string     `json:"location"`
}

// NewEnhancedSessionService creates a new enhanced session service
func NewEnhancedSessionService(config *config.Config, db *gorm.DB, logger *zap.Logger) *EnhancedSessionService {
	return &EnhancedSessionService{
		config: config,
		db:     db,
		logger: logger,
	}
}

// CreateSession creates a new user session with enhanced security
func (s *EnhancedSessionService) CreateSession(userID string, deviceInfo DeviceInfo, mfaVerified bool) (*UserSession, error) {
	// Check concurrent session limits
	if err := s.checkConcurrentSessionLimits(userID, deviceInfo); err != nil {
		return nil, err
	}

	// Generate session tokens
	sessionToken := s.generateSecureToken()
	refreshToken := s.generateSecureToken()

	// Create session
	session := &UserSession{
		ID:                uuid.New().String(),
		UserID:            userID,
		SessionToken:      sessionToken,
		SessionTokenHash:  s.hashToken(sessionToken),
		RefreshToken:      refreshToken,
		RefreshTokenHash:  s.hashToken(refreshToken),
		DeviceFingerprint: deviceInfo.Fingerprint,
		DeviceType:        deviceInfo.Type,
		DeviceName:        deviceInfo.Name,
		IPAddress:         deviceInfo.IPAddress,
		UserAgent:         deviceInfo.UserAgent,
		Location:          deviceInfo.Location,
		Status:            SessionStatusActive,
		LastActivity:      time.Now(),
		ExpiresAt:         time.Now().Add(s.getSessionTimeout()),
		TwoFactorVerified: mfaVerified,
		Metadata:          make(map[string]interface{}),
	}

	// Save session to database
	if err := s.db.Create(session).Error; err != nil {
		s.logger.Error("Failed to create session",
			zap.String("user_id", userID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	// Log session creation
	s.logSessionActivity(session.ID, userID, "session_created", deviceInfo.IPAddress, deviceInfo.UserAgent, map[string]interface{}{
		"device_type":        deviceInfo.Type,
		"device_fingerprint": deviceInfo.Fingerprint,
		"mfa_verified":       mfaVerified,
	})

	s.logger.Info("Session created successfully",
		zap.String("user_id", userID),
		zap.String("session_id", session.ID),
		zap.String("device_type", string(deviceInfo.Type)),
	)

	// Clear sensitive data before returning
	session.SessionToken = ""
	session.RefreshToken = ""

	return session, nil
}

// ValidateSession validates a session token
func (s *EnhancedSessionService) ValidateSession(sessionToken string) (*UserSession, error) {
	// Hash the token for lookup
	tokenHash := s.hashToken(sessionToken)

	// Find session by token hash
	var session UserSession
	if err := s.db.Where("session_token_hash = ? AND status = ?", tokenHash, SessionStatusActive).First(&session).Error; err != nil {
		return nil, fmt.Errorf("invalid session token")
	}

	// Check if session is expired
	if time.Now().After(session.ExpiresAt) {
		s.expireSession(session.ID)
		return nil, fmt.Errorf("session expired")
	}

	// Check inactivity timeout
	inactivityTimeout := s.getInactivityTimeout()
	if time.Since(session.LastActivity) > inactivityTimeout {
		s.expireSession(session.ID)
		return nil, fmt.Errorf("session inactive")
	}

	// Update last activity
	session.LastActivity = time.Now()
	s.db.Save(&session)

	return &session, nil
}

// RefreshSession refreshes a session using refresh token
func (s *EnhancedSessionService) RefreshSession(refreshToken string) (*UserSession, error) {
	// Hash the refresh token for lookup
	tokenHash := s.hashToken(refreshToken)

	// Find session by refresh token hash
	var session UserSession
	if err := s.db.Where("refresh_token_hash = ? AND status = ?", tokenHash, SessionStatusActive).First(&session).Error; err != nil {
		return nil, fmt.Errorf("invalid refresh token")
	}

	// Check if session is expired
	if time.Now().After(session.ExpiresAt) {
		s.expireSession(session.ID)
		return nil, fmt.Errorf("session expired")
	}

	// Generate new tokens
	newSessionToken := s.generateSecureToken()
	newRefreshToken := s.generateSecureToken()

	// Update session with new tokens
	session.SessionToken = newSessionToken
	session.SessionTokenHash = s.hashToken(newSessionToken)
	session.RefreshToken = newRefreshToken
	session.RefreshTokenHash = s.hashToken(newRefreshToken)
	session.LastActivity = time.Now()
	session.ExpiresAt = time.Now().Add(s.getSessionTimeout())

	if err := s.db.Save(&session).Error; err != nil {
		return nil, fmt.Errorf("failed to refresh session: %w", err)
	}

	// Log session refresh
	s.logSessionActivity(session.ID, session.UserID, "session_refreshed", session.IPAddress, session.UserAgent, nil)

	s.logger.Info("Session refreshed successfully",
		zap.String("user_id", session.UserID),
		zap.String("session_id", session.ID),
	)

	// Clear sensitive data before returning
	session.SessionToken = ""
	session.RefreshToken = ""

	return &session, nil
}

// RevokeSession revokes a specific session
func (s *EnhancedSessionService) RevokeSession(sessionID string) error {
	var session UserSession
	if err := s.db.Where("id = ?", sessionID).First(&session).Error; err != nil {
		return fmt.Errorf("session not found")
	}

	// Update session status
	session.Status = SessionStatusRevoked
	if err := s.db.Save(&session).Error; err != nil {
		return fmt.Errorf("failed to revoke session: %w", err)
	}

	// Log session revocation
	s.logSessionActivity(sessionID, session.UserID, "session_revoked", session.IPAddress, session.UserAgent, nil)

	s.logger.Info("Session revoked",
		zap.String("user_id", session.UserID),
		zap.String("session_id", sessionID),
	)

	return nil
}

// RevokeAllUserSessions revokes all sessions for a user
func (s *EnhancedSessionService) RevokeAllUserSessions(userID string, exceptSessionID string) error {
	query := s.db.Model(&UserSession{}).Where("user_id = ? AND status = ?", userID, SessionStatusActive)

	if exceptSessionID != "" {
		query = query.Where("id != ?", exceptSessionID)
	}

	result := query.Update("status", SessionStatusRevoked)
	if result.Error != nil {
		return fmt.Errorf("failed to revoke user sessions: %w", result.Error)
	}

	// Log bulk session revocation
	s.logSessionActivity("", userID, "all_sessions_revoked", "", "", map[string]interface{}{
		"revoked_count":     result.RowsAffected,
		"except_session_id": exceptSessionID,
	})

	s.logger.Info("All user sessions revoked",
		zap.String("user_id", userID),
		zap.Int64("revoked_count", result.RowsAffected),
	)

	return nil
}

// GetUserSessions retrieves all active sessions for a user
func (s *EnhancedSessionService) GetUserSessions(userID string) ([]UserSession, error) {
	var sessions []UserSession
	if err := s.db.Where("user_id = ? AND status = ?", userID, SessionStatusActive).Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get user sessions: %w", err)
	}

	// Clear sensitive data
	for i := range sessions {
		sessions[i].SessionToken = ""
		sessions[i].RefreshToken = ""
		sessions[i].SessionTokenHash = ""
		sessions[i].RefreshTokenHash = ""
	}

	return sessions, nil
}

// checkConcurrentSessionLimits checks if user has exceeded concurrent session limits
func (s *EnhancedSessionService) checkConcurrentSessionLimits(userID string, deviceInfo DeviceInfo) error {
	config := s.getSessionConfig()

	// Count active sessions
	var activeCount int64
	if err := s.db.Model(&UserSession{}).Where("user_id = ? AND status = ?", userID, SessionStatusActive).Count(&activeCount).Error; err != nil {
		return fmt.Errorf("failed to count active sessions: %w", err)
	}

	// Check concurrent session limit
	if int(activeCount) >= config.MaxConcurrentSessions {
		// If not allowing multiple devices, revoke sessions from different devices
		if !config.AllowMultipleDevices {
			s.db.Model(&UserSession{}).
				Where("user_id = ? AND device_fingerprint != ? AND status = ?", userID, deviceInfo.Fingerprint, SessionStatusActive).
				Update("status", SessionStatusRevoked)
		} else {
			return fmt.Errorf("maximum concurrent sessions exceeded")
		}
	}

	return nil
}

// Helper methods

func (s *EnhancedSessionService) generateSecureToken() string {
	return uuid.New().String() + "-" + uuid.New().String()
}

func (s *EnhancedSessionService) hashToken(token string) string {
	hash := sha256.Sum256([]byte(token + s.config.Security.EncryptionKey))
	return hex.EncodeToString(hash[:])
}

func (s *EnhancedSessionService) expireSession(sessionID string) {
	s.db.Model(&UserSession{}).Where("id = ?", sessionID).Update("status", SessionStatusExpired)
}

func (s *EnhancedSessionService) getSessionTimeout() time.Duration {
	return 24 * time.Hour // Default 24 hours
}

func (s *EnhancedSessionService) getInactivityTimeout() time.Duration {
	return 2 * time.Hour // Default 2 hours
}

func (s *EnhancedSessionService) getSessionConfig() SessionConfig {
	return SessionConfig{
		MaxConcurrentSessions: 5,
		SessionTimeout:        24 * time.Hour,
		RefreshTokenTimeout:   7 * 24 * time.Hour,
		InactivityTimeout:     2 * time.Hour,
		RequireMFA:            false,
		AllowMultipleDevices:  true,
		TrackLocation:         true,
	}
}

func (s *EnhancedSessionService) logSessionActivity(sessionID, userID, action, ipAddress, userAgent string, metadata map[string]interface{}) {
	activity := SessionActivity{
		ID:        uuid.New().String(),
		SessionID: sessionID,
		UserID:    userID,
		Action:    action,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Metadata:  metadata,
	}

	s.db.Create(&activity)
}

// GenerateDeviceFingerprint generates a device fingerprint
func (s *EnhancedSessionService) GenerateDeviceFingerprint(userAgent, ipAddress string, additionalData map[string]string) string {
	data := userAgent + ipAddress
	for key, value := range additionalData {
		data += key + value
	}

	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])[:16] // First 16 characters
}

// DetectDeviceType detects device type from user agent
func (s *EnhancedSessionService) DetectDeviceType(userAgent string) DeviceType {
	userAgent = strings.ToLower(userAgent)

	if strings.Contains(userAgent, "mobile") || strings.Contains(userAgent, "android") || strings.Contains(userAgent, "iphone") {
		return DeviceTypeMobile
	}

	if strings.Contains(userAgent, "tablet") || strings.Contains(userAgent, "ipad") {
		return DeviceTypeTablet
	}

	if strings.Contains(userAgent, "windows") || strings.Contains(userAgent, "macintosh") || strings.Contains(userAgent, "linux") {
		return DeviceTypeDesktop
	}

	return DeviceTypeUnknown
}

// GetLocationFromIP gets approximate location from IP address
func (s *EnhancedSessionService) GetLocationFromIP(ipAddress string) string {
	// TODO: Implement IP geolocation
	// This could use services like MaxMind GeoIP2 or similar

	// For now, just return the IP address
	if net.ParseIP(ipAddress) != nil {
		return fmt.Sprintf("IP: %s", ipAddress)
	}

	return "Unknown"
}
