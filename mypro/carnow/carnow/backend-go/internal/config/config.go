package config

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// maskSecret masks sensitive information for logging
func maskSecret(secret string) string {
	if secret == "" {
		return "(empty)"
	}
	if len(secret) <= 8 {
		return "***"
	}
	return secret[:4] + "***" + secret[len(secret)-4:]
}

// Config الإعدادات الرئيسية للتطبيق (Forever Plan - مبسط)
type Config struct {
	App      AppConfig      `mapstructure:"app"`
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Redis    RedisConfig    `mapstructure:"redis"`
	Supabase SupabaseConfig `mapstructure:"supabase"`
	Google   GoogleConfig   `mapstructure:"google"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Security SecurityConfig `mapstructure:"security"`
	Logging  LoggingConfig  `mapstructure:"logging"`
	Features FeatureFlags   `mapstructure:"features"`
	SMS      SMSConfig      `mapstructure:"sms"`
	Email    EmailConfig    `mapstructure:"email"`
	MFA      MFAConfig      `mapstructure:"mfa"`
}

// AppConfig إعدادات التطبيق الأساسية
type AppConfig struct {
	Name        string `mapstructure:"name"`
	Version     string `mapstructure:"version"`
	Environment string `mapstructure:"environment"`
	Debug       bool   `mapstructure:"debug"`
	Timezone    string `mapstructure:"timezone"`
	FrontendURL string `mapstructure:"frontend_url"`
}

// ServerConfig إعدادات الخادم
type ServerConfig struct {
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	ReadTimeout     time.Duration `mapstructure:"read_timeout"`
	WriteTimeout    time.Duration `mapstructure:"write_timeout"`
	IdleTimeout     time.Duration `mapstructure:"idle_timeout"`
	MaxHeaderBytes  int           `mapstructure:"max_header_bytes"`
	GracefulTimeout time.Duration `mapstructure:"graceful_timeout"`
}

// DatabaseConfig إعدادات قاعدة البيانات (Forever Plan - بسيط)
type DatabaseConfig struct {
	Host            string              `mapstructure:"host"`
	Port            int                 `mapstructure:"port"`
	Username        string              `mapstructure:"username"`
	Password        string              `mapstructure:"password"`
	Database        string              `mapstructure:"database"`
	SSLMode         string              `mapstructure:"ssl_mode"`
	MaxOpenConns    int                 `mapstructure:"max_open_conns"`
	MaxIdleConns    int                 `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration       `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration       `mapstructure:"conn_max_idle_time"`
	ReadReplicas    []ReadReplicaConfig `mapstructure:"read_replicas"`
}

// ReadReplicaConfig إعدادات قواعد البيانات المساعدة للقراءة
type ReadReplicaConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
	SSLMode  string `mapstructure:"ssl_mode"`
	Weight   int    `mapstructure:"weight"`
}

// DSN returns the formatted database connection string for pgx
func (c *DatabaseConfig) DSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.Username, c.Password, c.Database, c.SSLMode)
}

// RedisConfig إعدادات Redis Cache (Performance Optimization)
type RedisConfig struct {
	Enabled      bool          `mapstructure:"enabled"`
	Addrs        []string      `mapstructure:"addrs"`
	Password     string        `mapstructure:"password"`
	DB           int           `mapstructure:"db"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	MaxRetries   int           `mapstructure:"max_retries"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
	DefaultTTL   time.Duration `mapstructure:"default_ttl"`
	KeyPrefix    string        `mapstructure:"key_prefix"`
}

// SupabaseConfig إعدادات Supabase (Forever Plan - بسيط)
type SupabaseConfig struct {
	URL            string `mapstructure:"url"`
	AnonKey        string `mapstructure:"anon_key"`
	ServiceRoleKey string `mapstructure:"service_role_key"`
	JWTSecret      string `mapstructure:"jwt_secret"`
	ProjectRef     string `mapstructure:"project_ref"`
}

// GoogleConfig إعدادات Google OAuth
type GoogleConfig struct {
	ClientID          string `mapstructure:"client_id"`
	ClientSecret      string `mapstructure:"client_secret"`
	AndroidClientID   string `mapstructure:"android_client_id"`
	WebRedirectURL    string `mapstructure:"web_redirect_url"`
	MobileRedirectURL string `mapstructure:"mobile_redirect_url"`
}

// JWTConfig إعدادات JWT
type JWTConfig struct {
	Secret           string        `mapstructure:"secret"`
	ExpiresIn        time.Duration `mapstructure:"expires_in"`
	RefreshExpiresIn time.Duration `mapstructure:"refresh_expires_in"`
	Issuer           string        `mapstructure:"issuer"`
	Audience         string        `mapstructure:"audience"`
	Algorithm        string        `mapstructure:"algorithm"`
}

// SecurityConfig إعدادات الأمان (Forever Plan - مبسط)
type SecurityConfig struct {
	EncryptionKey        string   `mapstructure:"encryption_key"`
	RateLimitRequests    int      `mapstructure:"rate_limit_requests"`
	RateLimitWindow      string   `mapstructure:"rate_limit_window"`
	CorsAllowedOrigins   []string `mapstructure:"cors_allowed_origins"`
	CorsAllowedMethods   []string `mapstructure:"cors_allowed_methods"`
	CorsAllowedHeaders   []string `mapstructure:"cors_allowed_headers"`
	CorsAllowCredentials bool     `mapstructure:"cors_allow_credentials"`
	// Additional fields for validation
	RateLimit RateLimitConfig `mapstructure:"rate_limit"`
	CORS      CORSConfig      `mapstructure:"cors"`
}

// RateLimitConfig إعدادات تحديد المعدل
type RateLimitConfig struct {
	Enabled          bool `mapstructure:"enabled"`
	RequestsLimit    int  `mapstructure:"requests_limit"`
	WindowSize       int  `mapstructure:"window_size"`
	LoginAttempts    int  `mapstructure:"login_attempts"`
	RegisterAttempts int  `mapstructure:"register_attempts"`
	WindowMinutes    int  `mapstructure:"window_minutes"`
}

// CORSConfig إعدادات CORS
type CORSConfig struct {
	Enabled        bool     `mapstructure:"enabled"`
	AllowedOrigins []string `mapstructure:"allowed_origins"`
}

// LoggingConfig إعدادات التسجيل (Forever Plan - مبسط)
type LoggingConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	FilePath   string `mapstructure:"file_path"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

// FeatureFlags إعدادات الميزات (Forever Plan - مبسط)
type FeatureFlags struct {
	EnableSwagger          bool `mapstructure:"enable_swagger"`
	EnableMetrics          bool `mapstructure:"enable_metrics"`
	EnableHealthChecks     bool `mapstructure:"enable_health_checks"`
	EnableGracefulShutdown bool `mapstructure:"enable_graceful_shutdown"`

	// Forever Plan: هذه محظورة - يجب أن تكون false دائماً
	EnableComplexAuth      bool `mapstructure:"enable_complex_auth"`
	EnableDualDatabase     bool `mapstructure:"enable_dual_database"`
	EnableSyncServices     bool `mapstructure:"enable_sync_services"`
	EnableEnhancedFeatures bool `mapstructure:"enable_enhanced_features"`
}

// SMSConfig إعدادات الرسائل النصية
type SMSConfig struct {
	Provider         string `mapstructure:"provider"`
	TwilioAccountSID string `mapstructure:"twilio_account_sid"`
	TwilioAuthToken  string `mapstructure:"twilio_auth_token"`
	TwilioFromNumber string `mapstructure:"twilio_from_number"`
}

// EmailConfig إعدادات البريد الإلكتروني
type EmailConfig struct {
	Provider       string `mapstructure:"provider"`
	FromEmail      string `mapstructure:"from_email"`
	FromName       string `mapstructure:"from_name"`
	SMTPHost       string `mapstructure:"smtp_host"`
	SMTPPort       int    `mapstructure:"smtp_port"`
	SMTPUsername   string `mapstructure:"smtp_username"`
	SMTPPassword   string `mapstructure:"smtp_password"`
	SendGridAPIKey string `mapstructure:"sendgrid_api_key"`
}

// MFAConfig إعدادات المصادقة متعددة العوامل
type MFAConfig struct {
	Enabled               bool          `mapstructure:"enabled"`
	MaxConcurrentSessions int           `mapstructure:"max_concurrent_sessions"`
	SessionTimeout        time.Duration `mapstructure:"session_timeout"`
	InactivityTimeout     time.Duration `mapstructure:"inactivity_timeout"`
	RequireMFAForAdmin    bool          `mapstructure:"require_mfa_for_admin"`
}

var cfg *Config

// Load تحميل الإعدادات (Forever Plan - مبسط)
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// تعيين القيم الافتراضية
	setDefaults()

	// ربط متغيرات البيئة أولاً (أولوية عالية)
	bindEnvVars()

	// قراءة ملف التكوين الأساسي
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("Config file not found, using defaults and environment variables")
		} else {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
	}

	// دمج الإعدادات المحلية إذا كانت موجودة (فقط للتطوير المحلي)
	if os.Getenv("CARNOW_APP_ENVIRONMENT") != "production" {
		localViper := viper.New()
		localViper.SetConfigName("config.local")
		localViper.SetConfigType("yaml")
		localViper.AddConfigPath("./configs")
		localViper.AddConfigPath(".")

		if err := localViper.ReadInConfig(); err == nil {
			log.Println("Loading local config file: config.local.yaml (development mode)")
			if err := viper.MergeConfigMap(localViper.AllSettings()); err != nil {
				log.Printf("Could not merge local config: %v", err)
			}
		}
	} else {
		log.Println("Production mode: Skipping config.local.yaml, using environment variables only")
	}

	// تحويل الإعدادات إلى struct
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	// التحقق من صحة الإعدادات الأساسية
	if err := validateConfig(cfg); err != nil {
		return nil, fmt.Errorf("basic configuration validation failed: %w", err)
	}

	// Use Supabase JWT secret as fallback if JWT secret is not set
	if cfg.JWT.Secret == "" && cfg.Supabase.JWTSecret != "" {
		cfg.JWT.Secret = cfg.Supabase.JWTSecret
		log.Printf("✅ Using Supabase JWT secret as fallback for JWT configuration")
	}

	// التحقق الشامل من التكوين
	validator := NewConfigValidator(cfg)
	if err := validator.ValidateAll(); err != nil {
		return nil, fmt.Errorf("comprehensive configuration validation failed: %w", err)
	}

	// التحقق من الأسرار والأمان
	if err := ValidateSecrets(cfg); err != nil {
		return nil, fmt.Errorf("secrets validation failed: %w", err)
	}

	// التحقق من جاهزية الإنتاج (إذا كانت البيئة production)
	if cfg.App.Environment == "production" {
		prodValidator := NewProductionConfigValidator(cfg)
		if err := prodValidator.ValidateProductionReadiness(); err != nil {
			return nil, fmt.Errorf("production readiness validation failed: %w", err)
		}
		log.Printf("🚀 Production configuration validated successfully!")
		PrintProductionChecklist()
	} else {
		log.Printf("🛠️ Development/Staging environment detected")
	}

	log.Printf("✅ Configuration loaded and validated successfully (Environment: %s)", cfg.App.Environment)
	return cfg, nil
}

// setDefaults تعيين القيم الافتراضية (Forever Plan - مبسط)
func setDefaults() {
	// إعدادات التطبيق
	viper.SetDefault("app.name", "CarNow Backend")
	viper.SetDefault("app.version", "1.0.0")
	viper.SetDefault("app.environment", "production")
	viper.SetDefault("app.debug", false)
	viper.SetDefault("app.timezone", "Africa/Tripoli")

	// إعدادات الخادم
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "60s")
	viper.SetDefault("server.max_header_bytes", 1048576)
	viper.SetDefault("server.graceful_timeout", "15s")

	// إعدادات قاعدة البيانات
	viper.SetDefault("database.host", "aws-0-eu-central-1.pooler.supabase.com")
	viper.SetDefault("database.port", 6543)
	viper.SetDefault("database.username", "postgres.lpxtghyvxuenyyisrrro")
	viper.SetDefault("database.database", "postgres")
	viper.SetDefault("database.ssl_mode", "require")
	viper.SetDefault("database.max_open_conns", 10)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "1h")
	viper.SetDefault("database.conn_max_idle_time", "30m")

	// إعدادات Redis Cache
	viper.SetDefault("redis.enabled", true)
	viper.SetDefault("redis.addrs", []string{"localhost:6379"})
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.pool_size", 10)
	viper.SetDefault("redis.min_idle_conns", 5)
	viper.SetDefault("redis.max_retries", 3)
	viper.SetDefault("redis.dial_timeout", "5s")
	viper.SetDefault("redis.read_timeout", "3s")
	viper.SetDefault("redis.write_timeout", "3s")
	viper.SetDefault("redis.idle_timeout", "5m")
	viper.SetDefault("redis.default_ttl", "1h")
	viper.SetDefault("redis.key_prefix", "carnow:")

	// إعدادات Supabase (Forever Plan)
	viper.SetDefault("supabase.url", "https://lpxtghyvxuenyyisrrro.supabase.co")
	viper.SetDefault("supabase.anon_key", "")
	viper.SetDefault("supabase.service_role_key", "")
	viper.SetDefault("supabase.jwt_secret", "")
	viper.SetDefault("supabase.project_ref", "lpxtghyvxuenyyisrrro")

	// إعدادات JWT (Secure Configuration) - Extended for better user experience
	viper.SetDefault("jwt.secret", "default-jwt-secret-change-me-immediately")
	viper.SetDefault("jwt.expires_in", "168h")         // Extended to 7 days for better UX
	viper.SetDefault("jwt.refresh_expires_in", "720h") // 30 days refresh tokens
	viper.SetDefault("jwt.algorithm", "RS256")         // RSA256 for enhanced security
	viper.SetDefault("jwt.issuer", "carnow-backend")
	viper.SetDefault("jwt.audience", "carnow-app")

	// إعدادات الأمان
	viper.SetDefault("security.rate_limit_requests", 100)
	viper.SetDefault("security.rate_limit_window", "1m")
	viper.SetDefault("security.cors_allowed_origins", []string{"*"})
	viper.SetDefault("security.cors_allowed_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	viper.SetDefault("security.cors_allowed_headers", []string{"Origin", "Content-Type", "Accept", "Authorization"})
	viper.SetDefault("security.cors_allow_credentials", true)

	// إعدادات تحديد المعدل (Rate Limiting)
	viper.SetDefault("security.rate_limit.enabled", true)
	viper.SetDefault("security.rate_limit.requests_limit", 100)
	viper.SetDefault("security.rate_limit.window_size", 60)
	viper.SetDefault("security.rate_limit.login_attempts", 5)
	viper.SetDefault("security.rate_limit.register_attempts", 3)
	viper.SetDefault("security.rate_limit.window_minutes", 15)

	// إعدادات CORS
	viper.SetDefault("security.cors.enabled", true)
	viper.SetDefault("security.cors.allowed_origins", []string{"*"})

	// إعدادات التسجيل
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")
	viper.SetDefault("logging.max_size", 100)
	viper.SetDefault("logging.max_backups", 3)
	viper.SetDefault("logging.max_age", 28)
	viper.SetDefault("logging.compress", true)

	// إعدادات الميزات (Forever Plan)
	viper.SetDefault("features.enable_swagger", true)
	viper.SetDefault("features.enable_metrics", true)
	viper.SetDefault("features.enable_health_checks", true)
	viper.SetDefault("features.enable_graceful_shutdown", true)

	// Forever Plan: هذه محظورة
	viper.SetDefault("features.enable_complex_auth", false)
	viper.SetDefault("features.enable_dual_database", false)
	viper.SetDefault("features.enable_sync_services", false)
	viper.SetDefault("features.enable_enhanced_features", false)
}

// bindEnvVars ربط متغيرات البيئة
func bindEnvVars() {
	viper.SetEnvPrefix("CARNOW")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// ربط متغيرات البيئة المهمة
	// App configuration
	_ = viper.BindEnv("app.environment", "CARNOW_APP_ENVIRONMENT")
	_ = viper.BindEnv("app.debug", "CARNOW_APP_DEBUG")

	// Server configuration - خاص بـ Render.com deployment
	_ = viper.BindEnv("server.port", "PORT") // Render.com uses PORT env var
	_ = viper.BindEnv("server.host", "CARNOW_SERVER_HOST")

	// Database configuration
	_ = viper.BindEnv("database.host", "CARNOW_DATABASE_HOST")
	_ = viper.BindEnv("database.port", "CARNOW_DATABASE_PORT")
	_ = viper.BindEnv("database.username", "CARNOW_DATABASE_USERNAME")
	_ = viper.BindEnv("database.password", "CARNOW_DATABASE_PASSWORD")
	_ = viper.BindEnv("database.database", "CARNOW_DATABASE_DATABASE")

	// Redis configuration
	_ = viper.BindEnv("redis.enabled", "CARNOW_REDIS_ENABLED")
	_ = viper.BindEnv("redis.addrs", "CARNOW_REDIS_ADDRS")
	_ = viper.BindEnv("redis.password", "CARNOW_REDIS_PASSWORD")
	_ = viper.BindEnv("redis.db", "CARNOW_REDIS_DB")

	// Supabase configuration
	_ = viper.BindEnv("supabase.url", "CARNOW_SUPABASE_URL")
	_ = viper.BindEnv("supabase.anon_key", "CARNOW_SUPABASE_ANON_KEY")
	_ = viper.BindEnv("supabase.service_role_key", "CARNOW_SUPABASE_SERVICE_ROLE_KEY")
	_ = viper.BindEnv("supabase.jwt_secret", "CARNOW_SUPABASE_JWT_SECRET")

	// Google OAuth configuration
	_ = viper.BindEnv("google.client_id", "CARNOW_GOOGLE_CLIENT_ID")
	_ = viper.BindEnv("google.client_secret", "CARNOW_GOOGLE_CLIENT_SECRET")

	// JWT and Security
	_ = viper.BindEnv("jwt.secret", "CARNOW_JWT_SECRET")
	_ = viper.BindEnv("jwt.issuer", "CARNOW_JWT_ISSUER")
	_ = viper.BindEnv("jwt.audience", "CARNOW_JWT_AUDIENCE")
	_ = viper.BindEnv("security.encryption_key", "CARNOW_SECURITY_ENCRYPTION_KEY")

	// Rate limiting configuration
	_ = viper.BindEnv("security.rate_limit.enabled", "CARNOW_SECURITY_RATE_LIMIT_ENABLED")
	_ = viper.BindEnv("security.rate_limit.requests_limit", "CARNOW_SECURITY_RATE_LIMIT_REQUESTS_LIMIT")
	_ = viper.BindEnv("security.rate_limit.window_size", "CARNOW_SECURITY_RATE_LIMIT_WINDOW_SIZE")
	_ = viper.BindEnv("security.rate_limit.login_attempts", "CARNOW_SECURITY_RATE_LIMIT_LOGIN_ATTEMPTS")
	_ = viper.BindEnv("security.rate_limit.register_attempts", "CARNOW_SECURITY_RATE_LIMIT_REGISTER_ATTEMPTS")
	_ = viper.BindEnv("security.rate_limit.window_minutes", "CARNOW_SECURITY_RATE_LIMIT_WINDOW_MINUTES")
}

// validateConfig التحقق من صحة الإعدادات (Forever Plan)
func validateConfig(cfg *Config) error {
	// التحقق من الإعدادات المطلوبة
	log.Printf("🔍 Validating configuration (Environment: %s)", cfg.App.Environment)
	log.Printf("📊 Supabase URL loaded: %s", cfg.Supabase.URL)

	// Enhanced Debug: Print all authentication-related environment variables
	log.Printf("🔬 DEBUG - Authentication Environment Variables:")
	log.Printf("   CARNOW_SUPABASE_URL: '%s'", os.Getenv("CARNOW_SUPABASE_URL"))
	log.Printf("   CARNOW_SUPABASE_ANON_KEY: '%s'", maskSecret(os.Getenv("CARNOW_SUPABASE_ANON_KEY")))
	log.Printf("   CARNOW_SUPABASE_SERVICE_ROLE_KEY: '%s'", maskSecret(os.Getenv("CARNOW_SUPABASE_SERVICE_ROLE_KEY")))
	log.Printf("   CARNOW_SUPABASE_JWT_SECRET: '%s'", maskSecret(os.Getenv("CARNOW_SUPABASE_JWT_SECRET")))
	log.Printf("   CARNOW_GOOGLE_CLIENT_ID: '%s'", maskSecret(os.Getenv("CARNOW_GOOGLE_CLIENT_ID")))
	log.Printf("   CARNOW_GOOGLE_CLIENT_SECRET: '%s'", maskSecret(os.Getenv("CARNOW_GOOGLE_CLIENT_SECRET")))
	log.Printf("   CARNOW_JWT_SECRET: '%s'", maskSecret(os.Getenv("CARNOW_JWT_SECRET")))
	log.Printf("   CARNOW_JWT_ISSUER: '%s'", os.Getenv("CARNOW_JWT_ISSUER"))
	log.Printf("   CARNOW_JWT_AUDIENCE: '%s'", os.Getenv("CARNOW_JWT_AUDIENCE"))
	log.Printf("   CARNOW_SECURITY_ENCRYPTION_KEY: '%s'", maskSecret(os.Getenv("CARNOW_SECURITY_ENCRYPTION_KEY")))
	log.Printf("   CARNOW_APP_ENVIRONMENT: '%s'", os.Getenv("CARNOW_APP_ENVIRONMENT"))
	log.Printf("   PORT: '%s'", os.Getenv("PORT"))

	log.Printf("🔬 DEBUG - Loaded Config Values:")
	log.Printf("   cfg.Supabase.URL: '%s'", cfg.Supabase.URL)
	log.Printf("   cfg.Supabase.AnonKey: '%s'", maskSecret(cfg.Supabase.AnonKey))
	log.Printf("   cfg.Google.ClientID: '%s'", maskSecret(cfg.Google.ClientID))
	log.Printf("   cfg.JWT.Secret: '%s'", maskSecret(cfg.JWT.Secret))
	log.Printf("   cfg.JWT.Issuer: '%s'", cfg.JWT.Issuer)
	log.Printf("   cfg.App.Environment: '%s'", cfg.App.Environment)

	if cfg.Supabase.URL == "" {
		// Check if environment variable is set as fallback
		envURL := os.Getenv("CARNOW_SUPABASE_URL")
		log.Printf("🌍 Environment variable CARNOW_SUPABASE_URL: %s", envURL)

		if envURL == "" {
			return fmt.Errorf("CARNOW_SUPABASE_URL environment variable is required. Please set it in Render.com dashboard to: https://lpxtghyvxuenyyisrrro.supabase.co")
		}
		// Use environment variable if config is empty
		cfg.Supabase.URL = envURL
		log.Printf("📊 Using Supabase URL from environment: %s", cfg.Supabase.URL)
	} else {
		log.Printf("✅ Supabase URL loaded successfully: %s", cfg.Supabase.URL)
	}

	// Enhanced Authentication Configuration Validation
	log.Printf("🔐 Validating Authentication Configuration...")

	// Critical: Supabase Configuration
	if cfg.Supabase.AnonKey == "" {
		return fmt.Errorf("CARNOW_SUPABASE_ANON_KEY is required for authentication. Get it from Supabase Project Settings > API > anon public")
	}

	if cfg.Supabase.ServiceRoleKey == "" {
		log.Println("WARNING: CARNOW_SUPABASE_SERVICE_ROLE_KEY not set. Admin operations may be limited.")
		log.Println("Get the value from Supabase Project Settings > API > service_role secret")
	}

	if cfg.Supabase.JWTSecret == "" {
		return fmt.Errorf("CARNOW_SUPABASE_JWT_SECRET is required for JWT token verification. Get it from Supabase Project Settings > API > JWT Secret")
	}

	// Critical: JWT Configuration
	if cfg.JWT.Secret == "" {
		// Try to use Supabase JWT secret as fallback
		if cfg.Supabase.JWTSecret != "" {
			cfg.JWT.Secret = cfg.Supabase.JWTSecret
			log.Printf("✅ Using Supabase JWT secret as fallback for JWT configuration")
		} else {
			return fmt.Errorf("CARNOW_JWT_SECRET is required for JWT token generation. Generate with: openssl rand -base64 64")
		}
	}

	if cfg.JWT.Issuer == "" {
		cfg.JWT.Issuer = "carnow-backend" // Set default issuer
		log.Printf("ℹ️ Using default JWT issuer: %s", cfg.JWT.Issuer)
	}

	if cfg.JWT.Audience == "" {
		cfg.JWT.Audience = "carnow-app" // Set default audience
		log.Printf("ℹ️ Using default JWT audience: %s", cfg.JWT.Audience)
	}

	// Critical: Security Configuration
	if cfg.Security.EncryptionKey == "" {
		if cfg.App.Environment == "production" {
			return fmt.Errorf("CARNOW_SECURITY_ENCRYPTION_KEY is required in production. Generate with: openssl rand -base64 64")
		} else {
			log.Println("WARNING: CARNOW_SECURITY_ENCRYPTION_KEY not set. Using default key for development (NOT RECOMMENDED for production)")
			cfg.Security.EncryptionKey = "development-key-change-for-production"
		}
	}

	// Google OAuth Configuration (Optional but recommended)
	if cfg.Google.ClientID == "" {
		log.Println("WARNING: CARNOW_GOOGLE_CLIENT_ID not set. Google OAuth authentication will be disabled.")
		log.Println("To enable Google OAuth, set CARNOW_GOOGLE_CLIENT_ID and CARNOW_GOOGLE_CLIENT_SECRET")
	} else if cfg.Google.ClientSecret == "" {
		log.Println("WARNING: CARNOW_GOOGLE_CLIENT_SECRET not set. Google OAuth authentication will be disabled.")
		log.Println("Both CARNOW_GOOGLE_CLIENT_ID and CARNOW_GOOGLE_CLIENT_SECRET are required for Google OAuth")
	} else {
		log.Printf("✅ Google OAuth configuration loaded successfully")
	}

	// التحقق من البيئة
	validEnvs := []string{"development", "staging", "production"}
	validEnv := false
	for _, env := range validEnvs {
		if cfg.App.Environment == env {
			validEnv = true
			break
		}
	}
	if !validEnv {
		return fmt.Errorf("invalid environment: %s", cfg.App.Environment)
	}

	// التحقق من المنافذ
	if cfg.Server.Port < 1 || cfg.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", cfg.Server.Port)
	}

	// Forever Plan: التحقق من أن الميزات المحظورة معطلة
	if cfg.Features.EnableComplexAuth {
		log.Println("WARNING: EnableComplexAuth should be false for Forever Plan")
	}
	if cfg.Features.EnableDualDatabase {
		log.Println("WARNING: EnableDualDatabase should be false for Forever Plan")
	}
	if cfg.Features.EnableSyncServices {
		log.Println("WARNING: EnableSyncServices should be false for Forever Plan")
	}
	if cfg.Features.EnableEnhancedFeatures {
		log.Println("WARNING: EnableEnhancedFeatures should be false for Forever Plan")
	}

	return nil
}

// Get إرجاع الإعدادات المحملة
func Get() *Config {
	if cfg == nil {
		log.Fatal("Config not loaded. Call Load() first.")
	}
	return cfg
}

// IsProduction التحقق من بيئة الإنتاج
func IsProduction() bool {
	return Get().App.Environment == "production"
}

// IsDevelopment التحقق من بيئة التطوير
func IsDevelopment() bool {
	return Get().App.Environment == "development"
}

// GetEnvOrDefault الحصول على متغير البيئة أو القيمة الافتراضية
func GetEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
