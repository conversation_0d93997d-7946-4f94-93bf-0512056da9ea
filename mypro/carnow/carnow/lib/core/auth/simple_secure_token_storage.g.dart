// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'simple_secure_token_storage.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$simpleSecureTokenStorageHash() =>
    r'ba1725eb4fad611657cdf8e4de82c0fa9863e420';

/// Provider for simple secure token storage
///
/// Copied from [simpleSecureTokenStorage].
@ProviderFor(simpleSecureTokenStorage)
final simpleSecureTokenStorageProvider =
    AutoDisposeProvider<SimpleSecureTokenStorage>.internal(
      simpleSecureTokenStorage,
      name: r'simpleSecureTokenStorageProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$simpleSecureTokenStorageHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SimpleSecureTokenStorageRef =
    AutoDisposeProviderRef<SimpleSecureTokenStorage>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
