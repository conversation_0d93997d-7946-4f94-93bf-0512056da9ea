import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'enhanced_secure_token_storage.dart';
import 'i_token_storage.dart';

part 'session_persistence_diagnostics.g.dart';

/// Session Persistence Diagnostics Service
/// خدمة تشخيص استمرارية الجلسة
/// 
/// This service helps diagnose session persistence issues following Forever Plan Architecture:
/// - Real data only from Supabase database
/// - No mock data tolerance
/// - Comprehensive error handling
/// - Production-ready diagnostics
class SessionPersistenceDiagnostics {
  final ITokenStorage _tokenStorage;
  final FlutterSecureStorage _secureStorage;

  SessionPersistenceDiagnostics({
    required ITokenStorage tokenStorage,
    FlutterSecureStorage? secureStorage,
  }) : _tokenStorage = tokenStorage,
       _secureStorage = secureStorage ?? const FlutterSecureStorage(
         aOptions: AndroidOptions(encryptedSharedPreferences: true),
         iOptions: IOSOptions(accessibility: KeychainAccessibility.first_unlock_this_device),
       );

  /// Comprehensive session diagnostics
  /// تشخيص شامل للجلسة
  Future<SessionDiagnosticsResult> runDiagnostics() async {
    debugPrint('🔍 Starting comprehensive session diagnostics...');
    
    final diagnostics = <String, dynamic>{};
    final issues = <String>[];
    final recommendations = <String>[];

    try {
      // 1. Check storage availability
      await _checkStorageAvailability(diagnostics, issues, recommendations);
      
      // 2. Check encryption credentials
      await _checkEncryptionCredentials(diagnostics, issues, recommendations);
      
      // 3. Check stored tokens
      await _checkStoredTokens(diagnostics, issues, recommendations);
      
      // 4. Check token expiry
      await _checkTokenExpiry(diagnostics, issues, recommendations);
      
      // 5. Check session data
      await _checkSessionData(diagnostics, issues, recommendations);
      
      // 6. Check storage integrity
      await _checkStorageIntegrity(diagnostics, issues, recommendations);

      debugPrint('✅ Session diagnostics completed');
      
      return SessionDiagnosticsResult(
        isHealthy: issues.isEmpty,
        diagnostics: diagnostics,
        issues: issues,
        recommendations: recommendations,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('❌ Session diagnostics failed: $e');
      
      return SessionDiagnosticsResult(
        isHealthy: false,
        diagnostics: {'error': e.toString()},
        issues: ['Critical error during diagnostics: $e'],
        recommendations: ['Restart the app and try again'],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Check storage availability
  Future<void> _checkStorageAvailability(
    Map<String, dynamic> diagnostics,
    List<String> issues,
    List<String> recommendations,
  ) async {
    try {
      debugPrint('🔍 Checking storage availability...');
      
      // Test basic storage operations
      const testKey = 'carnow_test_key';
      const testValue = 'carnow_test_value';
      
      await _secureStorage.write(key: testKey, value: testValue);
      final readValue = await _secureStorage.read(key: testKey);
      await _secureStorage.delete(key: testKey);
      
      if (readValue == testValue) {
        diagnostics['storage_available'] = true;
        debugPrint('✅ Storage is available and working');
      } else {
        diagnostics['storage_available'] = false;
        issues.add('Storage read/write test failed');
        recommendations.add('Check device storage permissions');
      }
    } catch (e) {
      diagnostics['storage_available'] = false;
      diagnostics['storage_error'] = e.toString();
      issues.add('Storage is not available: $e');
      recommendations.add('Check device storage and app permissions');
    }
  }

  /// Check encryption credentials
  Future<void> _checkEncryptionCredentials(
    Map<String, dynamic> diagnostics,
    List<String> issues,
    List<String> recommendations,
  ) async {
    try {
      debugPrint('🔍 Checking encryption credentials...');
      
      final keyExists = await _secureStorage.containsKey(
        key: SecureStorageConfig.encryptionKeyKey,
      );
      final ivExists = await _secureStorage.containsKey(
        key: SecureStorageConfig.encryptionIvKey,
      );
      
      diagnostics['encryption_key_exists'] = keyExists;
      diagnostics['encryption_iv_exists'] = ivExists;
      
      if (!keyExists || !ivExists) {
        issues.add('Missing encryption credentials (key: $keyExists, iv: $ivExists)');
        recommendations.add('Clear app data and sign in again to regenerate encryption credentials');
      } else {
        debugPrint('✅ Encryption credentials exist');
      }
    } catch (e) {
      diagnostics['encryption_credentials_error'] = e.toString();
      issues.add('Failed to check encryption credentials: $e');
      recommendations.add('Clear app data and restart the app');
    }
  }

  /// Check stored tokens
  Future<void> _checkStoredTokens(
    Map<String, dynamic> diagnostics,
    List<String> issues,
    List<String> recommendations,
  ) async {
    try {
      debugPrint('🔍 Checking stored tokens...');
      
      final accessToken = await _tokenStorage.getToken();
      final refreshToken = await _tokenStorage.getRefreshToken();
      
      diagnostics['access_token_exists'] = accessToken != null;
      diagnostics['refresh_token_exists'] = refreshToken != null;
      
      if (accessToken != null) {
        diagnostics['access_token_length'] = accessToken.length;
        diagnostics['access_token_format_valid'] = _isValidJWTFormat(accessToken);
      }
      
      if (accessToken == null && refreshToken == null) {
        issues.add('No tokens found in storage');
        recommendations.add('Sign in again to create new session');
      } else if (accessToken == null && refreshToken != null) {
        issues.add('Access token missing but refresh token exists');
        recommendations.add('App should attempt token refresh');
      }
      
      debugPrint('✅ Token check completed');
    } catch (e) {
      diagnostics['token_check_error'] = e.toString();
      issues.add('Failed to check stored tokens: $e');
      recommendations.add('Clear app data and sign in again');
    }
  }

  /// Check token expiry
  Future<void> _checkTokenExpiry(
    Map<String, dynamic> diagnostics,
    List<String> issues,
    List<String> recommendations,
  ) async {
    try {
      debugPrint('🔍 Checking token expiry...');
      
      final isExpired = await _tokenStorage.isTokenExpired();
      final expiryDate = await _tokenStorage.getTokenExpiry();
      final timeUntilExpiry = await _tokenStorage.getTimeUntilExpiry();
      
      diagnostics['token_expired'] = isExpired;
      diagnostics['token_expiry_date'] = expiryDate?.toIso8601String();
      diagnostics['time_until_expiry_hours'] = timeUntilExpiry?.inHours;
      
      if (isExpired) {
        issues.add('Access token is expired');
        recommendations.add('App should attempt token refresh or require re-authentication');
      } else if (timeUntilExpiry != null && timeUntilExpiry.inHours < 24) {
        recommendations.add('Token expires soon, consider refreshing proactively');
      }
      
      debugPrint('✅ Token expiry check completed');
    } catch (e) {
      diagnostics['token_expiry_error'] = e.toString();
      issues.add('Failed to check token expiry: $e');
    }
  }

  /// Check session data
  Future<void> _checkSessionData(
    Map<String, dynamic> diagnostics,
    List<String> issues,
    List<String> recommendations,
  ) async {
    try {
      debugPrint('🔍 Checking session data...');
      
      final sessionData = await _tokenStorage.getSessionData();
      
      diagnostics['session_data_exists'] = sessionData.isNotEmpty;
      diagnostics['session_data_keys'] = sessionData.keys.toList();
      
      if (sessionData.isEmpty) {
        issues.add('No session data found');
        recommendations.add('Session data should be stored during authentication');
      } else if (!sessionData.containsKey('user')) {
        issues.add('Session data missing user information');
        recommendations.add('Re-authenticate to restore complete session data');
      }
      
      debugPrint('✅ Session data check completed');
    } catch (e) {
      diagnostics['session_data_error'] = e.toString();
      issues.add('Failed to check session data: $e');
    }
  }

  /// Check storage integrity
  Future<void> _checkStorageIntegrity(
    Map<String, dynamic> diagnostics,
    List<String> issues,
    List<String> recommendations,
  ) async {
    try {
      debugPrint('🔍 Checking storage integrity...');
      
      final isIntegrityValid = await _tokenStorage.validateStorageIntegrity();
      
      diagnostics['storage_integrity_valid'] = isIntegrityValid;
      
      if (!isIntegrityValid) {
        issues.add('Storage integrity validation failed');
        recommendations.add('Clear app data to reset storage integrity');
      }
      
      debugPrint('✅ Storage integrity check completed');
    } catch (e) {
      diagnostics['storage_integrity_error'] = e.toString();
      issues.add('Failed to check storage integrity: $e');
    }
  }

  /// Validate JWT token format (basic structure check)
  bool _isValidJWTFormat(String token) {
    if (token.isEmpty) return false;
    
    final parts = token.split('.');
    if (parts.length != 3) return false;
    
    // Check if each part is valid base64
    for (final part in parts) {
      try {
        base64Decode(base64.normalize(part));
      } catch (e) {
        return false;
      }
    }
    
    return true;
  }

  /// Clear all session data (for troubleshooting)
  Future<void> clearAllSessionData() async {
    try {
      debugPrint('🧹 Clearing all session data...');
      
      await _tokenStorage.clearAllData();
      
      debugPrint('✅ All session data cleared');
    } catch (e) {
      debugPrint('❌ Failed to clear session data: $e');
      throw Exception('Failed to clear session data: $e');
    }
  }
}

/// Session diagnostics result
class SessionDiagnosticsResult {
  final bool isHealthy;
  final Map<String, dynamic> diagnostics;
  final List<String> issues;
  final List<String> recommendations;
  final DateTime timestamp;

  const SessionDiagnosticsResult({
    required this.isHealthy,
    required this.diagnostics,
    required this.issues,
    required this.recommendations,
    required this.timestamp,
  });

  /// Convert to JSON for logging
  Map<String, dynamic> toJson() {
    return {
      'is_healthy': isHealthy,
      'diagnostics': diagnostics,
      'issues': issues,
      'recommendations': recommendations,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'SessionDiagnosticsResult(healthy: $isHealthy, issues: ${issues.length}, recommendations: ${recommendations.length})';
  }
}

/// Provider for session persistence diagnostics
@riverpod
SessionPersistenceDiagnostics sessionPersistenceDiagnostics(Ref ref) {
  final tokenStorage = ref.read(enhancedSecureTokenStorageProvider);
  return SessionPersistenceDiagnostics(tokenStorage: tokenStorage);
}
