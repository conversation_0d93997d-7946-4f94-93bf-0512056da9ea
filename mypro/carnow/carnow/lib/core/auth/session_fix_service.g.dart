// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_fix_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sessionFixServiceHash() => r'1893735365a16e0a0b893d19e0f5a9f1aa096136';

/// Provider for session fix service
///
/// Copied from [sessionFixService].
@ProviderFor(sessionFixService)
final sessionFixServiceProvider =
    AutoDisposeProvider<SessionFixService>.internal(
      sessionFixService,
      name: r'sessionFixServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sessionFixServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SessionFixServiceRef = AutoDisposeProviderRef<SessionFixService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
