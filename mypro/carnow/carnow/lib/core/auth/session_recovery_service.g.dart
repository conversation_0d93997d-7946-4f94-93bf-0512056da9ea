// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_recovery_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sessionRecoveryServiceHash() =>
    r'1e12ef2d0894f8461aa409826bddd4d808fcd557';

/// Provider for session recovery service
///
/// Copied from [sessionRecoveryService].
@ProviderFor(sessionRecoveryService)
final sessionRecoveryServiceProvider =
    AutoDisposeProvider<SessionRecoveryService>.internal(
      sessionRecoveryService,
      name: r'sessionRecoveryServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sessionRecoveryServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SessionRecoveryServiceRef =
    AutoDisposeProviderRef<SessionRecoveryService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
