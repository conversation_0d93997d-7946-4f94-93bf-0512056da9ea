// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_persistence_diagnostics.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sessionPersistenceDiagnosticsHash() =>
    r'68b6d3bc36e24a66b688ccc172f655ab14ce1686';

/// Provider for session persistence diagnostics
///
/// Copied from [sessionPersistenceDiagnostics].
@ProviderFor(sessionPersistenceDiagnostics)
final sessionPersistenceDiagnosticsProvider =
    AutoDisposeProvider<SessionPersistenceDiagnostics>.internal(
      sessionPersistenceDiagnostics,
      name: r'sessionPersistenceDiagnosticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sessionPersistenceDiagnosticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SessionPersistenceDiagnosticsRef =
    AutoDisposeProviderRef<SessionPersistenceDiagnostics>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
