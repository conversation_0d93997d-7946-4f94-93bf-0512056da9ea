// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_auth_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentAuthStateHash() => r'ce44aa84a214955b77183e9e51d7b8f2b7e689e2';

/// Provider for current authentication state
///
/// Copied from [currentAuthState].
@ProviderFor(currentAuthState)
final currentAuthStateProvider = AutoDisposeProvider<AuthState>.internal(
  currentAuthState,
  name: r'currentAuthStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAuthStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentAuthStateRef = AutoDisposeProviderRef<AuthState>;
String _$currentUserHash() => r'f1a7f6c71f56f8db6409fed53edfce9ea41adda9';

/// Provider for current authenticated user
///
/// Copied from [currentUser].
@ProviderFor(currentUser)
final currentUserProvider = AutoDisposeProvider<User?>.internal(
  currentUser,
  name: r'currentUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserRef = AutoDisposeProviderRef<User?>;
String _$isAuthenticatedHash() => r'e50c269dac96e72184602abf52642f11a44cdfe2';

/// Provider for authentication status
///
/// Copied from [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$currentAccessTokenHash() =>
    r'8e90fc67c6658200f40f51c8f1d157b01290cd91';

/// Provider for current access token
///
/// Copied from [currentAccessToken].
@ProviderFor(currentAccessToken)
final currentAccessTokenProvider = AutoDisposeProvider<String?>.internal(
  currentAccessToken,
  name: r'currentAccessTokenProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAccessTokenHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentAccessTokenRef = AutoDisposeProviderRef<String?>;
String _$unifiedAuthProviderHash() =>
    r'b8584ad23a1f5bb414f8a82c4e61d965714c218e';

/// Unified authentication provider that manages the complete authentication
/// state and lifecycle for the CarNow application.
///
/// This provider handles:
/// - Authentication state management
/// - Token refresh and session management
/// - Session persistence and restoration
/// - Integration with secure storage and API services
///
/// Architecture compliance: Flutter UI Only → Go API → Supabase Data
///
/// Copied from [UnifiedAuthProvider].
@ProviderFor(UnifiedAuthProvider)
final unifiedAuthProviderProvider =
    AutoDisposeNotifierProvider<UnifiedAuthProvider, AuthState>.internal(
      UnifiedAuthProvider.new,
      name: r'unifiedAuthProviderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$unifiedAuthProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UnifiedAuthProvider = AutoDisposeNotifier<AuthState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
