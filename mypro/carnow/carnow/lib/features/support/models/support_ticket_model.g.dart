// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'support_ticket_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SupportTicketModel _$SupportTicketModelFromJson(Map<String, dynamic> json) =>
    _SupportTicketModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      subject: json['subject'] as String,
      description: json['description'] as String,
      status: $enumDecode(_$TicketStatusEnumMap, json['status']),
      priority: $enumDecode(_$TicketPriorityEnumMap, json['priority']),
      category: $enumDecode(_$TicketCategoryEnumMap, json['category']),
      assignedTo: json['assignedTo'] as String?,
      resolvedAt: json['resolvedAt'] == null
          ? null
          : DateTime.parse(json['resolvedAt'] as String),
      lastMessageAt: json['lastMessageAt'] == null
          ? null
          : DateTime.parse(json['lastMessageAt'] as String),
      lastMessageText: json['lastMessageText'] as String?,
      unreadCount: (json['unreadCount'] as num?)?.toInt() ?? 0,
      userType: json['userType'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$SupportTicketModelToJson(_SupportTicketModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'subject': instance.subject,
      'description': instance.description,
      'status': _$TicketStatusEnumMap[instance.status]!,
      'priority': _$TicketPriorityEnumMap[instance.priority]!,
      'category': _$TicketCategoryEnumMap[instance.category]!,
      'assignedTo': instance.assignedTo,
      'resolvedAt': instance.resolvedAt?.toIso8601String(),
      'lastMessageAt': instance.lastMessageAt?.toIso8601String(),
      'lastMessageText': instance.lastMessageText,
      'unreadCount': instance.unreadCount,
      'userType': instance.userType,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'isActive': instance.isActive,
    };

const _$TicketStatusEnumMap = {
  TicketStatus.open: 'open',
  TicketStatus.inProgress: 'in_progress',
  TicketStatus.closed: 'closed',
  TicketStatus.urgent: 'urgent',
  TicketStatus.pending: 'pending',
  TicketStatus.resolved: 'resolved',
};

const _$TicketPriorityEnumMap = {
  TicketPriority.low: 'low',
  TicketPriority.medium: 'medium',
  TicketPriority.high: 'high',
  TicketPriority.urgent: 'urgent',
};

const _$TicketCategoryEnumMap = {
  TicketCategory.technical: 'technical',
  TicketCategory.billing: 'billing',
  TicketCategory.general: 'general',
  TicketCategory.featureRequest: 'feature_request',
  TicketCategory.bugReport: 'bug_report',
  TicketCategory.account: 'account',
  TicketCategory.orders: 'orders',
  TicketCategory.payments: 'payments',
  TicketCategory.seller: 'seller',
  TicketCategory.returns: 'returns',
};

_SupportTicketMessageModel _$SupportTicketMessageModelFromJson(
  Map<String, dynamic> json,
) => _SupportTicketMessageModel(
  id: json['id'] as String,
  ticketId: json['ticketId'] as String,
  senderId: json['senderId'] as String,
  message: json['message'] as String,
  senderType: json['senderType'] as String,
  isInternal: json['isInternal'] as bool? ?? false,
  readAt: json['readAt'] == null
      ? null
      : DateTime.parse(json['readAt'] as String),
  sentAt: json['sentAt'] == null
      ? null
      : DateTime.parse(json['sentAt'] as String),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
);

Map<String, dynamic> _$SupportTicketMessageModelToJson(
  _SupportTicketMessageModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'ticketId': instance.ticketId,
  'senderId': instance.senderId,
  'message': instance.message,
  'senderType': instance.senderType,
  'isInternal': instance.isInternal,
  'readAt': instance.readAt?.toIso8601String(),
  'sentAt': instance.sentAt?.toIso8601String(),
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'isDeleted': instance.isDeleted,
};
