// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_analytics_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionAnalyticsApiServiceHash() =>
    r'462c87c6c284c11d48c5ca4748409c22cb0ed586';

/// See also [subscriptionAnalyticsApiService].
@ProviderFor(subscriptionAnalyticsApiService)
final subscriptionAnalyticsApiServiceProvider =
    AutoDisposeProvider<SubscriptionAnalyticsApiService>.internal(
      subscriptionAnalyticsApiService,
      name: r'subscriptionAnalyticsApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionAnalyticsApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionAnalyticsApiServiceRef =
    AutoDisposeProviderRef<SubscriptionAnalyticsApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
