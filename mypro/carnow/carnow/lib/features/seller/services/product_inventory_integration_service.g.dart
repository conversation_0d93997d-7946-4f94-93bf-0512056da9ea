// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_inventory_integration_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productInventoryIntegrationServiceHash() =>
    r'6feae3a838958196925a9f286447962a8111a064';

/// مزود خدمة تكامل المنتجات والمخزون
///
/// Copied from [productInventoryIntegrationService].
@ProviderFor(productInventoryIntegrationService)
final productInventoryIntegrationServiceProvider =
    AutoDisposeProvider<ProductInventoryIntegrationService>.internal(
      productInventoryIntegrationService,
      name: r'productInventoryIntegrationServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$productInventoryIntegrationServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProductInventoryIntegrationServiceRef =
    AutoDisposeProviderRef<ProductInventoryIntegrationService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
