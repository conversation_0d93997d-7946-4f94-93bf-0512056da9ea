// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inventory_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$inventoryApiServiceHash() =>
    r'26aaeac1cdeb620ccc36170a396cc26a56b893e7';

/// See also [inventoryApiService].
@ProviderFor(inventoryApiService)
final inventoryApiServiceProvider =
    AutoDisposeProvider<InventoryApiService>.internal(
      inventoryApiService,
      name: r'inventoryApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InventoryApiServiceRef = AutoDisposeProviderRef<InventoryApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
