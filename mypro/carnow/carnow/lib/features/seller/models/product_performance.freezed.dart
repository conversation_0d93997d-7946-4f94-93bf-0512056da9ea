// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_performance.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductPerformance {

 String get productId; String get productName; String? get productImage; int get quantitySold; double get revenue; int get ordersCount;
/// Create a copy of ProductPerformance
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductPerformanceCopyWith<ProductPerformance> get copyWith => _$ProductPerformanceCopyWithImpl<ProductPerformance>(this as ProductPerformance, _$identity);

  /// Serializes this ProductPerformance to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductPerformance&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productImage, productImage) || other.productImage == productImage)&&(identical(other.quantitySold, quantitySold) || other.quantitySold == quantitySold)&&(identical(other.revenue, revenue) || other.revenue == revenue)&&(identical(other.ordersCount, ordersCount) || other.ordersCount == ordersCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,productImage,quantitySold,revenue,ordersCount);

@override
String toString() {
  return 'ProductPerformance(productId: $productId, productName: $productName, productImage: $productImage, quantitySold: $quantitySold, revenue: $revenue, ordersCount: $ordersCount)';
}


}

/// @nodoc
abstract mixin class $ProductPerformanceCopyWith<$Res>  {
  factory $ProductPerformanceCopyWith(ProductPerformance value, $Res Function(ProductPerformance) _then) = _$ProductPerformanceCopyWithImpl;
@useResult
$Res call({
 String productId, String productName, String? productImage, int quantitySold, double revenue, int ordersCount
});




}
/// @nodoc
class _$ProductPerformanceCopyWithImpl<$Res>
    implements $ProductPerformanceCopyWith<$Res> {
  _$ProductPerformanceCopyWithImpl(this._self, this._then);

  final ProductPerformance _self;
  final $Res Function(ProductPerformance) _then;

/// Create a copy of ProductPerformance
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = null,Object? productName = null,Object? productImage = freezed,Object? quantitySold = null,Object? revenue = null,Object? ordersCount = null,}) {
  return _then(_self.copyWith(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productImage: freezed == productImage ? _self.productImage : productImage // ignore: cast_nullable_to_non_nullable
as String?,quantitySold: null == quantitySold ? _self.quantitySold : quantitySold // ignore: cast_nullable_to_non_nullable
as int,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,ordersCount: null == ordersCount ? _self.ordersCount : ordersCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductPerformance].
extension ProductPerformancePatterns on ProductPerformance {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductPerformance value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductPerformance() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductPerformance value)  $default,){
final _that = this;
switch (_that) {
case _ProductPerformance():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductPerformance value)?  $default,){
final _that = this;
switch (_that) {
case _ProductPerformance() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String productId,  String productName,  String? productImage,  int quantitySold,  double revenue,  int ordersCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductPerformance() when $default != null:
return $default(_that.productId,_that.productName,_that.productImage,_that.quantitySold,_that.revenue,_that.ordersCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String productId,  String productName,  String? productImage,  int quantitySold,  double revenue,  int ordersCount)  $default,) {final _that = this;
switch (_that) {
case _ProductPerformance():
return $default(_that.productId,_that.productName,_that.productImage,_that.quantitySold,_that.revenue,_that.ordersCount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String productId,  String productName,  String? productImage,  int quantitySold,  double revenue,  int ordersCount)?  $default,) {final _that = this;
switch (_that) {
case _ProductPerformance() when $default != null:
return $default(_that.productId,_that.productName,_that.productImage,_that.quantitySold,_that.revenue,_that.ordersCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductPerformance implements ProductPerformance {
  const _ProductPerformance({required this.productId, required this.productName, this.productImage, this.quantitySold = 0, this.revenue = 0.0, this.ordersCount = 0});
  factory _ProductPerformance.fromJson(Map<String, dynamic> json) => _$ProductPerformanceFromJson(json);

@override final  String productId;
@override final  String productName;
@override final  String? productImage;
@override@JsonKey() final  int quantitySold;
@override@JsonKey() final  double revenue;
@override@JsonKey() final  int ordersCount;

/// Create a copy of ProductPerformance
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductPerformanceCopyWith<_ProductPerformance> get copyWith => __$ProductPerformanceCopyWithImpl<_ProductPerformance>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductPerformanceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductPerformance&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productImage, productImage) || other.productImage == productImage)&&(identical(other.quantitySold, quantitySold) || other.quantitySold == quantitySold)&&(identical(other.revenue, revenue) || other.revenue == revenue)&&(identical(other.ordersCount, ordersCount) || other.ordersCount == ordersCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,productImage,quantitySold,revenue,ordersCount);

@override
String toString() {
  return 'ProductPerformance(productId: $productId, productName: $productName, productImage: $productImage, quantitySold: $quantitySold, revenue: $revenue, ordersCount: $ordersCount)';
}


}

/// @nodoc
abstract mixin class _$ProductPerformanceCopyWith<$Res> implements $ProductPerformanceCopyWith<$Res> {
  factory _$ProductPerformanceCopyWith(_ProductPerformance value, $Res Function(_ProductPerformance) _then) = __$ProductPerformanceCopyWithImpl;
@override @useResult
$Res call({
 String productId, String productName, String? productImage, int quantitySold, double revenue, int ordersCount
});




}
/// @nodoc
class __$ProductPerformanceCopyWithImpl<$Res>
    implements _$ProductPerformanceCopyWith<$Res> {
  __$ProductPerformanceCopyWithImpl(this._self, this._then);

  final _ProductPerformance _self;
  final $Res Function(_ProductPerformance) _then;

/// Create a copy of ProductPerformance
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? productName = null,Object? productImage = freezed,Object? quantitySold = null,Object? revenue = null,Object? ordersCount = null,}) {
  return _then(_ProductPerformance(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productImage: freezed == productImage ? _self.productImage : productImage // ignore: cast_nullable_to_non_nullable
as String?,quantitySold: null == quantitySold ? _self.quantitySold : quantitySold // ignore: cast_nullable_to_non_nullable
as int,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,ordersCount: null == ordersCount ? _self.ordersCount : ordersCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
