// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sales_analytics_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SalesAnalyticsParams {

 DateTime get startDate; DateTime get endDate; String get grouping;
/// Create a copy of SalesAnalyticsParams
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SalesAnalyticsParamsCopyWith<SalesAnalyticsParams> get copyWith => _$SalesAnalyticsParamsCopyWithImpl<SalesAnalyticsParams>(this as SalesAnalyticsParams, _$identity);

  /// Serializes this SalesAnalyticsParams to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SalesAnalyticsParams&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.grouping, grouping) || other.grouping == grouping));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,startDate,endDate,grouping);

@override
String toString() {
  return 'SalesAnalyticsParams(startDate: $startDate, endDate: $endDate, grouping: $grouping)';
}


}

/// @nodoc
abstract mixin class $SalesAnalyticsParamsCopyWith<$Res>  {
  factory $SalesAnalyticsParamsCopyWith(SalesAnalyticsParams value, $Res Function(SalesAnalyticsParams) _then) = _$SalesAnalyticsParamsCopyWithImpl;
@useResult
$Res call({
 DateTime startDate, DateTime endDate, String grouping
});




}
/// @nodoc
class _$SalesAnalyticsParamsCopyWithImpl<$Res>
    implements $SalesAnalyticsParamsCopyWith<$Res> {
  _$SalesAnalyticsParamsCopyWithImpl(this._self, this._then);

  final SalesAnalyticsParams _self;
  final $Res Function(SalesAnalyticsParams) _then;

/// Create a copy of SalesAnalyticsParams
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? startDate = null,Object? endDate = null,Object? grouping = null,}) {
  return _then(_self.copyWith(
startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,grouping: null == grouping ? _self.grouping : grouping // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SalesAnalyticsParams].
extension SalesAnalyticsParamsPatterns on SalesAnalyticsParams {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SalesAnalyticsParams value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SalesAnalyticsParams() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SalesAnalyticsParams value)  $default,){
final _that = this;
switch (_that) {
case _SalesAnalyticsParams():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SalesAnalyticsParams value)?  $default,){
final _that = this;
switch (_that) {
case _SalesAnalyticsParams() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime startDate,  DateTime endDate,  String grouping)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SalesAnalyticsParams() when $default != null:
return $default(_that.startDate,_that.endDate,_that.grouping);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime startDate,  DateTime endDate,  String grouping)  $default,) {final _that = this;
switch (_that) {
case _SalesAnalyticsParams():
return $default(_that.startDate,_that.endDate,_that.grouping);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime startDate,  DateTime endDate,  String grouping)?  $default,) {final _that = this;
switch (_that) {
case _SalesAnalyticsParams() when $default != null:
return $default(_that.startDate,_that.endDate,_that.grouping);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SalesAnalyticsParams implements SalesAnalyticsParams {
  const _SalesAnalyticsParams({required this.startDate, required this.endDate, required this.grouping});
  factory _SalesAnalyticsParams.fromJson(Map<String, dynamic> json) => _$SalesAnalyticsParamsFromJson(json);

@override final  DateTime startDate;
@override final  DateTime endDate;
@override final  String grouping;

/// Create a copy of SalesAnalyticsParams
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SalesAnalyticsParamsCopyWith<_SalesAnalyticsParams> get copyWith => __$SalesAnalyticsParamsCopyWithImpl<_SalesAnalyticsParams>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SalesAnalyticsParamsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SalesAnalyticsParams&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.grouping, grouping) || other.grouping == grouping));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,startDate,endDate,grouping);

@override
String toString() {
  return 'SalesAnalyticsParams(startDate: $startDate, endDate: $endDate, grouping: $grouping)';
}


}

/// @nodoc
abstract mixin class _$SalesAnalyticsParamsCopyWith<$Res> implements $SalesAnalyticsParamsCopyWith<$Res> {
  factory _$SalesAnalyticsParamsCopyWith(_SalesAnalyticsParams value, $Res Function(_SalesAnalyticsParams) _then) = __$SalesAnalyticsParamsCopyWithImpl;
@override @useResult
$Res call({
 DateTime startDate, DateTime endDate, String grouping
});




}
/// @nodoc
class __$SalesAnalyticsParamsCopyWithImpl<$Res>
    implements _$SalesAnalyticsParamsCopyWith<$Res> {
  __$SalesAnalyticsParamsCopyWithImpl(this._self, this._then);

  final _SalesAnalyticsParams _self;
  final $Res Function(_SalesAnalyticsParams) _then;

/// Create a copy of SalesAnalyticsParams
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? startDate = null,Object? endDate = null,Object? grouping = null,}) {
  return _then(_SalesAnalyticsParams(
startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,grouping: null == grouping ? _self.grouping : grouping // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$SalesAnalyticsPoint {

 String get label; double get revenue; int get orders;
/// Create a copy of SalesAnalyticsPoint
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SalesAnalyticsPointCopyWith<SalesAnalyticsPoint> get copyWith => _$SalesAnalyticsPointCopyWithImpl<SalesAnalyticsPoint>(this as SalesAnalyticsPoint, _$identity);

  /// Serializes this SalesAnalyticsPoint to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SalesAnalyticsPoint&&(identical(other.label, label) || other.label == label)&&(identical(other.revenue, revenue) || other.revenue == revenue)&&(identical(other.orders, orders) || other.orders == orders));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,label,revenue,orders);

@override
String toString() {
  return 'SalesAnalyticsPoint(label: $label, revenue: $revenue, orders: $orders)';
}


}

/// @nodoc
abstract mixin class $SalesAnalyticsPointCopyWith<$Res>  {
  factory $SalesAnalyticsPointCopyWith(SalesAnalyticsPoint value, $Res Function(SalesAnalyticsPoint) _then) = _$SalesAnalyticsPointCopyWithImpl;
@useResult
$Res call({
 String label, double revenue, int orders
});




}
/// @nodoc
class _$SalesAnalyticsPointCopyWithImpl<$Res>
    implements $SalesAnalyticsPointCopyWith<$Res> {
  _$SalesAnalyticsPointCopyWithImpl(this._self, this._then);

  final SalesAnalyticsPoint _self;
  final $Res Function(SalesAnalyticsPoint) _then;

/// Create a copy of SalesAnalyticsPoint
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? label = null,Object? revenue = null,Object? orders = null,}) {
  return _then(_self.copyWith(
label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,orders: null == orders ? _self.orders : orders // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [SalesAnalyticsPoint].
extension SalesAnalyticsPointPatterns on SalesAnalyticsPoint {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SalesAnalyticsPoint value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SalesAnalyticsPoint() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SalesAnalyticsPoint value)  $default,){
final _that = this;
switch (_that) {
case _SalesAnalyticsPoint():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SalesAnalyticsPoint value)?  $default,){
final _that = this;
switch (_that) {
case _SalesAnalyticsPoint() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String label,  double revenue,  int orders)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SalesAnalyticsPoint() when $default != null:
return $default(_that.label,_that.revenue,_that.orders);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String label,  double revenue,  int orders)  $default,) {final _that = this;
switch (_that) {
case _SalesAnalyticsPoint():
return $default(_that.label,_that.revenue,_that.orders);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String label,  double revenue,  int orders)?  $default,) {final _that = this;
switch (_that) {
case _SalesAnalyticsPoint() when $default != null:
return $default(_that.label,_that.revenue,_that.orders);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SalesAnalyticsPoint implements SalesAnalyticsPoint {
  const _SalesAnalyticsPoint({required this.label, required this.revenue, required this.orders});
  factory _SalesAnalyticsPoint.fromJson(Map<String, dynamic> json) => _$SalesAnalyticsPointFromJson(json);

@override final  String label;
@override final  double revenue;
@override final  int orders;

/// Create a copy of SalesAnalyticsPoint
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SalesAnalyticsPointCopyWith<_SalesAnalyticsPoint> get copyWith => __$SalesAnalyticsPointCopyWithImpl<_SalesAnalyticsPoint>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SalesAnalyticsPointToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SalesAnalyticsPoint&&(identical(other.label, label) || other.label == label)&&(identical(other.revenue, revenue) || other.revenue == revenue)&&(identical(other.orders, orders) || other.orders == orders));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,label,revenue,orders);

@override
String toString() {
  return 'SalesAnalyticsPoint(label: $label, revenue: $revenue, orders: $orders)';
}


}

/// @nodoc
abstract mixin class _$SalesAnalyticsPointCopyWith<$Res> implements $SalesAnalyticsPointCopyWith<$Res> {
  factory _$SalesAnalyticsPointCopyWith(_SalesAnalyticsPoint value, $Res Function(_SalesAnalyticsPoint) _then) = __$SalesAnalyticsPointCopyWithImpl;
@override @useResult
$Res call({
 String label, double revenue, int orders
});




}
/// @nodoc
class __$SalesAnalyticsPointCopyWithImpl<$Res>
    implements _$SalesAnalyticsPointCopyWith<$Res> {
  __$SalesAnalyticsPointCopyWithImpl(this._self, this._then);

  final _SalesAnalyticsPoint _self;
  final $Res Function(_SalesAnalyticsPoint) _then;

/// Create a copy of SalesAnalyticsPoint
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? label = null,Object? revenue = null,Object? orders = null,}) {
  return _then(_SalesAnalyticsPoint(
label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,orders: null == orders ? _self.orders : orders // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$ProductPerformance {

 String get id; String get name; double get sales; int get orders; int get views; String? get imageUrl;
/// Create a copy of ProductPerformance
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductPerformanceCopyWith<ProductPerformance> get copyWith => _$ProductPerformanceCopyWithImpl<ProductPerformance>(this as ProductPerformance, _$identity);

  /// Serializes this ProductPerformance to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductPerformance&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.sales, sales) || other.sales == sales)&&(identical(other.orders, orders) || other.orders == orders)&&(identical(other.views, views) || other.views == views)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,sales,orders,views,imageUrl);

@override
String toString() {
  return 'ProductPerformance(id: $id, name: $name, sales: $sales, orders: $orders, views: $views, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class $ProductPerformanceCopyWith<$Res>  {
  factory $ProductPerformanceCopyWith(ProductPerformance value, $Res Function(ProductPerformance) _then) = _$ProductPerformanceCopyWithImpl;
@useResult
$Res call({
 String id, String name, double sales, int orders, int views, String? imageUrl
});




}
/// @nodoc
class _$ProductPerformanceCopyWithImpl<$Res>
    implements $ProductPerformanceCopyWith<$Res> {
  _$ProductPerformanceCopyWithImpl(this._self, this._then);

  final ProductPerformance _self;
  final $Res Function(ProductPerformance) _then;

/// Create a copy of ProductPerformance
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? sales = null,Object? orders = null,Object? views = null,Object? imageUrl = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,sales: null == sales ? _self.sales : sales // ignore: cast_nullable_to_non_nullable
as double,orders: null == orders ? _self.orders : orders // ignore: cast_nullable_to_non_nullable
as int,views: null == views ? _self.views : views // ignore: cast_nullable_to_non_nullable
as int,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductPerformance].
extension ProductPerformancePatterns on ProductPerformance {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductPerformance value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductPerformance() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductPerformance value)  $default,){
final _that = this;
switch (_that) {
case _ProductPerformance():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductPerformance value)?  $default,){
final _that = this;
switch (_that) {
case _ProductPerformance() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  double sales,  int orders,  int views,  String? imageUrl)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductPerformance() when $default != null:
return $default(_that.id,_that.name,_that.sales,_that.orders,_that.views,_that.imageUrl);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  double sales,  int orders,  int views,  String? imageUrl)  $default,) {final _that = this;
switch (_that) {
case _ProductPerformance():
return $default(_that.id,_that.name,_that.sales,_that.orders,_that.views,_that.imageUrl);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  double sales,  int orders,  int views,  String? imageUrl)?  $default,) {final _that = this;
switch (_that) {
case _ProductPerformance() when $default != null:
return $default(_that.id,_that.name,_that.sales,_that.orders,_that.views,_that.imageUrl);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductPerformance implements ProductPerformance {
  const _ProductPerformance({required this.id, required this.name, required this.sales, required this.orders, required this.views, this.imageUrl});
  factory _ProductPerformance.fromJson(Map<String, dynamic> json) => _$ProductPerformanceFromJson(json);

@override final  String id;
@override final  String name;
@override final  double sales;
@override final  int orders;
@override final  int views;
@override final  String? imageUrl;

/// Create a copy of ProductPerformance
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductPerformanceCopyWith<_ProductPerformance> get copyWith => __$ProductPerformanceCopyWithImpl<_ProductPerformance>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductPerformanceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductPerformance&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.sales, sales) || other.sales == sales)&&(identical(other.orders, orders) || other.orders == orders)&&(identical(other.views, views) || other.views == views)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,sales,orders,views,imageUrl);

@override
String toString() {
  return 'ProductPerformance(id: $id, name: $name, sales: $sales, orders: $orders, views: $views, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class _$ProductPerformanceCopyWith<$Res> implements $ProductPerformanceCopyWith<$Res> {
  factory _$ProductPerformanceCopyWith(_ProductPerformance value, $Res Function(_ProductPerformance) _then) = __$ProductPerformanceCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, double sales, int orders, int views, String? imageUrl
});




}
/// @nodoc
class __$ProductPerformanceCopyWithImpl<$Res>
    implements _$ProductPerformanceCopyWith<$Res> {
  __$ProductPerformanceCopyWithImpl(this._self, this._then);

  final _ProductPerformance _self;
  final $Res Function(_ProductPerformance) _then;

/// Create a copy of ProductPerformance
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? sales = null,Object? orders = null,Object? views = null,Object? imageUrl = freezed,}) {
  return _then(_ProductPerformance(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,sales: null == sales ? _self.sales : sales // ignore: cast_nullable_to_non_nullable
as double,orders: null == orders ? _self.orders : orders // ignore: cast_nullable_to_non_nullable
as int,views: null == views ? _self.views : views // ignore: cast_nullable_to_non_nullable
as int,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$CustomerInsight {

 String get region; int get customers; double get revenue; double get averageOrderValue;
/// Create a copy of CustomerInsight
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CustomerInsightCopyWith<CustomerInsight> get copyWith => _$CustomerInsightCopyWithImpl<CustomerInsight>(this as CustomerInsight, _$identity);

  /// Serializes this CustomerInsight to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CustomerInsight&&(identical(other.region, region) || other.region == region)&&(identical(other.customers, customers) || other.customers == customers)&&(identical(other.revenue, revenue) || other.revenue == revenue)&&(identical(other.averageOrderValue, averageOrderValue) || other.averageOrderValue == averageOrderValue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,region,customers,revenue,averageOrderValue);

@override
String toString() {
  return 'CustomerInsight(region: $region, customers: $customers, revenue: $revenue, averageOrderValue: $averageOrderValue)';
}


}

/// @nodoc
abstract mixin class $CustomerInsightCopyWith<$Res>  {
  factory $CustomerInsightCopyWith(CustomerInsight value, $Res Function(CustomerInsight) _then) = _$CustomerInsightCopyWithImpl;
@useResult
$Res call({
 String region, int customers, double revenue, double averageOrderValue
});




}
/// @nodoc
class _$CustomerInsightCopyWithImpl<$Res>
    implements $CustomerInsightCopyWith<$Res> {
  _$CustomerInsightCopyWithImpl(this._self, this._then);

  final CustomerInsight _self;
  final $Res Function(CustomerInsight) _then;

/// Create a copy of CustomerInsight
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? region = null,Object? customers = null,Object? revenue = null,Object? averageOrderValue = null,}) {
  return _then(_self.copyWith(
region: null == region ? _self.region : region // ignore: cast_nullable_to_non_nullable
as String,customers: null == customers ? _self.customers : customers // ignore: cast_nullable_to_non_nullable
as int,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,averageOrderValue: null == averageOrderValue ? _self.averageOrderValue : averageOrderValue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [CustomerInsight].
extension CustomerInsightPatterns on CustomerInsight {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CustomerInsight value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CustomerInsight() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CustomerInsight value)  $default,){
final _that = this;
switch (_that) {
case _CustomerInsight():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CustomerInsight value)?  $default,){
final _that = this;
switch (_that) {
case _CustomerInsight() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String region,  int customers,  double revenue,  double averageOrderValue)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CustomerInsight() when $default != null:
return $default(_that.region,_that.customers,_that.revenue,_that.averageOrderValue);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String region,  int customers,  double revenue,  double averageOrderValue)  $default,) {final _that = this;
switch (_that) {
case _CustomerInsight():
return $default(_that.region,_that.customers,_that.revenue,_that.averageOrderValue);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String region,  int customers,  double revenue,  double averageOrderValue)?  $default,) {final _that = this;
switch (_that) {
case _CustomerInsight() when $default != null:
return $default(_that.region,_that.customers,_that.revenue,_that.averageOrderValue);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CustomerInsight implements CustomerInsight {
  const _CustomerInsight({required this.region, required this.customers, required this.revenue, required this.averageOrderValue});
  factory _CustomerInsight.fromJson(Map<String, dynamic> json) => _$CustomerInsightFromJson(json);

@override final  String region;
@override final  int customers;
@override final  double revenue;
@override final  double averageOrderValue;

/// Create a copy of CustomerInsight
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CustomerInsightCopyWith<_CustomerInsight> get copyWith => __$CustomerInsightCopyWithImpl<_CustomerInsight>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CustomerInsightToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CustomerInsight&&(identical(other.region, region) || other.region == region)&&(identical(other.customers, customers) || other.customers == customers)&&(identical(other.revenue, revenue) || other.revenue == revenue)&&(identical(other.averageOrderValue, averageOrderValue) || other.averageOrderValue == averageOrderValue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,region,customers,revenue,averageOrderValue);

@override
String toString() {
  return 'CustomerInsight(region: $region, customers: $customers, revenue: $revenue, averageOrderValue: $averageOrderValue)';
}


}

/// @nodoc
abstract mixin class _$CustomerInsightCopyWith<$Res> implements $CustomerInsightCopyWith<$Res> {
  factory _$CustomerInsightCopyWith(_CustomerInsight value, $Res Function(_CustomerInsight) _then) = __$CustomerInsightCopyWithImpl;
@override @useResult
$Res call({
 String region, int customers, double revenue, double averageOrderValue
});




}
/// @nodoc
class __$CustomerInsightCopyWithImpl<$Res>
    implements _$CustomerInsightCopyWith<$Res> {
  __$CustomerInsightCopyWithImpl(this._self, this._then);

  final _CustomerInsight _self;
  final $Res Function(_CustomerInsight) _then;

/// Create a copy of CustomerInsight
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? region = null,Object? customers = null,Object? revenue = null,Object? averageOrderValue = null,}) {
  return _then(_CustomerInsight(
region: null == region ? _self.region : region // ignore: cast_nullable_to_non_nullable
as String,customers: null == customers ? _self.customers : customers // ignore: cast_nullable_to_non_nullable
as int,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,averageOrderValue: null == averageOrderValue ? _self.averageOrderValue : averageOrderValue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
