// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_application_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerApplicationModel {

@JsonKey(includeFromJson: true, includeToJson: false) String? get id;@JsonKey(name: 'user_id') String get userId;@JsonKey(name: 'store_name') String get storeName;@JsonKey(name: 'contact_phone') String get phoneNumber;@JsonKey(name: 'business_description') String? get businessDescription;@JsonKey(name: 'seller_type') SellerType get sellerType;@JsonKey(name: 'company_name') String? get companyName;@JsonKey(name: 'company_registration_id') String? get businessLicenseNumber;@JsonKey(name: 'company_tax_id') String? get taxId;@JsonKey(name: 'company_address') String? get companyAddress;@JsonKey(name: 'company_website') String? get website; ApplicationStatus get status;@JsonKey(name: 'admin_notes') String? get adminNotes;@JsonKey(name: 'reviewed_by') String? get reviewedBy;@JsonKey(name: 'reviewed_at') DateTime? get reviewedAt;@JsonKey(name: 'created_at', includeFromJson: true, includeToJson: false) DateTime? get submittedAt;@JsonKey(name: 'updated_at', includeFromJson: true, includeToJson: false) DateTime? get updatedAt;@JsonKey(name: 'is_deleted', includeFromJson: true, includeToJson: false) bool get isDeleted;// Additional fields for metadata (not in DB but useful for processing)
@JsonKey(includeFromJson: false, includeToJson: false) Map<String, dynamic>? get metadata;
/// Create a copy of SellerApplicationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerApplicationModelCopyWith<SellerApplicationModel> get copyWith => _$SellerApplicationModelCopyWithImpl<SellerApplicationModel>(this as SellerApplicationModel, _$identity);

  /// Serializes this SellerApplicationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerApplicationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.businessDescription, businessDescription) || other.businessDescription == businessDescription)&&(identical(other.sellerType, sellerType) || other.sellerType == sellerType)&&(identical(other.companyName, companyName) || other.companyName == companyName)&&(identical(other.businessLicenseNumber, businessLicenseNumber) || other.businessLicenseNumber == businessLicenseNumber)&&(identical(other.taxId, taxId) || other.taxId == taxId)&&(identical(other.companyAddress, companyAddress) || other.companyAddress == companyAddress)&&(identical(other.website, website) || other.website == website)&&(identical(other.status, status) || other.status == status)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,storeName,phoneNumber,businessDescription,sellerType,companyName,businessLicenseNumber,taxId,companyAddress,website,status,adminNotes,reviewedBy,reviewedAt,submittedAt,updatedAt,isDeleted,const DeepCollectionEquality().hash(metadata)]);

@override
String toString() {
  return 'SellerApplicationModel(id: $id, userId: $userId, storeName: $storeName, phoneNumber: $phoneNumber, businessDescription: $businessDescription, sellerType: $sellerType, companyName: $companyName, businessLicenseNumber: $businessLicenseNumber, taxId: $taxId, companyAddress: $companyAddress, website: $website, status: $status, adminNotes: $adminNotes, reviewedBy: $reviewedBy, reviewedAt: $reviewedAt, submittedAt: $submittedAt, updatedAt: $updatedAt, isDeleted: $isDeleted, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $SellerApplicationModelCopyWith<$Res>  {
  factory $SellerApplicationModelCopyWith(SellerApplicationModel value, $Res Function(SellerApplicationModel) _then) = _$SellerApplicationModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(includeFromJson: true, includeToJson: false) String? id,@JsonKey(name: 'user_id') String userId,@JsonKey(name: 'store_name') String storeName,@JsonKey(name: 'contact_phone') String phoneNumber,@JsonKey(name: 'business_description') String? businessDescription,@JsonKey(name: 'seller_type') SellerType sellerType,@JsonKey(name: 'company_name') String? companyName,@JsonKey(name: 'company_registration_id') String? businessLicenseNumber,@JsonKey(name: 'company_tax_id') String? taxId,@JsonKey(name: 'company_address') String? companyAddress,@JsonKey(name: 'company_website') String? website, ApplicationStatus status,@JsonKey(name: 'admin_notes') String? adminNotes,@JsonKey(name: 'reviewed_by') String? reviewedBy,@JsonKey(name: 'reviewed_at') DateTime? reviewedAt,@JsonKey(name: 'created_at', includeFromJson: true, includeToJson: false) DateTime? submittedAt,@JsonKey(name: 'updated_at', includeFromJson: true, includeToJson: false) DateTime? updatedAt,@JsonKey(name: 'is_deleted', includeFromJson: true, includeToJson: false) bool isDeleted,@JsonKey(includeFromJson: false, includeToJson: false) Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$SellerApplicationModelCopyWithImpl<$Res>
    implements $SellerApplicationModelCopyWith<$Res> {
  _$SellerApplicationModelCopyWithImpl(this._self, this._then);

  final SellerApplicationModel _self;
  final $Res Function(SellerApplicationModel) _then;

/// Create a copy of SellerApplicationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? userId = null,Object? storeName = null,Object? phoneNumber = null,Object? businessDescription = freezed,Object? sellerType = null,Object? companyName = freezed,Object? businessLicenseNumber = freezed,Object? taxId = freezed,Object? companyAddress = freezed,Object? website = freezed,Object? status = null,Object? adminNotes = freezed,Object? reviewedBy = freezed,Object? reviewedAt = freezed,Object? submittedAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,storeName: null == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,businessDescription: freezed == businessDescription ? _self.businessDescription : businessDescription // ignore: cast_nullable_to_non_nullable
as String?,sellerType: null == sellerType ? _self.sellerType : sellerType // ignore: cast_nullable_to_non_nullable
as SellerType,companyName: freezed == companyName ? _self.companyName : companyName // ignore: cast_nullable_to_non_nullable
as String?,businessLicenseNumber: freezed == businessLicenseNumber ? _self.businessLicenseNumber : businessLicenseNumber // ignore: cast_nullable_to_non_nullable
as String?,taxId: freezed == taxId ? _self.taxId : taxId // ignore: cast_nullable_to_non_nullable
as String?,companyAddress: freezed == companyAddress ? _self.companyAddress : companyAddress // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ApplicationStatus,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,submittedAt: freezed == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerApplicationModel].
extension SellerApplicationModelPatterns on SellerApplicationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerApplicationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerApplicationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerApplicationModel value)  $default,){
final _that = this;
switch (_that) {
case _SellerApplicationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerApplicationModel value)?  $default,){
final _that = this;
switch (_that) {
case _SellerApplicationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(includeFromJson: true, includeToJson: false)  String? id, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'store_name')  String storeName, @JsonKey(name: 'contact_phone')  String phoneNumber, @JsonKey(name: 'business_description')  String? businessDescription, @JsonKey(name: 'seller_type')  SellerType sellerType, @JsonKey(name: 'company_name')  String? companyName, @JsonKey(name: 'company_registration_id')  String? businessLicenseNumber, @JsonKey(name: 'company_tax_id')  String? taxId, @JsonKey(name: 'company_address')  String? companyAddress, @JsonKey(name: 'company_website')  String? website,  ApplicationStatus status, @JsonKey(name: 'admin_notes')  String? adminNotes, @JsonKey(name: 'reviewed_by')  String? reviewedBy, @JsonKey(name: 'reviewed_at')  DateTime? reviewedAt, @JsonKey(name: 'created_at', includeFromJson: true, includeToJson: false)  DateTime? submittedAt, @JsonKey(name: 'updated_at', includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(name: 'is_deleted', includeFromJson: true, includeToJson: false)  bool isDeleted, @JsonKey(includeFromJson: false, includeToJson: false)  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerApplicationModel() when $default != null:
return $default(_that.id,_that.userId,_that.storeName,_that.phoneNumber,_that.businessDescription,_that.sellerType,_that.companyName,_that.businessLicenseNumber,_that.taxId,_that.companyAddress,_that.website,_that.status,_that.adminNotes,_that.reviewedBy,_that.reviewedAt,_that.submittedAt,_that.updatedAt,_that.isDeleted,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(includeFromJson: true, includeToJson: false)  String? id, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'store_name')  String storeName, @JsonKey(name: 'contact_phone')  String phoneNumber, @JsonKey(name: 'business_description')  String? businessDescription, @JsonKey(name: 'seller_type')  SellerType sellerType, @JsonKey(name: 'company_name')  String? companyName, @JsonKey(name: 'company_registration_id')  String? businessLicenseNumber, @JsonKey(name: 'company_tax_id')  String? taxId, @JsonKey(name: 'company_address')  String? companyAddress, @JsonKey(name: 'company_website')  String? website,  ApplicationStatus status, @JsonKey(name: 'admin_notes')  String? adminNotes, @JsonKey(name: 'reviewed_by')  String? reviewedBy, @JsonKey(name: 'reviewed_at')  DateTime? reviewedAt, @JsonKey(name: 'created_at', includeFromJson: true, includeToJson: false)  DateTime? submittedAt, @JsonKey(name: 'updated_at', includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(name: 'is_deleted', includeFromJson: true, includeToJson: false)  bool isDeleted, @JsonKey(includeFromJson: false, includeToJson: false)  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _SellerApplicationModel():
return $default(_that.id,_that.userId,_that.storeName,_that.phoneNumber,_that.businessDescription,_that.sellerType,_that.companyName,_that.businessLicenseNumber,_that.taxId,_that.companyAddress,_that.website,_that.status,_that.adminNotes,_that.reviewedBy,_that.reviewedAt,_that.submittedAt,_that.updatedAt,_that.isDeleted,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(includeFromJson: true, includeToJson: false)  String? id, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'store_name')  String storeName, @JsonKey(name: 'contact_phone')  String phoneNumber, @JsonKey(name: 'business_description')  String? businessDescription, @JsonKey(name: 'seller_type')  SellerType sellerType, @JsonKey(name: 'company_name')  String? companyName, @JsonKey(name: 'company_registration_id')  String? businessLicenseNumber, @JsonKey(name: 'company_tax_id')  String? taxId, @JsonKey(name: 'company_address')  String? companyAddress, @JsonKey(name: 'company_website')  String? website,  ApplicationStatus status, @JsonKey(name: 'admin_notes')  String? adminNotes, @JsonKey(name: 'reviewed_by')  String? reviewedBy, @JsonKey(name: 'reviewed_at')  DateTime? reviewedAt, @JsonKey(name: 'created_at', includeFromJson: true, includeToJson: false)  DateTime? submittedAt, @JsonKey(name: 'updated_at', includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(name: 'is_deleted', includeFromJson: true, includeToJson: false)  bool isDeleted, @JsonKey(includeFromJson: false, includeToJson: false)  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _SellerApplicationModel() when $default != null:
return $default(_that.id,_that.userId,_that.storeName,_that.phoneNumber,_that.businessDescription,_that.sellerType,_that.companyName,_that.businessLicenseNumber,_that.taxId,_that.companyAddress,_that.website,_that.status,_that.adminNotes,_that.reviewedBy,_that.reviewedAt,_that.submittedAt,_that.updatedAt,_that.isDeleted,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SellerApplicationModel extends SellerApplicationModel {
  const _SellerApplicationModel({@JsonKey(includeFromJson: true, includeToJson: false) this.id, @JsonKey(name: 'user_id') required this.userId, @JsonKey(name: 'store_name') required this.storeName, @JsonKey(name: 'contact_phone') required this.phoneNumber, @JsonKey(name: 'business_description') this.businessDescription, @JsonKey(name: 'seller_type') required this.sellerType, @JsonKey(name: 'company_name') this.companyName, @JsonKey(name: 'company_registration_id') this.businessLicenseNumber, @JsonKey(name: 'company_tax_id') this.taxId, @JsonKey(name: 'company_address') this.companyAddress, @JsonKey(name: 'company_website') this.website, this.status = ApplicationStatus.pending, @JsonKey(name: 'admin_notes') this.adminNotes, @JsonKey(name: 'reviewed_by') this.reviewedBy, @JsonKey(name: 'reviewed_at') this.reviewedAt, @JsonKey(name: 'created_at', includeFromJson: true, includeToJson: false) this.submittedAt, @JsonKey(name: 'updated_at', includeFromJson: true, includeToJson: false) this.updatedAt, @JsonKey(name: 'is_deleted', includeFromJson: true, includeToJson: false) this.isDeleted = false, @JsonKey(includeFromJson: false, includeToJson: false) final  Map<String, dynamic>? metadata}): _metadata = metadata,super._();
  factory _SellerApplicationModel.fromJson(Map<String, dynamic> json) => _$SellerApplicationModelFromJson(json);

@override@JsonKey(includeFromJson: true, includeToJson: false) final  String? id;
@override@JsonKey(name: 'user_id') final  String userId;
@override@JsonKey(name: 'store_name') final  String storeName;
@override@JsonKey(name: 'contact_phone') final  String phoneNumber;
@override@JsonKey(name: 'business_description') final  String? businessDescription;
@override@JsonKey(name: 'seller_type') final  SellerType sellerType;
@override@JsonKey(name: 'company_name') final  String? companyName;
@override@JsonKey(name: 'company_registration_id') final  String? businessLicenseNumber;
@override@JsonKey(name: 'company_tax_id') final  String? taxId;
@override@JsonKey(name: 'company_address') final  String? companyAddress;
@override@JsonKey(name: 'company_website') final  String? website;
@override@JsonKey() final  ApplicationStatus status;
@override@JsonKey(name: 'admin_notes') final  String? adminNotes;
@override@JsonKey(name: 'reviewed_by') final  String? reviewedBy;
@override@JsonKey(name: 'reviewed_at') final  DateTime? reviewedAt;
@override@JsonKey(name: 'created_at', includeFromJson: true, includeToJson: false) final  DateTime? submittedAt;
@override@JsonKey(name: 'updated_at', includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted', includeFromJson: true, includeToJson: false) final  bool isDeleted;
// Additional fields for metadata (not in DB but useful for processing)
 final  Map<String, dynamic>? _metadata;
// Additional fields for metadata (not in DB but useful for processing)
@override@JsonKey(includeFromJson: false, includeToJson: false) Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SellerApplicationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerApplicationModelCopyWith<_SellerApplicationModel> get copyWith => __$SellerApplicationModelCopyWithImpl<_SellerApplicationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerApplicationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerApplicationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.businessDescription, businessDescription) || other.businessDescription == businessDescription)&&(identical(other.sellerType, sellerType) || other.sellerType == sellerType)&&(identical(other.companyName, companyName) || other.companyName == companyName)&&(identical(other.businessLicenseNumber, businessLicenseNumber) || other.businessLicenseNumber == businessLicenseNumber)&&(identical(other.taxId, taxId) || other.taxId == taxId)&&(identical(other.companyAddress, companyAddress) || other.companyAddress == companyAddress)&&(identical(other.website, website) || other.website == website)&&(identical(other.status, status) || other.status == status)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,storeName,phoneNumber,businessDescription,sellerType,companyName,businessLicenseNumber,taxId,companyAddress,website,status,adminNotes,reviewedBy,reviewedAt,submittedAt,updatedAt,isDeleted,const DeepCollectionEquality().hash(_metadata)]);

@override
String toString() {
  return 'SellerApplicationModel(id: $id, userId: $userId, storeName: $storeName, phoneNumber: $phoneNumber, businessDescription: $businessDescription, sellerType: $sellerType, companyName: $companyName, businessLicenseNumber: $businessLicenseNumber, taxId: $taxId, companyAddress: $companyAddress, website: $website, status: $status, adminNotes: $adminNotes, reviewedBy: $reviewedBy, reviewedAt: $reviewedAt, submittedAt: $submittedAt, updatedAt: $updatedAt, isDeleted: $isDeleted, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$SellerApplicationModelCopyWith<$Res> implements $SellerApplicationModelCopyWith<$Res> {
  factory _$SellerApplicationModelCopyWith(_SellerApplicationModel value, $Res Function(_SellerApplicationModel) _then) = __$SellerApplicationModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(includeFromJson: true, includeToJson: false) String? id,@JsonKey(name: 'user_id') String userId,@JsonKey(name: 'store_name') String storeName,@JsonKey(name: 'contact_phone') String phoneNumber,@JsonKey(name: 'business_description') String? businessDescription,@JsonKey(name: 'seller_type') SellerType sellerType,@JsonKey(name: 'company_name') String? companyName,@JsonKey(name: 'company_registration_id') String? businessLicenseNumber,@JsonKey(name: 'company_tax_id') String? taxId,@JsonKey(name: 'company_address') String? companyAddress,@JsonKey(name: 'company_website') String? website, ApplicationStatus status,@JsonKey(name: 'admin_notes') String? adminNotes,@JsonKey(name: 'reviewed_by') String? reviewedBy,@JsonKey(name: 'reviewed_at') DateTime? reviewedAt,@JsonKey(name: 'created_at', includeFromJson: true, includeToJson: false) DateTime? submittedAt,@JsonKey(name: 'updated_at', includeFromJson: true, includeToJson: false) DateTime? updatedAt,@JsonKey(name: 'is_deleted', includeFromJson: true, includeToJson: false) bool isDeleted,@JsonKey(includeFromJson: false, includeToJson: false) Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$SellerApplicationModelCopyWithImpl<$Res>
    implements _$SellerApplicationModelCopyWith<$Res> {
  __$SellerApplicationModelCopyWithImpl(this._self, this._then);

  final _SellerApplicationModel _self;
  final $Res Function(_SellerApplicationModel) _then;

/// Create a copy of SellerApplicationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? userId = null,Object? storeName = null,Object? phoneNumber = null,Object? businessDescription = freezed,Object? sellerType = null,Object? companyName = freezed,Object? businessLicenseNumber = freezed,Object? taxId = freezed,Object? companyAddress = freezed,Object? website = freezed,Object? status = null,Object? adminNotes = freezed,Object? reviewedBy = freezed,Object? reviewedAt = freezed,Object? submittedAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? metadata = freezed,}) {
  return _then(_SellerApplicationModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,storeName: null == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,businessDescription: freezed == businessDescription ? _self.businessDescription : businessDescription // ignore: cast_nullable_to_non_nullable
as String?,sellerType: null == sellerType ? _self.sellerType : sellerType // ignore: cast_nullable_to_non_nullable
as SellerType,companyName: freezed == companyName ? _self.companyName : companyName // ignore: cast_nullable_to_non_nullable
as String?,businessLicenseNumber: freezed == businessLicenseNumber ? _self.businessLicenseNumber : businessLicenseNumber // ignore: cast_nullable_to_non_nullable
as String?,taxId: freezed == taxId ? _self.taxId : taxId // ignore: cast_nullable_to_non_nullable
as String?,companyAddress: freezed == companyAddress ? _self.companyAddress : companyAddress // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ApplicationStatus,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,submittedAt: freezed == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on
