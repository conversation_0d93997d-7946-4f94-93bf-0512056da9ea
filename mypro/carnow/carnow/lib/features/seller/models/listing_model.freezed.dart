// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'listing_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductListing {

 String get id; String get sellerId; String get productId; ListingStatus get status; DateTime get listedAt; DateTime get expiresAt; DateTime? get renewedAt; DateTime? get pausedAt; DateTime? get suspendedAt; int get validityDays; bool get isFromFreeQuota; bool get isPaid; bool get hasPriority; double? get listingFee; String? get paymentTransactionId; ListingPaymentStatus? get paymentStatus; String? get subscriptionTierAtListing; Map<String, dynamic>? get metadata;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of ProductListing
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductListingCopyWith<ProductListing> get copyWith => _$ProductListingCopyWithImpl<ProductListing>(this as ProductListing, _$identity);

  /// Serializes this ProductListing to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductListing&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.status, status) || other.status == status)&&(identical(other.listedAt, listedAt) || other.listedAt == listedAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.renewedAt, renewedAt) || other.renewedAt == renewedAt)&&(identical(other.pausedAt, pausedAt) || other.pausedAt == pausedAt)&&(identical(other.suspendedAt, suspendedAt) || other.suspendedAt == suspendedAt)&&(identical(other.validityDays, validityDays) || other.validityDays == validityDays)&&(identical(other.isFromFreeQuota, isFromFreeQuota) || other.isFromFreeQuota == isFromFreeQuota)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid)&&(identical(other.hasPriority, hasPriority) || other.hasPriority == hasPriority)&&(identical(other.listingFee, listingFee) || other.listingFee == listingFee)&&(identical(other.paymentTransactionId, paymentTransactionId) || other.paymentTransactionId == paymentTransactionId)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.subscriptionTierAtListing, subscriptionTierAtListing) || other.subscriptionTierAtListing == subscriptionTierAtListing)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,productId,status,listedAt,expiresAt,renewedAt,pausedAt,suspendedAt,validityDays,isFromFreeQuota,isPaid,hasPriority,listingFee,paymentTransactionId,paymentStatus,subscriptionTierAtListing,const DeepCollectionEquality().hash(metadata),createdAt,updatedAt]);

@override
String toString() {
  return 'ProductListing(id: $id, sellerId: $sellerId, productId: $productId, status: $status, listedAt: $listedAt, expiresAt: $expiresAt, renewedAt: $renewedAt, pausedAt: $pausedAt, suspendedAt: $suspendedAt, validityDays: $validityDays, isFromFreeQuota: $isFromFreeQuota, isPaid: $isPaid, hasPriority: $hasPriority, listingFee: $listingFee, paymentTransactionId: $paymentTransactionId, paymentStatus: $paymentStatus, subscriptionTierAtListing: $subscriptionTierAtListing, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $ProductListingCopyWith<$Res>  {
  factory $ProductListingCopyWith(ProductListing value, $Res Function(ProductListing) _then) = _$ProductListingCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String productId, ListingStatus status, DateTime listedAt, DateTime expiresAt, DateTime? renewedAt, DateTime? pausedAt, DateTime? suspendedAt, int validityDays, bool isFromFreeQuota, bool isPaid, bool hasPriority, double? listingFee, String? paymentTransactionId, ListingPaymentStatus? paymentStatus, String? subscriptionTierAtListing, Map<String, dynamic>? metadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$ProductListingCopyWithImpl<$Res>
    implements $ProductListingCopyWith<$Res> {
  _$ProductListingCopyWithImpl(this._self, this._then);

  final ProductListing _self;
  final $Res Function(ProductListing) _then;

/// Create a copy of ProductListing
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? productId = null,Object? status = null,Object? listedAt = null,Object? expiresAt = null,Object? renewedAt = freezed,Object? pausedAt = freezed,Object? suspendedAt = freezed,Object? validityDays = null,Object? isFromFreeQuota = null,Object? isPaid = null,Object? hasPriority = null,Object? listingFee = freezed,Object? paymentTransactionId = freezed,Object? paymentStatus = freezed,Object? subscriptionTierAtListing = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ListingStatus,listedAt: null == listedAt ? _self.listedAt : listedAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,renewedAt: freezed == renewedAt ? _self.renewedAt : renewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,pausedAt: freezed == pausedAt ? _self.pausedAt : pausedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,suspendedAt: freezed == suspendedAt ? _self.suspendedAt : suspendedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,validityDays: null == validityDays ? _self.validityDays : validityDays // ignore: cast_nullable_to_non_nullable
as int,isFromFreeQuota: null == isFromFreeQuota ? _self.isFromFreeQuota : isFromFreeQuota // ignore: cast_nullable_to_non_nullable
as bool,isPaid: null == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool,hasPriority: null == hasPriority ? _self.hasPriority : hasPriority // ignore: cast_nullable_to_non_nullable
as bool,listingFee: freezed == listingFee ? _self.listingFee : listingFee // ignore: cast_nullable_to_non_nullable
as double?,paymentTransactionId: freezed == paymentTransactionId ? _self.paymentTransactionId : paymentTransactionId // ignore: cast_nullable_to_non_nullable
as String?,paymentStatus: freezed == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as ListingPaymentStatus?,subscriptionTierAtListing: freezed == subscriptionTierAtListing ? _self.subscriptionTierAtListing : subscriptionTierAtListing // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductListing].
extension ProductListingPatterns on ProductListing {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductListing value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductListing() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductListing value)  $default,){
final _that = this;
switch (_that) {
case _ProductListing():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductListing value)?  $default,){
final _that = this;
switch (_that) {
case _ProductListing() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String productId,  ListingStatus status,  DateTime listedAt,  DateTime expiresAt,  DateTime? renewedAt,  DateTime? pausedAt,  DateTime? suspendedAt,  int validityDays,  bool isFromFreeQuota,  bool isPaid,  bool hasPriority,  double? listingFee,  String? paymentTransactionId,  ListingPaymentStatus? paymentStatus,  String? subscriptionTierAtListing,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductListing() when $default != null:
return $default(_that.id,_that.sellerId,_that.productId,_that.status,_that.listedAt,_that.expiresAt,_that.renewedAt,_that.pausedAt,_that.suspendedAt,_that.validityDays,_that.isFromFreeQuota,_that.isPaid,_that.hasPriority,_that.listingFee,_that.paymentTransactionId,_that.paymentStatus,_that.subscriptionTierAtListing,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String productId,  ListingStatus status,  DateTime listedAt,  DateTime expiresAt,  DateTime? renewedAt,  DateTime? pausedAt,  DateTime? suspendedAt,  int validityDays,  bool isFromFreeQuota,  bool isPaid,  bool hasPriority,  double? listingFee,  String? paymentTransactionId,  ListingPaymentStatus? paymentStatus,  String? subscriptionTierAtListing,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _ProductListing():
return $default(_that.id,_that.sellerId,_that.productId,_that.status,_that.listedAt,_that.expiresAt,_that.renewedAt,_that.pausedAt,_that.suspendedAt,_that.validityDays,_that.isFromFreeQuota,_that.isPaid,_that.hasPriority,_that.listingFee,_that.paymentTransactionId,_that.paymentStatus,_that.subscriptionTierAtListing,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String productId,  ListingStatus status,  DateTime listedAt,  DateTime expiresAt,  DateTime? renewedAt,  DateTime? pausedAt,  DateTime? suspendedAt,  int validityDays,  bool isFromFreeQuota,  bool isPaid,  bool hasPriority,  double? listingFee,  String? paymentTransactionId,  ListingPaymentStatus? paymentStatus,  String? subscriptionTierAtListing,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _ProductListing() when $default != null:
return $default(_that.id,_that.sellerId,_that.productId,_that.status,_that.listedAt,_that.expiresAt,_that.renewedAt,_that.pausedAt,_that.suspendedAt,_that.validityDays,_that.isFromFreeQuota,_that.isPaid,_that.hasPriority,_that.listingFee,_that.paymentTransactionId,_that.paymentStatus,_that.subscriptionTierAtListing,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _ProductListing implements ProductListing {
  const _ProductListing({required this.id, required this.sellerId, required this.productId, required this.status, required this.listedAt, required this.expiresAt, this.renewedAt, this.pausedAt, this.suspendedAt, this.validityDays = 30, this.isFromFreeQuota = false, this.isPaid = false, this.hasPriority = false, this.listingFee, this.paymentTransactionId, this.paymentStatus, this.subscriptionTierAtListing, final  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _metadata = metadata;
  factory _ProductListing.fromJson(Map<String, dynamic> json) => _$ProductListingFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String productId;
@override final  ListingStatus status;
@override final  DateTime listedAt;
@override final  DateTime expiresAt;
@override final  DateTime? renewedAt;
@override final  DateTime? pausedAt;
@override final  DateTime? suspendedAt;
@override@JsonKey() final  int validityDays;
@override@JsonKey() final  bool isFromFreeQuota;
@override@JsonKey() final  bool isPaid;
@override@JsonKey() final  bool hasPriority;
@override final  double? listingFee;
@override final  String? paymentTransactionId;
@override final  ListingPaymentStatus? paymentStatus;
@override final  String? subscriptionTierAtListing;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of ProductListing
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductListingCopyWith<_ProductListing> get copyWith => __$ProductListingCopyWithImpl<_ProductListing>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductListingToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductListing&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.status, status) || other.status == status)&&(identical(other.listedAt, listedAt) || other.listedAt == listedAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.renewedAt, renewedAt) || other.renewedAt == renewedAt)&&(identical(other.pausedAt, pausedAt) || other.pausedAt == pausedAt)&&(identical(other.suspendedAt, suspendedAt) || other.suspendedAt == suspendedAt)&&(identical(other.validityDays, validityDays) || other.validityDays == validityDays)&&(identical(other.isFromFreeQuota, isFromFreeQuota) || other.isFromFreeQuota == isFromFreeQuota)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid)&&(identical(other.hasPriority, hasPriority) || other.hasPriority == hasPriority)&&(identical(other.listingFee, listingFee) || other.listingFee == listingFee)&&(identical(other.paymentTransactionId, paymentTransactionId) || other.paymentTransactionId == paymentTransactionId)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.subscriptionTierAtListing, subscriptionTierAtListing) || other.subscriptionTierAtListing == subscriptionTierAtListing)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,productId,status,listedAt,expiresAt,renewedAt,pausedAt,suspendedAt,validityDays,isFromFreeQuota,isPaid,hasPriority,listingFee,paymentTransactionId,paymentStatus,subscriptionTierAtListing,const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt]);

@override
String toString() {
  return 'ProductListing(id: $id, sellerId: $sellerId, productId: $productId, status: $status, listedAt: $listedAt, expiresAt: $expiresAt, renewedAt: $renewedAt, pausedAt: $pausedAt, suspendedAt: $suspendedAt, validityDays: $validityDays, isFromFreeQuota: $isFromFreeQuota, isPaid: $isPaid, hasPriority: $hasPriority, listingFee: $listingFee, paymentTransactionId: $paymentTransactionId, paymentStatus: $paymentStatus, subscriptionTierAtListing: $subscriptionTierAtListing, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$ProductListingCopyWith<$Res> implements $ProductListingCopyWith<$Res> {
  factory _$ProductListingCopyWith(_ProductListing value, $Res Function(_ProductListing) _then) = __$ProductListingCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String productId, ListingStatus status, DateTime listedAt, DateTime expiresAt, DateTime? renewedAt, DateTime? pausedAt, DateTime? suspendedAt, int validityDays, bool isFromFreeQuota, bool isPaid, bool hasPriority, double? listingFee, String? paymentTransactionId, ListingPaymentStatus? paymentStatus, String? subscriptionTierAtListing, Map<String, dynamic>? metadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$ProductListingCopyWithImpl<$Res>
    implements _$ProductListingCopyWith<$Res> {
  __$ProductListingCopyWithImpl(this._self, this._then);

  final _ProductListing _self;
  final $Res Function(_ProductListing) _then;

/// Create a copy of ProductListing
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? productId = null,Object? status = null,Object? listedAt = null,Object? expiresAt = null,Object? renewedAt = freezed,Object? pausedAt = freezed,Object? suspendedAt = freezed,Object? validityDays = null,Object? isFromFreeQuota = null,Object? isPaid = null,Object? hasPriority = null,Object? listingFee = freezed,Object? paymentTransactionId = freezed,Object? paymentStatus = freezed,Object? subscriptionTierAtListing = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_ProductListing(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ListingStatus,listedAt: null == listedAt ? _self.listedAt : listedAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,renewedAt: freezed == renewedAt ? _self.renewedAt : renewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,pausedAt: freezed == pausedAt ? _self.pausedAt : pausedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,suspendedAt: freezed == suspendedAt ? _self.suspendedAt : suspendedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,validityDays: null == validityDays ? _self.validityDays : validityDays // ignore: cast_nullable_to_non_nullable
as int,isFromFreeQuota: null == isFromFreeQuota ? _self.isFromFreeQuota : isFromFreeQuota // ignore: cast_nullable_to_non_nullable
as bool,isPaid: null == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool,hasPriority: null == hasPriority ? _self.hasPriority : hasPriority // ignore: cast_nullable_to_non_nullable
as bool,listingFee: freezed == listingFee ? _self.listingFee : listingFee // ignore: cast_nullable_to_non_nullable
as double?,paymentTransactionId: freezed == paymentTransactionId ? _self.paymentTransactionId : paymentTransactionId // ignore: cast_nullable_to_non_nullable
as String?,paymentStatus: freezed == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as ListingPaymentStatus?,subscriptionTierAtListing: freezed == subscriptionTierAtListing ? _self.subscriptionTierAtListing : subscriptionTierAtListing // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$ListingFeeTransaction {

 String get id; String get sellerId; String get listingId; double get amount; String get currency; ListingPaymentStatus get status; DateTime get createdAt; DateTime? get paidAt; DateTime? get failedAt; DateTime? get refundedAt; String? get paymentMethodId; String? get transactionReference; String? get failureReason; Map<String, dynamic>? get paymentMetadata;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of ListingFeeTransaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ListingFeeTransactionCopyWith<ListingFeeTransaction> get copyWith => _$ListingFeeTransactionCopyWithImpl<ListingFeeTransaction>(this as ListingFeeTransaction, _$identity);

  /// Serializes this ListingFeeTransaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ListingFeeTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.listingId, listingId) || other.listingId == listingId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.failedAt, failedAt) || other.failedAt == failedAt)&&(identical(other.refundedAt, refundedAt) || other.refundedAt == refundedAt)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.transactionReference, transactionReference) || other.transactionReference == transactionReference)&&(identical(other.failureReason, failureReason) || other.failureReason == failureReason)&&const DeepCollectionEquality().equals(other.paymentMetadata, paymentMetadata)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,listingId,amount,currency,status,createdAt,paidAt,failedAt,refundedAt,paymentMethodId,transactionReference,failureReason,const DeepCollectionEquality().hash(paymentMetadata),updatedAt);

@override
String toString() {
  return 'ListingFeeTransaction(id: $id, sellerId: $sellerId, listingId: $listingId, amount: $amount, currency: $currency, status: $status, createdAt: $createdAt, paidAt: $paidAt, failedAt: $failedAt, refundedAt: $refundedAt, paymentMethodId: $paymentMethodId, transactionReference: $transactionReference, failureReason: $failureReason, paymentMetadata: $paymentMetadata, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $ListingFeeTransactionCopyWith<$Res>  {
  factory $ListingFeeTransactionCopyWith(ListingFeeTransaction value, $Res Function(ListingFeeTransaction) _then) = _$ListingFeeTransactionCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String listingId, double amount, String currency, ListingPaymentStatus status, DateTime createdAt, DateTime? paidAt, DateTime? failedAt, DateTime? refundedAt, String? paymentMethodId, String? transactionReference, String? failureReason, Map<String, dynamic>? paymentMetadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$ListingFeeTransactionCopyWithImpl<$Res>
    implements $ListingFeeTransactionCopyWith<$Res> {
  _$ListingFeeTransactionCopyWithImpl(this._self, this._then);

  final ListingFeeTransaction _self;
  final $Res Function(ListingFeeTransaction) _then;

/// Create a copy of ListingFeeTransaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? listingId = null,Object? amount = null,Object? currency = null,Object? status = null,Object? createdAt = null,Object? paidAt = freezed,Object? failedAt = freezed,Object? refundedAt = freezed,Object? paymentMethodId = freezed,Object? transactionReference = freezed,Object? failureReason = freezed,Object? paymentMetadata = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,listingId: null == listingId ? _self.listingId : listingId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ListingPaymentStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,failedAt: freezed == failedAt ? _self.failedAt : failedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,refundedAt: freezed == refundedAt ? _self.refundedAt : refundedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,transactionReference: freezed == transactionReference ? _self.transactionReference : transactionReference // ignore: cast_nullable_to_non_nullable
as String?,failureReason: freezed == failureReason ? _self.failureReason : failureReason // ignore: cast_nullable_to_non_nullable
as String?,paymentMetadata: freezed == paymentMetadata ? _self.paymentMetadata : paymentMetadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [ListingFeeTransaction].
extension ListingFeeTransactionPatterns on ListingFeeTransaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ListingFeeTransaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ListingFeeTransaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ListingFeeTransaction value)  $default,){
final _that = this;
switch (_that) {
case _ListingFeeTransaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ListingFeeTransaction value)?  $default,){
final _that = this;
switch (_that) {
case _ListingFeeTransaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String listingId,  double amount,  String currency,  ListingPaymentStatus status,  DateTime createdAt,  DateTime? paidAt,  DateTime? failedAt,  DateTime? refundedAt,  String? paymentMethodId,  String? transactionReference,  String? failureReason,  Map<String, dynamic>? paymentMetadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ListingFeeTransaction() when $default != null:
return $default(_that.id,_that.sellerId,_that.listingId,_that.amount,_that.currency,_that.status,_that.createdAt,_that.paidAt,_that.failedAt,_that.refundedAt,_that.paymentMethodId,_that.transactionReference,_that.failureReason,_that.paymentMetadata,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String listingId,  double amount,  String currency,  ListingPaymentStatus status,  DateTime createdAt,  DateTime? paidAt,  DateTime? failedAt,  DateTime? refundedAt,  String? paymentMethodId,  String? transactionReference,  String? failureReason,  Map<String, dynamic>? paymentMetadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _ListingFeeTransaction():
return $default(_that.id,_that.sellerId,_that.listingId,_that.amount,_that.currency,_that.status,_that.createdAt,_that.paidAt,_that.failedAt,_that.refundedAt,_that.paymentMethodId,_that.transactionReference,_that.failureReason,_that.paymentMetadata,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String listingId,  double amount,  String currency,  ListingPaymentStatus status,  DateTime createdAt,  DateTime? paidAt,  DateTime? failedAt,  DateTime? refundedAt,  String? paymentMethodId,  String? transactionReference,  String? failureReason,  Map<String, dynamic>? paymentMetadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _ListingFeeTransaction() when $default != null:
return $default(_that.id,_that.sellerId,_that.listingId,_that.amount,_that.currency,_that.status,_that.createdAt,_that.paidAt,_that.failedAt,_that.refundedAt,_that.paymentMethodId,_that.transactionReference,_that.failureReason,_that.paymentMetadata,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _ListingFeeTransaction implements ListingFeeTransaction {
  const _ListingFeeTransaction({required this.id, required this.sellerId, required this.listingId, required this.amount, required this.currency, required this.status, required this.createdAt, this.paidAt, this.failedAt, this.refundedAt, this.paymentMethodId, this.transactionReference, this.failureReason, final  Map<String, dynamic>? paymentMetadata, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _paymentMetadata = paymentMetadata;
  factory _ListingFeeTransaction.fromJson(Map<String, dynamic> json) => _$ListingFeeTransactionFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String listingId;
@override final  double amount;
@override final  String currency;
@override final  ListingPaymentStatus status;
@override final  DateTime createdAt;
@override final  DateTime? paidAt;
@override final  DateTime? failedAt;
@override final  DateTime? refundedAt;
@override final  String? paymentMethodId;
@override final  String? transactionReference;
@override final  String? failureReason;
 final  Map<String, dynamic>? _paymentMetadata;
@override Map<String, dynamic>? get paymentMetadata {
  final value = _paymentMetadata;
  if (value == null) return null;
  if (_paymentMetadata is EqualUnmodifiableMapView) return _paymentMetadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of ListingFeeTransaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ListingFeeTransactionCopyWith<_ListingFeeTransaction> get copyWith => __$ListingFeeTransactionCopyWithImpl<_ListingFeeTransaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ListingFeeTransactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ListingFeeTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.listingId, listingId) || other.listingId == listingId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.failedAt, failedAt) || other.failedAt == failedAt)&&(identical(other.refundedAt, refundedAt) || other.refundedAt == refundedAt)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.transactionReference, transactionReference) || other.transactionReference == transactionReference)&&(identical(other.failureReason, failureReason) || other.failureReason == failureReason)&&const DeepCollectionEquality().equals(other._paymentMetadata, _paymentMetadata)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,listingId,amount,currency,status,createdAt,paidAt,failedAt,refundedAt,paymentMethodId,transactionReference,failureReason,const DeepCollectionEquality().hash(_paymentMetadata),updatedAt);

@override
String toString() {
  return 'ListingFeeTransaction(id: $id, sellerId: $sellerId, listingId: $listingId, amount: $amount, currency: $currency, status: $status, createdAt: $createdAt, paidAt: $paidAt, failedAt: $failedAt, refundedAt: $refundedAt, paymentMethodId: $paymentMethodId, transactionReference: $transactionReference, failureReason: $failureReason, paymentMetadata: $paymentMetadata, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$ListingFeeTransactionCopyWith<$Res> implements $ListingFeeTransactionCopyWith<$Res> {
  factory _$ListingFeeTransactionCopyWith(_ListingFeeTransaction value, $Res Function(_ListingFeeTransaction) _then) = __$ListingFeeTransactionCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String listingId, double amount, String currency, ListingPaymentStatus status, DateTime createdAt, DateTime? paidAt, DateTime? failedAt, DateTime? refundedAt, String? paymentMethodId, String? transactionReference, String? failureReason, Map<String, dynamic>? paymentMetadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$ListingFeeTransactionCopyWithImpl<$Res>
    implements _$ListingFeeTransactionCopyWith<$Res> {
  __$ListingFeeTransactionCopyWithImpl(this._self, this._then);

  final _ListingFeeTransaction _self;
  final $Res Function(_ListingFeeTransaction) _then;

/// Create a copy of ListingFeeTransaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? listingId = null,Object? amount = null,Object? currency = null,Object? status = null,Object? createdAt = null,Object? paidAt = freezed,Object? failedAt = freezed,Object? refundedAt = freezed,Object? paymentMethodId = freezed,Object? transactionReference = freezed,Object? failureReason = freezed,Object? paymentMetadata = freezed,Object? updatedAt = freezed,}) {
  return _then(_ListingFeeTransaction(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,listingId: null == listingId ? _self.listingId : listingId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ListingPaymentStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,failedAt: freezed == failedAt ? _self.failedAt : failedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,refundedAt: freezed == refundedAt ? _self.refundedAt : refundedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,transactionReference: freezed == transactionReference ? _self.transactionReference : transactionReference // ignore: cast_nullable_to_non_nullable
as String?,failureReason: freezed == failureReason ? _self.failureReason : failureReason // ignore: cast_nullable_to_non_nullable
as String?,paymentMetadata: freezed == paymentMetadata ? _self._paymentMetadata : paymentMetadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$MonthlyListingQuota {

 String get id; String get sellerId; SubscriptionTier get subscriptionTier; int get year; int get month; int get totalFreeListings; int get usedFreeListings; int get paidListings; double get totalFeesPaid; DateTime get periodStart; DateTime get periodEnd; List<String>? get freeListingIds; List<String>? get paidListingIds;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of MonthlyListingQuota
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MonthlyListingQuotaCopyWith<MonthlyListingQuota> get copyWith => _$MonthlyListingQuotaCopyWithImpl<MonthlyListingQuota>(this as MonthlyListingQuota, _$identity);

  /// Serializes this MonthlyListingQuota to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MonthlyListingQuota&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.subscriptionTier, subscriptionTier) || other.subscriptionTier == subscriptionTier)&&(identical(other.year, year) || other.year == year)&&(identical(other.month, month) || other.month == month)&&(identical(other.totalFreeListings, totalFreeListings) || other.totalFreeListings == totalFreeListings)&&(identical(other.usedFreeListings, usedFreeListings) || other.usedFreeListings == usedFreeListings)&&(identical(other.paidListings, paidListings) || other.paidListings == paidListings)&&(identical(other.totalFeesPaid, totalFeesPaid) || other.totalFeesPaid == totalFeesPaid)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.periodEnd, periodEnd) || other.periodEnd == periodEnd)&&const DeepCollectionEquality().equals(other.freeListingIds, freeListingIds)&&const DeepCollectionEquality().equals(other.paidListingIds, paidListingIds)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,subscriptionTier,year,month,totalFreeListings,usedFreeListings,paidListings,totalFeesPaid,periodStart,periodEnd,const DeepCollectionEquality().hash(freeListingIds),const DeepCollectionEquality().hash(paidListingIds),createdAt,updatedAt);

@override
String toString() {
  return 'MonthlyListingQuota(id: $id, sellerId: $sellerId, subscriptionTier: $subscriptionTier, year: $year, month: $month, totalFreeListings: $totalFreeListings, usedFreeListings: $usedFreeListings, paidListings: $paidListings, totalFeesPaid: $totalFeesPaid, periodStart: $periodStart, periodEnd: $periodEnd, freeListingIds: $freeListingIds, paidListingIds: $paidListingIds, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $MonthlyListingQuotaCopyWith<$Res>  {
  factory $MonthlyListingQuotaCopyWith(MonthlyListingQuota value, $Res Function(MonthlyListingQuota) _then) = _$MonthlyListingQuotaCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, SubscriptionTier subscriptionTier, int year, int month, int totalFreeListings, int usedFreeListings, int paidListings, double totalFeesPaid, DateTime periodStart, DateTime periodEnd, List<String>? freeListingIds, List<String>? paidListingIds,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$MonthlyListingQuotaCopyWithImpl<$Res>
    implements $MonthlyListingQuotaCopyWith<$Res> {
  _$MonthlyListingQuotaCopyWithImpl(this._self, this._then);

  final MonthlyListingQuota _self;
  final $Res Function(MonthlyListingQuota) _then;

/// Create a copy of MonthlyListingQuota
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? subscriptionTier = null,Object? year = null,Object? month = null,Object? totalFreeListings = null,Object? usedFreeListings = null,Object? paidListings = null,Object? totalFeesPaid = null,Object? periodStart = null,Object? periodEnd = null,Object? freeListingIds = freezed,Object? paidListingIds = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,subscriptionTier: null == subscriptionTier ? _self.subscriptionTier : subscriptionTier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,month: null == month ? _self.month : month // ignore: cast_nullable_to_non_nullable
as int,totalFreeListings: null == totalFreeListings ? _self.totalFreeListings : totalFreeListings // ignore: cast_nullable_to_non_nullable
as int,usedFreeListings: null == usedFreeListings ? _self.usedFreeListings : usedFreeListings // ignore: cast_nullable_to_non_nullable
as int,paidListings: null == paidListings ? _self.paidListings : paidListings // ignore: cast_nullable_to_non_nullable
as int,totalFeesPaid: null == totalFeesPaid ? _self.totalFeesPaid : totalFeesPaid // ignore: cast_nullable_to_non_nullable
as double,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,periodEnd: null == periodEnd ? _self.periodEnd : periodEnd // ignore: cast_nullable_to_non_nullable
as DateTime,freeListingIds: freezed == freeListingIds ? _self.freeListingIds : freeListingIds // ignore: cast_nullable_to_non_nullable
as List<String>?,paidListingIds: freezed == paidListingIds ? _self.paidListingIds : paidListingIds // ignore: cast_nullable_to_non_nullable
as List<String>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [MonthlyListingQuota].
extension MonthlyListingQuotaPatterns on MonthlyListingQuota {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MonthlyListingQuota value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MonthlyListingQuota() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MonthlyListingQuota value)  $default,){
final _that = this;
switch (_that) {
case _MonthlyListingQuota():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MonthlyListingQuota value)?  $default,){
final _that = this;
switch (_that) {
case _MonthlyListingQuota() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  SubscriptionTier subscriptionTier,  int year,  int month,  int totalFreeListings,  int usedFreeListings,  int paidListings,  double totalFeesPaid,  DateTime periodStart,  DateTime periodEnd,  List<String>? freeListingIds,  List<String>? paidListingIds, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MonthlyListingQuota() when $default != null:
return $default(_that.id,_that.sellerId,_that.subscriptionTier,_that.year,_that.month,_that.totalFreeListings,_that.usedFreeListings,_that.paidListings,_that.totalFeesPaid,_that.periodStart,_that.periodEnd,_that.freeListingIds,_that.paidListingIds,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  SubscriptionTier subscriptionTier,  int year,  int month,  int totalFreeListings,  int usedFreeListings,  int paidListings,  double totalFeesPaid,  DateTime periodStart,  DateTime periodEnd,  List<String>? freeListingIds,  List<String>? paidListingIds, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _MonthlyListingQuota():
return $default(_that.id,_that.sellerId,_that.subscriptionTier,_that.year,_that.month,_that.totalFreeListings,_that.usedFreeListings,_that.paidListings,_that.totalFeesPaid,_that.periodStart,_that.periodEnd,_that.freeListingIds,_that.paidListingIds,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  SubscriptionTier subscriptionTier,  int year,  int month,  int totalFreeListings,  int usedFreeListings,  int paidListings,  double totalFeesPaid,  DateTime periodStart,  DateTime periodEnd,  List<String>? freeListingIds,  List<String>? paidListingIds, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _MonthlyListingQuota() when $default != null:
return $default(_that.id,_that.sellerId,_that.subscriptionTier,_that.year,_that.month,_that.totalFreeListings,_that.usedFreeListings,_that.paidListings,_that.totalFeesPaid,_that.periodStart,_that.periodEnd,_that.freeListingIds,_that.paidListingIds,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _MonthlyListingQuota implements MonthlyListingQuota {
  const _MonthlyListingQuota({required this.id, required this.sellerId, required this.subscriptionTier, required this.year, required this.month, required this.totalFreeListings, this.usedFreeListings = 0, this.paidListings = 0, this.totalFeesPaid = 0.0, required this.periodStart, required this.periodEnd, final  List<String>? freeListingIds, final  List<String>? paidListingIds, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _freeListingIds = freeListingIds,_paidListingIds = paidListingIds;
  factory _MonthlyListingQuota.fromJson(Map<String, dynamic> json) => _$MonthlyListingQuotaFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  SubscriptionTier subscriptionTier;
@override final  int year;
@override final  int month;
@override final  int totalFreeListings;
@override@JsonKey() final  int usedFreeListings;
@override@JsonKey() final  int paidListings;
@override@JsonKey() final  double totalFeesPaid;
@override final  DateTime periodStart;
@override final  DateTime periodEnd;
 final  List<String>? _freeListingIds;
@override List<String>? get freeListingIds {
  final value = _freeListingIds;
  if (value == null) return null;
  if (_freeListingIds is EqualUnmodifiableListView) return _freeListingIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _paidListingIds;
@override List<String>? get paidListingIds {
  final value = _paidListingIds;
  if (value == null) return null;
  if (_paidListingIds is EqualUnmodifiableListView) return _paidListingIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of MonthlyListingQuota
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MonthlyListingQuotaCopyWith<_MonthlyListingQuota> get copyWith => __$MonthlyListingQuotaCopyWithImpl<_MonthlyListingQuota>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MonthlyListingQuotaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MonthlyListingQuota&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.subscriptionTier, subscriptionTier) || other.subscriptionTier == subscriptionTier)&&(identical(other.year, year) || other.year == year)&&(identical(other.month, month) || other.month == month)&&(identical(other.totalFreeListings, totalFreeListings) || other.totalFreeListings == totalFreeListings)&&(identical(other.usedFreeListings, usedFreeListings) || other.usedFreeListings == usedFreeListings)&&(identical(other.paidListings, paidListings) || other.paidListings == paidListings)&&(identical(other.totalFeesPaid, totalFeesPaid) || other.totalFeesPaid == totalFeesPaid)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.periodEnd, periodEnd) || other.periodEnd == periodEnd)&&const DeepCollectionEquality().equals(other._freeListingIds, _freeListingIds)&&const DeepCollectionEquality().equals(other._paidListingIds, _paidListingIds)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,subscriptionTier,year,month,totalFreeListings,usedFreeListings,paidListings,totalFeesPaid,periodStart,periodEnd,const DeepCollectionEquality().hash(_freeListingIds),const DeepCollectionEquality().hash(_paidListingIds),createdAt,updatedAt);

@override
String toString() {
  return 'MonthlyListingQuota(id: $id, sellerId: $sellerId, subscriptionTier: $subscriptionTier, year: $year, month: $month, totalFreeListings: $totalFreeListings, usedFreeListings: $usedFreeListings, paidListings: $paidListings, totalFeesPaid: $totalFeesPaid, periodStart: $periodStart, periodEnd: $periodEnd, freeListingIds: $freeListingIds, paidListingIds: $paidListingIds, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$MonthlyListingQuotaCopyWith<$Res> implements $MonthlyListingQuotaCopyWith<$Res> {
  factory _$MonthlyListingQuotaCopyWith(_MonthlyListingQuota value, $Res Function(_MonthlyListingQuota) _then) = __$MonthlyListingQuotaCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, SubscriptionTier subscriptionTier, int year, int month, int totalFreeListings, int usedFreeListings, int paidListings, double totalFeesPaid, DateTime periodStart, DateTime periodEnd, List<String>? freeListingIds, List<String>? paidListingIds,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$MonthlyListingQuotaCopyWithImpl<$Res>
    implements _$MonthlyListingQuotaCopyWith<$Res> {
  __$MonthlyListingQuotaCopyWithImpl(this._self, this._then);

  final _MonthlyListingQuota _self;
  final $Res Function(_MonthlyListingQuota) _then;

/// Create a copy of MonthlyListingQuota
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? subscriptionTier = null,Object? year = null,Object? month = null,Object? totalFreeListings = null,Object? usedFreeListings = null,Object? paidListings = null,Object? totalFeesPaid = null,Object? periodStart = null,Object? periodEnd = null,Object? freeListingIds = freezed,Object? paidListingIds = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_MonthlyListingQuota(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,subscriptionTier: null == subscriptionTier ? _self.subscriptionTier : subscriptionTier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,month: null == month ? _self.month : month // ignore: cast_nullable_to_non_nullable
as int,totalFreeListings: null == totalFreeListings ? _self.totalFreeListings : totalFreeListings // ignore: cast_nullable_to_non_nullable
as int,usedFreeListings: null == usedFreeListings ? _self.usedFreeListings : usedFreeListings // ignore: cast_nullable_to_non_nullable
as int,paidListings: null == paidListings ? _self.paidListings : paidListings // ignore: cast_nullable_to_non_nullable
as int,totalFeesPaid: null == totalFeesPaid ? _self.totalFeesPaid : totalFeesPaid // ignore: cast_nullable_to_non_nullable
as double,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,periodEnd: null == periodEnd ? _self.periodEnd : periodEnd // ignore: cast_nullable_to_non_nullable
as DateTime,freeListingIds: freezed == freeListingIds ? _self._freeListingIds : freeListingIds // ignore: cast_nullable_to_non_nullable
as List<String>?,paidListingIds: freezed == paidListingIds ? _self._paidListingIds : paidListingIds // ignore: cast_nullable_to_non_nullable
as List<String>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$ListingRenewalRequest {

 String get id; String get sellerId; String get listingId; DateTime get requestedAt; int get renewalDays; bool get useFreeQuota; double? get renewalFee; ListingPaymentStatus? get paymentStatus; String? get paymentTransactionId; DateTime? get processedAt; String? get rejectionReason;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of ListingRenewalRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ListingRenewalRequestCopyWith<ListingRenewalRequest> get copyWith => _$ListingRenewalRequestCopyWithImpl<ListingRenewalRequest>(this as ListingRenewalRequest, _$identity);

  /// Serializes this ListingRenewalRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ListingRenewalRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.listingId, listingId) || other.listingId == listingId)&&(identical(other.requestedAt, requestedAt) || other.requestedAt == requestedAt)&&(identical(other.renewalDays, renewalDays) || other.renewalDays == renewalDays)&&(identical(other.useFreeQuota, useFreeQuota) || other.useFreeQuota == useFreeQuota)&&(identical(other.renewalFee, renewalFee) || other.renewalFee == renewalFee)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.paymentTransactionId, paymentTransactionId) || other.paymentTransactionId == paymentTransactionId)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,listingId,requestedAt,renewalDays,useFreeQuota,renewalFee,paymentStatus,paymentTransactionId,processedAt,rejectionReason,createdAt,updatedAt);

@override
String toString() {
  return 'ListingRenewalRequest(id: $id, sellerId: $sellerId, listingId: $listingId, requestedAt: $requestedAt, renewalDays: $renewalDays, useFreeQuota: $useFreeQuota, renewalFee: $renewalFee, paymentStatus: $paymentStatus, paymentTransactionId: $paymentTransactionId, processedAt: $processedAt, rejectionReason: $rejectionReason, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $ListingRenewalRequestCopyWith<$Res>  {
  factory $ListingRenewalRequestCopyWith(ListingRenewalRequest value, $Res Function(ListingRenewalRequest) _then) = _$ListingRenewalRequestCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String listingId, DateTime requestedAt, int renewalDays, bool useFreeQuota, double? renewalFee, ListingPaymentStatus? paymentStatus, String? paymentTransactionId, DateTime? processedAt, String? rejectionReason,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$ListingRenewalRequestCopyWithImpl<$Res>
    implements $ListingRenewalRequestCopyWith<$Res> {
  _$ListingRenewalRequestCopyWithImpl(this._self, this._then);

  final ListingRenewalRequest _self;
  final $Res Function(ListingRenewalRequest) _then;

/// Create a copy of ListingRenewalRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? listingId = null,Object? requestedAt = null,Object? renewalDays = null,Object? useFreeQuota = null,Object? renewalFee = freezed,Object? paymentStatus = freezed,Object? paymentTransactionId = freezed,Object? processedAt = freezed,Object? rejectionReason = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,listingId: null == listingId ? _self.listingId : listingId // ignore: cast_nullable_to_non_nullable
as String,requestedAt: null == requestedAt ? _self.requestedAt : requestedAt // ignore: cast_nullable_to_non_nullable
as DateTime,renewalDays: null == renewalDays ? _self.renewalDays : renewalDays // ignore: cast_nullable_to_non_nullable
as int,useFreeQuota: null == useFreeQuota ? _self.useFreeQuota : useFreeQuota // ignore: cast_nullable_to_non_nullable
as bool,renewalFee: freezed == renewalFee ? _self.renewalFee : renewalFee // ignore: cast_nullable_to_non_nullable
as double?,paymentStatus: freezed == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as ListingPaymentStatus?,paymentTransactionId: freezed == paymentTransactionId ? _self.paymentTransactionId : paymentTransactionId // ignore: cast_nullable_to_non_nullable
as String?,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [ListingRenewalRequest].
extension ListingRenewalRequestPatterns on ListingRenewalRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ListingRenewalRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ListingRenewalRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ListingRenewalRequest value)  $default,){
final _that = this;
switch (_that) {
case _ListingRenewalRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ListingRenewalRequest value)?  $default,){
final _that = this;
switch (_that) {
case _ListingRenewalRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String listingId,  DateTime requestedAt,  int renewalDays,  bool useFreeQuota,  double? renewalFee,  ListingPaymentStatus? paymentStatus,  String? paymentTransactionId,  DateTime? processedAt,  String? rejectionReason, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ListingRenewalRequest() when $default != null:
return $default(_that.id,_that.sellerId,_that.listingId,_that.requestedAt,_that.renewalDays,_that.useFreeQuota,_that.renewalFee,_that.paymentStatus,_that.paymentTransactionId,_that.processedAt,_that.rejectionReason,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String listingId,  DateTime requestedAt,  int renewalDays,  bool useFreeQuota,  double? renewalFee,  ListingPaymentStatus? paymentStatus,  String? paymentTransactionId,  DateTime? processedAt,  String? rejectionReason, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _ListingRenewalRequest():
return $default(_that.id,_that.sellerId,_that.listingId,_that.requestedAt,_that.renewalDays,_that.useFreeQuota,_that.renewalFee,_that.paymentStatus,_that.paymentTransactionId,_that.processedAt,_that.rejectionReason,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String listingId,  DateTime requestedAt,  int renewalDays,  bool useFreeQuota,  double? renewalFee,  ListingPaymentStatus? paymentStatus,  String? paymentTransactionId,  DateTime? processedAt,  String? rejectionReason, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _ListingRenewalRequest() when $default != null:
return $default(_that.id,_that.sellerId,_that.listingId,_that.requestedAt,_that.renewalDays,_that.useFreeQuota,_that.renewalFee,_that.paymentStatus,_that.paymentTransactionId,_that.processedAt,_that.rejectionReason,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _ListingRenewalRequest implements ListingRenewalRequest {
  const _ListingRenewalRequest({required this.id, required this.sellerId, required this.listingId, required this.requestedAt, required this.renewalDays, required this.useFreeQuota, this.renewalFee, this.paymentStatus, this.paymentTransactionId, this.processedAt, this.rejectionReason, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _ListingRenewalRequest.fromJson(Map<String, dynamic> json) => _$ListingRenewalRequestFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String listingId;
@override final  DateTime requestedAt;
@override final  int renewalDays;
@override final  bool useFreeQuota;
@override final  double? renewalFee;
@override final  ListingPaymentStatus? paymentStatus;
@override final  String? paymentTransactionId;
@override final  DateTime? processedAt;
@override final  String? rejectionReason;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of ListingRenewalRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ListingRenewalRequestCopyWith<_ListingRenewalRequest> get copyWith => __$ListingRenewalRequestCopyWithImpl<_ListingRenewalRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ListingRenewalRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ListingRenewalRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.listingId, listingId) || other.listingId == listingId)&&(identical(other.requestedAt, requestedAt) || other.requestedAt == requestedAt)&&(identical(other.renewalDays, renewalDays) || other.renewalDays == renewalDays)&&(identical(other.useFreeQuota, useFreeQuota) || other.useFreeQuota == useFreeQuota)&&(identical(other.renewalFee, renewalFee) || other.renewalFee == renewalFee)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.paymentTransactionId, paymentTransactionId) || other.paymentTransactionId == paymentTransactionId)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,listingId,requestedAt,renewalDays,useFreeQuota,renewalFee,paymentStatus,paymentTransactionId,processedAt,rejectionReason,createdAt,updatedAt);

@override
String toString() {
  return 'ListingRenewalRequest(id: $id, sellerId: $sellerId, listingId: $listingId, requestedAt: $requestedAt, renewalDays: $renewalDays, useFreeQuota: $useFreeQuota, renewalFee: $renewalFee, paymentStatus: $paymentStatus, paymentTransactionId: $paymentTransactionId, processedAt: $processedAt, rejectionReason: $rejectionReason, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$ListingRenewalRequestCopyWith<$Res> implements $ListingRenewalRequestCopyWith<$Res> {
  factory _$ListingRenewalRequestCopyWith(_ListingRenewalRequest value, $Res Function(_ListingRenewalRequest) _then) = __$ListingRenewalRequestCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String listingId, DateTime requestedAt, int renewalDays, bool useFreeQuota, double? renewalFee, ListingPaymentStatus? paymentStatus, String? paymentTransactionId, DateTime? processedAt, String? rejectionReason,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$ListingRenewalRequestCopyWithImpl<$Res>
    implements _$ListingRenewalRequestCopyWith<$Res> {
  __$ListingRenewalRequestCopyWithImpl(this._self, this._then);

  final _ListingRenewalRequest _self;
  final $Res Function(_ListingRenewalRequest) _then;

/// Create a copy of ListingRenewalRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? listingId = null,Object? requestedAt = null,Object? renewalDays = null,Object? useFreeQuota = null,Object? renewalFee = freezed,Object? paymentStatus = freezed,Object? paymentTransactionId = freezed,Object? processedAt = freezed,Object? rejectionReason = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_ListingRenewalRequest(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,listingId: null == listingId ? _self.listingId : listingId // ignore: cast_nullable_to_non_nullable
as String,requestedAt: null == requestedAt ? _self.requestedAt : requestedAt // ignore: cast_nullable_to_non_nullable
as DateTime,renewalDays: null == renewalDays ? _self.renewalDays : renewalDays // ignore: cast_nullable_to_non_nullable
as int,useFreeQuota: null == useFreeQuota ? _self.useFreeQuota : useFreeQuota // ignore: cast_nullable_to_non_nullable
as bool,renewalFee: freezed == renewalFee ? _self.renewalFee : renewalFee // ignore: cast_nullable_to_non_nullable
as double?,paymentStatus: freezed == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as ListingPaymentStatus?,paymentTransactionId: freezed == paymentTransactionId ? _self.paymentTransactionId : paymentTransactionId // ignore: cast_nullable_to_non_nullable
as String?,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$BulkListingOperation {

 String get id; String get sellerId; List<String> get productIds; DateTime get initiatedAt; SubscriptionTier get subscriptionTier; int get successfulListings; int get failedListings; int get usedFreeQuota; int get paidListings; double get totalFees; Map<String, String>? get failures;// productId -> reason
 List<String>? get successfulListingIds; DateTime? get completedAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of BulkListingOperation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BulkListingOperationCopyWith<BulkListingOperation> get copyWith => _$BulkListingOperationCopyWithImpl<BulkListingOperation>(this as BulkListingOperation, _$identity);

  /// Serializes this BulkListingOperation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BulkListingOperation&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&const DeepCollectionEquality().equals(other.productIds, productIds)&&(identical(other.initiatedAt, initiatedAt) || other.initiatedAt == initiatedAt)&&(identical(other.subscriptionTier, subscriptionTier) || other.subscriptionTier == subscriptionTier)&&(identical(other.successfulListings, successfulListings) || other.successfulListings == successfulListings)&&(identical(other.failedListings, failedListings) || other.failedListings == failedListings)&&(identical(other.usedFreeQuota, usedFreeQuota) || other.usedFreeQuota == usedFreeQuota)&&(identical(other.paidListings, paidListings) || other.paidListings == paidListings)&&(identical(other.totalFees, totalFees) || other.totalFees == totalFees)&&const DeepCollectionEquality().equals(other.failures, failures)&&const DeepCollectionEquality().equals(other.successfulListingIds, successfulListingIds)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,const DeepCollectionEquality().hash(productIds),initiatedAt,subscriptionTier,successfulListings,failedListings,usedFreeQuota,paidListings,totalFees,const DeepCollectionEquality().hash(failures),const DeepCollectionEquality().hash(successfulListingIds),completedAt,createdAt,updatedAt);

@override
String toString() {
  return 'BulkListingOperation(id: $id, sellerId: $sellerId, productIds: $productIds, initiatedAt: $initiatedAt, subscriptionTier: $subscriptionTier, successfulListings: $successfulListings, failedListings: $failedListings, usedFreeQuota: $usedFreeQuota, paidListings: $paidListings, totalFees: $totalFees, failures: $failures, successfulListingIds: $successfulListingIds, completedAt: $completedAt, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $BulkListingOperationCopyWith<$Res>  {
  factory $BulkListingOperationCopyWith(BulkListingOperation value, $Res Function(BulkListingOperation) _then) = _$BulkListingOperationCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, List<String> productIds, DateTime initiatedAt, SubscriptionTier subscriptionTier, int successfulListings, int failedListings, int usedFreeQuota, int paidListings, double totalFees, Map<String, String>? failures, List<String>? successfulListingIds, DateTime? completedAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$BulkListingOperationCopyWithImpl<$Res>
    implements $BulkListingOperationCopyWith<$Res> {
  _$BulkListingOperationCopyWithImpl(this._self, this._then);

  final BulkListingOperation _self;
  final $Res Function(BulkListingOperation) _then;

/// Create a copy of BulkListingOperation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? productIds = null,Object? initiatedAt = null,Object? subscriptionTier = null,Object? successfulListings = null,Object? failedListings = null,Object? usedFreeQuota = null,Object? paidListings = null,Object? totalFees = null,Object? failures = freezed,Object? successfulListingIds = freezed,Object? completedAt = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,productIds: null == productIds ? _self.productIds : productIds // ignore: cast_nullable_to_non_nullable
as List<String>,initiatedAt: null == initiatedAt ? _self.initiatedAt : initiatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,subscriptionTier: null == subscriptionTier ? _self.subscriptionTier : subscriptionTier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier,successfulListings: null == successfulListings ? _self.successfulListings : successfulListings // ignore: cast_nullable_to_non_nullable
as int,failedListings: null == failedListings ? _self.failedListings : failedListings // ignore: cast_nullable_to_non_nullable
as int,usedFreeQuota: null == usedFreeQuota ? _self.usedFreeQuota : usedFreeQuota // ignore: cast_nullable_to_non_nullable
as int,paidListings: null == paidListings ? _self.paidListings : paidListings // ignore: cast_nullable_to_non_nullable
as int,totalFees: null == totalFees ? _self.totalFees : totalFees // ignore: cast_nullable_to_non_nullable
as double,failures: freezed == failures ? _self.failures : failures // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,successfulListingIds: freezed == successfulListingIds ? _self.successfulListingIds : successfulListingIds // ignore: cast_nullable_to_non_nullable
as List<String>?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [BulkListingOperation].
extension BulkListingOperationPatterns on BulkListingOperation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BulkListingOperation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BulkListingOperation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BulkListingOperation value)  $default,){
final _that = this;
switch (_that) {
case _BulkListingOperation():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BulkListingOperation value)?  $default,){
final _that = this;
switch (_that) {
case _BulkListingOperation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  List<String> productIds,  DateTime initiatedAt,  SubscriptionTier subscriptionTier,  int successfulListings,  int failedListings,  int usedFreeQuota,  int paidListings,  double totalFees,  Map<String, String>? failures,  List<String>? successfulListingIds,  DateTime? completedAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BulkListingOperation() when $default != null:
return $default(_that.id,_that.sellerId,_that.productIds,_that.initiatedAt,_that.subscriptionTier,_that.successfulListings,_that.failedListings,_that.usedFreeQuota,_that.paidListings,_that.totalFees,_that.failures,_that.successfulListingIds,_that.completedAt,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  List<String> productIds,  DateTime initiatedAt,  SubscriptionTier subscriptionTier,  int successfulListings,  int failedListings,  int usedFreeQuota,  int paidListings,  double totalFees,  Map<String, String>? failures,  List<String>? successfulListingIds,  DateTime? completedAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _BulkListingOperation():
return $default(_that.id,_that.sellerId,_that.productIds,_that.initiatedAt,_that.subscriptionTier,_that.successfulListings,_that.failedListings,_that.usedFreeQuota,_that.paidListings,_that.totalFees,_that.failures,_that.successfulListingIds,_that.completedAt,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  List<String> productIds,  DateTime initiatedAt,  SubscriptionTier subscriptionTier,  int successfulListings,  int failedListings,  int usedFreeQuota,  int paidListings,  double totalFees,  Map<String, String>? failures,  List<String>? successfulListingIds,  DateTime? completedAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _BulkListingOperation() when $default != null:
return $default(_that.id,_that.sellerId,_that.productIds,_that.initiatedAt,_that.subscriptionTier,_that.successfulListings,_that.failedListings,_that.usedFreeQuota,_that.paidListings,_that.totalFees,_that.failures,_that.successfulListingIds,_that.completedAt,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _BulkListingOperation implements BulkListingOperation {
  const _BulkListingOperation({required this.id, required this.sellerId, required final  List<String> productIds, required this.initiatedAt, required this.subscriptionTier, this.successfulListings = 0, this.failedListings = 0, this.usedFreeQuota = 0, this.paidListings = 0, this.totalFees = 0.0, final  Map<String, String>? failures, final  List<String>? successfulListingIds, this.completedAt, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _productIds = productIds,_failures = failures,_successfulListingIds = successfulListingIds;
  factory _BulkListingOperation.fromJson(Map<String, dynamic> json) => _$BulkListingOperationFromJson(json);

@override final  String id;
@override final  String sellerId;
 final  List<String> _productIds;
@override List<String> get productIds {
  if (_productIds is EqualUnmodifiableListView) return _productIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_productIds);
}

@override final  DateTime initiatedAt;
@override final  SubscriptionTier subscriptionTier;
@override@JsonKey() final  int successfulListings;
@override@JsonKey() final  int failedListings;
@override@JsonKey() final  int usedFreeQuota;
@override@JsonKey() final  int paidListings;
@override@JsonKey() final  double totalFees;
 final  Map<String, String>? _failures;
@override Map<String, String>? get failures {
  final value = _failures;
  if (value == null) return null;
  if (_failures is EqualUnmodifiableMapView) return _failures;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

// productId -> reason
 final  List<String>? _successfulListingIds;
// productId -> reason
@override List<String>? get successfulListingIds {
  final value = _successfulListingIds;
  if (value == null) return null;
  if (_successfulListingIds is EqualUnmodifiableListView) return _successfulListingIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  DateTime? completedAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of BulkListingOperation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BulkListingOperationCopyWith<_BulkListingOperation> get copyWith => __$BulkListingOperationCopyWithImpl<_BulkListingOperation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BulkListingOperationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BulkListingOperation&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&const DeepCollectionEquality().equals(other._productIds, _productIds)&&(identical(other.initiatedAt, initiatedAt) || other.initiatedAt == initiatedAt)&&(identical(other.subscriptionTier, subscriptionTier) || other.subscriptionTier == subscriptionTier)&&(identical(other.successfulListings, successfulListings) || other.successfulListings == successfulListings)&&(identical(other.failedListings, failedListings) || other.failedListings == failedListings)&&(identical(other.usedFreeQuota, usedFreeQuota) || other.usedFreeQuota == usedFreeQuota)&&(identical(other.paidListings, paidListings) || other.paidListings == paidListings)&&(identical(other.totalFees, totalFees) || other.totalFees == totalFees)&&const DeepCollectionEquality().equals(other._failures, _failures)&&const DeepCollectionEquality().equals(other._successfulListingIds, _successfulListingIds)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,const DeepCollectionEquality().hash(_productIds),initiatedAt,subscriptionTier,successfulListings,failedListings,usedFreeQuota,paidListings,totalFees,const DeepCollectionEquality().hash(_failures),const DeepCollectionEquality().hash(_successfulListingIds),completedAt,createdAt,updatedAt);

@override
String toString() {
  return 'BulkListingOperation(id: $id, sellerId: $sellerId, productIds: $productIds, initiatedAt: $initiatedAt, subscriptionTier: $subscriptionTier, successfulListings: $successfulListings, failedListings: $failedListings, usedFreeQuota: $usedFreeQuota, paidListings: $paidListings, totalFees: $totalFees, failures: $failures, successfulListingIds: $successfulListingIds, completedAt: $completedAt, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$BulkListingOperationCopyWith<$Res> implements $BulkListingOperationCopyWith<$Res> {
  factory _$BulkListingOperationCopyWith(_BulkListingOperation value, $Res Function(_BulkListingOperation) _then) = __$BulkListingOperationCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, List<String> productIds, DateTime initiatedAt, SubscriptionTier subscriptionTier, int successfulListings, int failedListings, int usedFreeQuota, int paidListings, double totalFees, Map<String, String>? failures, List<String>? successfulListingIds, DateTime? completedAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$BulkListingOperationCopyWithImpl<$Res>
    implements _$BulkListingOperationCopyWith<$Res> {
  __$BulkListingOperationCopyWithImpl(this._self, this._then);

  final _BulkListingOperation _self;
  final $Res Function(_BulkListingOperation) _then;

/// Create a copy of BulkListingOperation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? productIds = null,Object? initiatedAt = null,Object? subscriptionTier = null,Object? successfulListings = null,Object? failedListings = null,Object? usedFreeQuota = null,Object? paidListings = null,Object? totalFees = null,Object? failures = freezed,Object? successfulListingIds = freezed,Object? completedAt = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_BulkListingOperation(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,productIds: null == productIds ? _self._productIds : productIds // ignore: cast_nullable_to_non_nullable
as List<String>,initiatedAt: null == initiatedAt ? _self.initiatedAt : initiatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,subscriptionTier: null == subscriptionTier ? _self.subscriptionTier : subscriptionTier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier,successfulListings: null == successfulListings ? _self.successfulListings : successfulListings // ignore: cast_nullable_to_non_nullable
as int,failedListings: null == failedListings ? _self.failedListings : failedListings // ignore: cast_nullable_to_non_nullable
as int,usedFreeQuota: null == usedFreeQuota ? _self.usedFreeQuota : usedFreeQuota // ignore: cast_nullable_to_non_nullable
as int,paidListings: null == paidListings ? _self.paidListings : paidListings // ignore: cast_nullable_to_non_nullable
as int,totalFees: null == totalFees ? _self.totalFees : totalFees // ignore: cast_nullable_to_non_nullable
as double,failures: freezed == failures ? _self._failures : failures // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,successfulListingIds: freezed == successfulListingIds ? _self._successfulListingIds : successfulListingIds // ignore: cast_nullable_to_non_nullable
as List<String>?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
