// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'inventory_management_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerInventoryItem {

/// المعرف الفريد لعنصر المخزون.
 String get id;/// معرّف البائع.
 String get sellerId;/// معرّف المنتج.
 String get productId;/// وحدة حفظ المخزون (SKU).
 String get sku;/// اسم المنتج.
 String get name;/// الكمية الحالية في المخزون.
 int get currentStock;/// الكمية المحجوزة للطلبات.
 int get reservedStock;/// الكمية المتاحة للبيع (الحالية - المحجوزة).
 int get availableStock;/// أدنى مستوى للمخزون قبل التنبيه.
 int get minStockLevel;/// أقصى مستوى للمخزون يمكن تخزينه.
 int get maxStockLevel;/// نقطة إعادة الطلب (عند أي مستوى يتم إعادة الطلب).
 int get reorderPoint;/// الكمية التي يتم إعادة طلبها.
 int get reorderQuantity;/// سعر تكلفة المنتج.
 double? get costPrice;/// سعر بيع المنتج.
 double? get sellingPrice;/// موقع المنتج في المستودع.
 String? get location;/// المورد الذي تم شراء المنتج منه.
 String? get supplier;/// تاريخ آخر عملية إعادة تخزين.
 DateTime? get lastRestocked;/// تاريخ انتهاء صلاحية المنتج.
 DateTime? get expiryDate;/// حالة المخزون الحالية.
 InventoryStatus get status;/// قائمة بحركات المخزون لهذا العنصر.
 List<StockMovement>? get movements;/// بيانات وصفية إضافية.
 Map<String, dynamic>? get metadata;/// تاريخ إنشاء سجل المخزون.
 DateTime? get createdAt;/// تاريخ آخر تحديث لسجل المخزون.
 DateTime? get updatedAt;
/// Create a copy of SellerInventoryItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerInventoryItemCopyWith<SellerInventoryItem> get copyWith => _$SellerInventoryItemCopyWithImpl<SellerInventoryItem>(this as SellerInventoryItem, _$identity);

  /// Serializes this SellerInventoryItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerInventoryItem&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.name, name) || other.name == name)&&(identical(other.currentStock, currentStock) || other.currentStock == currentStock)&&(identical(other.reservedStock, reservedStock) || other.reservedStock == reservedStock)&&(identical(other.availableStock, availableStock) || other.availableStock == availableStock)&&(identical(other.minStockLevel, minStockLevel) || other.minStockLevel == minStockLevel)&&(identical(other.maxStockLevel, maxStockLevel) || other.maxStockLevel == maxStockLevel)&&(identical(other.reorderPoint, reorderPoint) || other.reorderPoint == reorderPoint)&&(identical(other.reorderQuantity, reorderQuantity) || other.reorderQuantity == reorderQuantity)&&(identical(other.costPrice, costPrice) || other.costPrice == costPrice)&&(identical(other.sellingPrice, sellingPrice) || other.sellingPrice == sellingPrice)&&(identical(other.location, location) || other.location == location)&&(identical(other.supplier, supplier) || other.supplier == supplier)&&(identical(other.lastRestocked, lastRestocked) || other.lastRestocked == lastRestocked)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.movements, movements)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,productId,sku,name,currentStock,reservedStock,availableStock,minStockLevel,maxStockLevel,reorderPoint,reorderQuantity,costPrice,sellingPrice,location,supplier,lastRestocked,expiryDate,status,const DeepCollectionEquality().hash(movements),const DeepCollectionEquality().hash(metadata),createdAt,updatedAt]);

@override
String toString() {
  return 'SellerInventoryItem(id: $id, sellerId: $sellerId, productId: $productId, sku: $sku, name: $name, currentStock: $currentStock, reservedStock: $reservedStock, availableStock: $availableStock, minStockLevel: $minStockLevel, maxStockLevel: $maxStockLevel, reorderPoint: $reorderPoint, reorderQuantity: $reorderQuantity, costPrice: $costPrice, sellingPrice: $sellingPrice, location: $location, supplier: $supplier, lastRestocked: $lastRestocked, expiryDate: $expiryDate, status: $status, movements: $movements, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SellerInventoryItemCopyWith<$Res>  {
  factory $SellerInventoryItemCopyWith(SellerInventoryItem value, $Res Function(SellerInventoryItem) _then) = _$SellerInventoryItemCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String productId, String sku, String name, int currentStock, int reservedStock, int availableStock, int minStockLevel, int maxStockLevel, int reorderPoint, int reorderQuantity, double? costPrice, double? sellingPrice, String? location, String? supplier, DateTime? lastRestocked, DateTime? expiryDate, InventoryStatus status, List<StockMovement>? movements, Map<String, dynamic>? metadata, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$SellerInventoryItemCopyWithImpl<$Res>
    implements $SellerInventoryItemCopyWith<$Res> {
  _$SellerInventoryItemCopyWithImpl(this._self, this._then);

  final SellerInventoryItem _self;
  final $Res Function(SellerInventoryItem) _then;

/// Create a copy of SellerInventoryItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? productId = null,Object? sku = null,Object? name = null,Object? currentStock = null,Object? reservedStock = null,Object? availableStock = null,Object? minStockLevel = null,Object? maxStockLevel = null,Object? reorderPoint = null,Object? reorderQuantity = null,Object? costPrice = freezed,Object? sellingPrice = freezed,Object? location = freezed,Object? supplier = freezed,Object? lastRestocked = freezed,Object? expiryDate = freezed,Object? status = null,Object? movements = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,sku: null == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,currentStock: null == currentStock ? _self.currentStock : currentStock // ignore: cast_nullable_to_non_nullable
as int,reservedStock: null == reservedStock ? _self.reservedStock : reservedStock // ignore: cast_nullable_to_non_nullable
as int,availableStock: null == availableStock ? _self.availableStock : availableStock // ignore: cast_nullable_to_non_nullable
as int,minStockLevel: null == minStockLevel ? _self.minStockLevel : minStockLevel // ignore: cast_nullable_to_non_nullable
as int,maxStockLevel: null == maxStockLevel ? _self.maxStockLevel : maxStockLevel // ignore: cast_nullable_to_non_nullable
as int,reorderPoint: null == reorderPoint ? _self.reorderPoint : reorderPoint // ignore: cast_nullable_to_non_nullable
as int,reorderQuantity: null == reorderQuantity ? _self.reorderQuantity : reorderQuantity // ignore: cast_nullable_to_non_nullable
as int,costPrice: freezed == costPrice ? _self.costPrice : costPrice // ignore: cast_nullable_to_non_nullable
as double?,sellingPrice: freezed == sellingPrice ? _self.sellingPrice : sellingPrice // ignore: cast_nullable_to_non_nullable
as double?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,supplier: freezed == supplier ? _self.supplier : supplier // ignore: cast_nullable_to_non_nullable
as String?,lastRestocked: freezed == lastRestocked ? _self.lastRestocked : lastRestocked // ignore: cast_nullable_to_non_nullable
as DateTime?,expiryDate: freezed == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InventoryStatus,movements: freezed == movements ? _self.movements : movements // ignore: cast_nullable_to_non_nullable
as List<StockMovement>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerInventoryItem].
extension SellerInventoryItemPatterns on SellerInventoryItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerInventoryItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerInventoryItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerInventoryItem value)  $default,){
final _that = this;
switch (_that) {
case _SellerInventoryItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerInventoryItem value)?  $default,){
final _that = this;
switch (_that) {
case _SellerInventoryItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String productId,  String sku,  String name,  int currentStock,  int reservedStock,  int availableStock,  int minStockLevel,  int maxStockLevel,  int reorderPoint,  int reorderQuantity,  double? costPrice,  double? sellingPrice,  String? location,  String? supplier,  DateTime? lastRestocked,  DateTime? expiryDate,  InventoryStatus status,  List<StockMovement>? movements,  Map<String, dynamic>? metadata,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerInventoryItem() when $default != null:
return $default(_that.id,_that.sellerId,_that.productId,_that.sku,_that.name,_that.currentStock,_that.reservedStock,_that.availableStock,_that.minStockLevel,_that.maxStockLevel,_that.reorderPoint,_that.reorderQuantity,_that.costPrice,_that.sellingPrice,_that.location,_that.supplier,_that.lastRestocked,_that.expiryDate,_that.status,_that.movements,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String productId,  String sku,  String name,  int currentStock,  int reservedStock,  int availableStock,  int minStockLevel,  int maxStockLevel,  int reorderPoint,  int reorderQuantity,  double? costPrice,  double? sellingPrice,  String? location,  String? supplier,  DateTime? lastRestocked,  DateTime? expiryDate,  InventoryStatus status,  List<StockMovement>? movements,  Map<String, dynamic>? metadata,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SellerInventoryItem():
return $default(_that.id,_that.sellerId,_that.productId,_that.sku,_that.name,_that.currentStock,_that.reservedStock,_that.availableStock,_that.minStockLevel,_that.maxStockLevel,_that.reorderPoint,_that.reorderQuantity,_that.costPrice,_that.sellingPrice,_that.location,_that.supplier,_that.lastRestocked,_that.expiryDate,_that.status,_that.movements,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String productId,  String sku,  String name,  int currentStock,  int reservedStock,  int availableStock,  int minStockLevel,  int maxStockLevel,  int reorderPoint,  int reorderQuantity,  double? costPrice,  double? sellingPrice,  String? location,  String? supplier,  DateTime? lastRestocked,  DateTime? expiryDate,  InventoryStatus status,  List<StockMovement>? movements,  Map<String, dynamic>? metadata,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerInventoryItem() when $default != null:
return $default(_that.id,_that.sellerId,_that.productId,_that.sku,_that.name,_that.currentStock,_that.reservedStock,_that.availableStock,_that.minStockLevel,_that.maxStockLevel,_that.reorderPoint,_that.reorderQuantity,_that.costPrice,_that.sellingPrice,_that.location,_that.supplier,_that.lastRestocked,_that.expiryDate,_that.status,_that.movements,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SellerInventoryItem implements SellerInventoryItem {
  const _SellerInventoryItem({required this.id, required this.sellerId, required this.productId, required this.sku, required this.name, this.currentStock = 0, this.reservedStock = 0, this.availableStock = 0, this.minStockLevel = 0, this.maxStockLevel = 0, this.reorderPoint = 0, this.reorderQuantity = 0, this.costPrice, this.sellingPrice, this.location, this.supplier, this.lastRestocked, this.expiryDate, this.status = InventoryStatus.inStock, final  List<StockMovement>? movements, final  Map<String, dynamic>? metadata, this.createdAt, this.updatedAt}): _movements = movements,_metadata = metadata;
  factory _SellerInventoryItem.fromJson(Map<String, dynamic> json) => _$SellerInventoryItemFromJson(json);

/// المعرف الفريد لعنصر المخزون.
@override final  String id;
/// معرّف البائع.
@override final  String sellerId;
/// معرّف المنتج.
@override final  String productId;
/// وحدة حفظ المخزون (SKU).
@override final  String sku;
/// اسم المنتج.
@override final  String name;
/// الكمية الحالية في المخزون.
@override@JsonKey() final  int currentStock;
/// الكمية المحجوزة للطلبات.
@override@JsonKey() final  int reservedStock;
/// الكمية المتاحة للبيع (الحالية - المحجوزة).
@override@JsonKey() final  int availableStock;
/// أدنى مستوى للمخزون قبل التنبيه.
@override@JsonKey() final  int minStockLevel;
/// أقصى مستوى للمخزون يمكن تخزينه.
@override@JsonKey() final  int maxStockLevel;
/// نقطة إعادة الطلب (عند أي مستوى يتم إعادة الطلب).
@override@JsonKey() final  int reorderPoint;
/// الكمية التي يتم إعادة طلبها.
@override@JsonKey() final  int reorderQuantity;
/// سعر تكلفة المنتج.
@override final  double? costPrice;
/// سعر بيع المنتج.
@override final  double? sellingPrice;
/// موقع المنتج في المستودع.
@override final  String? location;
/// المورد الذي تم شراء المنتج منه.
@override final  String? supplier;
/// تاريخ آخر عملية إعادة تخزين.
@override final  DateTime? lastRestocked;
/// تاريخ انتهاء صلاحية المنتج.
@override final  DateTime? expiryDate;
/// حالة المخزون الحالية.
@override@JsonKey() final  InventoryStatus status;
/// قائمة بحركات المخزون لهذا العنصر.
 final  List<StockMovement>? _movements;
/// قائمة بحركات المخزون لهذا العنصر.
@override List<StockMovement>? get movements {
  final value = _movements;
  if (value == null) return null;
  if (_movements is EqualUnmodifiableListView) return _movements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

/// بيانات وصفية إضافية.
 final  Map<String, dynamic>? _metadata;
/// بيانات وصفية إضافية.
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

/// تاريخ إنشاء سجل المخزون.
@override final  DateTime? createdAt;
/// تاريخ آخر تحديث لسجل المخزون.
@override final  DateTime? updatedAt;

/// Create a copy of SellerInventoryItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerInventoryItemCopyWith<_SellerInventoryItem> get copyWith => __$SellerInventoryItemCopyWithImpl<_SellerInventoryItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerInventoryItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerInventoryItem&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.name, name) || other.name == name)&&(identical(other.currentStock, currentStock) || other.currentStock == currentStock)&&(identical(other.reservedStock, reservedStock) || other.reservedStock == reservedStock)&&(identical(other.availableStock, availableStock) || other.availableStock == availableStock)&&(identical(other.minStockLevel, minStockLevel) || other.minStockLevel == minStockLevel)&&(identical(other.maxStockLevel, maxStockLevel) || other.maxStockLevel == maxStockLevel)&&(identical(other.reorderPoint, reorderPoint) || other.reorderPoint == reorderPoint)&&(identical(other.reorderQuantity, reorderQuantity) || other.reorderQuantity == reorderQuantity)&&(identical(other.costPrice, costPrice) || other.costPrice == costPrice)&&(identical(other.sellingPrice, sellingPrice) || other.sellingPrice == sellingPrice)&&(identical(other.location, location) || other.location == location)&&(identical(other.supplier, supplier) || other.supplier == supplier)&&(identical(other.lastRestocked, lastRestocked) || other.lastRestocked == lastRestocked)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._movements, _movements)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,productId,sku,name,currentStock,reservedStock,availableStock,minStockLevel,maxStockLevel,reorderPoint,reorderQuantity,costPrice,sellingPrice,location,supplier,lastRestocked,expiryDate,status,const DeepCollectionEquality().hash(_movements),const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt]);

@override
String toString() {
  return 'SellerInventoryItem(id: $id, sellerId: $sellerId, productId: $productId, sku: $sku, name: $name, currentStock: $currentStock, reservedStock: $reservedStock, availableStock: $availableStock, minStockLevel: $minStockLevel, maxStockLevel: $maxStockLevel, reorderPoint: $reorderPoint, reorderQuantity: $reorderQuantity, costPrice: $costPrice, sellingPrice: $sellingPrice, location: $location, supplier: $supplier, lastRestocked: $lastRestocked, expiryDate: $expiryDate, status: $status, movements: $movements, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SellerInventoryItemCopyWith<$Res> implements $SellerInventoryItemCopyWith<$Res> {
  factory _$SellerInventoryItemCopyWith(_SellerInventoryItem value, $Res Function(_SellerInventoryItem) _then) = __$SellerInventoryItemCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String productId, String sku, String name, int currentStock, int reservedStock, int availableStock, int minStockLevel, int maxStockLevel, int reorderPoint, int reorderQuantity, double? costPrice, double? sellingPrice, String? location, String? supplier, DateTime? lastRestocked, DateTime? expiryDate, InventoryStatus status, List<StockMovement>? movements, Map<String, dynamic>? metadata, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$SellerInventoryItemCopyWithImpl<$Res>
    implements _$SellerInventoryItemCopyWith<$Res> {
  __$SellerInventoryItemCopyWithImpl(this._self, this._then);

  final _SellerInventoryItem _self;
  final $Res Function(_SellerInventoryItem) _then;

/// Create a copy of SellerInventoryItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? productId = null,Object? sku = null,Object? name = null,Object? currentStock = null,Object? reservedStock = null,Object? availableStock = null,Object? minStockLevel = null,Object? maxStockLevel = null,Object? reorderPoint = null,Object? reorderQuantity = null,Object? costPrice = freezed,Object? sellingPrice = freezed,Object? location = freezed,Object? supplier = freezed,Object? lastRestocked = freezed,Object? expiryDate = freezed,Object? status = null,Object? movements = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SellerInventoryItem(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,sku: null == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,currentStock: null == currentStock ? _self.currentStock : currentStock // ignore: cast_nullable_to_non_nullable
as int,reservedStock: null == reservedStock ? _self.reservedStock : reservedStock // ignore: cast_nullable_to_non_nullable
as int,availableStock: null == availableStock ? _self.availableStock : availableStock // ignore: cast_nullable_to_non_nullable
as int,minStockLevel: null == minStockLevel ? _self.minStockLevel : minStockLevel // ignore: cast_nullable_to_non_nullable
as int,maxStockLevel: null == maxStockLevel ? _self.maxStockLevel : maxStockLevel // ignore: cast_nullable_to_non_nullable
as int,reorderPoint: null == reorderPoint ? _self.reorderPoint : reorderPoint // ignore: cast_nullable_to_non_nullable
as int,reorderQuantity: null == reorderQuantity ? _self.reorderQuantity : reorderQuantity // ignore: cast_nullable_to_non_nullable
as int,costPrice: freezed == costPrice ? _self.costPrice : costPrice // ignore: cast_nullable_to_non_nullable
as double?,sellingPrice: freezed == sellingPrice ? _self.sellingPrice : sellingPrice // ignore: cast_nullable_to_non_nullable
as double?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,supplier: freezed == supplier ? _self.supplier : supplier // ignore: cast_nullable_to_non_nullable
as String?,lastRestocked: freezed == lastRestocked ? _self.lastRestocked : lastRestocked // ignore: cast_nullable_to_non_nullable
as DateTime?,expiryDate: freezed == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InventoryStatus,movements: freezed == movements ? _self._movements : movements // ignore: cast_nullable_to_non_nullable
as List<StockMovement>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$StockMovement {

/// المعرف الفريد للحركة.
 String get id;/// معرّف عنصر المخزون المرتبط.
 String get inventoryItemId;/// نوع الحركة (شراء، بيع، إرجاع، ...).
 MovementType get type;/// الكمية التي تم تحريكها.
 int get quantity;/// رصيد المخزون قبل الحركة.
 int? get previousStock;/// رصيد المخزون بعد الحركة.
 int? get newStock;/// سبب الحركة (إذا كان تعديلاً).
 String? get reason;/// مرجع للحركة (مثل رقم الفاتورة أو الطلب).
 String? get reference;/// ملاحظات إضافية.
 String? get notes;/// المستخدم الذي قام بالحركة.
 String? get performedBy;/// تاريخ إنشاء الحركة.
 DateTime? get createdAt;
/// Create a copy of StockMovement
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StockMovementCopyWith<StockMovement> get copyWith => _$StockMovementCopyWithImpl<StockMovement>(this as StockMovement, _$identity);

  /// Serializes this StockMovement to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StockMovement&&(identical(other.id, id) || other.id == id)&&(identical(other.inventoryItemId, inventoryItemId) || other.inventoryItemId == inventoryItemId)&&(identical(other.type, type) || other.type == type)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.previousStock, previousStock) || other.previousStock == previousStock)&&(identical(other.newStock, newStock) || other.newStock == newStock)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.reference, reference) || other.reference == reference)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.performedBy, performedBy) || other.performedBy == performedBy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,inventoryItemId,type,quantity,previousStock,newStock,reason,reference,notes,performedBy,createdAt);

@override
String toString() {
  return 'StockMovement(id: $id, inventoryItemId: $inventoryItemId, type: $type, quantity: $quantity, previousStock: $previousStock, newStock: $newStock, reason: $reason, reference: $reference, notes: $notes, performedBy: $performedBy, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $StockMovementCopyWith<$Res>  {
  factory $StockMovementCopyWith(StockMovement value, $Res Function(StockMovement) _then) = _$StockMovementCopyWithImpl;
@useResult
$Res call({
 String id, String inventoryItemId, MovementType type, int quantity, int? previousStock, int? newStock, String? reason, String? reference, String? notes, String? performedBy, DateTime? createdAt
});




}
/// @nodoc
class _$StockMovementCopyWithImpl<$Res>
    implements $StockMovementCopyWith<$Res> {
  _$StockMovementCopyWithImpl(this._self, this._then);

  final StockMovement _self;
  final $Res Function(StockMovement) _then;

/// Create a copy of StockMovement
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? inventoryItemId = null,Object? type = null,Object? quantity = null,Object? previousStock = freezed,Object? newStock = freezed,Object? reason = freezed,Object? reference = freezed,Object? notes = freezed,Object? performedBy = freezed,Object? createdAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,inventoryItemId: null == inventoryItemId ? _self.inventoryItemId : inventoryItemId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MovementType,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,previousStock: freezed == previousStock ? _self.previousStock : previousStock // ignore: cast_nullable_to_non_nullable
as int?,newStock: freezed == newStock ? _self.newStock : newStock // ignore: cast_nullable_to_non_nullable
as int?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,performedBy: freezed == performedBy ? _self.performedBy : performedBy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [StockMovement].
extension StockMovementPatterns on StockMovement {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StockMovement value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StockMovement() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StockMovement value)  $default,){
final _that = this;
switch (_that) {
case _StockMovement():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StockMovement value)?  $default,){
final _that = this;
switch (_that) {
case _StockMovement() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String inventoryItemId,  MovementType type,  int quantity,  int? previousStock,  int? newStock,  String? reason,  String? reference,  String? notes,  String? performedBy,  DateTime? createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StockMovement() when $default != null:
return $default(_that.id,_that.inventoryItemId,_that.type,_that.quantity,_that.previousStock,_that.newStock,_that.reason,_that.reference,_that.notes,_that.performedBy,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String inventoryItemId,  MovementType type,  int quantity,  int? previousStock,  int? newStock,  String? reason,  String? reference,  String? notes,  String? performedBy,  DateTime? createdAt)  $default,) {final _that = this;
switch (_that) {
case _StockMovement():
return $default(_that.id,_that.inventoryItemId,_that.type,_that.quantity,_that.previousStock,_that.newStock,_that.reason,_that.reference,_that.notes,_that.performedBy,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String inventoryItemId,  MovementType type,  int quantity,  int? previousStock,  int? newStock,  String? reason,  String? reference,  String? notes,  String? performedBy,  DateTime? createdAt)?  $default,) {final _that = this;
switch (_that) {
case _StockMovement() when $default != null:
return $default(_that.id,_that.inventoryItemId,_that.type,_that.quantity,_that.previousStock,_that.newStock,_that.reason,_that.reference,_that.notes,_that.performedBy,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StockMovement implements StockMovement {
  const _StockMovement({required this.id, required this.inventoryItemId, required this.type, required this.quantity, this.previousStock, this.newStock, this.reason, this.reference, this.notes, this.performedBy, this.createdAt});
  factory _StockMovement.fromJson(Map<String, dynamic> json) => _$StockMovementFromJson(json);

/// المعرف الفريد للحركة.
@override final  String id;
/// معرّف عنصر المخزون المرتبط.
@override final  String inventoryItemId;
/// نوع الحركة (شراء، بيع، إرجاع، ...).
@override final  MovementType type;
/// الكمية التي تم تحريكها.
@override final  int quantity;
/// رصيد المخزون قبل الحركة.
@override final  int? previousStock;
/// رصيد المخزون بعد الحركة.
@override final  int? newStock;
/// سبب الحركة (إذا كان تعديلاً).
@override final  String? reason;
/// مرجع للحركة (مثل رقم الفاتورة أو الطلب).
@override final  String? reference;
/// ملاحظات إضافية.
@override final  String? notes;
/// المستخدم الذي قام بالحركة.
@override final  String? performedBy;
/// تاريخ إنشاء الحركة.
@override final  DateTime? createdAt;

/// Create a copy of StockMovement
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StockMovementCopyWith<_StockMovement> get copyWith => __$StockMovementCopyWithImpl<_StockMovement>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StockMovementToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StockMovement&&(identical(other.id, id) || other.id == id)&&(identical(other.inventoryItemId, inventoryItemId) || other.inventoryItemId == inventoryItemId)&&(identical(other.type, type) || other.type == type)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.previousStock, previousStock) || other.previousStock == previousStock)&&(identical(other.newStock, newStock) || other.newStock == newStock)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.reference, reference) || other.reference == reference)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.performedBy, performedBy) || other.performedBy == performedBy)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,inventoryItemId,type,quantity,previousStock,newStock,reason,reference,notes,performedBy,createdAt);

@override
String toString() {
  return 'StockMovement(id: $id, inventoryItemId: $inventoryItemId, type: $type, quantity: $quantity, previousStock: $previousStock, newStock: $newStock, reason: $reason, reference: $reference, notes: $notes, performedBy: $performedBy, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$StockMovementCopyWith<$Res> implements $StockMovementCopyWith<$Res> {
  factory _$StockMovementCopyWith(_StockMovement value, $Res Function(_StockMovement) _then) = __$StockMovementCopyWithImpl;
@override @useResult
$Res call({
 String id, String inventoryItemId, MovementType type, int quantity, int? previousStock, int? newStock, String? reason, String? reference, String? notes, String? performedBy, DateTime? createdAt
});




}
/// @nodoc
class __$StockMovementCopyWithImpl<$Res>
    implements _$StockMovementCopyWith<$Res> {
  __$StockMovementCopyWithImpl(this._self, this._then);

  final _StockMovement _self;
  final $Res Function(_StockMovement) _then;

/// Create a copy of StockMovement
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? inventoryItemId = null,Object? type = null,Object? quantity = null,Object? previousStock = freezed,Object? newStock = freezed,Object? reason = freezed,Object? reference = freezed,Object? notes = freezed,Object? performedBy = freezed,Object? createdAt = freezed,}) {
  return _then(_StockMovement(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,inventoryItemId: null == inventoryItemId ? _self.inventoryItemId : inventoryItemId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MovementType,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,previousStock: freezed == previousStock ? _self.previousStock : previousStock // ignore: cast_nullable_to_non_nullable
as int?,newStock: freezed == newStock ? _self.newStock : newStock // ignore: cast_nullable_to_non_nullable
as int?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,performedBy: freezed == performedBy ? _self.performedBy : performedBy // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$InventoryReport {

/// معرّف البائع الذي يخصه هذا التقرير.
 String get sellerId;/// إجمالي عدد أنواع المنتجات المختلفة في المخزون.
 int get totalItems;/// عدد المنتجات المتوفرة حاليًا.
 int get inStockItems;/// عدد المنتجات التي وصل مخزونها إلى مستوى منخفض.
 int get lowStockItems;/// عدد المنتجات التي نفدت من المخزون.
 int get outOfStockItems;/// عدد المنتجات التي تقترب من تاريخ انتهاء صلاحيتها.
 int get expiringSoonItems;/// القيمة الإجمالية لجميع المنتجات في المخزون.
 double get totalValue;/// متوسط معدل دوران المخزون.
 double get averageTurnover;/// قائمة بالتنبيهات المتعلقة بالمخزون.
 List<InventoryAlert>? get alerts;/// توزيع عدد المنتجات حسب الفئة.
 Map<String, int>? get categoryBreakdown;/// توزيع عدد المنتجات حسب الحالة.
 Map<String, int>? get statusBreakdown;/// تاريخ إنشاء التقرير.
 DateTime? get generatedAt;
/// Create a copy of InventoryReport
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InventoryReportCopyWith<InventoryReport> get copyWith => _$InventoryReportCopyWithImpl<InventoryReport>(this as InventoryReport, _$identity);

  /// Serializes this InventoryReport to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InventoryReport&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems)&&(identical(other.inStockItems, inStockItems) || other.inStockItems == inStockItems)&&(identical(other.lowStockItems, lowStockItems) || other.lowStockItems == lowStockItems)&&(identical(other.outOfStockItems, outOfStockItems) || other.outOfStockItems == outOfStockItems)&&(identical(other.expiringSoonItems, expiringSoonItems) || other.expiringSoonItems == expiringSoonItems)&&(identical(other.totalValue, totalValue) || other.totalValue == totalValue)&&(identical(other.averageTurnover, averageTurnover) || other.averageTurnover == averageTurnover)&&const DeepCollectionEquality().equals(other.alerts, alerts)&&const DeepCollectionEquality().equals(other.categoryBreakdown, categoryBreakdown)&&const DeepCollectionEquality().equals(other.statusBreakdown, statusBreakdown)&&(identical(other.generatedAt, generatedAt) || other.generatedAt == generatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sellerId,totalItems,inStockItems,lowStockItems,outOfStockItems,expiringSoonItems,totalValue,averageTurnover,const DeepCollectionEquality().hash(alerts),const DeepCollectionEquality().hash(categoryBreakdown),const DeepCollectionEquality().hash(statusBreakdown),generatedAt);

@override
String toString() {
  return 'InventoryReport(sellerId: $sellerId, totalItems: $totalItems, inStockItems: $inStockItems, lowStockItems: $lowStockItems, outOfStockItems: $outOfStockItems, expiringSoonItems: $expiringSoonItems, totalValue: $totalValue, averageTurnover: $averageTurnover, alerts: $alerts, categoryBreakdown: $categoryBreakdown, statusBreakdown: $statusBreakdown, generatedAt: $generatedAt)';
}


}

/// @nodoc
abstract mixin class $InventoryReportCopyWith<$Res>  {
  factory $InventoryReportCopyWith(InventoryReport value, $Res Function(InventoryReport) _then) = _$InventoryReportCopyWithImpl;
@useResult
$Res call({
 String sellerId, int totalItems, int inStockItems, int lowStockItems, int outOfStockItems, int expiringSoonItems, double totalValue, double averageTurnover, List<InventoryAlert>? alerts, Map<String, int>? categoryBreakdown, Map<String, int>? statusBreakdown, DateTime? generatedAt
});




}
/// @nodoc
class _$InventoryReportCopyWithImpl<$Res>
    implements $InventoryReportCopyWith<$Res> {
  _$InventoryReportCopyWithImpl(this._self, this._then);

  final InventoryReport _self;
  final $Res Function(InventoryReport) _then;

/// Create a copy of InventoryReport
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sellerId = null,Object? totalItems = null,Object? inStockItems = null,Object? lowStockItems = null,Object? outOfStockItems = null,Object? expiringSoonItems = null,Object? totalValue = null,Object? averageTurnover = null,Object? alerts = freezed,Object? categoryBreakdown = freezed,Object? statusBreakdown = freezed,Object? generatedAt = freezed,}) {
  return _then(_self.copyWith(
sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,totalItems: null == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int,inStockItems: null == inStockItems ? _self.inStockItems : inStockItems // ignore: cast_nullable_to_non_nullable
as int,lowStockItems: null == lowStockItems ? _self.lowStockItems : lowStockItems // ignore: cast_nullable_to_non_nullable
as int,outOfStockItems: null == outOfStockItems ? _self.outOfStockItems : outOfStockItems // ignore: cast_nullable_to_non_nullable
as int,expiringSoonItems: null == expiringSoonItems ? _self.expiringSoonItems : expiringSoonItems // ignore: cast_nullable_to_non_nullable
as int,totalValue: null == totalValue ? _self.totalValue : totalValue // ignore: cast_nullable_to_non_nullable
as double,averageTurnover: null == averageTurnover ? _self.averageTurnover : averageTurnover // ignore: cast_nullable_to_non_nullable
as double,alerts: freezed == alerts ? _self.alerts : alerts // ignore: cast_nullable_to_non_nullable
as List<InventoryAlert>?,categoryBreakdown: freezed == categoryBreakdown ? _self.categoryBreakdown : categoryBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,statusBreakdown: freezed == statusBreakdown ? _self.statusBreakdown : statusBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,generatedAt: freezed == generatedAt ? _self.generatedAt : generatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [InventoryReport].
extension InventoryReportPatterns on InventoryReport {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _InventoryReport value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _InventoryReport() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _InventoryReport value)  $default,){
final _that = this;
switch (_that) {
case _InventoryReport():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _InventoryReport value)?  $default,){
final _that = this;
switch (_that) {
case _InventoryReport() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String sellerId,  int totalItems,  int inStockItems,  int lowStockItems,  int outOfStockItems,  int expiringSoonItems,  double totalValue,  double averageTurnover,  List<InventoryAlert>? alerts,  Map<String, int>? categoryBreakdown,  Map<String, int>? statusBreakdown,  DateTime? generatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _InventoryReport() when $default != null:
return $default(_that.sellerId,_that.totalItems,_that.inStockItems,_that.lowStockItems,_that.outOfStockItems,_that.expiringSoonItems,_that.totalValue,_that.averageTurnover,_that.alerts,_that.categoryBreakdown,_that.statusBreakdown,_that.generatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String sellerId,  int totalItems,  int inStockItems,  int lowStockItems,  int outOfStockItems,  int expiringSoonItems,  double totalValue,  double averageTurnover,  List<InventoryAlert>? alerts,  Map<String, int>? categoryBreakdown,  Map<String, int>? statusBreakdown,  DateTime? generatedAt)  $default,) {final _that = this;
switch (_that) {
case _InventoryReport():
return $default(_that.sellerId,_that.totalItems,_that.inStockItems,_that.lowStockItems,_that.outOfStockItems,_that.expiringSoonItems,_that.totalValue,_that.averageTurnover,_that.alerts,_that.categoryBreakdown,_that.statusBreakdown,_that.generatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String sellerId,  int totalItems,  int inStockItems,  int lowStockItems,  int outOfStockItems,  int expiringSoonItems,  double totalValue,  double averageTurnover,  List<InventoryAlert>? alerts,  Map<String, int>? categoryBreakdown,  Map<String, int>? statusBreakdown,  DateTime? generatedAt)?  $default,) {final _that = this;
switch (_that) {
case _InventoryReport() when $default != null:
return $default(_that.sellerId,_that.totalItems,_that.inStockItems,_that.lowStockItems,_that.outOfStockItems,_that.expiringSoonItems,_that.totalValue,_that.averageTurnover,_that.alerts,_that.categoryBreakdown,_that.statusBreakdown,_that.generatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _InventoryReport implements InventoryReport {
  const _InventoryReport({required this.sellerId, this.totalItems = 0, this.inStockItems = 0, this.lowStockItems = 0, this.outOfStockItems = 0, this.expiringSoonItems = 0, this.totalValue = 0, this.averageTurnover = 0, final  List<InventoryAlert>? alerts, final  Map<String, int>? categoryBreakdown, final  Map<String, int>? statusBreakdown, this.generatedAt}): _alerts = alerts,_categoryBreakdown = categoryBreakdown,_statusBreakdown = statusBreakdown;
  factory _InventoryReport.fromJson(Map<String, dynamic> json) => _$InventoryReportFromJson(json);

/// معرّف البائع الذي يخصه هذا التقرير.
@override final  String sellerId;
/// إجمالي عدد أنواع المنتجات المختلفة في المخزون.
@override@JsonKey() final  int totalItems;
/// عدد المنتجات المتوفرة حاليًا.
@override@JsonKey() final  int inStockItems;
/// عدد المنتجات التي وصل مخزونها إلى مستوى منخفض.
@override@JsonKey() final  int lowStockItems;
/// عدد المنتجات التي نفدت من المخزون.
@override@JsonKey() final  int outOfStockItems;
/// عدد المنتجات التي تقترب من تاريخ انتهاء صلاحيتها.
@override@JsonKey() final  int expiringSoonItems;
/// القيمة الإجمالية لجميع المنتجات في المخزون.
@override@JsonKey() final  double totalValue;
/// متوسط معدل دوران المخزون.
@override@JsonKey() final  double averageTurnover;
/// قائمة بالتنبيهات المتعلقة بالمخزون.
 final  List<InventoryAlert>? _alerts;
/// قائمة بالتنبيهات المتعلقة بالمخزون.
@override List<InventoryAlert>? get alerts {
  final value = _alerts;
  if (value == null) return null;
  if (_alerts is EqualUnmodifiableListView) return _alerts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

/// توزيع عدد المنتجات حسب الفئة.
 final  Map<String, int>? _categoryBreakdown;
/// توزيع عدد المنتجات حسب الفئة.
@override Map<String, int>? get categoryBreakdown {
  final value = _categoryBreakdown;
  if (value == null) return null;
  if (_categoryBreakdown is EqualUnmodifiableMapView) return _categoryBreakdown;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

/// توزيع عدد المنتجات حسب الحالة.
 final  Map<String, int>? _statusBreakdown;
/// توزيع عدد المنتجات حسب الحالة.
@override Map<String, int>? get statusBreakdown {
  final value = _statusBreakdown;
  if (value == null) return null;
  if (_statusBreakdown is EqualUnmodifiableMapView) return _statusBreakdown;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

/// تاريخ إنشاء التقرير.
@override final  DateTime? generatedAt;

/// Create a copy of InventoryReport
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InventoryReportCopyWith<_InventoryReport> get copyWith => __$InventoryReportCopyWithImpl<_InventoryReport>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InventoryReportToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InventoryReport&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems)&&(identical(other.inStockItems, inStockItems) || other.inStockItems == inStockItems)&&(identical(other.lowStockItems, lowStockItems) || other.lowStockItems == lowStockItems)&&(identical(other.outOfStockItems, outOfStockItems) || other.outOfStockItems == outOfStockItems)&&(identical(other.expiringSoonItems, expiringSoonItems) || other.expiringSoonItems == expiringSoonItems)&&(identical(other.totalValue, totalValue) || other.totalValue == totalValue)&&(identical(other.averageTurnover, averageTurnover) || other.averageTurnover == averageTurnover)&&const DeepCollectionEquality().equals(other._alerts, _alerts)&&const DeepCollectionEquality().equals(other._categoryBreakdown, _categoryBreakdown)&&const DeepCollectionEquality().equals(other._statusBreakdown, _statusBreakdown)&&(identical(other.generatedAt, generatedAt) || other.generatedAt == generatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sellerId,totalItems,inStockItems,lowStockItems,outOfStockItems,expiringSoonItems,totalValue,averageTurnover,const DeepCollectionEquality().hash(_alerts),const DeepCollectionEquality().hash(_categoryBreakdown),const DeepCollectionEquality().hash(_statusBreakdown),generatedAt);

@override
String toString() {
  return 'InventoryReport(sellerId: $sellerId, totalItems: $totalItems, inStockItems: $inStockItems, lowStockItems: $lowStockItems, outOfStockItems: $outOfStockItems, expiringSoonItems: $expiringSoonItems, totalValue: $totalValue, averageTurnover: $averageTurnover, alerts: $alerts, categoryBreakdown: $categoryBreakdown, statusBreakdown: $statusBreakdown, generatedAt: $generatedAt)';
}


}

/// @nodoc
abstract mixin class _$InventoryReportCopyWith<$Res> implements $InventoryReportCopyWith<$Res> {
  factory _$InventoryReportCopyWith(_InventoryReport value, $Res Function(_InventoryReport) _then) = __$InventoryReportCopyWithImpl;
@override @useResult
$Res call({
 String sellerId, int totalItems, int inStockItems, int lowStockItems, int outOfStockItems, int expiringSoonItems, double totalValue, double averageTurnover, List<InventoryAlert>? alerts, Map<String, int>? categoryBreakdown, Map<String, int>? statusBreakdown, DateTime? generatedAt
});




}
/// @nodoc
class __$InventoryReportCopyWithImpl<$Res>
    implements _$InventoryReportCopyWith<$Res> {
  __$InventoryReportCopyWithImpl(this._self, this._then);

  final _InventoryReport _self;
  final $Res Function(_InventoryReport) _then;

/// Create a copy of InventoryReport
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sellerId = null,Object? totalItems = null,Object? inStockItems = null,Object? lowStockItems = null,Object? outOfStockItems = null,Object? expiringSoonItems = null,Object? totalValue = null,Object? averageTurnover = null,Object? alerts = freezed,Object? categoryBreakdown = freezed,Object? statusBreakdown = freezed,Object? generatedAt = freezed,}) {
  return _then(_InventoryReport(
sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,totalItems: null == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int,inStockItems: null == inStockItems ? _self.inStockItems : inStockItems // ignore: cast_nullable_to_non_nullable
as int,lowStockItems: null == lowStockItems ? _self.lowStockItems : lowStockItems // ignore: cast_nullable_to_non_nullable
as int,outOfStockItems: null == outOfStockItems ? _self.outOfStockItems : outOfStockItems // ignore: cast_nullable_to_non_nullable
as int,expiringSoonItems: null == expiringSoonItems ? _self.expiringSoonItems : expiringSoonItems // ignore: cast_nullable_to_non_nullable
as int,totalValue: null == totalValue ? _self.totalValue : totalValue // ignore: cast_nullable_to_non_nullable
as double,averageTurnover: null == averageTurnover ? _self.averageTurnover : averageTurnover // ignore: cast_nullable_to_non_nullable
as double,alerts: freezed == alerts ? _self._alerts : alerts // ignore: cast_nullable_to_non_nullable
as List<InventoryAlert>?,categoryBreakdown: freezed == categoryBreakdown ? _self._categoryBreakdown : categoryBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,statusBreakdown: freezed == statusBreakdown ? _self._statusBreakdown : statusBreakdown // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,generatedAt: freezed == generatedAt ? _self.generatedAt : generatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$InventoryAlert {

 String get id; String get sellerId; String get inventoryId; String get alertType; String get title; String get message; AlertPriority get priority; bool get isRead; DateTime? get readAt; DateTime? get createdAt; SellerInventoryItem? get inventoryItem;
/// Create a copy of InventoryAlert
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InventoryAlertCopyWith<InventoryAlert> get copyWith => _$InventoryAlertCopyWithImpl<InventoryAlert>(this as InventoryAlert, _$identity);

  /// Serializes this InventoryAlert to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InventoryAlert&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.inventoryId, inventoryId) || other.inventoryId == inventoryId)&&(identical(other.alertType, alertType) || other.alertType == alertType)&&(identical(other.title, title) || other.title == title)&&(identical(other.message, message) || other.message == message)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.isRead, isRead) || other.isRead == isRead)&&(identical(other.readAt, readAt) || other.readAt == readAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.inventoryItem, inventoryItem) || other.inventoryItem == inventoryItem));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,inventoryId,alertType,title,message,priority,isRead,readAt,createdAt,inventoryItem);

@override
String toString() {
  return 'InventoryAlert(id: $id, sellerId: $sellerId, inventoryId: $inventoryId, alertType: $alertType, title: $title, message: $message, priority: $priority, isRead: $isRead, readAt: $readAt, createdAt: $createdAt, inventoryItem: $inventoryItem)';
}


}

/// @nodoc
abstract mixin class $InventoryAlertCopyWith<$Res>  {
  factory $InventoryAlertCopyWith(InventoryAlert value, $Res Function(InventoryAlert) _then) = _$InventoryAlertCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String inventoryId, String alertType, String title, String message, AlertPriority priority, bool isRead, DateTime? readAt, DateTime? createdAt, SellerInventoryItem? inventoryItem
});


$SellerInventoryItemCopyWith<$Res>? get inventoryItem;

}
/// @nodoc
class _$InventoryAlertCopyWithImpl<$Res>
    implements $InventoryAlertCopyWith<$Res> {
  _$InventoryAlertCopyWithImpl(this._self, this._then);

  final InventoryAlert _self;
  final $Res Function(InventoryAlert) _then;

/// Create a copy of InventoryAlert
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? inventoryId = null,Object? alertType = null,Object? title = null,Object? message = null,Object? priority = null,Object? isRead = null,Object? readAt = freezed,Object? createdAt = freezed,Object? inventoryItem = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,inventoryId: null == inventoryId ? _self.inventoryId : inventoryId // ignore: cast_nullable_to_non_nullable
as String,alertType: null == alertType ? _self.alertType : alertType // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as AlertPriority,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,inventoryItem: freezed == inventoryItem ? _self.inventoryItem : inventoryItem // ignore: cast_nullable_to_non_nullable
as SellerInventoryItem?,
  ));
}
/// Create a copy of InventoryAlert
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerInventoryItemCopyWith<$Res>? get inventoryItem {
    if (_self.inventoryItem == null) {
    return null;
  }

  return $SellerInventoryItemCopyWith<$Res>(_self.inventoryItem!, (value) {
    return _then(_self.copyWith(inventoryItem: value));
  });
}
}


/// Adds pattern-matching-related methods to [InventoryAlert].
extension InventoryAlertPatterns on InventoryAlert {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _InventoryAlert value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _InventoryAlert() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _InventoryAlert value)  $default,){
final _that = this;
switch (_that) {
case _InventoryAlert():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _InventoryAlert value)?  $default,){
final _that = this;
switch (_that) {
case _InventoryAlert() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String inventoryId,  String alertType,  String title,  String message,  AlertPriority priority,  bool isRead,  DateTime? readAt,  DateTime? createdAt,  SellerInventoryItem? inventoryItem)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _InventoryAlert() when $default != null:
return $default(_that.id,_that.sellerId,_that.inventoryId,_that.alertType,_that.title,_that.message,_that.priority,_that.isRead,_that.readAt,_that.createdAt,_that.inventoryItem);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String inventoryId,  String alertType,  String title,  String message,  AlertPriority priority,  bool isRead,  DateTime? readAt,  DateTime? createdAt,  SellerInventoryItem? inventoryItem)  $default,) {final _that = this;
switch (_that) {
case _InventoryAlert():
return $default(_that.id,_that.sellerId,_that.inventoryId,_that.alertType,_that.title,_that.message,_that.priority,_that.isRead,_that.readAt,_that.createdAt,_that.inventoryItem);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String inventoryId,  String alertType,  String title,  String message,  AlertPriority priority,  bool isRead,  DateTime? readAt,  DateTime? createdAt,  SellerInventoryItem? inventoryItem)?  $default,) {final _that = this;
switch (_that) {
case _InventoryAlert() when $default != null:
return $default(_that.id,_that.sellerId,_that.inventoryId,_that.alertType,_that.title,_that.message,_that.priority,_that.isRead,_that.readAt,_that.createdAt,_that.inventoryItem);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _InventoryAlert implements InventoryAlert {
  const _InventoryAlert({required this.id, required this.sellerId, required this.inventoryId, required this.alertType, required this.title, required this.message, this.priority = AlertPriority.medium, this.isRead = false, this.readAt, this.createdAt, this.inventoryItem});
  factory _InventoryAlert.fromJson(Map<String, dynamic> json) => _$InventoryAlertFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String inventoryId;
@override final  String alertType;
@override final  String title;
@override final  String message;
@override@JsonKey() final  AlertPriority priority;
@override@JsonKey() final  bool isRead;
@override final  DateTime? readAt;
@override final  DateTime? createdAt;
@override final  SellerInventoryItem? inventoryItem;

/// Create a copy of InventoryAlert
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InventoryAlertCopyWith<_InventoryAlert> get copyWith => __$InventoryAlertCopyWithImpl<_InventoryAlert>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InventoryAlertToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InventoryAlert&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.inventoryId, inventoryId) || other.inventoryId == inventoryId)&&(identical(other.alertType, alertType) || other.alertType == alertType)&&(identical(other.title, title) || other.title == title)&&(identical(other.message, message) || other.message == message)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.isRead, isRead) || other.isRead == isRead)&&(identical(other.readAt, readAt) || other.readAt == readAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.inventoryItem, inventoryItem) || other.inventoryItem == inventoryItem));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,inventoryId,alertType,title,message,priority,isRead,readAt,createdAt,inventoryItem);

@override
String toString() {
  return 'InventoryAlert(id: $id, sellerId: $sellerId, inventoryId: $inventoryId, alertType: $alertType, title: $title, message: $message, priority: $priority, isRead: $isRead, readAt: $readAt, createdAt: $createdAt, inventoryItem: $inventoryItem)';
}


}

/// @nodoc
abstract mixin class _$InventoryAlertCopyWith<$Res> implements $InventoryAlertCopyWith<$Res> {
  factory _$InventoryAlertCopyWith(_InventoryAlert value, $Res Function(_InventoryAlert) _then) = __$InventoryAlertCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String inventoryId, String alertType, String title, String message, AlertPriority priority, bool isRead, DateTime? readAt, DateTime? createdAt, SellerInventoryItem? inventoryItem
});


@override $SellerInventoryItemCopyWith<$Res>? get inventoryItem;

}
/// @nodoc
class __$InventoryAlertCopyWithImpl<$Res>
    implements _$InventoryAlertCopyWith<$Res> {
  __$InventoryAlertCopyWithImpl(this._self, this._then);

  final _InventoryAlert _self;
  final $Res Function(_InventoryAlert) _then;

/// Create a copy of InventoryAlert
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? inventoryId = null,Object? alertType = null,Object? title = null,Object? message = null,Object? priority = null,Object? isRead = null,Object? readAt = freezed,Object? createdAt = freezed,Object? inventoryItem = freezed,}) {
  return _then(_InventoryAlert(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,inventoryId: null == inventoryId ? _self.inventoryId : inventoryId // ignore: cast_nullable_to_non_nullable
as String,alertType: null == alertType ? _self.alertType : alertType // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as AlertPriority,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,inventoryItem: freezed == inventoryItem ? _self.inventoryItem : inventoryItem // ignore: cast_nullable_to_non_nullable
as SellerInventoryItem?,
  ));
}

/// Create a copy of InventoryAlert
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerInventoryItemCopyWith<$Res>? get inventoryItem {
    if (_self.inventoryItem == null) {
    return null;
  }

  return $SellerInventoryItemCopyWith<$Res>(_self.inventoryItem!, (value) {
    return _then(_self.copyWith(inventoryItem: value));
  });
}
}


/// @nodoc
mixin _$InventoryStats {

 int get totalItems; int get lowStockItems; int get outOfStockItems; int get activeItems; double get totalValue;
/// Create a copy of InventoryStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InventoryStatsCopyWith<InventoryStats> get copyWith => _$InventoryStatsCopyWithImpl<InventoryStats>(this as InventoryStats, _$identity);

  /// Serializes this InventoryStats to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InventoryStats&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems)&&(identical(other.lowStockItems, lowStockItems) || other.lowStockItems == lowStockItems)&&(identical(other.outOfStockItems, outOfStockItems) || other.outOfStockItems == outOfStockItems)&&(identical(other.activeItems, activeItems) || other.activeItems == activeItems)&&(identical(other.totalValue, totalValue) || other.totalValue == totalValue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalItems,lowStockItems,outOfStockItems,activeItems,totalValue);

@override
String toString() {
  return 'InventoryStats(totalItems: $totalItems, lowStockItems: $lowStockItems, outOfStockItems: $outOfStockItems, activeItems: $activeItems, totalValue: $totalValue)';
}


}

/// @nodoc
abstract mixin class $InventoryStatsCopyWith<$Res>  {
  factory $InventoryStatsCopyWith(InventoryStats value, $Res Function(InventoryStats) _then) = _$InventoryStatsCopyWithImpl;
@useResult
$Res call({
 int totalItems, int lowStockItems, int outOfStockItems, int activeItems, double totalValue
});




}
/// @nodoc
class _$InventoryStatsCopyWithImpl<$Res>
    implements $InventoryStatsCopyWith<$Res> {
  _$InventoryStatsCopyWithImpl(this._self, this._then);

  final InventoryStats _self;
  final $Res Function(InventoryStats) _then;

/// Create a copy of InventoryStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalItems = null,Object? lowStockItems = null,Object? outOfStockItems = null,Object? activeItems = null,Object? totalValue = null,}) {
  return _then(_self.copyWith(
totalItems: null == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int,lowStockItems: null == lowStockItems ? _self.lowStockItems : lowStockItems // ignore: cast_nullable_to_non_nullable
as int,outOfStockItems: null == outOfStockItems ? _self.outOfStockItems : outOfStockItems // ignore: cast_nullable_to_non_nullable
as int,activeItems: null == activeItems ? _self.activeItems : activeItems // ignore: cast_nullable_to_non_nullable
as int,totalValue: null == totalValue ? _self.totalValue : totalValue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [InventoryStats].
extension InventoryStatsPatterns on InventoryStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _InventoryStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _InventoryStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _InventoryStats value)  $default,){
final _that = this;
switch (_that) {
case _InventoryStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _InventoryStats value)?  $default,){
final _that = this;
switch (_that) {
case _InventoryStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalItems,  int lowStockItems,  int outOfStockItems,  int activeItems,  double totalValue)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _InventoryStats() when $default != null:
return $default(_that.totalItems,_that.lowStockItems,_that.outOfStockItems,_that.activeItems,_that.totalValue);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalItems,  int lowStockItems,  int outOfStockItems,  int activeItems,  double totalValue)  $default,) {final _that = this;
switch (_that) {
case _InventoryStats():
return $default(_that.totalItems,_that.lowStockItems,_that.outOfStockItems,_that.activeItems,_that.totalValue);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalItems,  int lowStockItems,  int outOfStockItems,  int activeItems,  double totalValue)?  $default,) {final _that = this;
switch (_that) {
case _InventoryStats() when $default != null:
return $default(_that.totalItems,_that.lowStockItems,_that.outOfStockItems,_that.activeItems,_that.totalValue);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _InventoryStats implements InventoryStats {
  const _InventoryStats({this.totalItems = 0, this.lowStockItems = 0, this.outOfStockItems = 0, this.activeItems = 0, this.totalValue = 0});
  factory _InventoryStats.fromJson(Map<String, dynamic> json) => _$InventoryStatsFromJson(json);

@override@JsonKey() final  int totalItems;
@override@JsonKey() final  int lowStockItems;
@override@JsonKey() final  int outOfStockItems;
@override@JsonKey() final  int activeItems;
@override@JsonKey() final  double totalValue;

/// Create a copy of InventoryStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InventoryStatsCopyWith<_InventoryStats> get copyWith => __$InventoryStatsCopyWithImpl<_InventoryStats>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InventoryStatsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InventoryStats&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems)&&(identical(other.lowStockItems, lowStockItems) || other.lowStockItems == lowStockItems)&&(identical(other.outOfStockItems, outOfStockItems) || other.outOfStockItems == outOfStockItems)&&(identical(other.activeItems, activeItems) || other.activeItems == activeItems)&&(identical(other.totalValue, totalValue) || other.totalValue == totalValue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalItems,lowStockItems,outOfStockItems,activeItems,totalValue);

@override
String toString() {
  return 'InventoryStats(totalItems: $totalItems, lowStockItems: $lowStockItems, outOfStockItems: $outOfStockItems, activeItems: $activeItems, totalValue: $totalValue)';
}


}

/// @nodoc
abstract mixin class _$InventoryStatsCopyWith<$Res> implements $InventoryStatsCopyWith<$Res> {
  factory _$InventoryStatsCopyWith(_InventoryStats value, $Res Function(_InventoryStats) _then) = __$InventoryStatsCopyWithImpl;
@override @useResult
$Res call({
 int totalItems, int lowStockItems, int outOfStockItems, int activeItems, double totalValue
});




}
/// @nodoc
class __$InventoryStatsCopyWithImpl<$Res>
    implements _$InventoryStatsCopyWith<$Res> {
  __$InventoryStatsCopyWithImpl(this._self, this._then);

  final _InventoryStats _self;
  final $Res Function(_InventoryStats) _then;

/// Create a copy of InventoryStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalItems = null,Object? lowStockItems = null,Object? outOfStockItems = null,Object? activeItems = null,Object? totalValue = null,}) {
  return _then(_InventoryStats(
totalItems: null == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int,lowStockItems: null == lowStockItems ? _self.lowStockItems : lowStockItems // ignore: cast_nullable_to_non_nullable
as int,outOfStockItems: null == outOfStockItems ? _self.outOfStockItems : outOfStockItems // ignore: cast_nullable_to_non_nullable
as int,activeItems: null == activeItems ? _self.activeItems : activeItems // ignore: cast_nullable_to_non_nullable
as int,totalValue: null == totalValue ? _self.totalValue : totalValue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$InventoryManagementModel {

/// المعرف الفريد للمنتج.
 String? get productId;/// اسم المنتج.
 String? get productName;/// إجمالي الكمية المتاحة حاليًا في المخزون.
 int? get inStock;/// الكمية المحجوزة للطلبات التي لم تكتمل بعد.
 int? get reserved;/// إجمالي الكمية التي تم بيعها.
 int? get sold;/// تاريخ آخر تحديث لمعلومات المخزون.
 DateTime? get lastUpdated;/// قائمة بالموردين الذين يوفرون هذا المنتج.
 List<String>? get suppliers;/// الموقع الفعلي للمنتج في المستودع (مثل: "الرف A، القسم 3").
 String? get warehouseLocation;/// الحد الأدنى لكمية المخزون قبل إعادة الطلب.
 int? get reorderLevel;/// الكمية المثلى لإعادة الطلب.
 int? get reorderQuantity;/// تكلفة الوحدة الواحدة من المنتج.
 double? get costPerUnit;/// ملاحظات إضافية حول حالة المخزون.
 String? get notes;
/// Create a copy of InventoryManagementModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InventoryManagementModelCopyWith<InventoryManagementModel> get copyWith => _$InventoryManagementModelCopyWithImpl<InventoryManagementModel>(this as InventoryManagementModel, _$identity);

  /// Serializes this InventoryManagementModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InventoryManagementModel&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.inStock, inStock) || other.inStock == inStock)&&(identical(other.reserved, reserved) || other.reserved == reserved)&&(identical(other.sold, sold) || other.sold == sold)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&const DeepCollectionEquality().equals(other.suppliers, suppliers)&&(identical(other.warehouseLocation, warehouseLocation) || other.warehouseLocation == warehouseLocation)&&(identical(other.reorderLevel, reorderLevel) || other.reorderLevel == reorderLevel)&&(identical(other.reorderQuantity, reorderQuantity) || other.reorderQuantity == reorderQuantity)&&(identical(other.costPerUnit, costPerUnit) || other.costPerUnit == costPerUnit)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,inStock,reserved,sold,lastUpdated,const DeepCollectionEquality().hash(suppliers),warehouseLocation,reorderLevel,reorderQuantity,costPerUnit,notes);

@override
String toString() {
  return 'InventoryManagementModel(productId: $productId, productName: $productName, inStock: $inStock, reserved: $reserved, sold: $sold, lastUpdated: $lastUpdated, suppliers: $suppliers, warehouseLocation: $warehouseLocation, reorderLevel: $reorderLevel, reorderQuantity: $reorderQuantity, costPerUnit: $costPerUnit, notes: $notes)';
}


}

/// @nodoc
abstract mixin class $InventoryManagementModelCopyWith<$Res>  {
  factory $InventoryManagementModelCopyWith(InventoryManagementModel value, $Res Function(InventoryManagementModel) _then) = _$InventoryManagementModelCopyWithImpl;
@useResult
$Res call({
 String? productId, String? productName, int? inStock, int? reserved, int? sold, DateTime? lastUpdated, List<String>? suppliers, String? warehouseLocation, int? reorderLevel, int? reorderQuantity, double? costPerUnit, String? notes
});




}
/// @nodoc
class _$InventoryManagementModelCopyWithImpl<$Res>
    implements $InventoryManagementModelCopyWith<$Res> {
  _$InventoryManagementModelCopyWithImpl(this._self, this._then);

  final InventoryManagementModel _self;
  final $Res Function(InventoryManagementModel) _then;

/// Create a copy of InventoryManagementModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = freezed,Object? productName = freezed,Object? inStock = freezed,Object? reserved = freezed,Object? sold = freezed,Object? lastUpdated = freezed,Object? suppliers = freezed,Object? warehouseLocation = freezed,Object? reorderLevel = freezed,Object? reorderQuantity = freezed,Object? costPerUnit = freezed,Object? notes = freezed,}) {
  return _then(_self.copyWith(
productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,productName: freezed == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String?,inStock: freezed == inStock ? _self.inStock : inStock // ignore: cast_nullable_to_non_nullable
as int?,reserved: freezed == reserved ? _self.reserved : reserved // ignore: cast_nullable_to_non_nullable
as int?,sold: freezed == sold ? _self.sold : sold // ignore: cast_nullable_to_non_nullable
as int?,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,suppliers: freezed == suppliers ? _self.suppliers : suppliers // ignore: cast_nullable_to_non_nullable
as List<String>?,warehouseLocation: freezed == warehouseLocation ? _self.warehouseLocation : warehouseLocation // ignore: cast_nullable_to_non_nullable
as String?,reorderLevel: freezed == reorderLevel ? _self.reorderLevel : reorderLevel // ignore: cast_nullable_to_non_nullable
as int?,reorderQuantity: freezed == reorderQuantity ? _self.reorderQuantity : reorderQuantity // ignore: cast_nullable_to_non_nullable
as int?,costPerUnit: freezed == costPerUnit ? _self.costPerUnit : costPerUnit // ignore: cast_nullable_to_non_nullable
as double?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [InventoryManagementModel].
extension InventoryManagementModelPatterns on InventoryManagementModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _InventoryManagementModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _InventoryManagementModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _InventoryManagementModel value)  $default,){
final _that = this;
switch (_that) {
case _InventoryManagementModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _InventoryManagementModel value)?  $default,){
final _that = this;
switch (_that) {
case _InventoryManagementModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? productId,  String? productName,  int? inStock,  int? reserved,  int? sold,  DateTime? lastUpdated,  List<String>? suppliers,  String? warehouseLocation,  int? reorderLevel,  int? reorderQuantity,  double? costPerUnit,  String? notes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _InventoryManagementModel() when $default != null:
return $default(_that.productId,_that.productName,_that.inStock,_that.reserved,_that.sold,_that.lastUpdated,_that.suppliers,_that.warehouseLocation,_that.reorderLevel,_that.reorderQuantity,_that.costPerUnit,_that.notes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? productId,  String? productName,  int? inStock,  int? reserved,  int? sold,  DateTime? lastUpdated,  List<String>? suppliers,  String? warehouseLocation,  int? reorderLevel,  int? reorderQuantity,  double? costPerUnit,  String? notes)  $default,) {final _that = this;
switch (_that) {
case _InventoryManagementModel():
return $default(_that.productId,_that.productName,_that.inStock,_that.reserved,_that.sold,_that.lastUpdated,_that.suppliers,_that.warehouseLocation,_that.reorderLevel,_that.reorderQuantity,_that.costPerUnit,_that.notes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? productId,  String? productName,  int? inStock,  int? reserved,  int? sold,  DateTime? lastUpdated,  List<String>? suppliers,  String? warehouseLocation,  int? reorderLevel,  int? reorderQuantity,  double? costPerUnit,  String? notes)?  $default,) {final _that = this;
switch (_that) {
case _InventoryManagementModel() when $default != null:
return $default(_that.productId,_that.productName,_that.inStock,_that.reserved,_that.sold,_that.lastUpdated,_that.suppliers,_that.warehouseLocation,_that.reorderLevel,_that.reorderQuantity,_that.costPerUnit,_that.notes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _InventoryManagementModel implements InventoryManagementModel {
  const _InventoryManagementModel({this.productId, this.productName, this.inStock, this.reserved, this.sold, this.lastUpdated, final  List<String>? suppliers, this.warehouseLocation, this.reorderLevel, this.reorderQuantity, this.costPerUnit, this.notes}): _suppliers = suppliers;
  factory _InventoryManagementModel.fromJson(Map<String, dynamic> json) => _$InventoryManagementModelFromJson(json);

/// المعرف الفريد للمنتج.
@override final  String? productId;
/// اسم المنتج.
@override final  String? productName;
/// إجمالي الكمية المتاحة حاليًا في المخزون.
@override final  int? inStock;
/// الكمية المحجوزة للطلبات التي لم تكتمل بعد.
@override final  int? reserved;
/// إجمالي الكمية التي تم بيعها.
@override final  int? sold;
/// تاريخ آخر تحديث لمعلومات المخزون.
@override final  DateTime? lastUpdated;
/// قائمة بالموردين الذين يوفرون هذا المنتج.
 final  List<String>? _suppliers;
/// قائمة بالموردين الذين يوفرون هذا المنتج.
@override List<String>? get suppliers {
  final value = _suppliers;
  if (value == null) return null;
  if (_suppliers is EqualUnmodifiableListView) return _suppliers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

/// الموقع الفعلي للمنتج في المستودع (مثل: "الرف A، القسم 3").
@override final  String? warehouseLocation;
/// الحد الأدنى لكمية المخزون قبل إعادة الطلب.
@override final  int? reorderLevel;
/// الكمية المثلى لإعادة الطلب.
@override final  int? reorderQuantity;
/// تكلفة الوحدة الواحدة من المنتج.
@override final  double? costPerUnit;
/// ملاحظات إضافية حول حالة المخزون.
@override final  String? notes;

/// Create a copy of InventoryManagementModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InventoryManagementModelCopyWith<_InventoryManagementModel> get copyWith => __$InventoryManagementModelCopyWithImpl<_InventoryManagementModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InventoryManagementModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InventoryManagementModel&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.inStock, inStock) || other.inStock == inStock)&&(identical(other.reserved, reserved) || other.reserved == reserved)&&(identical(other.sold, sold) || other.sold == sold)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&const DeepCollectionEquality().equals(other._suppliers, _suppliers)&&(identical(other.warehouseLocation, warehouseLocation) || other.warehouseLocation == warehouseLocation)&&(identical(other.reorderLevel, reorderLevel) || other.reorderLevel == reorderLevel)&&(identical(other.reorderQuantity, reorderQuantity) || other.reorderQuantity == reorderQuantity)&&(identical(other.costPerUnit, costPerUnit) || other.costPerUnit == costPerUnit)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,inStock,reserved,sold,lastUpdated,const DeepCollectionEquality().hash(_suppliers),warehouseLocation,reorderLevel,reorderQuantity,costPerUnit,notes);

@override
String toString() {
  return 'InventoryManagementModel(productId: $productId, productName: $productName, inStock: $inStock, reserved: $reserved, sold: $sold, lastUpdated: $lastUpdated, suppliers: $suppliers, warehouseLocation: $warehouseLocation, reorderLevel: $reorderLevel, reorderQuantity: $reorderQuantity, costPerUnit: $costPerUnit, notes: $notes)';
}


}

/// @nodoc
abstract mixin class _$InventoryManagementModelCopyWith<$Res> implements $InventoryManagementModelCopyWith<$Res> {
  factory _$InventoryManagementModelCopyWith(_InventoryManagementModel value, $Res Function(_InventoryManagementModel) _then) = __$InventoryManagementModelCopyWithImpl;
@override @useResult
$Res call({
 String? productId, String? productName, int? inStock, int? reserved, int? sold, DateTime? lastUpdated, List<String>? suppliers, String? warehouseLocation, int? reorderLevel, int? reorderQuantity, double? costPerUnit, String? notes
});




}
/// @nodoc
class __$InventoryManagementModelCopyWithImpl<$Res>
    implements _$InventoryManagementModelCopyWith<$Res> {
  __$InventoryManagementModelCopyWithImpl(this._self, this._then);

  final _InventoryManagementModel _self;
  final $Res Function(_InventoryManagementModel) _then;

/// Create a copy of InventoryManagementModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = freezed,Object? productName = freezed,Object? inStock = freezed,Object? reserved = freezed,Object? sold = freezed,Object? lastUpdated = freezed,Object? suppliers = freezed,Object? warehouseLocation = freezed,Object? reorderLevel = freezed,Object? reorderQuantity = freezed,Object? costPerUnit = freezed,Object? notes = freezed,}) {
  return _then(_InventoryManagementModel(
productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,productName: freezed == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String?,inStock: freezed == inStock ? _self.inStock : inStock // ignore: cast_nullable_to_non_nullable
as int?,reserved: freezed == reserved ? _self.reserved : reserved // ignore: cast_nullable_to_non_nullable
as int?,sold: freezed == sold ? _self.sold : sold // ignore: cast_nullable_to_non_nullable
as int?,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,suppliers: freezed == suppliers ? _self._suppliers : suppliers // ignore: cast_nullable_to_non_nullable
as List<String>?,warehouseLocation: freezed == warehouseLocation ? _self.warehouseLocation : warehouseLocation // ignore: cast_nullable_to_non_nullable
as String?,reorderLevel: freezed == reorderLevel ? _self.reorderLevel : reorderLevel // ignore: cast_nullable_to_non_nullable
as int?,reorderQuantity: freezed == reorderQuantity ? _self.reorderQuantity : reorderQuantity // ignore: cast_nullable_to_non_nullable
as int?,costPerUnit: freezed == costPerUnit ? _self.costPerUnit : costPerUnit // ignore: cast_nullable_to_non_nullable
as double?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
