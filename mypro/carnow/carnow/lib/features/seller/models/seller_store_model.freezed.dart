// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_store_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerStoreModel {

 int? get id; int? get sellerId; String? get name; String? get description; String? get logoUrl; String? get bannerUrl; String? get phone; String? get email; String? get address; bool get isVerified; Map<String, dynamic> get settings; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of SellerStoreModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerStoreModelCopyWith<SellerStoreModel> get copyWith => _$SellerStoreModelCopyWithImpl<SellerStoreModel>(this as SellerStoreModel, _$identity);

  /// Serializes this SellerStoreModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerStoreModel&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.logoUrl, logoUrl) || other.logoUrl == logoUrl)&&(identical(other.bannerUrl, bannerUrl) || other.bannerUrl == bannerUrl)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.address, address) || other.address == address)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&const DeepCollectionEquality().equals(other.settings, settings)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,name,description,logoUrl,bannerUrl,phone,email,address,isVerified,const DeepCollectionEquality().hash(settings),createdAt,updatedAt);

@override
String toString() {
  return 'SellerStoreModel(id: $id, sellerId: $sellerId, name: $name, description: $description, logoUrl: $logoUrl, bannerUrl: $bannerUrl, phone: $phone, email: $email, address: $address, isVerified: $isVerified, settings: $settings, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SellerStoreModelCopyWith<$Res>  {
  factory $SellerStoreModelCopyWith(SellerStoreModel value, $Res Function(SellerStoreModel) _then) = _$SellerStoreModelCopyWithImpl;
@useResult
$Res call({
 int? id, int? sellerId, String? name, String? description, String? logoUrl, String? bannerUrl, String? phone, String? email, String? address, bool isVerified, Map<String, dynamic> settings, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$SellerStoreModelCopyWithImpl<$Res>
    implements $SellerStoreModelCopyWith<$Res> {
  _$SellerStoreModelCopyWithImpl(this._self, this._then);

  final SellerStoreModel _self;
  final $Res Function(SellerStoreModel) _then;

/// Create a copy of SellerStoreModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? sellerId = freezed,Object? name = freezed,Object? description = freezed,Object? logoUrl = freezed,Object? bannerUrl = freezed,Object? phone = freezed,Object? email = freezed,Object? address = freezed,Object? isVerified = null,Object? settings = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,logoUrl: freezed == logoUrl ? _self.logoUrl : logoUrl // ignore: cast_nullable_to_non_nullable
as String?,bannerUrl: freezed == bannerUrl ? _self.bannerUrl : bannerUrl // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,settings: null == settings ? _self.settings : settings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerStoreModel].
extension SellerStoreModelPatterns on SellerStoreModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerStoreModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerStoreModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerStoreModel value)  $default,){
final _that = this;
switch (_that) {
case _SellerStoreModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerStoreModel value)?  $default,){
final _that = this;
switch (_that) {
case _SellerStoreModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  int? sellerId,  String? name,  String? description,  String? logoUrl,  String? bannerUrl,  String? phone,  String? email,  String? address,  bool isVerified,  Map<String, dynamic> settings,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerStoreModel() when $default != null:
return $default(_that.id,_that.sellerId,_that.name,_that.description,_that.logoUrl,_that.bannerUrl,_that.phone,_that.email,_that.address,_that.isVerified,_that.settings,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  int? sellerId,  String? name,  String? description,  String? logoUrl,  String? bannerUrl,  String? phone,  String? email,  String? address,  bool isVerified,  Map<String, dynamic> settings,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SellerStoreModel():
return $default(_that.id,_that.sellerId,_that.name,_that.description,_that.logoUrl,_that.bannerUrl,_that.phone,_that.email,_that.address,_that.isVerified,_that.settings,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  int? sellerId,  String? name,  String? description,  String? logoUrl,  String? bannerUrl,  String? phone,  String? email,  String? address,  bool isVerified,  Map<String, dynamic> settings,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerStoreModel() when $default != null:
return $default(_that.id,_that.sellerId,_that.name,_that.description,_that.logoUrl,_that.bannerUrl,_that.phone,_that.email,_that.address,_that.isVerified,_that.settings,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SellerStoreModel implements SellerStoreModel {
  const _SellerStoreModel({this.id, this.sellerId, this.name, this.description, this.logoUrl, this.bannerUrl, this.phone, this.email, this.address, this.isVerified = false, final  Map<String, dynamic> settings = const {}, this.createdAt, this.updatedAt}): _settings = settings;
  factory _SellerStoreModel.fromJson(Map<String, dynamic> json) => _$SellerStoreModelFromJson(json);

@override final  int? id;
@override final  int? sellerId;
@override final  String? name;
@override final  String? description;
@override final  String? logoUrl;
@override final  String? bannerUrl;
@override final  String? phone;
@override final  String? email;
@override final  String? address;
@override@JsonKey() final  bool isVerified;
 final  Map<String, dynamic> _settings;
@override@JsonKey() Map<String, dynamic> get settings {
  if (_settings is EqualUnmodifiableMapView) return _settings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_settings);
}

@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of SellerStoreModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerStoreModelCopyWith<_SellerStoreModel> get copyWith => __$SellerStoreModelCopyWithImpl<_SellerStoreModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerStoreModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerStoreModel&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.logoUrl, logoUrl) || other.logoUrl == logoUrl)&&(identical(other.bannerUrl, bannerUrl) || other.bannerUrl == bannerUrl)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.address, address) || other.address == address)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&const DeepCollectionEquality().equals(other._settings, _settings)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,name,description,logoUrl,bannerUrl,phone,email,address,isVerified,const DeepCollectionEquality().hash(_settings),createdAt,updatedAt);

@override
String toString() {
  return 'SellerStoreModel(id: $id, sellerId: $sellerId, name: $name, description: $description, logoUrl: $logoUrl, bannerUrl: $bannerUrl, phone: $phone, email: $email, address: $address, isVerified: $isVerified, settings: $settings, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SellerStoreModelCopyWith<$Res> implements $SellerStoreModelCopyWith<$Res> {
  factory _$SellerStoreModelCopyWith(_SellerStoreModel value, $Res Function(_SellerStoreModel) _then) = __$SellerStoreModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, int? sellerId, String? name, String? description, String? logoUrl, String? bannerUrl, String? phone, String? email, String? address, bool isVerified, Map<String, dynamic> settings, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$SellerStoreModelCopyWithImpl<$Res>
    implements _$SellerStoreModelCopyWith<$Res> {
  __$SellerStoreModelCopyWithImpl(this._self, this._then);

  final _SellerStoreModel _self;
  final $Res Function(_SellerStoreModel) _then;

/// Create a copy of SellerStoreModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? sellerId = freezed,Object? name = freezed,Object? description = freezed,Object? logoUrl = freezed,Object? bannerUrl = freezed,Object? phone = freezed,Object? email = freezed,Object? address = freezed,Object? isVerified = null,Object? settings = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SellerStoreModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,logoUrl: freezed == logoUrl ? _self.logoUrl : logoUrl // ignore: cast_nullable_to_non_nullable
as String?,bannerUrl: freezed == bannerUrl ? _self.bannerUrl : bannerUrl // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,settings: null == settings ? _self._settings : settings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
