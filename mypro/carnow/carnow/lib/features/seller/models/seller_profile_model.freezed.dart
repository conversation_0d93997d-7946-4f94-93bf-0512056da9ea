// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_profile_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerProfile {

 String get id; String get userId; String get fullName; String get storeName; String get email; String get phone; String? get whatsappNumber; String get city; String get address; String? get businessDescription; String? get businessType; String? get businessLicense; String? get taxNumber; List<String>? get businessDocuments; String? get profileImageUrl; String? get storeLogoUrl; bool get isVerified; bool get isActive; double? get rating; int? get totalSales; int? get totalProducts;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of SellerProfile
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerProfileCopyWith<SellerProfile> get copyWith => _$SellerProfileCopyWithImpl<SellerProfile>(this as SellerProfile, _$identity);

  /// Serializes this SellerProfile to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerProfile&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.email, email) || other.email == email)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.city, city) || other.city == city)&&(identical(other.address, address) || other.address == address)&&(identical(other.businessDescription, businessDescription) || other.businessDescription == businessDescription)&&(identical(other.businessType, businessType) || other.businessType == businessType)&&(identical(other.businessLicense, businessLicense) || other.businessLicense == businessLicense)&&(identical(other.taxNumber, taxNumber) || other.taxNumber == taxNumber)&&const DeepCollectionEquality().equals(other.businessDocuments, businessDocuments)&&(identical(other.profileImageUrl, profileImageUrl) || other.profileImageUrl == profileImageUrl)&&(identical(other.storeLogoUrl, storeLogoUrl) || other.storeLogoUrl == storeLogoUrl)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,fullName,storeName,email,phone,whatsappNumber,city,address,businessDescription,businessType,businessLicense,taxNumber,const DeepCollectionEquality().hash(businessDocuments),profileImageUrl,storeLogoUrl,isVerified,isActive,rating,totalSales,totalProducts,createdAt,updatedAt]);

@override
String toString() {
  return 'SellerProfile(id: $id, userId: $userId, fullName: $fullName, storeName: $storeName, email: $email, phone: $phone, whatsappNumber: $whatsappNumber, city: $city, address: $address, businessDescription: $businessDescription, businessType: $businessType, businessLicense: $businessLicense, taxNumber: $taxNumber, businessDocuments: $businessDocuments, profileImageUrl: $profileImageUrl, storeLogoUrl: $storeLogoUrl, isVerified: $isVerified, isActive: $isActive, rating: $rating, totalSales: $totalSales, totalProducts: $totalProducts, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SellerProfileCopyWith<$Res>  {
  factory $SellerProfileCopyWith(SellerProfile value, $Res Function(SellerProfile) _then) = _$SellerProfileCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String fullName, String storeName, String email, String phone, String? whatsappNumber, String city, String address, String? businessDescription, String? businessType, String? businessLicense, String? taxNumber, List<String>? businessDocuments, String? profileImageUrl, String? storeLogoUrl, bool isVerified, bool isActive, double? rating, int? totalSales, int? totalProducts,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$SellerProfileCopyWithImpl<$Res>
    implements $SellerProfileCopyWith<$Res> {
  _$SellerProfileCopyWithImpl(this._self, this._then);

  final SellerProfile _self;
  final $Res Function(SellerProfile) _then;

/// Create a copy of SellerProfile
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? fullName = null,Object? storeName = null,Object? email = null,Object? phone = null,Object? whatsappNumber = freezed,Object? city = null,Object? address = null,Object? businessDescription = freezed,Object? businessType = freezed,Object? businessLicense = freezed,Object? taxNumber = freezed,Object? businessDocuments = freezed,Object? profileImageUrl = freezed,Object? storeLogoUrl = freezed,Object? isVerified = null,Object? isActive = null,Object? rating = freezed,Object? totalSales = freezed,Object? totalProducts = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,storeName: null == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,whatsappNumber: freezed == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String?,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,businessDescription: freezed == businessDescription ? _self.businessDescription : businessDescription // ignore: cast_nullable_to_non_nullable
as String?,businessType: freezed == businessType ? _self.businessType : businessType // ignore: cast_nullable_to_non_nullable
as String?,businessLicense: freezed == businessLicense ? _self.businessLicense : businessLicense // ignore: cast_nullable_to_non_nullable
as String?,taxNumber: freezed == taxNumber ? _self.taxNumber : taxNumber // ignore: cast_nullable_to_non_nullable
as String?,businessDocuments: freezed == businessDocuments ? _self.businessDocuments : businessDocuments // ignore: cast_nullable_to_non_nullable
as List<String>?,profileImageUrl: freezed == profileImageUrl ? _self.profileImageUrl : profileImageUrl // ignore: cast_nullable_to_non_nullable
as String?,storeLogoUrl: freezed == storeLogoUrl ? _self.storeLogoUrl : storeLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double?,totalSales: freezed == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as int?,totalProducts: freezed == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerProfile].
extension SellerProfilePatterns on SellerProfile {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerProfile value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerProfile() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerProfile value)  $default,){
final _that = this;
switch (_that) {
case _SellerProfile():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerProfile value)?  $default,){
final _that = this;
switch (_that) {
case _SellerProfile() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String fullName,  String storeName,  String email,  String phone,  String? whatsappNumber,  String city,  String address,  String? businessDescription,  String? businessType,  String? businessLicense,  String? taxNumber,  List<String>? businessDocuments,  String? profileImageUrl,  String? storeLogoUrl,  bool isVerified,  bool isActive,  double? rating,  int? totalSales,  int? totalProducts, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerProfile() when $default != null:
return $default(_that.id,_that.userId,_that.fullName,_that.storeName,_that.email,_that.phone,_that.whatsappNumber,_that.city,_that.address,_that.businessDescription,_that.businessType,_that.businessLicense,_that.taxNumber,_that.businessDocuments,_that.profileImageUrl,_that.storeLogoUrl,_that.isVerified,_that.isActive,_that.rating,_that.totalSales,_that.totalProducts,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String fullName,  String storeName,  String email,  String phone,  String? whatsappNumber,  String city,  String address,  String? businessDescription,  String? businessType,  String? businessLicense,  String? taxNumber,  List<String>? businessDocuments,  String? profileImageUrl,  String? storeLogoUrl,  bool isVerified,  bool isActive,  double? rating,  int? totalSales,  int? totalProducts, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SellerProfile():
return $default(_that.id,_that.userId,_that.fullName,_that.storeName,_that.email,_that.phone,_that.whatsappNumber,_that.city,_that.address,_that.businessDescription,_that.businessType,_that.businessLicense,_that.taxNumber,_that.businessDocuments,_that.profileImageUrl,_that.storeLogoUrl,_that.isVerified,_that.isActive,_that.rating,_that.totalSales,_that.totalProducts,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String fullName,  String storeName,  String email,  String phone,  String? whatsappNumber,  String city,  String address,  String? businessDescription,  String? businessType,  String? businessLicense,  String? taxNumber,  List<String>? businessDocuments,  String? profileImageUrl,  String? storeLogoUrl,  bool isVerified,  bool isActive,  double? rating,  int? totalSales,  int? totalProducts, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerProfile() when $default != null:
return $default(_that.id,_that.userId,_that.fullName,_that.storeName,_that.email,_that.phone,_that.whatsappNumber,_that.city,_that.address,_that.businessDescription,_that.businessType,_that.businessLicense,_that.taxNumber,_that.businessDocuments,_that.profileImageUrl,_that.storeLogoUrl,_that.isVerified,_that.isActive,_that.rating,_that.totalSales,_that.totalProducts,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SellerProfile implements SellerProfile {
  const _SellerProfile({required this.id, required this.userId, required this.fullName, required this.storeName, required this.email, required this.phone, this.whatsappNumber, required this.city, required this.address, this.businessDescription, this.businessType, this.businessLicense, this.taxNumber, final  List<String>? businessDocuments, this.profileImageUrl, this.storeLogoUrl, this.isVerified = false, this.isActive = false, this.rating, this.totalSales, this.totalProducts, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _businessDocuments = businessDocuments;
  factory _SellerProfile.fromJson(Map<String, dynamic> json) => _$SellerProfileFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String fullName;
@override final  String storeName;
@override final  String email;
@override final  String phone;
@override final  String? whatsappNumber;
@override final  String city;
@override final  String address;
@override final  String? businessDescription;
@override final  String? businessType;
@override final  String? businessLicense;
@override final  String? taxNumber;
 final  List<String>? _businessDocuments;
@override List<String>? get businessDocuments {
  final value = _businessDocuments;
  if (value == null) return null;
  if (_businessDocuments is EqualUnmodifiableListView) return _businessDocuments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? profileImageUrl;
@override final  String? storeLogoUrl;
@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isActive;
@override final  double? rating;
@override final  int? totalSales;
@override final  int? totalProducts;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of SellerProfile
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerProfileCopyWith<_SellerProfile> get copyWith => __$SellerProfileCopyWithImpl<_SellerProfile>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerProfileToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerProfile&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.email, email) || other.email == email)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.city, city) || other.city == city)&&(identical(other.address, address) || other.address == address)&&(identical(other.businessDescription, businessDescription) || other.businessDescription == businessDescription)&&(identical(other.businessType, businessType) || other.businessType == businessType)&&(identical(other.businessLicense, businessLicense) || other.businessLicense == businessLicense)&&(identical(other.taxNumber, taxNumber) || other.taxNumber == taxNumber)&&const DeepCollectionEquality().equals(other._businessDocuments, _businessDocuments)&&(identical(other.profileImageUrl, profileImageUrl) || other.profileImageUrl == profileImageUrl)&&(identical(other.storeLogoUrl, storeLogoUrl) || other.storeLogoUrl == storeLogoUrl)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,fullName,storeName,email,phone,whatsappNumber,city,address,businessDescription,businessType,businessLicense,taxNumber,const DeepCollectionEquality().hash(_businessDocuments),profileImageUrl,storeLogoUrl,isVerified,isActive,rating,totalSales,totalProducts,createdAt,updatedAt]);

@override
String toString() {
  return 'SellerProfile(id: $id, userId: $userId, fullName: $fullName, storeName: $storeName, email: $email, phone: $phone, whatsappNumber: $whatsappNumber, city: $city, address: $address, businessDescription: $businessDescription, businessType: $businessType, businessLicense: $businessLicense, taxNumber: $taxNumber, businessDocuments: $businessDocuments, profileImageUrl: $profileImageUrl, storeLogoUrl: $storeLogoUrl, isVerified: $isVerified, isActive: $isActive, rating: $rating, totalSales: $totalSales, totalProducts: $totalProducts, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SellerProfileCopyWith<$Res> implements $SellerProfileCopyWith<$Res> {
  factory _$SellerProfileCopyWith(_SellerProfile value, $Res Function(_SellerProfile) _then) = __$SellerProfileCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String fullName, String storeName, String email, String phone, String? whatsappNumber, String city, String address, String? businessDescription, String? businessType, String? businessLicense, String? taxNumber, List<String>? businessDocuments, String? profileImageUrl, String? storeLogoUrl, bool isVerified, bool isActive, double? rating, int? totalSales, int? totalProducts,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$SellerProfileCopyWithImpl<$Res>
    implements _$SellerProfileCopyWith<$Res> {
  __$SellerProfileCopyWithImpl(this._self, this._then);

  final _SellerProfile _self;
  final $Res Function(_SellerProfile) _then;

/// Create a copy of SellerProfile
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? fullName = null,Object? storeName = null,Object? email = null,Object? phone = null,Object? whatsappNumber = freezed,Object? city = null,Object? address = null,Object? businessDescription = freezed,Object? businessType = freezed,Object? businessLicense = freezed,Object? taxNumber = freezed,Object? businessDocuments = freezed,Object? profileImageUrl = freezed,Object? storeLogoUrl = freezed,Object? isVerified = null,Object? isActive = null,Object? rating = freezed,Object? totalSales = freezed,Object? totalProducts = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SellerProfile(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,storeName: null == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,whatsappNumber: freezed == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String?,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,businessDescription: freezed == businessDescription ? _self.businessDescription : businessDescription // ignore: cast_nullable_to_non_nullable
as String?,businessType: freezed == businessType ? _self.businessType : businessType // ignore: cast_nullable_to_non_nullable
as String?,businessLicense: freezed == businessLicense ? _self.businessLicense : businessLicense // ignore: cast_nullable_to_non_nullable
as String?,taxNumber: freezed == taxNumber ? _self.taxNumber : taxNumber // ignore: cast_nullable_to_non_nullable
as String?,businessDocuments: freezed == businessDocuments ? _self._businessDocuments : businessDocuments // ignore: cast_nullable_to_non_nullable
as List<String>?,profileImageUrl: freezed == profileImageUrl ? _self.profileImageUrl : profileImageUrl // ignore: cast_nullable_to_non_nullable
as String?,storeLogoUrl: freezed == storeLogoUrl ? _self.storeLogoUrl : storeLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double?,totalSales: freezed == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as int?,totalProducts: freezed == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$EnhancedSubscriptionRequest {

 String get id; String get sellerId; String get planId; String get requestedTierName; String get billingCycleName; SubscriptionRequestStatus get status; String get statusName; DateTime get requestDate; DateTime? get approvedDate; DateTime? get rejectedDate; String? get adminId; String? get adminNotes; String? get rejectionReason; double get requestedPriceLD; String? get paymentMethodId; int? get priority;// معلومات البائع التفصيلية
 String get sellerFullName; String get sellerStoreName; String get sellerEmail; String get sellerPhone; String? get sellerWhatsapp; String get sellerCity; String get sellerAddress; String? get sellerBusinessType; String? get sellerBusinessDescription; String? get sellerProfileImageUrl; String? get sellerStoreLogoUrl;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of EnhancedSubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EnhancedSubscriptionRequestCopyWith<EnhancedSubscriptionRequest> get copyWith => _$EnhancedSubscriptionRequestCopyWithImpl<EnhancedSubscriptionRequest>(this as EnhancedSubscriptionRequest, _$identity);

  /// Serializes this EnhancedSubscriptionRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EnhancedSubscriptionRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.planId, planId) || other.planId == planId)&&(identical(other.requestedTierName, requestedTierName) || other.requestedTierName == requestedTierName)&&(identical(other.billingCycleName, billingCycleName) || other.billingCycleName == billingCycleName)&&(identical(other.status, status) || other.status == status)&&(identical(other.statusName, statusName) || other.statusName == statusName)&&(identical(other.requestDate, requestDate) || other.requestDate == requestDate)&&(identical(other.approvedDate, approvedDate) || other.approvedDate == approvedDate)&&(identical(other.rejectedDate, rejectedDate) || other.rejectedDate == rejectedDate)&&(identical(other.adminId, adminId) || other.adminId == adminId)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.requestedPriceLD, requestedPriceLD) || other.requestedPriceLD == requestedPriceLD)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.sellerFullName, sellerFullName) || other.sellerFullName == sellerFullName)&&(identical(other.sellerStoreName, sellerStoreName) || other.sellerStoreName == sellerStoreName)&&(identical(other.sellerEmail, sellerEmail) || other.sellerEmail == sellerEmail)&&(identical(other.sellerPhone, sellerPhone) || other.sellerPhone == sellerPhone)&&(identical(other.sellerWhatsapp, sellerWhatsapp) || other.sellerWhatsapp == sellerWhatsapp)&&(identical(other.sellerCity, sellerCity) || other.sellerCity == sellerCity)&&(identical(other.sellerAddress, sellerAddress) || other.sellerAddress == sellerAddress)&&(identical(other.sellerBusinessType, sellerBusinessType) || other.sellerBusinessType == sellerBusinessType)&&(identical(other.sellerBusinessDescription, sellerBusinessDescription) || other.sellerBusinessDescription == sellerBusinessDescription)&&(identical(other.sellerProfileImageUrl, sellerProfileImageUrl) || other.sellerProfileImageUrl == sellerProfileImageUrl)&&(identical(other.sellerStoreLogoUrl, sellerStoreLogoUrl) || other.sellerStoreLogoUrl == sellerStoreLogoUrl)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,planId,requestedTierName,billingCycleName,status,statusName,requestDate,approvedDate,rejectedDate,adminId,adminNotes,rejectionReason,requestedPriceLD,paymentMethodId,priority,sellerFullName,sellerStoreName,sellerEmail,sellerPhone,sellerWhatsapp,sellerCity,sellerAddress,sellerBusinessType,sellerBusinessDescription,sellerProfileImageUrl,sellerStoreLogoUrl,createdAt,updatedAt]);

@override
String toString() {
  return 'EnhancedSubscriptionRequest(id: $id, sellerId: $sellerId, planId: $planId, requestedTierName: $requestedTierName, billingCycleName: $billingCycleName, status: $status, statusName: $statusName, requestDate: $requestDate, approvedDate: $approvedDate, rejectedDate: $rejectedDate, adminId: $adminId, adminNotes: $adminNotes, rejectionReason: $rejectionReason, requestedPriceLD: $requestedPriceLD, paymentMethodId: $paymentMethodId, priority: $priority, sellerFullName: $sellerFullName, sellerStoreName: $sellerStoreName, sellerEmail: $sellerEmail, sellerPhone: $sellerPhone, sellerWhatsapp: $sellerWhatsapp, sellerCity: $sellerCity, sellerAddress: $sellerAddress, sellerBusinessType: $sellerBusinessType, sellerBusinessDescription: $sellerBusinessDescription, sellerProfileImageUrl: $sellerProfileImageUrl, sellerStoreLogoUrl: $sellerStoreLogoUrl, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $EnhancedSubscriptionRequestCopyWith<$Res>  {
  factory $EnhancedSubscriptionRequestCopyWith(EnhancedSubscriptionRequest value, $Res Function(EnhancedSubscriptionRequest) _then) = _$EnhancedSubscriptionRequestCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String planId, String requestedTierName, String billingCycleName, SubscriptionRequestStatus status, String statusName, DateTime requestDate, DateTime? approvedDate, DateTime? rejectedDate, String? adminId, String? adminNotes, String? rejectionReason, double requestedPriceLD, String? paymentMethodId, int? priority, String sellerFullName, String sellerStoreName, String sellerEmail, String sellerPhone, String? sellerWhatsapp, String sellerCity, String sellerAddress, String? sellerBusinessType, String? sellerBusinessDescription, String? sellerProfileImageUrl, String? sellerStoreLogoUrl,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$EnhancedSubscriptionRequestCopyWithImpl<$Res>
    implements $EnhancedSubscriptionRequestCopyWith<$Res> {
  _$EnhancedSubscriptionRequestCopyWithImpl(this._self, this._then);

  final EnhancedSubscriptionRequest _self;
  final $Res Function(EnhancedSubscriptionRequest) _then;

/// Create a copy of EnhancedSubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? planId = null,Object? requestedTierName = null,Object? billingCycleName = null,Object? status = null,Object? statusName = null,Object? requestDate = null,Object? approvedDate = freezed,Object? rejectedDate = freezed,Object? adminId = freezed,Object? adminNotes = freezed,Object? rejectionReason = freezed,Object? requestedPriceLD = null,Object? paymentMethodId = freezed,Object? priority = freezed,Object? sellerFullName = null,Object? sellerStoreName = null,Object? sellerEmail = null,Object? sellerPhone = null,Object? sellerWhatsapp = freezed,Object? sellerCity = null,Object? sellerAddress = null,Object? sellerBusinessType = freezed,Object? sellerBusinessDescription = freezed,Object? sellerProfileImageUrl = freezed,Object? sellerStoreLogoUrl = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,planId: null == planId ? _self.planId : planId // ignore: cast_nullable_to_non_nullable
as String,requestedTierName: null == requestedTierName ? _self.requestedTierName : requestedTierName // ignore: cast_nullable_to_non_nullable
as String,billingCycleName: null == billingCycleName ? _self.billingCycleName : billingCycleName // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SubscriptionRequestStatus,statusName: null == statusName ? _self.statusName : statusName // ignore: cast_nullable_to_non_nullable
as String,requestDate: null == requestDate ? _self.requestDate : requestDate // ignore: cast_nullable_to_non_nullable
as DateTime,approvedDate: freezed == approvedDate ? _self.approvedDate : approvedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,rejectedDate: freezed == rejectedDate ? _self.rejectedDate : rejectedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,adminId: freezed == adminId ? _self.adminId : adminId // ignore: cast_nullable_to_non_nullable
as String?,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,requestedPriceLD: null == requestedPriceLD ? _self.requestedPriceLD : requestedPriceLD // ignore: cast_nullable_to_non_nullable
as double,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,priority: freezed == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as int?,sellerFullName: null == sellerFullName ? _self.sellerFullName : sellerFullName // ignore: cast_nullable_to_non_nullable
as String,sellerStoreName: null == sellerStoreName ? _self.sellerStoreName : sellerStoreName // ignore: cast_nullable_to_non_nullable
as String,sellerEmail: null == sellerEmail ? _self.sellerEmail : sellerEmail // ignore: cast_nullable_to_non_nullable
as String,sellerPhone: null == sellerPhone ? _self.sellerPhone : sellerPhone // ignore: cast_nullable_to_non_nullable
as String,sellerWhatsapp: freezed == sellerWhatsapp ? _self.sellerWhatsapp : sellerWhatsapp // ignore: cast_nullable_to_non_nullable
as String?,sellerCity: null == sellerCity ? _self.sellerCity : sellerCity // ignore: cast_nullable_to_non_nullable
as String,sellerAddress: null == sellerAddress ? _self.sellerAddress : sellerAddress // ignore: cast_nullable_to_non_nullable
as String,sellerBusinessType: freezed == sellerBusinessType ? _self.sellerBusinessType : sellerBusinessType // ignore: cast_nullable_to_non_nullable
as String?,sellerBusinessDescription: freezed == sellerBusinessDescription ? _self.sellerBusinessDescription : sellerBusinessDescription // ignore: cast_nullable_to_non_nullable
as String?,sellerProfileImageUrl: freezed == sellerProfileImageUrl ? _self.sellerProfileImageUrl : sellerProfileImageUrl // ignore: cast_nullable_to_non_nullable
as String?,sellerStoreLogoUrl: freezed == sellerStoreLogoUrl ? _self.sellerStoreLogoUrl : sellerStoreLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [EnhancedSubscriptionRequest].
extension EnhancedSubscriptionRequestPatterns on EnhancedSubscriptionRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EnhancedSubscriptionRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EnhancedSubscriptionRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EnhancedSubscriptionRequest value)  $default,){
final _that = this;
switch (_that) {
case _EnhancedSubscriptionRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EnhancedSubscriptionRequest value)?  $default,){
final _that = this;
switch (_that) {
case _EnhancedSubscriptionRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String planId,  String requestedTierName,  String billingCycleName,  SubscriptionRequestStatus status,  String statusName,  DateTime requestDate,  DateTime? approvedDate,  DateTime? rejectedDate,  String? adminId,  String? adminNotes,  String? rejectionReason,  double requestedPriceLD,  String? paymentMethodId,  int? priority,  String sellerFullName,  String sellerStoreName,  String sellerEmail,  String sellerPhone,  String? sellerWhatsapp,  String sellerCity,  String sellerAddress,  String? sellerBusinessType,  String? sellerBusinessDescription,  String? sellerProfileImageUrl,  String? sellerStoreLogoUrl, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EnhancedSubscriptionRequest() when $default != null:
return $default(_that.id,_that.sellerId,_that.planId,_that.requestedTierName,_that.billingCycleName,_that.status,_that.statusName,_that.requestDate,_that.approvedDate,_that.rejectedDate,_that.adminId,_that.adminNotes,_that.rejectionReason,_that.requestedPriceLD,_that.paymentMethodId,_that.priority,_that.sellerFullName,_that.sellerStoreName,_that.sellerEmail,_that.sellerPhone,_that.sellerWhatsapp,_that.sellerCity,_that.sellerAddress,_that.sellerBusinessType,_that.sellerBusinessDescription,_that.sellerProfileImageUrl,_that.sellerStoreLogoUrl,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String planId,  String requestedTierName,  String billingCycleName,  SubscriptionRequestStatus status,  String statusName,  DateTime requestDate,  DateTime? approvedDate,  DateTime? rejectedDate,  String? adminId,  String? adminNotes,  String? rejectionReason,  double requestedPriceLD,  String? paymentMethodId,  int? priority,  String sellerFullName,  String sellerStoreName,  String sellerEmail,  String sellerPhone,  String? sellerWhatsapp,  String sellerCity,  String sellerAddress,  String? sellerBusinessType,  String? sellerBusinessDescription,  String? sellerProfileImageUrl,  String? sellerStoreLogoUrl, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _EnhancedSubscriptionRequest():
return $default(_that.id,_that.sellerId,_that.planId,_that.requestedTierName,_that.billingCycleName,_that.status,_that.statusName,_that.requestDate,_that.approvedDate,_that.rejectedDate,_that.adminId,_that.adminNotes,_that.rejectionReason,_that.requestedPriceLD,_that.paymentMethodId,_that.priority,_that.sellerFullName,_that.sellerStoreName,_that.sellerEmail,_that.sellerPhone,_that.sellerWhatsapp,_that.sellerCity,_that.sellerAddress,_that.sellerBusinessType,_that.sellerBusinessDescription,_that.sellerProfileImageUrl,_that.sellerStoreLogoUrl,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String planId,  String requestedTierName,  String billingCycleName,  SubscriptionRequestStatus status,  String statusName,  DateTime requestDate,  DateTime? approvedDate,  DateTime? rejectedDate,  String? adminId,  String? adminNotes,  String? rejectionReason,  double requestedPriceLD,  String? paymentMethodId,  int? priority,  String sellerFullName,  String sellerStoreName,  String sellerEmail,  String sellerPhone,  String? sellerWhatsapp,  String sellerCity,  String sellerAddress,  String? sellerBusinessType,  String? sellerBusinessDescription,  String? sellerProfileImageUrl,  String? sellerStoreLogoUrl, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _EnhancedSubscriptionRequest() when $default != null:
return $default(_that.id,_that.sellerId,_that.planId,_that.requestedTierName,_that.billingCycleName,_that.status,_that.statusName,_that.requestDate,_that.approvedDate,_that.rejectedDate,_that.adminId,_that.adminNotes,_that.rejectionReason,_that.requestedPriceLD,_that.paymentMethodId,_that.priority,_that.sellerFullName,_that.sellerStoreName,_that.sellerEmail,_that.sellerPhone,_that.sellerWhatsapp,_that.sellerCity,_that.sellerAddress,_that.sellerBusinessType,_that.sellerBusinessDescription,_that.sellerProfileImageUrl,_that.sellerStoreLogoUrl,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _EnhancedSubscriptionRequest implements EnhancedSubscriptionRequest {
  const _EnhancedSubscriptionRequest({required this.id, required this.sellerId, required this.planId, required this.requestedTierName, required this.billingCycleName, required this.status, required this.statusName, required this.requestDate, this.approvedDate, this.rejectedDate, this.adminId, this.adminNotes, this.rejectionReason, required this.requestedPriceLD, this.paymentMethodId, this.priority, required this.sellerFullName, required this.sellerStoreName, required this.sellerEmail, required this.sellerPhone, this.sellerWhatsapp, required this.sellerCity, required this.sellerAddress, this.sellerBusinessType, this.sellerBusinessDescription, this.sellerProfileImageUrl, this.sellerStoreLogoUrl, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _EnhancedSubscriptionRequest.fromJson(Map<String, dynamic> json) => _$EnhancedSubscriptionRequestFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String planId;
@override final  String requestedTierName;
@override final  String billingCycleName;
@override final  SubscriptionRequestStatus status;
@override final  String statusName;
@override final  DateTime requestDate;
@override final  DateTime? approvedDate;
@override final  DateTime? rejectedDate;
@override final  String? adminId;
@override final  String? adminNotes;
@override final  String? rejectionReason;
@override final  double requestedPriceLD;
@override final  String? paymentMethodId;
@override final  int? priority;
// معلومات البائع التفصيلية
@override final  String sellerFullName;
@override final  String sellerStoreName;
@override final  String sellerEmail;
@override final  String sellerPhone;
@override final  String? sellerWhatsapp;
@override final  String sellerCity;
@override final  String sellerAddress;
@override final  String? sellerBusinessType;
@override final  String? sellerBusinessDescription;
@override final  String? sellerProfileImageUrl;
@override final  String? sellerStoreLogoUrl;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of EnhancedSubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EnhancedSubscriptionRequestCopyWith<_EnhancedSubscriptionRequest> get copyWith => __$EnhancedSubscriptionRequestCopyWithImpl<_EnhancedSubscriptionRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EnhancedSubscriptionRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EnhancedSubscriptionRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.planId, planId) || other.planId == planId)&&(identical(other.requestedTierName, requestedTierName) || other.requestedTierName == requestedTierName)&&(identical(other.billingCycleName, billingCycleName) || other.billingCycleName == billingCycleName)&&(identical(other.status, status) || other.status == status)&&(identical(other.statusName, statusName) || other.statusName == statusName)&&(identical(other.requestDate, requestDate) || other.requestDate == requestDate)&&(identical(other.approvedDate, approvedDate) || other.approvedDate == approvedDate)&&(identical(other.rejectedDate, rejectedDate) || other.rejectedDate == rejectedDate)&&(identical(other.adminId, adminId) || other.adminId == adminId)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.requestedPriceLD, requestedPriceLD) || other.requestedPriceLD == requestedPriceLD)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.sellerFullName, sellerFullName) || other.sellerFullName == sellerFullName)&&(identical(other.sellerStoreName, sellerStoreName) || other.sellerStoreName == sellerStoreName)&&(identical(other.sellerEmail, sellerEmail) || other.sellerEmail == sellerEmail)&&(identical(other.sellerPhone, sellerPhone) || other.sellerPhone == sellerPhone)&&(identical(other.sellerWhatsapp, sellerWhatsapp) || other.sellerWhatsapp == sellerWhatsapp)&&(identical(other.sellerCity, sellerCity) || other.sellerCity == sellerCity)&&(identical(other.sellerAddress, sellerAddress) || other.sellerAddress == sellerAddress)&&(identical(other.sellerBusinessType, sellerBusinessType) || other.sellerBusinessType == sellerBusinessType)&&(identical(other.sellerBusinessDescription, sellerBusinessDescription) || other.sellerBusinessDescription == sellerBusinessDescription)&&(identical(other.sellerProfileImageUrl, sellerProfileImageUrl) || other.sellerProfileImageUrl == sellerProfileImageUrl)&&(identical(other.sellerStoreLogoUrl, sellerStoreLogoUrl) || other.sellerStoreLogoUrl == sellerStoreLogoUrl)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,planId,requestedTierName,billingCycleName,status,statusName,requestDate,approvedDate,rejectedDate,adminId,adminNotes,rejectionReason,requestedPriceLD,paymentMethodId,priority,sellerFullName,sellerStoreName,sellerEmail,sellerPhone,sellerWhatsapp,sellerCity,sellerAddress,sellerBusinessType,sellerBusinessDescription,sellerProfileImageUrl,sellerStoreLogoUrl,createdAt,updatedAt]);

@override
String toString() {
  return 'EnhancedSubscriptionRequest(id: $id, sellerId: $sellerId, planId: $planId, requestedTierName: $requestedTierName, billingCycleName: $billingCycleName, status: $status, statusName: $statusName, requestDate: $requestDate, approvedDate: $approvedDate, rejectedDate: $rejectedDate, adminId: $adminId, adminNotes: $adminNotes, rejectionReason: $rejectionReason, requestedPriceLD: $requestedPriceLD, paymentMethodId: $paymentMethodId, priority: $priority, sellerFullName: $sellerFullName, sellerStoreName: $sellerStoreName, sellerEmail: $sellerEmail, sellerPhone: $sellerPhone, sellerWhatsapp: $sellerWhatsapp, sellerCity: $sellerCity, sellerAddress: $sellerAddress, sellerBusinessType: $sellerBusinessType, sellerBusinessDescription: $sellerBusinessDescription, sellerProfileImageUrl: $sellerProfileImageUrl, sellerStoreLogoUrl: $sellerStoreLogoUrl, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$EnhancedSubscriptionRequestCopyWith<$Res> implements $EnhancedSubscriptionRequestCopyWith<$Res> {
  factory _$EnhancedSubscriptionRequestCopyWith(_EnhancedSubscriptionRequest value, $Res Function(_EnhancedSubscriptionRequest) _then) = __$EnhancedSubscriptionRequestCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String planId, String requestedTierName, String billingCycleName, SubscriptionRequestStatus status, String statusName, DateTime requestDate, DateTime? approvedDate, DateTime? rejectedDate, String? adminId, String? adminNotes, String? rejectionReason, double requestedPriceLD, String? paymentMethodId, int? priority, String sellerFullName, String sellerStoreName, String sellerEmail, String sellerPhone, String? sellerWhatsapp, String sellerCity, String sellerAddress, String? sellerBusinessType, String? sellerBusinessDescription, String? sellerProfileImageUrl, String? sellerStoreLogoUrl,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$EnhancedSubscriptionRequestCopyWithImpl<$Res>
    implements _$EnhancedSubscriptionRequestCopyWith<$Res> {
  __$EnhancedSubscriptionRequestCopyWithImpl(this._self, this._then);

  final _EnhancedSubscriptionRequest _self;
  final $Res Function(_EnhancedSubscriptionRequest) _then;

/// Create a copy of EnhancedSubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? planId = null,Object? requestedTierName = null,Object? billingCycleName = null,Object? status = null,Object? statusName = null,Object? requestDate = null,Object? approvedDate = freezed,Object? rejectedDate = freezed,Object? adminId = freezed,Object? adminNotes = freezed,Object? rejectionReason = freezed,Object? requestedPriceLD = null,Object? paymentMethodId = freezed,Object? priority = freezed,Object? sellerFullName = null,Object? sellerStoreName = null,Object? sellerEmail = null,Object? sellerPhone = null,Object? sellerWhatsapp = freezed,Object? sellerCity = null,Object? sellerAddress = null,Object? sellerBusinessType = freezed,Object? sellerBusinessDescription = freezed,Object? sellerProfileImageUrl = freezed,Object? sellerStoreLogoUrl = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_EnhancedSubscriptionRequest(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,planId: null == planId ? _self.planId : planId // ignore: cast_nullable_to_non_nullable
as String,requestedTierName: null == requestedTierName ? _self.requestedTierName : requestedTierName // ignore: cast_nullable_to_non_nullable
as String,billingCycleName: null == billingCycleName ? _self.billingCycleName : billingCycleName // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SubscriptionRequestStatus,statusName: null == statusName ? _self.statusName : statusName // ignore: cast_nullable_to_non_nullable
as String,requestDate: null == requestDate ? _self.requestDate : requestDate // ignore: cast_nullable_to_non_nullable
as DateTime,approvedDate: freezed == approvedDate ? _self.approvedDate : approvedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,rejectedDate: freezed == rejectedDate ? _self.rejectedDate : rejectedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,adminId: freezed == adminId ? _self.adminId : adminId // ignore: cast_nullable_to_non_nullable
as String?,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,requestedPriceLD: null == requestedPriceLD ? _self.requestedPriceLD : requestedPriceLD // ignore: cast_nullable_to_non_nullable
as double,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,priority: freezed == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as int?,sellerFullName: null == sellerFullName ? _self.sellerFullName : sellerFullName // ignore: cast_nullable_to_non_nullable
as String,sellerStoreName: null == sellerStoreName ? _self.sellerStoreName : sellerStoreName // ignore: cast_nullable_to_non_nullable
as String,sellerEmail: null == sellerEmail ? _self.sellerEmail : sellerEmail // ignore: cast_nullable_to_non_nullable
as String,sellerPhone: null == sellerPhone ? _self.sellerPhone : sellerPhone // ignore: cast_nullable_to_non_nullable
as String,sellerWhatsapp: freezed == sellerWhatsapp ? _self.sellerWhatsapp : sellerWhatsapp // ignore: cast_nullable_to_non_nullable
as String?,sellerCity: null == sellerCity ? _self.sellerCity : sellerCity // ignore: cast_nullable_to_non_nullable
as String,sellerAddress: null == sellerAddress ? _self.sellerAddress : sellerAddress // ignore: cast_nullable_to_non_nullable
as String,sellerBusinessType: freezed == sellerBusinessType ? _self.sellerBusinessType : sellerBusinessType // ignore: cast_nullable_to_non_nullable
as String?,sellerBusinessDescription: freezed == sellerBusinessDescription ? _self.sellerBusinessDescription : sellerBusinessDescription // ignore: cast_nullable_to_non_nullable
as String?,sellerProfileImageUrl: freezed == sellerProfileImageUrl ? _self.sellerProfileImageUrl : sellerProfileImageUrl // ignore: cast_nullable_to_non_nullable
as String?,sellerStoreLogoUrl: freezed == sellerStoreLogoUrl ? _self.sellerStoreLogoUrl : sellerStoreLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
