// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unified_communication_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerCommunication {

 String get id; String get sellerId; String get buyerId; String? get productId; CommunicationType get type; String get subject; String? get content; CommunicationStatus get status; CommunicationPriority get priority; List<String>? get attachments; Map<String, dynamic>? get metadata; DateTime? get lastActivity;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of SellerCommunication
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerCommunicationCopyWith<SellerCommunication> get copyWith => _$SellerCommunicationCopyWithImpl<SellerCommunication>(this as SellerCommunication, _$identity);

  /// Serializes this SellerCommunication to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerCommunication&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.type, type) || other.type == type)&&(identical(other.subject, subject) || other.subject == subject)&&(identical(other.content, content) || other.content == content)&&(identical(other.status, status) || other.status == status)&&(identical(other.priority, priority) || other.priority == priority)&&const DeepCollectionEquality().equals(other.attachments, attachments)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.lastActivity, lastActivity) || other.lastActivity == lastActivity)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,buyerId,productId,type,subject,content,status,priority,const DeepCollectionEquality().hash(attachments),const DeepCollectionEquality().hash(metadata),lastActivity,createdAt,updatedAt);

@override
String toString() {
  return 'SellerCommunication(id: $id, sellerId: $sellerId, buyerId: $buyerId, productId: $productId, type: $type, subject: $subject, content: $content, status: $status, priority: $priority, attachments: $attachments, metadata: $metadata, lastActivity: $lastActivity, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SellerCommunicationCopyWith<$Res>  {
  factory $SellerCommunicationCopyWith(SellerCommunication value, $Res Function(SellerCommunication) _then) = _$SellerCommunicationCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String buyerId, String? productId, CommunicationType type, String subject, String? content, CommunicationStatus status, CommunicationPriority priority, List<String>? attachments, Map<String, dynamic>? metadata, DateTime? lastActivity,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$SellerCommunicationCopyWithImpl<$Res>
    implements $SellerCommunicationCopyWith<$Res> {
  _$SellerCommunicationCopyWithImpl(this._self, this._then);

  final SellerCommunication _self;
  final $Res Function(SellerCommunication) _then;

/// Create a copy of SellerCommunication
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? buyerId = null,Object? productId = freezed,Object? type = null,Object? subject = null,Object? content = freezed,Object? status = null,Object? priority = null,Object? attachments = freezed,Object? metadata = freezed,Object? lastActivity = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as CommunicationType,subject: null == subject ? _self.subject : subject // ignore: cast_nullable_to_non_nullable
as String,content: freezed == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as CommunicationStatus,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as CommunicationPriority,attachments: freezed == attachments ? _self.attachments : attachments // ignore: cast_nullable_to_non_nullable
as List<String>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,lastActivity: freezed == lastActivity ? _self.lastActivity : lastActivity // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerCommunication].
extension SellerCommunicationPatterns on SellerCommunication {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerCommunication value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerCommunication() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerCommunication value)  $default,){
final _that = this;
switch (_that) {
case _SellerCommunication():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerCommunication value)?  $default,){
final _that = this;
switch (_that) {
case _SellerCommunication() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String buyerId,  String? productId,  CommunicationType type,  String subject,  String? content,  CommunicationStatus status,  CommunicationPriority priority,  List<String>? attachments,  Map<String, dynamic>? metadata,  DateTime? lastActivity, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerCommunication() when $default != null:
return $default(_that.id,_that.sellerId,_that.buyerId,_that.productId,_that.type,_that.subject,_that.content,_that.status,_that.priority,_that.attachments,_that.metadata,_that.lastActivity,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String buyerId,  String? productId,  CommunicationType type,  String subject,  String? content,  CommunicationStatus status,  CommunicationPriority priority,  List<String>? attachments,  Map<String, dynamic>? metadata,  DateTime? lastActivity, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SellerCommunication():
return $default(_that.id,_that.sellerId,_that.buyerId,_that.productId,_that.type,_that.subject,_that.content,_that.status,_that.priority,_that.attachments,_that.metadata,_that.lastActivity,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String buyerId,  String? productId,  CommunicationType type,  String subject,  String? content,  CommunicationStatus status,  CommunicationPriority priority,  List<String>? attachments,  Map<String, dynamic>? metadata,  DateTime? lastActivity, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerCommunication() when $default != null:
return $default(_that.id,_that.sellerId,_that.buyerId,_that.productId,_that.type,_that.subject,_that.content,_that.status,_that.priority,_that.attachments,_that.metadata,_that.lastActivity,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SellerCommunication implements SellerCommunication {
  const _SellerCommunication({required this.id, required this.sellerId, required this.buyerId, this.productId, required this.type, required this.subject, this.content, this.status = CommunicationStatus.active, this.priority = CommunicationPriority.normal, final  List<String>? attachments, final  Map<String, dynamic>? metadata, this.lastActivity, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _attachments = attachments,_metadata = metadata;
  factory _SellerCommunication.fromJson(Map<String, dynamic> json) => _$SellerCommunicationFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String buyerId;
@override final  String? productId;
@override final  CommunicationType type;
@override final  String subject;
@override final  String? content;
@override@JsonKey() final  CommunicationStatus status;
@override@JsonKey() final  CommunicationPriority priority;
 final  List<String>? _attachments;
@override List<String>? get attachments {
  final value = _attachments;
  if (value == null) return null;
  if (_attachments is EqualUnmodifiableListView) return _attachments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  DateTime? lastActivity;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of SellerCommunication
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerCommunicationCopyWith<_SellerCommunication> get copyWith => __$SellerCommunicationCopyWithImpl<_SellerCommunication>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerCommunicationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerCommunication&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.type, type) || other.type == type)&&(identical(other.subject, subject) || other.subject == subject)&&(identical(other.content, content) || other.content == content)&&(identical(other.status, status) || other.status == status)&&(identical(other.priority, priority) || other.priority == priority)&&const DeepCollectionEquality().equals(other._attachments, _attachments)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.lastActivity, lastActivity) || other.lastActivity == lastActivity)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,buyerId,productId,type,subject,content,status,priority,const DeepCollectionEquality().hash(_attachments),const DeepCollectionEquality().hash(_metadata),lastActivity,createdAt,updatedAt);

@override
String toString() {
  return 'SellerCommunication(id: $id, sellerId: $sellerId, buyerId: $buyerId, productId: $productId, type: $type, subject: $subject, content: $content, status: $status, priority: $priority, attachments: $attachments, metadata: $metadata, lastActivity: $lastActivity, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SellerCommunicationCopyWith<$Res> implements $SellerCommunicationCopyWith<$Res> {
  factory _$SellerCommunicationCopyWith(_SellerCommunication value, $Res Function(_SellerCommunication) _then) = __$SellerCommunicationCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String buyerId, String? productId, CommunicationType type, String subject, String? content, CommunicationStatus status, CommunicationPriority priority, List<String>? attachments, Map<String, dynamic>? metadata, DateTime? lastActivity,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$SellerCommunicationCopyWithImpl<$Res>
    implements _$SellerCommunicationCopyWith<$Res> {
  __$SellerCommunicationCopyWithImpl(this._self, this._then);

  final _SellerCommunication _self;
  final $Res Function(_SellerCommunication) _then;

/// Create a copy of SellerCommunication
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? buyerId = null,Object? productId = freezed,Object? type = null,Object? subject = null,Object? content = freezed,Object? status = null,Object? priority = null,Object? attachments = freezed,Object? metadata = freezed,Object? lastActivity = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SellerCommunication(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as CommunicationType,subject: null == subject ? _self.subject : subject // ignore: cast_nullable_to_non_nullable
as String,content: freezed == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as CommunicationStatus,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as CommunicationPriority,attachments: freezed == attachments ? _self._attachments : attachments // ignore: cast_nullable_to_non_nullable
as List<String>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,lastActivity: freezed == lastActivity ? _self.lastActivity : lastActivity // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$SellerCommunicationStats {

 int get totalConversations; int get activeConversations; int get pendingResponses; int get resolvedToday; double get averageResponseTime;// بالدقائق
 double get satisfactionRating; Map<CommunicationType, int>? get conversationsByType; Map<CommunicationPriority, int>? get conversationsByPriority;
/// Create a copy of SellerCommunicationStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerCommunicationStatsCopyWith<SellerCommunicationStats> get copyWith => _$SellerCommunicationStatsCopyWithImpl<SellerCommunicationStats>(this as SellerCommunicationStats, _$identity);

  /// Serializes this SellerCommunicationStats to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerCommunicationStats&&(identical(other.totalConversations, totalConversations) || other.totalConversations == totalConversations)&&(identical(other.activeConversations, activeConversations) || other.activeConversations == activeConversations)&&(identical(other.pendingResponses, pendingResponses) || other.pendingResponses == pendingResponses)&&(identical(other.resolvedToday, resolvedToday) || other.resolvedToday == resolvedToday)&&(identical(other.averageResponseTime, averageResponseTime) || other.averageResponseTime == averageResponseTime)&&(identical(other.satisfactionRating, satisfactionRating) || other.satisfactionRating == satisfactionRating)&&const DeepCollectionEquality().equals(other.conversationsByType, conversationsByType)&&const DeepCollectionEquality().equals(other.conversationsByPriority, conversationsByPriority));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalConversations,activeConversations,pendingResponses,resolvedToday,averageResponseTime,satisfactionRating,const DeepCollectionEquality().hash(conversationsByType),const DeepCollectionEquality().hash(conversationsByPriority));

@override
String toString() {
  return 'SellerCommunicationStats(totalConversations: $totalConversations, activeConversations: $activeConversations, pendingResponses: $pendingResponses, resolvedToday: $resolvedToday, averageResponseTime: $averageResponseTime, satisfactionRating: $satisfactionRating, conversationsByType: $conversationsByType, conversationsByPriority: $conversationsByPriority)';
}


}

/// @nodoc
abstract mixin class $SellerCommunicationStatsCopyWith<$Res>  {
  factory $SellerCommunicationStatsCopyWith(SellerCommunicationStats value, $Res Function(SellerCommunicationStats) _then) = _$SellerCommunicationStatsCopyWithImpl;
@useResult
$Res call({
 int totalConversations, int activeConversations, int pendingResponses, int resolvedToday, double averageResponseTime, double satisfactionRating, Map<CommunicationType, int>? conversationsByType, Map<CommunicationPriority, int>? conversationsByPriority
});




}
/// @nodoc
class _$SellerCommunicationStatsCopyWithImpl<$Res>
    implements $SellerCommunicationStatsCopyWith<$Res> {
  _$SellerCommunicationStatsCopyWithImpl(this._self, this._then);

  final SellerCommunicationStats _self;
  final $Res Function(SellerCommunicationStats) _then;

/// Create a copy of SellerCommunicationStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalConversations = null,Object? activeConversations = null,Object? pendingResponses = null,Object? resolvedToday = null,Object? averageResponseTime = null,Object? satisfactionRating = null,Object? conversationsByType = freezed,Object? conversationsByPriority = freezed,}) {
  return _then(_self.copyWith(
totalConversations: null == totalConversations ? _self.totalConversations : totalConversations // ignore: cast_nullable_to_non_nullable
as int,activeConversations: null == activeConversations ? _self.activeConversations : activeConversations // ignore: cast_nullable_to_non_nullable
as int,pendingResponses: null == pendingResponses ? _self.pendingResponses : pendingResponses // ignore: cast_nullable_to_non_nullable
as int,resolvedToday: null == resolvedToday ? _self.resolvedToday : resolvedToday // ignore: cast_nullable_to_non_nullable
as int,averageResponseTime: null == averageResponseTime ? _self.averageResponseTime : averageResponseTime // ignore: cast_nullable_to_non_nullable
as double,satisfactionRating: null == satisfactionRating ? _self.satisfactionRating : satisfactionRating // ignore: cast_nullable_to_non_nullable
as double,conversationsByType: freezed == conversationsByType ? _self.conversationsByType : conversationsByType // ignore: cast_nullable_to_non_nullable
as Map<CommunicationType, int>?,conversationsByPriority: freezed == conversationsByPriority ? _self.conversationsByPriority : conversationsByPriority // ignore: cast_nullable_to_non_nullable
as Map<CommunicationPriority, int>?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerCommunicationStats].
extension SellerCommunicationStatsPatterns on SellerCommunicationStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerCommunicationStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerCommunicationStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerCommunicationStats value)  $default,){
final _that = this;
switch (_that) {
case _SellerCommunicationStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerCommunicationStats value)?  $default,){
final _that = this;
switch (_that) {
case _SellerCommunicationStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalConversations,  int activeConversations,  int pendingResponses,  int resolvedToday,  double averageResponseTime,  double satisfactionRating,  Map<CommunicationType, int>? conversationsByType,  Map<CommunicationPriority, int>? conversationsByPriority)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerCommunicationStats() when $default != null:
return $default(_that.totalConversations,_that.activeConversations,_that.pendingResponses,_that.resolvedToday,_that.averageResponseTime,_that.satisfactionRating,_that.conversationsByType,_that.conversationsByPriority);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalConversations,  int activeConversations,  int pendingResponses,  int resolvedToday,  double averageResponseTime,  double satisfactionRating,  Map<CommunicationType, int>? conversationsByType,  Map<CommunicationPriority, int>? conversationsByPriority)  $default,) {final _that = this;
switch (_that) {
case _SellerCommunicationStats():
return $default(_that.totalConversations,_that.activeConversations,_that.pendingResponses,_that.resolvedToday,_that.averageResponseTime,_that.satisfactionRating,_that.conversationsByType,_that.conversationsByPriority);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalConversations,  int activeConversations,  int pendingResponses,  int resolvedToday,  double averageResponseTime,  double satisfactionRating,  Map<CommunicationType, int>? conversationsByType,  Map<CommunicationPriority, int>? conversationsByPriority)?  $default,) {final _that = this;
switch (_that) {
case _SellerCommunicationStats() when $default != null:
return $default(_that.totalConversations,_that.activeConversations,_that.pendingResponses,_that.resolvedToday,_that.averageResponseTime,_that.satisfactionRating,_that.conversationsByType,_that.conversationsByPriority);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SellerCommunicationStats implements SellerCommunicationStats {
  const _SellerCommunicationStats({this.totalConversations = 0, this.activeConversations = 0, this.pendingResponses = 0, this.resolvedToday = 0, this.averageResponseTime = 0, this.satisfactionRating = 0, final  Map<CommunicationType, int>? conversationsByType, final  Map<CommunicationPriority, int>? conversationsByPriority}): _conversationsByType = conversationsByType,_conversationsByPriority = conversationsByPriority;
  factory _SellerCommunicationStats.fromJson(Map<String, dynamic> json) => _$SellerCommunicationStatsFromJson(json);

@override@JsonKey() final  int totalConversations;
@override@JsonKey() final  int activeConversations;
@override@JsonKey() final  int pendingResponses;
@override@JsonKey() final  int resolvedToday;
@override@JsonKey() final  double averageResponseTime;
// بالدقائق
@override@JsonKey() final  double satisfactionRating;
 final  Map<CommunicationType, int>? _conversationsByType;
@override Map<CommunicationType, int>? get conversationsByType {
  final value = _conversationsByType;
  if (value == null) return null;
  if (_conversationsByType is EqualUnmodifiableMapView) return _conversationsByType;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<CommunicationPriority, int>? _conversationsByPriority;
@override Map<CommunicationPriority, int>? get conversationsByPriority {
  final value = _conversationsByPriority;
  if (value == null) return null;
  if (_conversationsByPriority is EqualUnmodifiableMapView) return _conversationsByPriority;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SellerCommunicationStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerCommunicationStatsCopyWith<_SellerCommunicationStats> get copyWith => __$SellerCommunicationStatsCopyWithImpl<_SellerCommunicationStats>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerCommunicationStatsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerCommunicationStats&&(identical(other.totalConversations, totalConversations) || other.totalConversations == totalConversations)&&(identical(other.activeConversations, activeConversations) || other.activeConversations == activeConversations)&&(identical(other.pendingResponses, pendingResponses) || other.pendingResponses == pendingResponses)&&(identical(other.resolvedToday, resolvedToday) || other.resolvedToday == resolvedToday)&&(identical(other.averageResponseTime, averageResponseTime) || other.averageResponseTime == averageResponseTime)&&(identical(other.satisfactionRating, satisfactionRating) || other.satisfactionRating == satisfactionRating)&&const DeepCollectionEquality().equals(other._conversationsByType, _conversationsByType)&&const DeepCollectionEquality().equals(other._conversationsByPriority, _conversationsByPriority));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalConversations,activeConversations,pendingResponses,resolvedToday,averageResponseTime,satisfactionRating,const DeepCollectionEquality().hash(_conversationsByType),const DeepCollectionEquality().hash(_conversationsByPriority));

@override
String toString() {
  return 'SellerCommunicationStats(totalConversations: $totalConversations, activeConversations: $activeConversations, pendingResponses: $pendingResponses, resolvedToday: $resolvedToday, averageResponseTime: $averageResponseTime, satisfactionRating: $satisfactionRating, conversationsByType: $conversationsByType, conversationsByPriority: $conversationsByPriority)';
}


}

/// @nodoc
abstract mixin class _$SellerCommunicationStatsCopyWith<$Res> implements $SellerCommunicationStatsCopyWith<$Res> {
  factory _$SellerCommunicationStatsCopyWith(_SellerCommunicationStats value, $Res Function(_SellerCommunicationStats) _then) = __$SellerCommunicationStatsCopyWithImpl;
@override @useResult
$Res call({
 int totalConversations, int activeConversations, int pendingResponses, int resolvedToday, double averageResponseTime, double satisfactionRating, Map<CommunicationType, int>? conversationsByType, Map<CommunicationPriority, int>? conversationsByPriority
});




}
/// @nodoc
class __$SellerCommunicationStatsCopyWithImpl<$Res>
    implements _$SellerCommunicationStatsCopyWith<$Res> {
  __$SellerCommunicationStatsCopyWithImpl(this._self, this._then);

  final _SellerCommunicationStats _self;
  final $Res Function(_SellerCommunicationStats) _then;

/// Create a copy of SellerCommunicationStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalConversations = null,Object? activeConversations = null,Object? pendingResponses = null,Object? resolvedToday = null,Object? averageResponseTime = null,Object? satisfactionRating = null,Object? conversationsByType = freezed,Object? conversationsByPriority = freezed,}) {
  return _then(_SellerCommunicationStats(
totalConversations: null == totalConversations ? _self.totalConversations : totalConversations // ignore: cast_nullable_to_non_nullable
as int,activeConversations: null == activeConversations ? _self.activeConversations : activeConversations // ignore: cast_nullable_to_non_nullable
as int,pendingResponses: null == pendingResponses ? _self.pendingResponses : pendingResponses // ignore: cast_nullable_to_non_nullable
as int,resolvedToday: null == resolvedToday ? _self.resolvedToday : resolvedToday // ignore: cast_nullable_to_non_nullable
as int,averageResponseTime: null == averageResponseTime ? _self.averageResponseTime : averageResponseTime // ignore: cast_nullable_to_non_nullable
as double,satisfactionRating: null == satisfactionRating ? _self.satisfactionRating : satisfactionRating // ignore: cast_nullable_to_non_nullable
as double,conversationsByType: freezed == conversationsByType ? _self._conversationsByType : conversationsByType // ignore: cast_nullable_to_non_nullable
as Map<CommunicationType, int>?,conversationsByPriority: freezed == conversationsByPriority ? _self._conversationsByPriority : conversationsByPriority // ignore: cast_nullable_to_non_nullable
as Map<CommunicationPriority, int>?,
  ));
}


}

// dart format on
