// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerStatsModel {

 int get totalProducts; int get totalViews; int get activeListings; int get pendingListings; int get totalOrders; int get pendingOrders; double get totalRevenue; int get unreadMessages; DateTime? get lastUpdated;
/// Create a copy of SellerStatsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerStatsModelCopyWith<SellerStatsModel> get copyWith => _$SellerStatsModelCopyWithImpl<SellerStatsModel>(this as SellerStatsModel, _$identity);

  /// Serializes this SellerStatsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerStatsModel&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalViews, totalViews) || other.totalViews == totalViews)&&(identical(other.activeListings, activeListings) || other.activeListings == activeListings)&&(identical(other.pendingListings, pendingListings) || other.pendingListings == pendingListings)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.pendingOrders, pendingOrders) || other.pendingOrders == pendingOrders)&&(identical(other.totalRevenue, totalRevenue) || other.totalRevenue == totalRevenue)&&(identical(other.unreadMessages, unreadMessages) || other.unreadMessages == unreadMessages)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalProducts,totalViews,activeListings,pendingListings,totalOrders,pendingOrders,totalRevenue,unreadMessages,lastUpdated);

@override
String toString() {
  return 'SellerStatsModel(totalProducts: $totalProducts, totalViews: $totalViews, activeListings: $activeListings, pendingListings: $pendingListings, totalOrders: $totalOrders, pendingOrders: $pendingOrders, totalRevenue: $totalRevenue, unreadMessages: $unreadMessages, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class $SellerStatsModelCopyWith<$Res>  {
  factory $SellerStatsModelCopyWith(SellerStatsModel value, $Res Function(SellerStatsModel) _then) = _$SellerStatsModelCopyWithImpl;
@useResult
$Res call({
 int totalProducts, int totalViews, int activeListings, int pendingListings, int totalOrders, int pendingOrders, double totalRevenue, int unreadMessages, DateTime? lastUpdated
});




}
/// @nodoc
class _$SellerStatsModelCopyWithImpl<$Res>
    implements $SellerStatsModelCopyWith<$Res> {
  _$SellerStatsModelCopyWithImpl(this._self, this._then);

  final SellerStatsModel _self;
  final $Res Function(SellerStatsModel) _then;

/// Create a copy of SellerStatsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalProducts = null,Object? totalViews = null,Object? activeListings = null,Object? pendingListings = null,Object? totalOrders = null,Object? pendingOrders = null,Object? totalRevenue = null,Object? unreadMessages = null,Object? lastUpdated = freezed,}) {
  return _then(_self.copyWith(
totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalViews: null == totalViews ? _self.totalViews : totalViews // ignore: cast_nullable_to_non_nullable
as int,activeListings: null == activeListings ? _self.activeListings : activeListings // ignore: cast_nullable_to_non_nullable
as int,pendingListings: null == pendingListings ? _self.pendingListings : pendingListings // ignore: cast_nullable_to_non_nullable
as int,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,pendingOrders: null == pendingOrders ? _self.pendingOrders : pendingOrders // ignore: cast_nullable_to_non_nullable
as int,totalRevenue: null == totalRevenue ? _self.totalRevenue : totalRevenue // ignore: cast_nullable_to_non_nullable
as double,unreadMessages: null == unreadMessages ? _self.unreadMessages : unreadMessages // ignore: cast_nullable_to_non_nullable
as int,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerStatsModel].
extension SellerStatsModelPatterns on SellerStatsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerStatsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerStatsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerStatsModel value)  $default,){
final _that = this;
switch (_that) {
case _SellerStatsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerStatsModel value)?  $default,){
final _that = this;
switch (_that) {
case _SellerStatsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalProducts,  int totalViews,  int activeListings,  int pendingListings,  int totalOrders,  int pendingOrders,  double totalRevenue,  int unreadMessages,  DateTime? lastUpdated)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerStatsModel() when $default != null:
return $default(_that.totalProducts,_that.totalViews,_that.activeListings,_that.pendingListings,_that.totalOrders,_that.pendingOrders,_that.totalRevenue,_that.unreadMessages,_that.lastUpdated);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalProducts,  int totalViews,  int activeListings,  int pendingListings,  int totalOrders,  int pendingOrders,  double totalRevenue,  int unreadMessages,  DateTime? lastUpdated)  $default,) {final _that = this;
switch (_that) {
case _SellerStatsModel():
return $default(_that.totalProducts,_that.totalViews,_that.activeListings,_that.pendingListings,_that.totalOrders,_that.pendingOrders,_that.totalRevenue,_that.unreadMessages,_that.lastUpdated);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalProducts,  int totalViews,  int activeListings,  int pendingListings,  int totalOrders,  int pendingOrders,  double totalRevenue,  int unreadMessages,  DateTime? lastUpdated)?  $default,) {final _that = this;
switch (_that) {
case _SellerStatsModel() when $default != null:
return $default(_that.totalProducts,_that.totalViews,_that.activeListings,_that.pendingListings,_that.totalOrders,_that.pendingOrders,_that.totalRevenue,_that.unreadMessages,_that.lastUpdated);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SellerStatsModel implements SellerStatsModel {
  const _SellerStatsModel({this.totalProducts = 0, this.totalViews = 0, this.activeListings = 0, this.pendingListings = 0, this.totalOrders = 0, this.pendingOrders = 0, this.totalRevenue = 0, this.unreadMessages = 0, this.lastUpdated});
  factory _SellerStatsModel.fromJson(Map<String, dynamic> json) => _$SellerStatsModelFromJson(json);

@override@JsonKey() final  int totalProducts;
@override@JsonKey() final  int totalViews;
@override@JsonKey() final  int activeListings;
@override@JsonKey() final  int pendingListings;
@override@JsonKey() final  int totalOrders;
@override@JsonKey() final  int pendingOrders;
@override@JsonKey() final  double totalRevenue;
@override@JsonKey() final  int unreadMessages;
@override final  DateTime? lastUpdated;

/// Create a copy of SellerStatsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerStatsModelCopyWith<_SellerStatsModel> get copyWith => __$SellerStatsModelCopyWithImpl<_SellerStatsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerStatsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerStatsModel&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalViews, totalViews) || other.totalViews == totalViews)&&(identical(other.activeListings, activeListings) || other.activeListings == activeListings)&&(identical(other.pendingListings, pendingListings) || other.pendingListings == pendingListings)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.pendingOrders, pendingOrders) || other.pendingOrders == pendingOrders)&&(identical(other.totalRevenue, totalRevenue) || other.totalRevenue == totalRevenue)&&(identical(other.unreadMessages, unreadMessages) || other.unreadMessages == unreadMessages)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalProducts,totalViews,activeListings,pendingListings,totalOrders,pendingOrders,totalRevenue,unreadMessages,lastUpdated);

@override
String toString() {
  return 'SellerStatsModel(totalProducts: $totalProducts, totalViews: $totalViews, activeListings: $activeListings, pendingListings: $pendingListings, totalOrders: $totalOrders, pendingOrders: $pendingOrders, totalRevenue: $totalRevenue, unreadMessages: $unreadMessages, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class _$SellerStatsModelCopyWith<$Res> implements $SellerStatsModelCopyWith<$Res> {
  factory _$SellerStatsModelCopyWith(_SellerStatsModel value, $Res Function(_SellerStatsModel) _then) = __$SellerStatsModelCopyWithImpl;
@override @useResult
$Res call({
 int totalProducts, int totalViews, int activeListings, int pendingListings, int totalOrders, int pendingOrders, double totalRevenue, int unreadMessages, DateTime? lastUpdated
});




}
/// @nodoc
class __$SellerStatsModelCopyWithImpl<$Res>
    implements _$SellerStatsModelCopyWith<$Res> {
  __$SellerStatsModelCopyWithImpl(this._self, this._then);

  final _SellerStatsModel _self;
  final $Res Function(_SellerStatsModel) _then;

/// Create a copy of SellerStatsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalProducts = null,Object? totalViews = null,Object? activeListings = null,Object? pendingListings = null,Object? totalOrders = null,Object? pendingOrders = null,Object? totalRevenue = null,Object? unreadMessages = null,Object? lastUpdated = freezed,}) {
  return _then(_SellerStatsModel(
totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalViews: null == totalViews ? _self.totalViews : totalViews // ignore: cast_nullable_to_non_nullable
as int,activeListings: null == activeListings ? _self.activeListings : activeListings // ignore: cast_nullable_to_non_nullable
as int,pendingListings: null == pendingListings ? _self.pendingListings : pendingListings // ignore: cast_nullable_to_non_nullable
as int,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,pendingOrders: null == pendingOrders ? _self.pendingOrders : pendingOrders // ignore: cast_nullable_to_non_nullable
as int,totalRevenue: null == totalRevenue ? _self.totalRevenue : totalRevenue // ignore: cast_nullable_to_non_nullable
as double,unreadMessages: null == unreadMessages ? _self.unreadMessages : unreadMessages // ignore: cast_nullable_to_non_nullable
as int,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
