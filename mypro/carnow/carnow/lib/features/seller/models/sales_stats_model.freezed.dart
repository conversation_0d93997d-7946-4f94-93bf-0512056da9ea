// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sales_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SalesStatsModel {

/// إجمالي الإيرادات الكلية للبائع.
 double? get totalRevenue;/// إجمالي عدد الطلبات الكلي.
 int? get totalOrders;/// عدد الطلبات التي لا تزال قيد الانتظار.
 int? get pendingOrders;/// عدد الطلبات المكتملة.
 int? get completedOrders;/// إيرادات اليوم الحالي.
 double get dailyRevenue;/// إيرادات الأسبوع الحالي.
 double get weeklyRevenue;/// إيرادات الشهر الحالي.
 double get monthlyRevenue;/// عدد طلبات اليوم الحالي.
 int get dailyOrders;/// عدد طلبات الأسبوع الحالي.
 int get weeklyOrders;/// عدد طلبات الشهر الحالي.
 int get monthlyOrders;/// قائمة بالمنتجات الأكثر مبيعًا.
 List<ProductSaleStats> get topSellingProducts;/// قائمة ببيانات المبيعات للأيام الأخيرة.
 List<DailySales> get recentSalesData;
/// Create a copy of SalesStatsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SalesStatsModelCopyWith<SalesStatsModel> get copyWith => _$SalesStatsModelCopyWithImpl<SalesStatsModel>(this as SalesStatsModel, _$identity);

  /// Serializes this SalesStatsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SalesStatsModel&&(identical(other.totalRevenue, totalRevenue) || other.totalRevenue == totalRevenue)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.pendingOrders, pendingOrders) || other.pendingOrders == pendingOrders)&&(identical(other.completedOrders, completedOrders) || other.completedOrders == completedOrders)&&(identical(other.dailyRevenue, dailyRevenue) || other.dailyRevenue == dailyRevenue)&&(identical(other.weeklyRevenue, weeklyRevenue) || other.weeklyRevenue == weeklyRevenue)&&(identical(other.monthlyRevenue, monthlyRevenue) || other.monthlyRevenue == monthlyRevenue)&&(identical(other.dailyOrders, dailyOrders) || other.dailyOrders == dailyOrders)&&(identical(other.weeklyOrders, weeklyOrders) || other.weeklyOrders == weeklyOrders)&&(identical(other.monthlyOrders, monthlyOrders) || other.monthlyOrders == monthlyOrders)&&const DeepCollectionEquality().equals(other.topSellingProducts, topSellingProducts)&&const DeepCollectionEquality().equals(other.recentSalesData, recentSalesData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalRevenue,totalOrders,pendingOrders,completedOrders,dailyRevenue,weeklyRevenue,monthlyRevenue,dailyOrders,weeklyOrders,monthlyOrders,const DeepCollectionEquality().hash(topSellingProducts),const DeepCollectionEquality().hash(recentSalesData));

@override
String toString() {
  return 'SalesStatsModel(totalRevenue: $totalRevenue, totalOrders: $totalOrders, pendingOrders: $pendingOrders, completedOrders: $completedOrders, dailyRevenue: $dailyRevenue, weeklyRevenue: $weeklyRevenue, monthlyRevenue: $monthlyRevenue, dailyOrders: $dailyOrders, weeklyOrders: $weeklyOrders, monthlyOrders: $monthlyOrders, topSellingProducts: $topSellingProducts, recentSalesData: $recentSalesData)';
}


}

/// @nodoc
abstract mixin class $SalesStatsModelCopyWith<$Res>  {
  factory $SalesStatsModelCopyWith(SalesStatsModel value, $Res Function(SalesStatsModel) _then) = _$SalesStatsModelCopyWithImpl;
@useResult
$Res call({
 double? totalRevenue, int? totalOrders, int? pendingOrders, int? completedOrders, double dailyRevenue, double weeklyRevenue, double monthlyRevenue, int dailyOrders, int weeklyOrders, int monthlyOrders, List<ProductSaleStats> topSellingProducts, List<DailySales> recentSalesData
});




}
/// @nodoc
class _$SalesStatsModelCopyWithImpl<$Res>
    implements $SalesStatsModelCopyWith<$Res> {
  _$SalesStatsModelCopyWithImpl(this._self, this._then);

  final SalesStatsModel _self;
  final $Res Function(SalesStatsModel) _then;

/// Create a copy of SalesStatsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalRevenue = freezed,Object? totalOrders = freezed,Object? pendingOrders = freezed,Object? completedOrders = freezed,Object? dailyRevenue = null,Object? weeklyRevenue = null,Object? monthlyRevenue = null,Object? dailyOrders = null,Object? weeklyOrders = null,Object? monthlyOrders = null,Object? topSellingProducts = null,Object? recentSalesData = null,}) {
  return _then(_self.copyWith(
totalRevenue: freezed == totalRevenue ? _self.totalRevenue : totalRevenue // ignore: cast_nullable_to_non_nullable
as double?,totalOrders: freezed == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int?,pendingOrders: freezed == pendingOrders ? _self.pendingOrders : pendingOrders // ignore: cast_nullable_to_non_nullable
as int?,completedOrders: freezed == completedOrders ? _self.completedOrders : completedOrders // ignore: cast_nullable_to_non_nullable
as int?,dailyRevenue: null == dailyRevenue ? _self.dailyRevenue : dailyRevenue // ignore: cast_nullable_to_non_nullable
as double,weeklyRevenue: null == weeklyRevenue ? _self.weeklyRevenue : weeklyRevenue // ignore: cast_nullable_to_non_nullable
as double,monthlyRevenue: null == monthlyRevenue ? _self.monthlyRevenue : monthlyRevenue // ignore: cast_nullable_to_non_nullable
as double,dailyOrders: null == dailyOrders ? _self.dailyOrders : dailyOrders // ignore: cast_nullable_to_non_nullable
as int,weeklyOrders: null == weeklyOrders ? _self.weeklyOrders : weeklyOrders // ignore: cast_nullable_to_non_nullable
as int,monthlyOrders: null == monthlyOrders ? _self.monthlyOrders : monthlyOrders // ignore: cast_nullable_to_non_nullable
as int,topSellingProducts: null == topSellingProducts ? _self.topSellingProducts : topSellingProducts // ignore: cast_nullable_to_non_nullable
as List<ProductSaleStats>,recentSalesData: null == recentSalesData ? _self.recentSalesData : recentSalesData // ignore: cast_nullable_to_non_nullable
as List<DailySales>,
  ));
}

}


/// Adds pattern-matching-related methods to [SalesStatsModel].
extension SalesStatsModelPatterns on SalesStatsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SalesStatsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SalesStatsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SalesStatsModel value)  $default,){
final _that = this;
switch (_that) {
case _SalesStatsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SalesStatsModel value)?  $default,){
final _that = this;
switch (_that) {
case _SalesStatsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double? totalRevenue,  int? totalOrders,  int? pendingOrders,  int? completedOrders,  double dailyRevenue,  double weeklyRevenue,  double monthlyRevenue,  int dailyOrders,  int weeklyOrders,  int monthlyOrders,  List<ProductSaleStats> topSellingProducts,  List<DailySales> recentSalesData)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SalesStatsModel() when $default != null:
return $default(_that.totalRevenue,_that.totalOrders,_that.pendingOrders,_that.completedOrders,_that.dailyRevenue,_that.weeklyRevenue,_that.monthlyRevenue,_that.dailyOrders,_that.weeklyOrders,_that.monthlyOrders,_that.topSellingProducts,_that.recentSalesData);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double? totalRevenue,  int? totalOrders,  int? pendingOrders,  int? completedOrders,  double dailyRevenue,  double weeklyRevenue,  double monthlyRevenue,  int dailyOrders,  int weeklyOrders,  int monthlyOrders,  List<ProductSaleStats> topSellingProducts,  List<DailySales> recentSalesData)  $default,) {final _that = this;
switch (_that) {
case _SalesStatsModel():
return $default(_that.totalRevenue,_that.totalOrders,_that.pendingOrders,_that.completedOrders,_that.dailyRevenue,_that.weeklyRevenue,_that.monthlyRevenue,_that.dailyOrders,_that.weeklyOrders,_that.monthlyOrders,_that.topSellingProducts,_that.recentSalesData);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double? totalRevenue,  int? totalOrders,  int? pendingOrders,  int? completedOrders,  double dailyRevenue,  double weeklyRevenue,  double monthlyRevenue,  int dailyOrders,  int weeklyOrders,  int monthlyOrders,  List<ProductSaleStats> topSellingProducts,  List<DailySales> recentSalesData)?  $default,) {final _that = this;
switch (_that) {
case _SalesStatsModel() when $default != null:
return $default(_that.totalRevenue,_that.totalOrders,_that.pendingOrders,_that.completedOrders,_that.dailyRevenue,_that.weeklyRevenue,_that.monthlyRevenue,_that.dailyOrders,_that.weeklyOrders,_that.monthlyOrders,_that.topSellingProducts,_that.recentSalesData);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SalesStatsModel implements SalesStatsModel {
  const _SalesStatsModel({this.totalRevenue, this.totalOrders, this.pendingOrders, this.completedOrders, this.dailyRevenue = 0, this.weeklyRevenue = 0, this.monthlyRevenue = 0, this.dailyOrders = 0, this.weeklyOrders = 0, this.monthlyOrders = 0, final  List<ProductSaleStats> topSellingProducts = const [], final  List<DailySales> recentSalesData = const []}): _topSellingProducts = topSellingProducts,_recentSalesData = recentSalesData;
  factory _SalesStatsModel.fromJson(Map<String, dynamic> json) => _$SalesStatsModelFromJson(json);

/// إجمالي الإيرادات الكلية للبائع.
@override final  double? totalRevenue;
/// إجمالي عدد الطلبات الكلي.
@override final  int? totalOrders;
/// عدد الطلبات التي لا تزال قيد الانتظار.
@override final  int? pendingOrders;
/// عدد الطلبات المكتملة.
@override final  int? completedOrders;
/// إيرادات اليوم الحالي.
@override@JsonKey() final  double dailyRevenue;
/// إيرادات الأسبوع الحالي.
@override@JsonKey() final  double weeklyRevenue;
/// إيرادات الشهر الحالي.
@override@JsonKey() final  double monthlyRevenue;
/// عدد طلبات اليوم الحالي.
@override@JsonKey() final  int dailyOrders;
/// عدد طلبات الأسبوع الحالي.
@override@JsonKey() final  int weeklyOrders;
/// عدد طلبات الشهر الحالي.
@override@JsonKey() final  int monthlyOrders;
/// قائمة بالمنتجات الأكثر مبيعًا.
 final  List<ProductSaleStats> _topSellingProducts;
/// قائمة بالمنتجات الأكثر مبيعًا.
@override@JsonKey() List<ProductSaleStats> get topSellingProducts {
  if (_topSellingProducts is EqualUnmodifiableListView) return _topSellingProducts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topSellingProducts);
}

/// قائمة ببيانات المبيعات للأيام الأخيرة.
 final  List<DailySales> _recentSalesData;
/// قائمة ببيانات المبيعات للأيام الأخيرة.
@override@JsonKey() List<DailySales> get recentSalesData {
  if (_recentSalesData is EqualUnmodifiableListView) return _recentSalesData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_recentSalesData);
}


/// Create a copy of SalesStatsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SalesStatsModelCopyWith<_SalesStatsModel> get copyWith => __$SalesStatsModelCopyWithImpl<_SalesStatsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SalesStatsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SalesStatsModel&&(identical(other.totalRevenue, totalRevenue) || other.totalRevenue == totalRevenue)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.pendingOrders, pendingOrders) || other.pendingOrders == pendingOrders)&&(identical(other.completedOrders, completedOrders) || other.completedOrders == completedOrders)&&(identical(other.dailyRevenue, dailyRevenue) || other.dailyRevenue == dailyRevenue)&&(identical(other.weeklyRevenue, weeklyRevenue) || other.weeklyRevenue == weeklyRevenue)&&(identical(other.monthlyRevenue, monthlyRevenue) || other.monthlyRevenue == monthlyRevenue)&&(identical(other.dailyOrders, dailyOrders) || other.dailyOrders == dailyOrders)&&(identical(other.weeklyOrders, weeklyOrders) || other.weeklyOrders == weeklyOrders)&&(identical(other.monthlyOrders, monthlyOrders) || other.monthlyOrders == monthlyOrders)&&const DeepCollectionEquality().equals(other._topSellingProducts, _topSellingProducts)&&const DeepCollectionEquality().equals(other._recentSalesData, _recentSalesData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalRevenue,totalOrders,pendingOrders,completedOrders,dailyRevenue,weeklyRevenue,monthlyRevenue,dailyOrders,weeklyOrders,monthlyOrders,const DeepCollectionEquality().hash(_topSellingProducts),const DeepCollectionEquality().hash(_recentSalesData));

@override
String toString() {
  return 'SalesStatsModel(totalRevenue: $totalRevenue, totalOrders: $totalOrders, pendingOrders: $pendingOrders, completedOrders: $completedOrders, dailyRevenue: $dailyRevenue, weeklyRevenue: $weeklyRevenue, monthlyRevenue: $monthlyRevenue, dailyOrders: $dailyOrders, weeklyOrders: $weeklyOrders, monthlyOrders: $monthlyOrders, topSellingProducts: $topSellingProducts, recentSalesData: $recentSalesData)';
}


}

/// @nodoc
abstract mixin class _$SalesStatsModelCopyWith<$Res> implements $SalesStatsModelCopyWith<$Res> {
  factory _$SalesStatsModelCopyWith(_SalesStatsModel value, $Res Function(_SalesStatsModel) _then) = __$SalesStatsModelCopyWithImpl;
@override @useResult
$Res call({
 double? totalRevenue, int? totalOrders, int? pendingOrders, int? completedOrders, double dailyRevenue, double weeklyRevenue, double monthlyRevenue, int dailyOrders, int weeklyOrders, int monthlyOrders, List<ProductSaleStats> topSellingProducts, List<DailySales> recentSalesData
});




}
/// @nodoc
class __$SalesStatsModelCopyWithImpl<$Res>
    implements _$SalesStatsModelCopyWith<$Res> {
  __$SalesStatsModelCopyWithImpl(this._self, this._then);

  final _SalesStatsModel _self;
  final $Res Function(_SalesStatsModel) _then;

/// Create a copy of SalesStatsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalRevenue = freezed,Object? totalOrders = freezed,Object? pendingOrders = freezed,Object? completedOrders = freezed,Object? dailyRevenue = null,Object? weeklyRevenue = null,Object? monthlyRevenue = null,Object? dailyOrders = null,Object? weeklyOrders = null,Object? monthlyOrders = null,Object? topSellingProducts = null,Object? recentSalesData = null,}) {
  return _then(_SalesStatsModel(
totalRevenue: freezed == totalRevenue ? _self.totalRevenue : totalRevenue // ignore: cast_nullable_to_non_nullable
as double?,totalOrders: freezed == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int?,pendingOrders: freezed == pendingOrders ? _self.pendingOrders : pendingOrders // ignore: cast_nullable_to_non_nullable
as int?,completedOrders: freezed == completedOrders ? _self.completedOrders : completedOrders // ignore: cast_nullable_to_non_nullable
as int?,dailyRevenue: null == dailyRevenue ? _self.dailyRevenue : dailyRevenue // ignore: cast_nullable_to_non_nullable
as double,weeklyRevenue: null == weeklyRevenue ? _self.weeklyRevenue : weeklyRevenue // ignore: cast_nullable_to_non_nullable
as double,monthlyRevenue: null == monthlyRevenue ? _self.monthlyRevenue : monthlyRevenue // ignore: cast_nullable_to_non_nullable
as double,dailyOrders: null == dailyOrders ? _self.dailyOrders : dailyOrders // ignore: cast_nullable_to_non_nullable
as int,weeklyOrders: null == weeklyOrders ? _self.weeklyOrders : weeklyOrders // ignore: cast_nullable_to_non_nullable
as int,monthlyOrders: null == monthlyOrders ? _self.monthlyOrders : monthlyOrders // ignore: cast_nullable_to_non_nullable
as int,topSellingProducts: null == topSellingProducts ? _self._topSellingProducts : topSellingProducts // ignore: cast_nullable_to_non_nullable
as List<ProductSaleStats>,recentSalesData: null == recentSalesData ? _self._recentSalesData : recentSalesData // ignore: cast_nullable_to_non_nullable
as List<DailySales>,
  ));
}


}

// dart format on
