// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'daily_sales.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DailySales {

/// تاريخ اليوم.
 DateTime? get date;/// إجمالي الإيرادات في هذا اليوم.
 double? get revenue;/// إجمالي عدد الطلبات في هذا اليوم.
 int? get orders;
/// Create a copy of DailySales
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DailySalesCopyWith<DailySales> get copyWith => _$DailySalesCopyWithImpl<DailySales>(this as DailySales, _$identity);

  /// Serializes this DailySales to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DailySales&&(identical(other.date, date) || other.date == date)&&(identical(other.revenue, revenue) || other.revenue == revenue)&&(identical(other.orders, orders) || other.orders == orders));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,revenue,orders);

@override
String toString() {
  return 'DailySales(date: $date, revenue: $revenue, orders: $orders)';
}


}

/// @nodoc
abstract mixin class $DailySalesCopyWith<$Res>  {
  factory $DailySalesCopyWith(DailySales value, $Res Function(DailySales) _then) = _$DailySalesCopyWithImpl;
@useResult
$Res call({
 DateTime? date, double? revenue, int? orders
});




}
/// @nodoc
class _$DailySalesCopyWithImpl<$Res>
    implements $DailySalesCopyWith<$Res> {
  _$DailySalesCopyWithImpl(this._self, this._then);

  final DailySales _self;
  final $Res Function(DailySales) _then;

/// Create a copy of DailySales
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? date = freezed,Object? revenue = freezed,Object? orders = freezed,}) {
  return _then(_self.copyWith(
date: freezed == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime?,revenue: freezed == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double?,orders: freezed == orders ? _self.orders : orders // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [DailySales].
extension DailySalesPatterns on DailySales {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DailySales value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DailySales() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DailySales value)  $default,){
final _that = this;
switch (_that) {
case _DailySales():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DailySales value)?  $default,){
final _that = this;
switch (_that) {
case _DailySales() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime? date,  double? revenue,  int? orders)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DailySales() when $default != null:
return $default(_that.date,_that.revenue,_that.orders);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime? date,  double? revenue,  int? orders)  $default,) {final _that = this;
switch (_that) {
case _DailySales():
return $default(_that.date,_that.revenue,_that.orders);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime? date,  double? revenue,  int? orders)?  $default,) {final _that = this;
switch (_that) {
case _DailySales() when $default != null:
return $default(_that.date,_that.revenue,_that.orders);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DailySales implements DailySales {
  const _DailySales({this.date, this.revenue, this.orders});
  factory _DailySales.fromJson(Map<String, dynamic> json) => _$DailySalesFromJson(json);

/// تاريخ اليوم.
@override final  DateTime? date;
/// إجمالي الإيرادات في هذا اليوم.
@override final  double? revenue;
/// إجمالي عدد الطلبات في هذا اليوم.
@override final  int? orders;

/// Create a copy of DailySales
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DailySalesCopyWith<_DailySales> get copyWith => __$DailySalesCopyWithImpl<_DailySales>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DailySalesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DailySales&&(identical(other.date, date) || other.date == date)&&(identical(other.revenue, revenue) || other.revenue == revenue)&&(identical(other.orders, orders) || other.orders == orders));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,revenue,orders);

@override
String toString() {
  return 'DailySales(date: $date, revenue: $revenue, orders: $orders)';
}


}

/// @nodoc
abstract mixin class _$DailySalesCopyWith<$Res> implements $DailySalesCopyWith<$Res> {
  factory _$DailySalesCopyWith(_DailySales value, $Res Function(_DailySales) _then) = __$DailySalesCopyWithImpl;
@override @useResult
$Res call({
 DateTime? date, double? revenue, int? orders
});




}
/// @nodoc
class __$DailySalesCopyWithImpl<$Res>
    implements _$DailySalesCopyWith<$Res> {
  __$DailySalesCopyWithImpl(this._self, this._then);

  final _DailySales _self;
  final $Res Function(_DailySales) _then;

/// Create a copy of DailySales
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? date = freezed,Object? revenue = freezed,Object? orders = freezed,}) {
  return _then(_DailySales(
date: freezed == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime?,revenue: freezed == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double?,orders: freezed == orders ? _self.orders : orders // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
