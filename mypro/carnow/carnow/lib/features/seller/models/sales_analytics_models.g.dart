// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sales_analytics_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SalesAnalyticsParams _$SalesAnalyticsParamsFromJson(
  Map<String, dynamic> json,
) => _SalesAnalyticsParams(
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  grouping: json['grouping'] as String,
);

Map<String, dynamic> _$SalesAnalyticsParamsToJson(
  _SalesAnalyticsParams instance,
) => <String, dynamic>{
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate.toIso8601String(),
  'grouping': instance.grouping,
};

_SalesAnalyticsPoint _$SalesAnalyticsPointFromJson(Map<String, dynamic> json) =>
    _SalesAnalyticsPoint(
      label: json['label'] as String,
      revenue: (json['revenue'] as num).toDouble(),
      orders: (json['orders'] as num).toInt(),
    );

Map<String, dynamic> _$SalesAnalyticsPointToJson(
  _SalesAnalyticsPoint instance,
) => <String, dynamic>{
  'label': instance.label,
  'revenue': instance.revenue,
  'orders': instance.orders,
};

_ProductPerformance _$ProductPerformanceFromJson(Map<String, dynamic> json) =>
    _ProductPerformance(
      id: json['id'] as String,
      name: json['name'] as String,
      sales: (json['sales'] as num).toDouble(),
      orders: (json['orders'] as num).toInt(),
      views: (json['views'] as num).toInt(),
      imageUrl: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$ProductPerformanceToJson(_ProductPerformance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'sales': instance.sales,
      'orders': instance.orders,
      'views': instance.views,
      'imageUrl': instance.imageUrl,
    };

_CustomerInsight _$CustomerInsightFromJson(Map<String, dynamic> json) =>
    _CustomerInsight(
      region: json['region'] as String,
      customers: (json['customers'] as num).toInt(),
      revenue: (json['revenue'] as num).toDouble(),
      averageOrderValue: (json['averageOrderValue'] as num).toDouble(),
    );

Map<String, dynamic> _$CustomerInsightToJson(_CustomerInsight instance) =>
    <String, dynamic>{
      'region': instance.region,
      'customers': instance.customers,
      'revenue': instance.revenue,
      'averageOrderValue': instance.averageOrderValue,
    };
