// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_rating_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerRatingModel {

 int? get id; int? get sellerId; int? get buyerId; int? get rating; String? get comment; DateTime? get createdAt;
/// Create a copy of SellerRatingModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerRatingModelCopyWith<SellerRatingModel> get copyWith => _$SellerRatingModelCopyWithImpl<SellerRatingModel>(this as SellerRatingModel, _$identity);

  /// Serializes this SellerRatingModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerRatingModel&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comment, comment) || other.comment == comment)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,buyerId,rating,comment,createdAt);

@override
String toString() {
  return 'SellerRatingModel(id: $id, sellerId: $sellerId, buyerId: $buyerId, rating: $rating, comment: $comment, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $SellerRatingModelCopyWith<$Res>  {
  factory $SellerRatingModelCopyWith(SellerRatingModel value, $Res Function(SellerRatingModel) _then) = _$SellerRatingModelCopyWithImpl;
@useResult
$Res call({
 int? id, int? sellerId, int? buyerId, int? rating, String? comment, DateTime? createdAt
});




}
/// @nodoc
class _$SellerRatingModelCopyWithImpl<$Res>
    implements $SellerRatingModelCopyWith<$Res> {
  _$SellerRatingModelCopyWithImpl(this._self, this._then);

  final SellerRatingModel _self;
  final $Res Function(SellerRatingModel) _then;

/// Create a copy of SellerRatingModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? sellerId = freezed,Object? buyerId = freezed,Object? rating = freezed,Object? comment = freezed,Object? createdAt = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as int?,buyerId: freezed == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as int?,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as int?,comment: freezed == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerRatingModel].
extension SellerRatingModelPatterns on SellerRatingModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerRatingModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerRatingModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerRatingModel value)  $default,){
final _that = this;
switch (_that) {
case _SellerRatingModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerRatingModel value)?  $default,){
final _that = this;
switch (_that) {
case _SellerRatingModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  int? sellerId,  int? buyerId,  int? rating,  String? comment,  DateTime? createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerRatingModel() when $default != null:
return $default(_that.id,_that.sellerId,_that.buyerId,_that.rating,_that.comment,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  int? sellerId,  int? buyerId,  int? rating,  String? comment,  DateTime? createdAt)  $default,) {final _that = this;
switch (_that) {
case _SellerRatingModel():
return $default(_that.id,_that.sellerId,_that.buyerId,_that.rating,_that.comment,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  int? sellerId,  int? buyerId,  int? rating,  String? comment,  DateTime? createdAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerRatingModel() when $default != null:
return $default(_that.id,_that.sellerId,_that.buyerId,_that.rating,_that.comment,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SellerRatingModel implements SellerRatingModel {
  const _SellerRatingModel({this.id, this.sellerId, this.buyerId, this.rating, this.comment, this.createdAt});
  factory _SellerRatingModel.fromJson(Map<String, dynamic> json) => _$SellerRatingModelFromJson(json);

@override final  int? id;
@override final  int? sellerId;
@override final  int? buyerId;
@override final  int? rating;
@override final  String? comment;
@override final  DateTime? createdAt;

/// Create a copy of SellerRatingModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerRatingModelCopyWith<_SellerRatingModel> get copyWith => __$SellerRatingModelCopyWithImpl<_SellerRatingModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerRatingModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerRatingModel&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comment, comment) || other.comment == comment)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,buyerId,rating,comment,createdAt);

@override
String toString() {
  return 'SellerRatingModel(id: $id, sellerId: $sellerId, buyerId: $buyerId, rating: $rating, comment: $comment, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$SellerRatingModelCopyWith<$Res> implements $SellerRatingModelCopyWith<$Res> {
  factory _$SellerRatingModelCopyWith(_SellerRatingModel value, $Res Function(_SellerRatingModel) _then) = __$SellerRatingModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, int? sellerId, int? buyerId, int? rating, String? comment, DateTime? createdAt
});




}
/// @nodoc
class __$SellerRatingModelCopyWithImpl<$Res>
    implements _$SellerRatingModelCopyWith<$Res> {
  __$SellerRatingModelCopyWithImpl(this._self, this._then);

  final _SellerRatingModel _self;
  final $Res Function(_SellerRatingModel) _then;

/// Create a copy of SellerRatingModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? sellerId = freezed,Object? buyerId = freezed,Object? rating = freezed,Object? comment = freezed,Object? createdAt = freezed,}) {
  return _then(_SellerRatingModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as int?,buyerId: freezed == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as int?,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as int?,comment: freezed == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
