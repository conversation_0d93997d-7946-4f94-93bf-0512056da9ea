// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_communication_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerCommunication _$SellerCommunicationFromJson(Map<String, dynamic> json) =>
    _SellerCommunication(
      id: json['id'] as String,
      sellerId: json['seller_id'] as String,
      buyerId: json['buyer_id'] as String,
      productId: json['product_id'] as String?,
      type: $enumDecode(_$CommunicationTypeEnumMap, json['type']),
      subject: json['subject'] as String,
      content: json['content'] as String?,
      status:
          $enumDecodeNullable(_$CommunicationStatusEnumMap, json['status']) ??
          CommunicationStatus.active,
      priority:
          $enumDecodeNullable(
            _$CommunicationPriorityEnumMap,
            json['priority'],
          ) ??
          CommunicationPriority.normal,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      lastActivity: json['last_activity'] == null
          ? null
          : DateTime.parse(json['last_activity'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$SellerCommunicationToJson(
  _SellerCommunication instance,
) => <String, dynamic>{
  'id': instance.id,
  'seller_id': instance.sellerId,
  'buyer_id': instance.buyerId,
  'product_id': instance.productId,
  'type': _$CommunicationTypeEnumMap[instance.type]!,
  'subject': instance.subject,
  'content': instance.content,
  'status': _$CommunicationStatusEnumMap[instance.status]!,
  'priority': _$CommunicationPriorityEnumMap[instance.priority]!,
  'attachments': instance.attachments,
  'metadata': instance.metadata,
  'last_activity': instance.lastActivity?.toIso8601String(),
};

const _$CommunicationTypeEnumMap = {
  CommunicationType.productInquiry: 'inquiry',
  CommunicationType.orderSupport: 'order_support',
  CommunicationType.priceNegotiation: 'negotiation',
  CommunicationType.technicalSupport: 'technical_support',
  CommunicationType.complaint: 'complaint',
  CommunicationType.general: 'general',
};

const _$CommunicationStatusEnumMap = {
  CommunicationStatus.active: 'active',
  CommunicationStatus.resolved: 'resolved',
  CommunicationStatus.escalated: 'escalated',
  CommunicationStatus.archived: 'archived',
};

const _$CommunicationPriorityEnumMap = {
  CommunicationPriority.low: 'low',
  CommunicationPriority.normal: 'normal',
  CommunicationPriority.high: 'high',
  CommunicationPriority.urgent: 'urgent',
};

_SellerCommunicationStats _$SellerCommunicationStatsFromJson(
  Map<String, dynamic> json,
) => _SellerCommunicationStats(
  totalConversations: (json['totalConversations'] as num?)?.toInt() ?? 0,
  activeConversations: (json['activeConversations'] as num?)?.toInt() ?? 0,
  pendingResponses: (json['pendingResponses'] as num?)?.toInt() ?? 0,
  resolvedToday: (json['resolvedToday'] as num?)?.toInt() ?? 0,
  averageResponseTime: (json['averageResponseTime'] as num?)?.toDouble() ?? 0,
  satisfactionRating: (json['satisfactionRating'] as num?)?.toDouble() ?? 0,
  conversationsByType: (json['conversationsByType'] as Map<String, dynamic>?)
      ?.map(
        (k, e) => MapEntry(
          $enumDecode(_$CommunicationTypeEnumMap, k),
          (e as num).toInt(),
        ),
      ),
  conversationsByPriority:
      (json['conversationsByPriority'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
          $enumDecode(_$CommunicationPriorityEnumMap, k),
          (e as num).toInt(),
        ),
      ),
);

Map<String, dynamic> _$SellerCommunicationStatsToJson(
  _SellerCommunicationStats instance,
) => <String, dynamic>{
  'totalConversations': instance.totalConversations,
  'activeConversations': instance.activeConversations,
  'pendingResponses': instance.pendingResponses,
  'resolvedToday': instance.resolvedToday,
  'averageResponseTime': instance.averageResponseTime,
  'satisfactionRating': instance.satisfactionRating,
  'conversationsByType': instance.conversationsByType?.map(
    (k, e) => MapEntry(_$CommunicationTypeEnumMap[k]!, e),
  ),
  'conversationsByPriority': instance.conversationsByPriority?.map(
    (k, e) => MapEntry(_$CommunicationPriorityEnumMap[k]!, e),
  ),
};
