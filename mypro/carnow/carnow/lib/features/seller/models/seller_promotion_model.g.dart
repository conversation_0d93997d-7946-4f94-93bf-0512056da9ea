// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_promotion_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerPromotionModel _$SellerPromotionModelFromJson(
  Map<String, dynamic> json,
) => _SellerPromotionModel(
  id: (json['id'] as num?)?.toInt(),
  storeId: (json['storeId'] as num?)?.toInt(),
  title: json['title'] as String?,
  description: json['description'] as String?,
  discountType: $enumDecodeNullable(
    _$DiscountTypeEnumMap,
    json['discountType'],
  ),
  discountValue: (json['discountValue'] as num?)?.toDouble(),
  startDate: json['startDate'] == null
      ? null
      : DateTime.parse(json['startDate'] as String),
  endDate: json['endDate'] == null
      ? null
      : DateTime.parse(json['endDate'] as String),
  status: $enumDecodeNullable(_$PromotionStatusEnumMap, json['status']),
  productIds:
      (json['productIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList() ??
      const [],
  minPurchaseAmount: (json['minPurchaseAmount'] as num?)?.toDouble() ?? 0,
  maxDiscountAmount: (json['maxDiscountAmount'] as num?)?.toDouble(),
  usageLimit: (json['usageLimit'] as num?)?.toInt(),
  usageCount: (json['usageCount'] as num?)?.toInt() ?? 0,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$SellerPromotionModelToJson(
  _SellerPromotionModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'storeId': instance.storeId,
  'title': instance.title,
  'description': instance.description,
  'discountType': _$DiscountTypeEnumMap[instance.discountType],
  'discountValue': instance.discountValue,
  'startDate': instance.startDate?.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'status': _$PromotionStatusEnumMap[instance.status],
  'productIds': instance.productIds,
  'minPurchaseAmount': instance.minPurchaseAmount,
  'maxDiscountAmount': instance.maxDiscountAmount,
  'usageLimit': instance.usageLimit,
  'usageCount': instance.usageCount,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$DiscountTypeEnumMap = {
  DiscountType.percentage: 0,
  DiscountType.fixed: 1,
};

const _$PromotionStatusEnumMap = {
  PromotionStatus.active: 0,
  PromotionStatus.scheduled: 1,
  PromotionStatus.expired: 2,
  PromotionStatus.cancelled: 3,
  PromotionStatus.draft: 4,
};
