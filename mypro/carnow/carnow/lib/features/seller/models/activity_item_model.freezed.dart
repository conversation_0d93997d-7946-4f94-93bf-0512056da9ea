// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'activity_item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ActivityItemModel {

/// معرّف المنتج المرتبط بالنشاط.
 String get productId;/// نوع النشاط (مشاهدة، استفسار، بيع).
 ActivityType get type;/// وقت حدوث النشاط.
 DateTime get timestamp;/// معرّف النشاط الفريد.
 String? get id;/// اسم المنتج.
 String? get productName;/// معرّف المستخدم الذي قام بالنشاط.
 String? get userId;/// اسم المستخدم الذي قام بالنشاط.
 String? get userName;/// تفاصيل إضافية حول النشاط.
 String? get details;
/// Create a copy of ActivityItemModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ActivityItemModelCopyWith<ActivityItemModel> get copyWith => _$ActivityItemModelCopyWithImpl<ActivityItemModel>(this as ActivityItemModel, _$identity);

  /// Serializes this ActivityItemModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivityItemModel&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.id, id) || other.id == id)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.details, details) || other.details == details));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,type,timestamp,id,productName,userId,userName,details);

@override
String toString() {
  return 'ActivityItemModel(productId: $productId, type: $type, timestamp: $timestamp, id: $id, productName: $productName, userId: $userId, userName: $userName, details: $details)';
}


}

/// @nodoc
abstract mixin class $ActivityItemModelCopyWith<$Res>  {
  factory $ActivityItemModelCopyWith(ActivityItemModel value, $Res Function(ActivityItemModel) _then) = _$ActivityItemModelCopyWithImpl;
@useResult
$Res call({
 String productId, ActivityType type, DateTime timestamp, String? id, String? productName, String? userId, String? userName, String? details
});




}
/// @nodoc
class _$ActivityItemModelCopyWithImpl<$Res>
    implements $ActivityItemModelCopyWith<$Res> {
  _$ActivityItemModelCopyWithImpl(this._self, this._then);

  final ActivityItemModel _self;
  final $Res Function(ActivityItemModel) _then;

/// Create a copy of ActivityItemModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = null,Object? type = null,Object? timestamp = null,Object? id = freezed,Object? productName = freezed,Object? userId = freezed,Object? userName = freezed,Object? details = freezed,}) {
  return _then(_self.copyWith(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ActivityType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,productName: freezed == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,userName: freezed == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ActivityItemModel].
extension ActivityItemModelPatterns on ActivityItemModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ActivityItemModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ActivityItemModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ActivityItemModel value)  $default,){
final _that = this;
switch (_that) {
case _ActivityItemModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ActivityItemModel value)?  $default,){
final _that = this;
switch (_that) {
case _ActivityItemModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String productId,  ActivityType type,  DateTime timestamp,  String? id,  String? productName,  String? userId,  String? userName,  String? details)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ActivityItemModel() when $default != null:
return $default(_that.productId,_that.type,_that.timestamp,_that.id,_that.productName,_that.userId,_that.userName,_that.details);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String productId,  ActivityType type,  DateTime timestamp,  String? id,  String? productName,  String? userId,  String? userName,  String? details)  $default,) {final _that = this;
switch (_that) {
case _ActivityItemModel():
return $default(_that.productId,_that.type,_that.timestamp,_that.id,_that.productName,_that.userId,_that.userName,_that.details);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String productId,  ActivityType type,  DateTime timestamp,  String? id,  String? productName,  String? userId,  String? userName,  String? details)?  $default,) {final _that = this;
switch (_that) {
case _ActivityItemModel() when $default != null:
return $default(_that.productId,_that.type,_that.timestamp,_that.id,_that.productName,_that.userId,_that.userName,_that.details);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ActivityItemModel implements ActivityItemModel {
  const _ActivityItemModel({required this.productId, required this.type, required this.timestamp, this.id, this.productName, this.userId, this.userName, this.details});
  factory _ActivityItemModel.fromJson(Map<String, dynamic> json) => _$ActivityItemModelFromJson(json);

/// معرّف المنتج المرتبط بالنشاط.
@override final  String productId;
/// نوع النشاط (مشاهدة، استفسار، بيع).
@override final  ActivityType type;
/// وقت حدوث النشاط.
@override final  DateTime timestamp;
/// معرّف النشاط الفريد.
@override final  String? id;
/// اسم المنتج.
@override final  String? productName;
/// معرّف المستخدم الذي قام بالنشاط.
@override final  String? userId;
/// اسم المستخدم الذي قام بالنشاط.
@override final  String? userName;
/// تفاصيل إضافية حول النشاط.
@override final  String? details;

/// Create a copy of ActivityItemModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ActivityItemModelCopyWith<_ActivityItemModel> get copyWith => __$ActivityItemModelCopyWithImpl<_ActivityItemModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ActivityItemModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ActivityItemModel&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.id, id) || other.id == id)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.details, details) || other.details == details));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,type,timestamp,id,productName,userId,userName,details);

@override
String toString() {
  return 'ActivityItemModel(productId: $productId, type: $type, timestamp: $timestamp, id: $id, productName: $productName, userId: $userId, userName: $userName, details: $details)';
}


}

/// @nodoc
abstract mixin class _$ActivityItemModelCopyWith<$Res> implements $ActivityItemModelCopyWith<$Res> {
  factory _$ActivityItemModelCopyWith(_ActivityItemModel value, $Res Function(_ActivityItemModel) _then) = __$ActivityItemModelCopyWithImpl;
@override @useResult
$Res call({
 String productId, ActivityType type, DateTime timestamp, String? id, String? productName, String? userId, String? userName, String? details
});




}
/// @nodoc
class __$ActivityItemModelCopyWithImpl<$Res>
    implements _$ActivityItemModelCopyWith<$Res> {
  __$ActivityItemModelCopyWithImpl(this._self, this._then);

  final _ActivityItemModel _self;
  final $Res Function(_ActivityItemModel) _then;

/// Create a copy of ActivityItemModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? type = null,Object? timestamp = null,Object? id = freezed,Object? productName = freezed,Object? userId = freezed,Object? userName = freezed,Object? details = freezed,}) {
  return _then(_ActivityItemModel(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ActivityType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,productName: freezed == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,userName: freezed == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
