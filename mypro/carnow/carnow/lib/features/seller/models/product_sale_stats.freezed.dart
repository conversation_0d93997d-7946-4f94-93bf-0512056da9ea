// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_sale_stats.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductSaleStats {

/// معرّف المنتج.
 int get productId;/// اسم المنتج.
 String get productName;/// الكمية المباعة.
 int get quantitySold;/// إجمالي الإيرادات من هذا المنتج.
 double get revenue;
/// Create a copy of ProductSaleStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductSaleStatsCopyWith<ProductSaleStats> get copyWith => _$ProductSaleStatsCopyWithImpl<ProductSaleStats>(this as ProductSaleStats, _$identity);

  /// Serializes this ProductSaleStats to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductSaleStats&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.quantitySold, quantitySold) || other.quantitySold == quantitySold)&&(identical(other.revenue, revenue) || other.revenue == revenue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,quantitySold,revenue);

@override
String toString() {
  return 'ProductSaleStats(productId: $productId, productName: $productName, quantitySold: $quantitySold, revenue: $revenue)';
}


}

/// @nodoc
abstract mixin class $ProductSaleStatsCopyWith<$Res>  {
  factory $ProductSaleStatsCopyWith(ProductSaleStats value, $Res Function(ProductSaleStats) _then) = _$ProductSaleStatsCopyWithImpl;
@useResult
$Res call({
 int productId, String productName, int quantitySold, double revenue
});




}
/// @nodoc
class _$ProductSaleStatsCopyWithImpl<$Res>
    implements $ProductSaleStatsCopyWith<$Res> {
  _$ProductSaleStatsCopyWithImpl(this._self, this._then);

  final ProductSaleStats _self;
  final $Res Function(ProductSaleStats) _then;

/// Create a copy of ProductSaleStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = null,Object? productName = null,Object? quantitySold = null,Object? revenue = null,}) {
  return _then(_self.copyWith(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as int,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,quantitySold: null == quantitySold ? _self.quantitySold : quantitySold // ignore: cast_nullable_to_non_nullable
as int,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductSaleStats].
extension ProductSaleStatsPatterns on ProductSaleStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductSaleStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductSaleStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductSaleStats value)  $default,){
final _that = this;
switch (_that) {
case _ProductSaleStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductSaleStats value)?  $default,){
final _that = this;
switch (_that) {
case _ProductSaleStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int productId,  String productName,  int quantitySold,  double revenue)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductSaleStats() when $default != null:
return $default(_that.productId,_that.productName,_that.quantitySold,_that.revenue);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int productId,  String productName,  int quantitySold,  double revenue)  $default,) {final _that = this;
switch (_that) {
case _ProductSaleStats():
return $default(_that.productId,_that.productName,_that.quantitySold,_that.revenue);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int productId,  String productName,  int quantitySold,  double revenue)?  $default,) {final _that = this;
switch (_that) {
case _ProductSaleStats() when $default != null:
return $default(_that.productId,_that.productName,_that.quantitySold,_that.revenue);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductSaleStats implements ProductSaleStats {
  const _ProductSaleStats({required this.productId, required this.productName, this.quantitySold = 0, this.revenue = 0.0});
  factory _ProductSaleStats.fromJson(Map<String, dynamic> json) => _$ProductSaleStatsFromJson(json);

/// معرّف المنتج.
@override final  int productId;
/// اسم المنتج.
@override final  String productName;
/// الكمية المباعة.
@override@JsonKey() final  int quantitySold;
/// إجمالي الإيرادات من هذا المنتج.
@override@JsonKey() final  double revenue;

/// Create a copy of ProductSaleStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductSaleStatsCopyWith<_ProductSaleStats> get copyWith => __$ProductSaleStatsCopyWithImpl<_ProductSaleStats>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductSaleStatsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductSaleStats&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.quantitySold, quantitySold) || other.quantitySold == quantitySold)&&(identical(other.revenue, revenue) || other.revenue == revenue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,quantitySold,revenue);

@override
String toString() {
  return 'ProductSaleStats(productId: $productId, productName: $productName, quantitySold: $quantitySold, revenue: $revenue)';
}


}

/// @nodoc
abstract mixin class _$ProductSaleStatsCopyWith<$Res> implements $ProductSaleStatsCopyWith<$Res> {
  factory _$ProductSaleStatsCopyWith(_ProductSaleStats value, $Res Function(_ProductSaleStats) _then) = __$ProductSaleStatsCopyWithImpl;
@override @useResult
$Res call({
 int productId, String productName, int quantitySold, double revenue
});




}
/// @nodoc
class __$ProductSaleStatsCopyWithImpl<$Res>
    implements _$ProductSaleStatsCopyWith<$Res> {
  __$ProductSaleStatsCopyWithImpl(this._self, this._then);

  final _ProductSaleStats _self;
  final $Res Function(_ProductSaleStats) _then;

/// Create a copy of ProductSaleStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? productName = null,Object? quantitySold = null,Object? revenue = null,}) {
  return _then(_ProductSaleStats(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as int,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,quantitySold: null == quantitySold ? _self.quantitySold : quantitySold // ignore: cast_nullable_to_non_nullable
as int,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
