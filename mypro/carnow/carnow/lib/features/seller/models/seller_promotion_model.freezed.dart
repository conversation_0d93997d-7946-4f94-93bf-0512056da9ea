// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_promotion_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerPromotionModel {

 int? get id; int? get storeId; String? get title; String? get description; DiscountType? get discountType; double? get discountValue; DateTime? get startDate; DateTime? get endDate; PromotionStatus? get status; List<int> get productIds; double get minPurchaseAmount; double? get maxDiscountAmount; int? get usageLimit; int get usageCount; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of SellerPromotionModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerPromotionModelCopyWith<SellerPromotionModel> get copyWith => _$SellerPromotionModelCopyWithImpl<SellerPromotionModel>(this as SellerPromotionModel, _$identity);

  /// Serializes this SellerPromotionModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerPromotionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.discountType, discountType) || other.discountType == discountType)&&(identical(other.discountValue, discountValue) || other.discountValue == discountValue)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.productIds, productIds)&&(identical(other.minPurchaseAmount, minPurchaseAmount) || other.minPurchaseAmount == minPurchaseAmount)&&(identical(other.maxDiscountAmount, maxDiscountAmount) || other.maxDiscountAmount == maxDiscountAmount)&&(identical(other.usageLimit, usageLimit) || other.usageLimit == usageLimit)&&(identical(other.usageCount, usageCount) || other.usageCount == usageCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storeId,title,description,discountType,discountValue,startDate,endDate,status,const DeepCollectionEquality().hash(productIds),minPurchaseAmount,maxDiscountAmount,usageLimit,usageCount,createdAt,updatedAt);

@override
String toString() {
  return 'SellerPromotionModel(id: $id, storeId: $storeId, title: $title, description: $description, discountType: $discountType, discountValue: $discountValue, startDate: $startDate, endDate: $endDate, status: $status, productIds: $productIds, minPurchaseAmount: $minPurchaseAmount, maxDiscountAmount: $maxDiscountAmount, usageLimit: $usageLimit, usageCount: $usageCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SellerPromotionModelCopyWith<$Res>  {
  factory $SellerPromotionModelCopyWith(SellerPromotionModel value, $Res Function(SellerPromotionModel) _then) = _$SellerPromotionModelCopyWithImpl;
@useResult
$Res call({
 int? id, int? storeId, String? title, String? description, DiscountType? discountType, double? discountValue, DateTime? startDate, DateTime? endDate, PromotionStatus? status, List<int> productIds, double minPurchaseAmount, double? maxDiscountAmount, int? usageLimit, int usageCount, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$SellerPromotionModelCopyWithImpl<$Res>
    implements $SellerPromotionModelCopyWith<$Res> {
  _$SellerPromotionModelCopyWithImpl(this._self, this._then);

  final SellerPromotionModel _self;
  final $Res Function(SellerPromotionModel) _then;

/// Create a copy of SellerPromotionModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? storeId = freezed,Object? title = freezed,Object? description = freezed,Object? discountType = freezed,Object? discountValue = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? status = freezed,Object? productIds = null,Object? minPurchaseAmount = null,Object? maxDiscountAmount = freezed,Object? usageLimit = freezed,Object? usageCount = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,storeId: freezed == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,discountType: freezed == discountType ? _self.discountType : discountType // ignore: cast_nullable_to_non_nullable
as DiscountType?,discountValue: freezed == discountValue ? _self.discountValue : discountValue // ignore: cast_nullable_to_non_nullable
as double?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PromotionStatus?,productIds: null == productIds ? _self.productIds : productIds // ignore: cast_nullable_to_non_nullable
as List<int>,minPurchaseAmount: null == minPurchaseAmount ? _self.minPurchaseAmount : minPurchaseAmount // ignore: cast_nullable_to_non_nullable
as double,maxDiscountAmount: freezed == maxDiscountAmount ? _self.maxDiscountAmount : maxDiscountAmount // ignore: cast_nullable_to_non_nullable
as double?,usageLimit: freezed == usageLimit ? _self.usageLimit : usageLimit // ignore: cast_nullable_to_non_nullable
as int?,usageCount: null == usageCount ? _self.usageCount : usageCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerPromotionModel].
extension SellerPromotionModelPatterns on SellerPromotionModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerPromotionModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerPromotionModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerPromotionModel value)  $default,){
final _that = this;
switch (_that) {
case _SellerPromotionModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerPromotionModel value)?  $default,){
final _that = this;
switch (_that) {
case _SellerPromotionModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  int? storeId,  String? title,  String? description,  DiscountType? discountType,  double? discountValue,  DateTime? startDate,  DateTime? endDate,  PromotionStatus? status,  List<int> productIds,  double minPurchaseAmount,  double? maxDiscountAmount,  int? usageLimit,  int usageCount,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerPromotionModel() when $default != null:
return $default(_that.id,_that.storeId,_that.title,_that.description,_that.discountType,_that.discountValue,_that.startDate,_that.endDate,_that.status,_that.productIds,_that.minPurchaseAmount,_that.maxDiscountAmount,_that.usageLimit,_that.usageCount,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  int? storeId,  String? title,  String? description,  DiscountType? discountType,  double? discountValue,  DateTime? startDate,  DateTime? endDate,  PromotionStatus? status,  List<int> productIds,  double minPurchaseAmount,  double? maxDiscountAmount,  int? usageLimit,  int usageCount,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SellerPromotionModel():
return $default(_that.id,_that.storeId,_that.title,_that.description,_that.discountType,_that.discountValue,_that.startDate,_that.endDate,_that.status,_that.productIds,_that.minPurchaseAmount,_that.maxDiscountAmount,_that.usageLimit,_that.usageCount,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  int? storeId,  String? title,  String? description,  DiscountType? discountType,  double? discountValue,  DateTime? startDate,  DateTime? endDate,  PromotionStatus? status,  List<int> productIds,  double minPurchaseAmount,  double? maxDiscountAmount,  int? usageLimit,  int usageCount,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerPromotionModel() when $default != null:
return $default(_that.id,_that.storeId,_that.title,_that.description,_that.discountType,_that.discountValue,_that.startDate,_that.endDate,_that.status,_that.productIds,_that.minPurchaseAmount,_that.maxDiscountAmount,_that.usageLimit,_that.usageCount,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SellerPromotionModel implements SellerPromotionModel {
  const _SellerPromotionModel({this.id, this.storeId, this.title, this.description, this.discountType, this.discountValue, this.startDate, this.endDate, this.status, final  List<int> productIds = const [], this.minPurchaseAmount = 0, this.maxDiscountAmount, this.usageLimit, this.usageCount = 0, this.createdAt, this.updatedAt}): _productIds = productIds;
  factory _SellerPromotionModel.fromJson(Map<String, dynamic> json) => _$SellerPromotionModelFromJson(json);

@override final  int? id;
@override final  int? storeId;
@override final  String? title;
@override final  String? description;
@override final  DiscountType? discountType;
@override final  double? discountValue;
@override final  DateTime? startDate;
@override final  DateTime? endDate;
@override final  PromotionStatus? status;
 final  List<int> _productIds;
@override@JsonKey() List<int> get productIds {
  if (_productIds is EqualUnmodifiableListView) return _productIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_productIds);
}

@override@JsonKey() final  double minPurchaseAmount;
@override final  double? maxDiscountAmount;
@override final  int? usageLimit;
@override@JsonKey() final  int usageCount;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of SellerPromotionModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerPromotionModelCopyWith<_SellerPromotionModel> get copyWith => __$SellerPromotionModelCopyWithImpl<_SellerPromotionModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerPromotionModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerPromotionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.discountType, discountType) || other.discountType == discountType)&&(identical(other.discountValue, discountValue) || other.discountValue == discountValue)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._productIds, _productIds)&&(identical(other.minPurchaseAmount, minPurchaseAmount) || other.minPurchaseAmount == minPurchaseAmount)&&(identical(other.maxDiscountAmount, maxDiscountAmount) || other.maxDiscountAmount == maxDiscountAmount)&&(identical(other.usageLimit, usageLimit) || other.usageLimit == usageLimit)&&(identical(other.usageCount, usageCount) || other.usageCount == usageCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storeId,title,description,discountType,discountValue,startDate,endDate,status,const DeepCollectionEquality().hash(_productIds),minPurchaseAmount,maxDiscountAmount,usageLimit,usageCount,createdAt,updatedAt);

@override
String toString() {
  return 'SellerPromotionModel(id: $id, storeId: $storeId, title: $title, description: $description, discountType: $discountType, discountValue: $discountValue, startDate: $startDate, endDate: $endDate, status: $status, productIds: $productIds, minPurchaseAmount: $minPurchaseAmount, maxDiscountAmount: $maxDiscountAmount, usageLimit: $usageLimit, usageCount: $usageCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SellerPromotionModelCopyWith<$Res> implements $SellerPromotionModelCopyWith<$Res> {
  factory _$SellerPromotionModelCopyWith(_SellerPromotionModel value, $Res Function(_SellerPromotionModel) _then) = __$SellerPromotionModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, int? storeId, String? title, String? description, DiscountType? discountType, double? discountValue, DateTime? startDate, DateTime? endDate, PromotionStatus? status, List<int> productIds, double minPurchaseAmount, double? maxDiscountAmount, int? usageLimit, int usageCount, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$SellerPromotionModelCopyWithImpl<$Res>
    implements _$SellerPromotionModelCopyWith<$Res> {
  __$SellerPromotionModelCopyWithImpl(this._self, this._then);

  final _SellerPromotionModel _self;
  final $Res Function(_SellerPromotionModel) _then;

/// Create a copy of SellerPromotionModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? storeId = freezed,Object? title = freezed,Object? description = freezed,Object? discountType = freezed,Object? discountValue = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? status = freezed,Object? productIds = null,Object? minPurchaseAmount = null,Object? maxDiscountAmount = freezed,Object? usageLimit = freezed,Object? usageCount = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SellerPromotionModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,storeId: freezed == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,discountType: freezed == discountType ? _self.discountType : discountType // ignore: cast_nullable_to_non_nullable
as DiscountType?,discountValue: freezed == discountValue ? _self.discountValue : discountValue // ignore: cast_nullable_to_non_nullable
as double?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PromotionStatus?,productIds: null == productIds ? _self._productIds : productIds // ignore: cast_nullable_to_non_nullable
as List<int>,minPurchaseAmount: null == minPurchaseAmount ? _self.minPurchaseAmount : minPurchaseAmount // ignore: cast_nullable_to_non_nullable
as double,maxDiscountAmount: freezed == maxDiscountAmount ? _self.maxDiscountAmount : maxDiscountAmount // ignore: cast_nullable_to_non_nullable
as double?,usageLimit: freezed == usageLimit ? _self.usageLimit : usageLimit // ignore: cast_nullable_to_non_nullable
as int?,usageCount: null == usageCount ? _self.usageCount : usageCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
