// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subscription_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerSubscription {

 String get id; String get sellerId; String get planId; SubscriptionTier get tier; BillingCycle get billingCycle; SubscriptionStatus get status; DateTime get startDate; DateTime? get endDate; DateTime? get trialEndDate; bool get isTrialUsed; double get priceLD; int get monthlyListingQuota; double get additionalListingFeeLD; String? get paymentMethodId; DateTime? get lastPaymentDate; DateTime? get nextBillingDate; bool get autoRenewal; double get yearlyDiscountPercentage; bool get hasPriorityListing; bool get hasPrioritySupport; bool get hasDedicatedSupport; Map<String, dynamic>? get features; Map<String, dynamic>? get metadata;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of SellerSubscription
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerSubscriptionCopyWith<SellerSubscription> get copyWith => _$SellerSubscriptionCopyWithImpl<SellerSubscription>(this as SellerSubscription, _$identity);

  /// Serializes this SellerSubscription to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerSubscription&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.planId, planId) || other.planId == planId)&&(identical(other.tier, tier) || other.tier == tier)&&(identical(other.billingCycle, billingCycle) || other.billingCycle == billingCycle)&&(identical(other.status, status) || other.status == status)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.trialEndDate, trialEndDate) || other.trialEndDate == trialEndDate)&&(identical(other.isTrialUsed, isTrialUsed) || other.isTrialUsed == isTrialUsed)&&(identical(other.priceLD, priceLD) || other.priceLD == priceLD)&&(identical(other.monthlyListingQuota, monthlyListingQuota) || other.monthlyListingQuota == monthlyListingQuota)&&(identical(other.additionalListingFeeLD, additionalListingFeeLD) || other.additionalListingFeeLD == additionalListingFeeLD)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.lastPaymentDate, lastPaymentDate) || other.lastPaymentDate == lastPaymentDate)&&(identical(other.nextBillingDate, nextBillingDate) || other.nextBillingDate == nextBillingDate)&&(identical(other.autoRenewal, autoRenewal) || other.autoRenewal == autoRenewal)&&(identical(other.yearlyDiscountPercentage, yearlyDiscountPercentage) || other.yearlyDiscountPercentage == yearlyDiscountPercentage)&&(identical(other.hasPriorityListing, hasPriorityListing) || other.hasPriorityListing == hasPriorityListing)&&(identical(other.hasPrioritySupport, hasPrioritySupport) || other.hasPrioritySupport == hasPrioritySupport)&&(identical(other.hasDedicatedSupport, hasDedicatedSupport) || other.hasDedicatedSupport == hasDedicatedSupport)&&const DeepCollectionEquality().equals(other.features, features)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,planId,tier,billingCycle,status,startDate,endDate,trialEndDate,isTrialUsed,priceLD,monthlyListingQuota,additionalListingFeeLD,paymentMethodId,lastPaymentDate,nextBillingDate,autoRenewal,yearlyDiscountPercentage,hasPriorityListing,hasPrioritySupport,hasDedicatedSupport,const DeepCollectionEquality().hash(features),const DeepCollectionEquality().hash(metadata),createdAt,updatedAt]);

@override
String toString() {
  return 'SellerSubscription(id: $id, sellerId: $sellerId, planId: $planId, tier: $tier, billingCycle: $billingCycle, status: $status, startDate: $startDate, endDate: $endDate, trialEndDate: $trialEndDate, isTrialUsed: $isTrialUsed, priceLD: $priceLD, monthlyListingQuota: $monthlyListingQuota, additionalListingFeeLD: $additionalListingFeeLD, paymentMethodId: $paymentMethodId, lastPaymentDate: $lastPaymentDate, nextBillingDate: $nextBillingDate, autoRenewal: $autoRenewal, yearlyDiscountPercentage: $yearlyDiscountPercentage, hasPriorityListing: $hasPriorityListing, hasPrioritySupport: $hasPrioritySupport, hasDedicatedSupport: $hasDedicatedSupport, features: $features, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SellerSubscriptionCopyWith<$Res>  {
  factory $SellerSubscriptionCopyWith(SellerSubscription value, $Res Function(SellerSubscription) _then) = _$SellerSubscriptionCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String planId, SubscriptionTier tier, BillingCycle billingCycle, SubscriptionStatus status, DateTime startDate, DateTime? endDate, DateTime? trialEndDate, bool isTrialUsed, double priceLD, int monthlyListingQuota, double additionalListingFeeLD, String? paymentMethodId, DateTime? lastPaymentDate, DateTime? nextBillingDate, bool autoRenewal, double yearlyDiscountPercentage, bool hasPriorityListing, bool hasPrioritySupport, bool hasDedicatedSupport, Map<String, dynamic>? features, Map<String, dynamic>? metadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$SellerSubscriptionCopyWithImpl<$Res>
    implements $SellerSubscriptionCopyWith<$Res> {
  _$SellerSubscriptionCopyWithImpl(this._self, this._then);

  final SellerSubscription _self;
  final $Res Function(SellerSubscription) _then;

/// Create a copy of SellerSubscription
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? planId = null,Object? tier = null,Object? billingCycle = null,Object? status = null,Object? startDate = null,Object? endDate = freezed,Object? trialEndDate = freezed,Object? isTrialUsed = null,Object? priceLD = null,Object? monthlyListingQuota = null,Object? additionalListingFeeLD = null,Object? paymentMethodId = freezed,Object? lastPaymentDate = freezed,Object? nextBillingDate = freezed,Object? autoRenewal = null,Object? yearlyDiscountPercentage = null,Object? hasPriorityListing = null,Object? hasPrioritySupport = null,Object? hasDedicatedSupport = null,Object? features = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,planId: null == planId ? _self.planId : planId // ignore: cast_nullable_to_non_nullable
as String,tier: null == tier ? _self.tier : tier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier,billingCycle: null == billingCycle ? _self.billingCycle : billingCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,trialEndDate: freezed == trialEndDate ? _self.trialEndDate : trialEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isTrialUsed: null == isTrialUsed ? _self.isTrialUsed : isTrialUsed // ignore: cast_nullable_to_non_nullable
as bool,priceLD: null == priceLD ? _self.priceLD : priceLD // ignore: cast_nullable_to_non_nullable
as double,monthlyListingQuota: null == monthlyListingQuota ? _self.monthlyListingQuota : monthlyListingQuota // ignore: cast_nullable_to_non_nullable
as int,additionalListingFeeLD: null == additionalListingFeeLD ? _self.additionalListingFeeLD : additionalListingFeeLD // ignore: cast_nullable_to_non_nullable
as double,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,lastPaymentDate: freezed == lastPaymentDate ? _self.lastPaymentDate : lastPaymentDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextBillingDate: freezed == nextBillingDate ? _self.nextBillingDate : nextBillingDate // ignore: cast_nullable_to_non_nullable
as DateTime?,autoRenewal: null == autoRenewal ? _self.autoRenewal : autoRenewal // ignore: cast_nullable_to_non_nullable
as bool,yearlyDiscountPercentage: null == yearlyDiscountPercentage ? _self.yearlyDiscountPercentage : yearlyDiscountPercentage // ignore: cast_nullable_to_non_nullable
as double,hasPriorityListing: null == hasPriorityListing ? _self.hasPriorityListing : hasPriorityListing // ignore: cast_nullable_to_non_nullable
as bool,hasPrioritySupport: null == hasPrioritySupport ? _self.hasPrioritySupport : hasPrioritySupport // ignore: cast_nullable_to_non_nullable
as bool,hasDedicatedSupport: null == hasDedicatedSupport ? _self.hasDedicatedSupport : hasDedicatedSupport // ignore: cast_nullable_to_non_nullable
as bool,features: freezed == features ? _self.features : features // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerSubscription].
extension SellerSubscriptionPatterns on SellerSubscription {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerSubscription value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerSubscription() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerSubscription value)  $default,){
final _that = this;
switch (_that) {
case _SellerSubscription():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerSubscription value)?  $default,){
final _that = this;
switch (_that) {
case _SellerSubscription() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String planId,  SubscriptionTier tier,  BillingCycle billingCycle,  SubscriptionStatus status,  DateTime startDate,  DateTime? endDate,  DateTime? trialEndDate,  bool isTrialUsed,  double priceLD,  int monthlyListingQuota,  double additionalListingFeeLD,  String? paymentMethodId,  DateTime? lastPaymentDate,  DateTime? nextBillingDate,  bool autoRenewal,  double yearlyDiscountPercentage,  bool hasPriorityListing,  bool hasPrioritySupport,  bool hasDedicatedSupport,  Map<String, dynamic>? features,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerSubscription() when $default != null:
return $default(_that.id,_that.sellerId,_that.planId,_that.tier,_that.billingCycle,_that.status,_that.startDate,_that.endDate,_that.trialEndDate,_that.isTrialUsed,_that.priceLD,_that.monthlyListingQuota,_that.additionalListingFeeLD,_that.paymentMethodId,_that.lastPaymentDate,_that.nextBillingDate,_that.autoRenewal,_that.yearlyDiscountPercentage,_that.hasPriorityListing,_that.hasPrioritySupport,_that.hasDedicatedSupport,_that.features,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String planId,  SubscriptionTier tier,  BillingCycle billingCycle,  SubscriptionStatus status,  DateTime startDate,  DateTime? endDate,  DateTime? trialEndDate,  bool isTrialUsed,  double priceLD,  int monthlyListingQuota,  double additionalListingFeeLD,  String? paymentMethodId,  DateTime? lastPaymentDate,  DateTime? nextBillingDate,  bool autoRenewal,  double yearlyDiscountPercentage,  bool hasPriorityListing,  bool hasPrioritySupport,  bool hasDedicatedSupport,  Map<String, dynamic>? features,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SellerSubscription():
return $default(_that.id,_that.sellerId,_that.planId,_that.tier,_that.billingCycle,_that.status,_that.startDate,_that.endDate,_that.trialEndDate,_that.isTrialUsed,_that.priceLD,_that.monthlyListingQuota,_that.additionalListingFeeLD,_that.paymentMethodId,_that.lastPaymentDate,_that.nextBillingDate,_that.autoRenewal,_that.yearlyDiscountPercentage,_that.hasPriorityListing,_that.hasPrioritySupport,_that.hasDedicatedSupport,_that.features,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String planId,  SubscriptionTier tier,  BillingCycle billingCycle,  SubscriptionStatus status,  DateTime startDate,  DateTime? endDate,  DateTime? trialEndDate,  bool isTrialUsed,  double priceLD,  int monthlyListingQuota,  double additionalListingFeeLD,  String? paymentMethodId,  DateTime? lastPaymentDate,  DateTime? nextBillingDate,  bool autoRenewal,  double yearlyDiscountPercentage,  bool hasPriorityListing,  bool hasPrioritySupport,  bool hasDedicatedSupport,  Map<String, dynamic>? features,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerSubscription() when $default != null:
return $default(_that.id,_that.sellerId,_that.planId,_that.tier,_that.billingCycle,_that.status,_that.startDate,_that.endDate,_that.trialEndDate,_that.isTrialUsed,_that.priceLD,_that.monthlyListingQuota,_that.additionalListingFeeLD,_that.paymentMethodId,_that.lastPaymentDate,_that.nextBillingDate,_that.autoRenewal,_that.yearlyDiscountPercentage,_that.hasPriorityListing,_that.hasPrioritySupport,_that.hasDedicatedSupport,_that.features,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SellerSubscription implements SellerSubscription {
  const _SellerSubscription({required this.id, required this.sellerId, required this.planId, required this.tier, required this.billingCycle, required this.status, required this.startDate, this.endDate, this.trialEndDate, this.isTrialUsed = false, this.priceLD = 0.0, this.monthlyListingQuota = 0, this.additionalListingFeeLD = 0.0, this.paymentMethodId, this.lastPaymentDate, this.nextBillingDate, this.autoRenewal = false, this.yearlyDiscountPercentage = 0.0, this.hasPriorityListing = false, this.hasPrioritySupport = false, this.hasDedicatedSupport = false, final  Map<String, dynamic>? features, final  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _features = features,_metadata = metadata;
  factory _SellerSubscription.fromJson(Map<String, dynamic> json) => _$SellerSubscriptionFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String planId;
@override final  SubscriptionTier tier;
@override final  BillingCycle billingCycle;
@override final  SubscriptionStatus status;
@override final  DateTime startDate;
@override final  DateTime? endDate;
@override final  DateTime? trialEndDate;
@override@JsonKey() final  bool isTrialUsed;
@override@JsonKey() final  double priceLD;
@override@JsonKey() final  int monthlyListingQuota;
@override@JsonKey() final  double additionalListingFeeLD;
@override final  String? paymentMethodId;
@override final  DateTime? lastPaymentDate;
@override final  DateTime? nextBillingDate;
@override@JsonKey() final  bool autoRenewal;
@override@JsonKey() final  double yearlyDiscountPercentage;
@override@JsonKey() final  bool hasPriorityListing;
@override@JsonKey() final  bool hasPrioritySupport;
@override@JsonKey() final  bool hasDedicatedSupport;
 final  Map<String, dynamic>? _features;
@override Map<String, dynamic>? get features {
  final value = _features;
  if (value == null) return null;
  if (_features is EqualUnmodifiableMapView) return _features;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of SellerSubscription
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerSubscriptionCopyWith<_SellerSubscription> get copyWith => __$SellerSubscriptionCopyWithImpl<_SellerSubscription>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerSubscriptionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerSubscription&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.planId, planId) || other.planId == planId)&&(identical(other.tier, tier) || other.tier == tier)&&(identical(other.billingCycle, billingCycle) || other.billingCycle == billingCycle)&&(identical(other.status, status) || other.status == status)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.trialEndDate, trialEndDate) || other.trialEndDate == trialEndDate)&&(identical(other.isTrialUsed, isTrialUsed) || other.isTrialUsed == isTrialUsed)&&(identical(other.priceLD, priceLD) || other.priceLD == priceLD)&&(identical(other.monthlyListingQuota, monthlyListingQuota) || other.monthlyListingQuota == monthlyListingQuota)&&(identical(other.additionalListingFeeLD, additionalListingFeeLD) || other.additionalListingFeeLD == additionalListingFeeLD)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.lastPaymentDate, lastPaymentDate) || other.lastPaymentDate == lastPaymentDate)&&(identical(other.nextBillingDate, nextBillingDate) || other.nextBillingDate == nextBillingDate)&&(identical(other.autoRenewal, autoRenewal) || other.autoRenewal == autoRenewal)&&(identical(other.yearlyDiscountPercentage, yearlyDiscountPercentage) || other.yearlyDiscountPercentage == yearlyDiscountPercentage)&&(identical(other.hasPriorityListing, hasPriorityListing) || other.hasPriorityListing == hasPriorityListing)&&(identical(other.hasPrioritySupport, hasPrioritySupport) || other.hasPrioritySupport == hasPrioritySupport)&&(identical(other.hasDedicatedSupport, hasDedicatedSupport) || other.hasDedicatedSupport == hasDedicatedSupport)&&const DeepCollectionEquality().equals(other._features, _features)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,planId,tier,billingCycle,status,startDate,endDate,trialEndDate,isTrialUsed,priceLD,monthlyListingQuota,additionalListingFeeLD,paymentMethodId,lastPaymentDate,nextBillingDate,autoRenewal,yearlyDiscountPercentage,hasPriorityListing,hasPrioritySupport,hasDedicatedSupport,const DeepCollectionEquality().hash(_features),const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt]);

@override
String toString() {
  return 'SellerSubscription(id: $id, sellerId: $sellerId, planId: $planId, tier: $tier, billingCycle: $billingCycle, status: $status, startDate: $startDate, endDate: $endDate, trialEndDate: $trialEndDate, isTrialUsed: $isTrialUsed, priceLD: $priceLD, monthlyListingQuota: $monthlyListingQuota, additionalListingFeeLD: $additionalListingFeeLD, paymentMethodId: $paymentMethodId, lastPaymentDate: $lastPaymentDate, nextBillingDate: $nextBillingDate, autoRenewal: $autoRenewal, yearlyDiscountPercentage: $yearlyDiscountPercentage, hasPriorityListing: $hasPriorityListing, hasPrioritySupport: $hasPrioritySupport, hasDedicatedSupport: $hasDedicatedSupport, features: $features, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SellerSubscriptionCopyWith<$Res> implements $SellerSubscriptionCopyWith<$Res> {
  factory _$SellerSubscriptionCopyWith(_SellerSubscription value, $Res Function(_SellerSubscription) _then) = __$SellerSubscriptionCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String planId, SubscriptionTier tier, BillingCycle billingCycle, SubscriptionStatus status, DateTime startDate, DateTime? endDate, DateTime? trialEndDate, bool isTrialUsed, double priceLD, int monthlyListingQuota, double additionalListingFeeLD, String? paymentMethodId, DateTime? lastPaymentDate, DateTime? nextBillingDate, bool autoRenewal, double yearlyDiscountPercentage, bool hasPriorityListing, bool hasPrioritySupport, bool hasDedicatedSupport, Map<String, dynamic>? features, Map<String, dynamic>? metadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$SellerSubscriptionCopyWithImpl<$Res>
    implements _$SellerSubscriptionCopyWith<$Res> {
  __$SellerSubscriptionCopyWithImpl(this._self, this._then);

  final _SellerSubscription _self;
  final $Res Function(_SellerSubscription) _then;

/// Create a copy of SellerSubscription
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? planId = null,Object? tier = null,Object? billingCycle = null,Object? status = null,Object? startDate = null,Object? endDate = freezed,Object? trialEndDate = freezed,Object? isTrialUsed = null,Object? priceLD = null,Object? monthlyListingQuota = null,Object? additionalListingFeeLD = null,Object? paymentMethodId = freezed,Object? lastPaymentDate = freezed,Object? nextBillingDate = freezed,Object? autoRenewal = null,Object? yearlyDiscountPercentage = null,Object? hasPriorityListing = null,Object? hasPrioritySupport = null,Object? hasDedicatedSupport = null,Object? features = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SellerSubscription(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,planId: null == planId ? _self.planId : planId // ignore: cast_nullable_to_non_nullable
as String,tier: null == tier ? _self.tier : tier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier,billingCycle: null == billingCycle ? _self.billingCycle : billingCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,trialEndDate: freezed == trialEndDate ? _self.trialEndDate : trialEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isTrialUsed: null == isTrialUsed ? _self.isTrialUsed : isTrialUsed // ignore: cast_nullable_to_non_nullable
as bool,priceLD: null == priceLD ? _self.priceLD : priceLD // ignore: cast_nullable_to_non_nullable
as double,monthlyListingQuota: null == monthlyListingQuota ? _self.monthlyListingQuota : monthlyListingQuota // ignore: cast_nullable_to_non_nullable
as int,additionalListingFeeLD: null == additionalListingFeeLD ? _self.additionalListingFeeLD : additionalListingFeeLD // ignore: cast_nullable_to_non_nullable
as double,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,lastPaymentDate: freezed == lastPaymentDate ? _self.lastPaymentDate : lastPaymentDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextBillingDate: freezed == nextBillingDate ? _self.nextBillingDate : nextBillingDate // ignore: cast_nullable_to_non_nullable
as DateTime?,autoRenewal: null == autoRenewal ? _self.autoRenewal : autoRenewal // ignore: cast_nullable_to_non_nullable
as bool,yearlyDiscountPercentage: null == yearlyDiscountPercentage ? _self.yearlyDiscountPercentage : yearlyDiscountPercentage // ignore: cast_nullable_to_non_nullable
as double,hasPriorityListing: null == hasPriorityListing ? _self.hasPriorityListing : hasPriorityListing // ignore: cast_nullable_to_non_nullable
as bool,hasPrioritySupport: null == hasPrioritySupport ? _self.hasPrioritySupport : hasPrioritySupport // ignore: cast_nullable_to_non_nullable
as bool,hasDedicatedSupport: null == hasDedicatedSupport ? _self.hasDedicatedSupport : hasDedicatedSupport // ignore: cast_nullable_to_non_nullable
as bool,features: freezed == features ? _self._features : features // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$SubscriptionPlan {

 String get id; String get name; String get nameAr; String get description; String get descriptionAr; SubscriptionTier get tier; double get monthlyPriceLD; double get yearlyPriceLD; int get monthlyListingQuota; double get additionalListingFeeLD; int get trialDays; bool get isActive; bool get isPopular; bool get hasPriorityListing; bool get hasPrioritySupport; bool get hasDedicatedSupport; List<String>? get benefits; List<String>? get benefitsAr; Map<String, dynamic>? get features; Map<String, dynamic>? get limits; String? get stripePriceId; String? get stripeYearlyPriceId;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of SubscriptionPlan
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionPlanCopyWith<SubscriptionPlan> get copyWith => _$SubscriptionPlanCopyWithImpl<SubscriptionPlan>(this as SubscriptionPlan, _$identity);

  /// Serializes this SubscriptionPlan to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubscriptionPlan&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.tier, tier) || other.tier == tier)&&(identical(other.monthlyPriceLD, monthlyPriceLD) || other.monthlyPriceLD == monthlyPriceLD)&&(identical(other.yearlyPriceLD, yearlyPriceLD) || other.yearlyPriceLD == yearlyPriceLD)&&(identical(other.monthlyListingQuota, monthlyListingQuota) || other.monthlyListingQuota == monthlyListingQuota)&&(identical(other.additionalListingFeeLD, additionalListingFeeLD) || other.additionalListingFeeLD == additionalListingFeeLD)&&(identical(other.trialDays, trialDays) || other.trialDays == trialDays)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isPopular, isPopular) || other.isPopular == isPopular)&&(identical(other.hasPriorityListing, hasPriorityListing) || other.hasPriorityListing == hasPriorityListing)&&(identical(other.hasPrioritySupport, hasPrioritySupport) || other.hasPrioritySupport == hasPrioritySupport)&&(identical(other.hasDedicatedSupport, hasDedicatedSupport) || other.hasDedicatedSupport == hasDedicatedSupport)&&const DeepCollectionEquality().equals(other.benefits, benefits)&&const DeepCollectionEquality().equals(other.benefitsAr, benefitsAr)&&const DeepCollectionEquality().equals(other.features, features)&&const DeepCollectionEquality().equals(other.limits, limits)&&(identical(other.stripePriceId, stripePriceId) || other.stripePriceId == stripePriceId)&&(identical(other.stripeYearlyPriceId, stripeYearlyPriceId) || other.stripeYearlyPriceId == stripeYearlyPriceId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,nameAr,description,descriptionAr,tier,monthlyPriceLD,yearlyPriceLD,monthlyListingQuota,additionalListingFeeLD,trialDays,isActive,isPopular,hasPriorityListing,hasPrioritySupport,hasDedicatedSupport,const DeepCollectionEquality().hash(benefits),const DeepCollectionEquality().hash(benefitsAr),const DeepCollectionEquality().hash(features),const DeepCollectionEquality().hash(limits),stripePriceId,stripeYearlyPriceId,createdAt,updatedAt]);

@override
String toString() {
  return 'SubscriptionPlan(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, tier: $tier, monthlyPriceLD: $monthlyPriceLD, yearlyPriceLD: $yearlyPriceLD, monthlyListingQuota: $monthlyListingQuota, additionalListingFeeLD: $additionalListingFeeLD, trialDays: $trialDays, isActive: $isActive, isPopular: $isPopular, hasPriorityListing: $hasPriorityListing, hasPrioritySupport: $hasPrioritySupport, hasDedicatedSupport: $hasDedicatedSupport, benefits: $benefits, benefitsAr: $benefitsAr, features: $features, limits: $limits, stripePriceId: $stripePriceId, stripeYearlyPriceId: $stripeYearlyPriceId, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SubscriptionPlanCopyWith<$Res>  {
  factory $SubscriptionPlanCopyWith(SubscriptionPlan value, $Res Function(SubscriptionPlan) _then) = _$SubscriptionPlanCopyWithImpl;
@useResult
$Res call({
 String id, String name, String nameAr, String description, String descriptionAr, SubscriptionTier tier, double monthlyPriceLD, double yearlyPriceLD, int monthlyListingQuota, double additionalListingFeeLD, int trialDays, bool isActive, bool isPopular, bool hasPriorityListing, bool hasPrioritySupport, bool hasDedicatedSupport, List<String>? benefits, List<String>? benefitsAr, Map<String, dynamic>? features, Map<String, dynamic>? limits, String? stripePriceId, String? stripeYearlyPriceId,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$SubscriptionPlanCopyWithImpl<$Res>
    implements $SubscriptionPlanCopyWith<$Res> {
  _$SubscriptionPlanCopyWithImpl(this._self, this._then);

  final SubscriptionPlan _self;
  final $Res Function(SubscriptionPlan) _then;

/// Create a copy of SubscriptionPlan
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = null,Object? descriptionAr = null,Object? tier = null,Object? monthlyPriceLD = null,Object? yearlyPriceLD = null,Object? monthlyListingQuota = null,Object? additionalListingFeeLD = null,Object? trialDays = null,Object? isActive = null,Object? isPopular = null,Object? hasPriorityListing = null,Object? hasPrioritySupport = null,Object? hasDedicatedSupport = null,Object? benefits = freezed,Object? benefitsAr = freezed,Object? features = freezed,Object? limits = freezed,Object? stripePriceId = freezed,Object? stripeYearlyPriceId = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,descriptionAr: null == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String,tier: null == tier ? _self.tier : tier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier,monthlyPriceLD: null == monthlyPriceLD ? _self.monthlyPriceLD : monthlyPriceLD // ignore: cast_nullable_to_non_nullable
as double,yearlyPriceLD: null == yearlyPriceLD ? _self.yearlyPriceLD : yearlyPriceLD // ignore: cast_nullable_to_non_nullable
as double,monthlyListingQuota: null == monthlyListingQuota ? _self.monthlyListingQuota : monthlyListingQuota // ignore: cast_nullable_to_non_nullable
as int,additionalListingFeeLD: null == additionalListingFeeLD ? _self.additionalListingFeeLD : additionalListingFeeLD // ignore: cast_nullable_to_non_nullable
as double,trialDays: null == trialDays ? _self.trialDays : trialDays // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isPopular: null == isPopular ? _self.isPopular : isPopular // ignore: cast_nullable_to_non_nullable
as bool,hasPriorityListing: null == hasPriorityListing ? _self.hasPriorityListing : hasPriorityListing // ignore: cast_nullable_to_non_nullable
as bool,hasPrioritySupport: null == hasPrioritySupport ? _self.hasPrioritySupport : hasPrioritySupport // ignore: cast_nullable_to_non_nullable
as bool,hasDedicatedSupport: null == hasDedicatedSupport ? _self.hasDedicatedSupport : hasDedicatedSupport // ignore: cast_nullable_to_non_nullable
as bool,benefits: freezed == benefits ? _self.benefits : benefits // ignore: cast_nullable_to_non_nullable
as List<String>?,benefitsAr: freezed == benefitsAr ? _self.benefitsAr : benefitsAr // ignore: cast_nullable_to_non_nullable
as List<String>?,features: freezed == features ? _self.features : features // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,limits: freezed == limits ? _self.limits : limits // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,stripePriceId: freezed == stripePriceId ? _self.stripePriceId : stripePriceId // ignore: cast_nullable_to_non_nullable
as String?,stripeYearlyPriceId: freezed == stripeYearlyPriceId ? _self.stripeYearlyPriceId : stripeYearlyPriceId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SubscriptionPlan].
extension SubscriptionPlanPatterns on SubscriptionPlan {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SubscriptionPlan value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SubscriptionPlan() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SubscriptionPlan value)  $default,){
final _that = this;
switch (_that) {
case _SubscriptionPlan():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SubscriptionPlan value)?  $default,){
final _that = this;
switch (_that) {
case _SubscriptionPlan() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String nameAr,  String description,  String descriptionAr,  SubscriptionTier tier,  double monthlyPriceLD,  double yearlyPriceLD,  int monthlyListingQuota,  double additionalListingFeeLD,  int trialDays,  bool isActive,  bool isPopular,  bool hasPriorityListing,  bool hasPrioritySupport,  bool hasDedicatedSupport,  List<String>? benefits,  List<String>? benefitsAr,  Map<String, dynamic>? features,  Map<String, dynamic>? limits,  String? stripePriceId,  String? stripeYearlyPriceId, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SubscriptionPlan() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.tier,_that.monthlyPriceLD,_that.yearlyPriceLD,_that.monthlyListingQuota,_that.additionalListingFeeLD,_that.trialDays,_that.isActive,_that.isPopular,_that.hasPriorityListing,_that.hasPrioritySupport,_that.hasDedicatedSupport,_that.benefits,_that.benefitsAr,_that.features,_that.limits,_that.stripePriceId,_that.stripeYearlyPriceId,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String nameAr,  String description,  String descriptionAr,  SubscriptionTier tier,  double monthlyPriceLD,  double yearlyPriceLD,  int monthlyListingQuota,  double additionalListingFeeLD,  int trialDays,  bool isActive,  bool isPopular,  bool hasPriorityListing,  bool hasPrioritySupport,  bool hasDedicatedSupport,  List<String>? benefits,  List<String>? benefitsAr,  Map<String, dynamic>? features,  Map<String, dynamic>? limits,  String? stripePriceId,  String? stripeYearlyPriceId, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SubscriptionPlan():
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.tier,_that.monthlyPriceLD,_that.yearlyPriceLD,_that.monthlyListingQuota,_that.additionalListingFeeLD,_that.trialDays,_that.isActive,_that.isPopular,_that.hasPriorityListing,_that.hasPrioritySupport,_that.hasDedicatedSupport,_that.benefits,_that.benefitsAr,_that.features,_that.limits,_that.stripePriceId,_that.stripeYearlyPriceId,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String nameAr,  String description,  String descriptionAr,  SubscriptionTier tier,  double monthlyPriceLD,  double yearlyPriceLD,  int monthlyListingQuota,  double additionalListingFeeLD,  int trialDays,  bool isActive,  bool isPopular,  bool hasPriorityListing,  bool hasPrioritySupport,  bool hasDedicatedSupport,  List<String>? benefits,  List<String>? benefitsAr,  Map<String, dynamic>? features,  Map<String, dynamic>? limits,  String? stripePriceId,  String? stripeYearlyPriceId, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SubscriptionPlan() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.tier,_that.monthlyPriceLD,_that.yearlyPriceLD,_that.monthlyListingQuota,_that.additionalListingFeeLD,_that.trialDays,_that.isActive,_that.isPopular,_that.hasPriorityListing,_that.hasPrioritySupport,_that.hasDedicatedSupport,_that.benefits,_that.benefitsAr,_that.features,_that.limits,_that.stripePriceId,_that.stripeYearlyPriceId,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SubscriptionPlan implements SubscriptionPlan {
  const _SubscriptionPlan({required this.id, required this.name, required this.nameAr, required this.description, required this.descriptionAr, required this.tier, required this.monthlyPriceLD, required this.yearlyPriceLD, required this.monthlyListingQuota, required this.additionalListingFeeLD, this.trialDays = 0, this.isActive = true, this.isPopular = false, this.hasPriorityListing = false, this.hasPrioritySupport = false, this.hasDedicatedSupport = false, final  List<String>? benefits, final  List<String>? benefitsAr, final  Map<String, dynamic>? features, final  Map<String, dynamic>? limits, this.stripePriceId, this.stripeYearlyPriceId, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _benefits = benefits,_benefitsAr = benefitsAr,_features = features,_limits = limits;
  factory _SubscriptionPlan.fromJson(Map<String, dynamic> json) => _$SubscriptionPlanFromJson(json);

@override final  String id;
@override final  String name;
@override final  String nameAr;
@override final  String description;
@override final  String descriptionAr;
@override final  SubscriptionTier tier;
@override final  double monthlyPriceLD;
@override final  double yearlyPriceLD;
@override final  int monthlyListingQuota;
@override final  double additionalListingFeeLD;
@override@JsonKey() final  int trialDays;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  bool isPopular;
@override@JsonKey() final  bool hasPriorityListing;
@override@JsonKey() final  bool hasPrioritySupport;
@override@JsonKey() final  bool hasDedicatedSupport;
 final  List<String>? _benefits;
@override List<String>? get benefits {
  final value = _benefits;
  if (value == null) return null;
  if (_benefits is EqualUnmodifiableListView) return _benefits;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _benefitsAr;
@override List<String>? get benefitsAr {
  final value = _benefitsAr;
  if (value == null) return null;
  if (_benefitsAr is EqualUnmodifiableListView) return _benefitsAr;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  Map<String, dynamic>? _features;
@override Map<String, dynamic>? get features {
  final value = _features;
  if (value == null) return null;
  if (_features is EqualUnmodifiableMapView) return _features;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _limits;
@override Map<String, dynamic>? get limits {
  final value = _limits;
  if (value == null) return null;
  if (_limits is EqualUnmodifiableMapView) return _limits;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? stripePriceId;
@override final  String? stripeYearlyPriceId;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of SubscriptionPlan
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscriptionPlanCopyWith<_SubscriptionPlan> get copyWith => __$SubscriptionPlanCopyWithImpl<_SubscriptionPlan>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubscriptionPlanToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscriptionPlan&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.tier, tier) || other.tier == tier)&&(identical(other.monthlyPriceLD, monthlyPriceLD) || other.monthlyPriceLD == monthlyPriceLD)&&(identical(other.yearlyPriceLD, yearlyPriceLD) || other.yearlyPriceLD == yearlyPriceLD)&&(identical(other.monthlyListingQuota, monthlyListingQuota) || other.monthlyListingQuota == monthlyListingQuota)&&(identical(other.additionalListingFeeLD, additionalListingFeeLD) || other.additionalListingFeeLD == additionalListingFeeLD)&&(identical(other.trialDays, trialDays) || other.trialDays == trialDays)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isPopular, isPopular) || other.isPopular == isPopular)&&(identical(other.hasPriorityListing, hasPriorityListing) || other.hasPriorityListing == hasPriorityListing)&&(identical(other.hasPrioritySupport, hasPrioritySupport) || other.hasPrioritySupport == hasPrioritySupport)&&(identical(other.hasDedicatedSupport, hasDedicatedSupport) || other.hasDedicatedSupport == hasDedicatedSupport)&&const DeepCollectionEquality().equals(other._benefits, _benefits)&&const DeepCollectionEquality().equals(other._benefitsAr, _benefitsAr)&&const DeepCollectionEquality().equals(other._features, _features)&&const DeepCollectionEquality().equals(other._limits, _limits)&&(identical(other.stripePriceId, stripePriceId) || other.stripePriceId == stripePriceId)&&(identical(other.stripeYearlyPriceId, stripeYearlyPriceId) || other.stripeYearlyPriceId == stripeYearlyPriceId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,nameAr,description,descriptionAr,tier,monthlyPriceLD,yearlyPriceLD,monthlyListingQuota,additionalListingFeeLD,trialDays,isActive,isPopular,hasPriorityListing,hasPrioritySupport,hasDedicatedSupport,const DeepCollectionEquality().hash(_benefits),const DeepCollectionEquality().hash(_benefitsAr),const DeepCollectionEquality().hash(_features),const DeepCollectionEquality().hash(_limits),stripePriceId,stripeYearlyPriceId,createdAt,updatedAt]);

@override
String toString() {
  return 'SubscriptionPlan(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, tier: $tier, monthlyPriceLD: $monthlyPriceLD, yearlyPriceLD: $yearlyPriceLD, monthlyListingQuota: $monthlyListingQuota, additionalListingFeeLD: $additionalListingFeeLD, trialDays: $trialDays, isActive: $isActive, isPopular: $isPopular, hasPriorityListing: $hasPriorityListing, hasPrioritySupport: $hasPrioritySupport, hasDedicatedSupport: $hasDedicatedSupport, benefits: $benefits, benefitsAr: $benefitsAr, features: $features, limits: $limits, stripePriceId: $stripePriceId, stripeYearlyPriceId: $stripeYearlyPriceId, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SubscriptionPlanCopyWith<$Res> implements $SubscriptionPlanCopyWith<$Res> {
  factory _$SubscriptionPlanCopyWith(_SubscriptionPlan value, $Res Function(_SubscriptionPlan) _then) = __$SubscriptionPlanCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String nameAr, String description, String descriptionAr, SubscriptionTier tier, double monthlyPriceLD, double yearlyPriceLD, int monthlyListingQuota, double additionalListingFeeLD, int trialDays, bool isActive, bool isPopular, bool hasPriorityListing, bool hasPrioritySupport, bool hasDedicatedSupport, List<String>? benefits, List<String>? benefitsAr, Map<String, dynamic>? features, Map<String, dynamic>? limits, String? stripePriceId, String? stripeYearlyPriceId,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$SubscriptionPlanCopyWithImpl<$Res>
    implements _$SubscriptionPlanCopyWith<$Res> {
  __$SubscriptionPlanCopyWithImpl(this._self, this._then);

  final _SubscriptionPlan _self;
  final $Res Function(_SubscriptionPlan) _then;

/// Create a copy of SubscriptionPlan
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = null,Object? descriptionAr = null,Object? tier = null,Object? monthlyPriceLD = null,Object? yearlyPriceLD = null,Object? monthlyListingQuota = null,Object? additionalListingFeeLD = null,Object? trialDays = null,Object? isActive = null,Object? isPopular = null,Object? hasPriorityListing = null,Object? hasPrioritySupport = null,Object? hasDedicatedSupport = null,Object? benefits = freezed,Object? benefitsAr = freezed,Object? features = freezed,Object? limits = freezed,Object? stripePriceId = freezed,Object? stripeYearlyPriceId = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SubscriptionPlan(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,descriptionAr: null == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String,tier: null == tier ? _self.tier : tier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier,monthlyPriceLD: null == monthlyPriceLD ? _self.monthlyPriceLD : monthlyPriceLD // ignore: cast_nullable_to_non_nullable
as double,yearlyPriceLD: null == yearlyPriceLD ? _self.yearlyPriceLD : yearlyPriceLD // ignore: cast_nullable_to_non_nullable
as double,monthlyListingQuota: null == monthlyListingQuota ? _self.monthlyListingQuota : monthlyListingQuota // ignore: cast_nullable_to_non_nullable
as int,additionalListingFeeLD: null == additionalListingFeeLD ? _self.additionalListingFeeLD : additionalListingFeeLD // ignore: cast_nullable_to_non_nullable
as double,trialDays: null == trialDays ? _self.trialDays : trialDays // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isPopular: null == isPopular ? _self.isPopular : isPopular // ignore: cast_nullable_to_non_nullable
as bool,hasPriorityListing: null == hasPriorityListing ? _self.hasPriorityListing : hasPriorityListing // ignore: cast_nullable_to_non_nullable
as bool,hasPrioritySupport: null == hasPrioritySupport ? _self.hasPrioritySupport : hasPrioritySupport // ignore: cast_nullable_to_non_nullable
as bool,hasDedicatedSupport: null == hasDedicatedSupport ? _self.hasDedicatedSupport : hasDedicatedSupport // ignore: cast_nullable_to_non_nullable
as bool,benefits: freezed == benefits ? _self._benefits : benefits // ignore: cast_nullable_to_non_nullable
as List<String>?,benefitsAr: freezed == benefitsAr ? _self._benefitsAr : benefitsAr // ignore: cast_nullable_to_non_nullable
as List<String>?,features: freezed == features ? _self._features : features // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,limits: freezed == limits ? _self._limits : limits // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,stripePriceId: freezed == stripePriceId ? _self.stripePriceId : stripePriceId // ignore: cast_nullable_to_non_nullable
as String?,stripeYearlyPriceId: freezed == stripeYearlyPriceId ? _self.stripeYearlyPriceId : stripeYearlyPriceId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$MonthlyQuotaUsage {

 String get id; String get subscriptionId; String get sellerId; int get month; int get year; int get freeListingsUsed; int get paidListingsCount; double get totalPaidListingFeesLD; int get quotaRemaining; DateTime? get quotaResetDate; Map<String, int>? get dailyUsage;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of MonthlyQuotaUsage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MonthlyQuotaUsageCopyWith<MonthlyQuotaUsage> get copyWith => _$MonthlyQuotaUsageCopyWithImpl<MonthlyQuotaUsage>(this as MonthlyQuotaUsage, _$identity);

  /// Serializes this MonthlyQuotaUsage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MonthlyQuotaUsage&&(identical(other.id, id) || other.id == id)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.month, month) || other.month == month)&&(identical(other.year, year) || other.year == year)&&(identical(other.freeListingsUsed, freeListingsUsed) || other.freeListingsUsed == freeListingsUsed)&&(identical(other.paidListingsCount, paidListingsCount) || other.paidListingsCount == paidListingsCount)&&(identical(other.totalPaidListingFeesLD, totalPaidListingFeesLD) || other.totalPaidListingFeesLD == totalPaidListingFeesLD)&&(identical(other.quotaRemaining, quotaRemaining) || other.quotaRemaining == quotaRemaining)&&(identical(other.quotaResetDate, quotaResetDate) || other.quotaResetDate == quotaResetDate)&&const DeepCollectionEquality().equals(other.dailyUsage, dailyUsage)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,subscriptionId,sellerId,month,year,freeListingsUsed,paidListingsCount,totalPaidListingFeesLD,quotaRemaining,quotaResetDate,const DeepCollectionEquality().hash(dailyUsage),createdAt,updatedAt);

@override
String toString() {
  return 'MonthlyQuotaUsage(id: $id, subscriptionId: $subscriptionId, sellerId: $sellerId, month: $month, year: $year, freeListingsUsed: $freeListingsUsed, paidListingsCount: $paidListingsCount, totalPaidListingFeesLD: $totalPaidListingFeesLD, quotaRemaining: $quotaRemaining, quotaResetDate: $quotaResetDate, dailyUsage: $dailyUsage, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $MonthlyQuotaUsageCopyWith<$Res>  {
  factory $MonthlyQuotaUsageCopyWith(MonthlyQuotaUsage value, $Res Function(MonthlyQuotaUsage) _then) = _$MonthlyQuotaUsageCopyWithImpl;
@useResult
$Res call({
 String id, String subscriptionId, String sellerId, int month, int year, int freeListingsUsed, int paidListingsCount, double totalPaidListingFeesLD, int quotaRemaining, DateTime? quotaResetDate, Map<String, int>? dailyUsage,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$MonthlyQuotaUsageCopyWithImpl<$Res>
    implements $MonthlyQuotaUsageCopyWith<$Res> {
  _$MonthlyQuotaUsageCopyWithImpl(this._self, this._then);

  final MonthlyQuotaUsage _self;
  final $Res Function(MonthlyQuotaUsage) _then;

/// Create a copy of MonthlyQuotaUsage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? subscriptionId = null,Object? sellerId = null,Object? month = null,Object? year = null,Object? freeListingsUsed = null,Object? paidListingsCount = null,Object? totalPaidListingFeesLD = null,Object? quotaRemaining = null,Object? quotaResetDate = freezed,Object? dailyUsage = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,month: null == month ? _self.month : month // ignore: cast_nullable_to_non_nullable
as int,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,freeListingsUsed: null == freeListingsUsed ? _self.freeListingsUsed : freeListingsUsed // ignore: cast_nullable_to_non_nullable
as int,paidListingsCount: null == paidListingsCount ? _self.paidListingsCount : paidListingsCount // ignore: cast_nullable_to_non_nullable
as int,totalPaidListingFeesLD: null == totalPaidListingFeesLD ? _self.totalPaidListingFeesLD : totalPaidListingFeesLD // ignore: cast_nullable_to_non_nullable
as double,quotaRemaining: null == quotaRemaining ? _self.quotaRemaining : quotaRemaining // ignore: cast_nullable_to_non_nullable
as int,quotaResetDate: freezed == quotaResetDate ? _self.quotaResetDate : quotaResetDate // ignore: cast_nullable_to_non_nullable
as DateTime?,dailyUsage: freezed == dailyUsage ? _self.dailyUsage : dailyUsage // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [MonthlyQuotaUsage].
extension MonthlyQuotaUsagePatterns on MonthlyQuotaUsage {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MonthlyQuotaUsage value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MonthlyQuotaUsage() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MonthlyQuotaUsage value)  $default,){
final _that = this;
switch (_that) {
case _MonthlyQuotaUsage():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MonthlyQuotaUsage value)?  $default,){
final _that = this;
switch (_that) {
case _MonthlyQuotaUsage() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String subscriptionId,  String sellerId,  int month,  int year,  int freeListingsUsed,  int paidListingsCount,  double totalPaidListingFeesLD,  int quotaRemaining,  DateTime? quotaResetDate,  Map<String, int>? dailyUsage, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MonthlyQuotaUsage() when $default != null:
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.month,_that.year,_that.freeListingsUsed,_that.paidListingsCount,_that.totalPaidListingFeesLD,_that.quotaRemaining,_that.quotaResetDate,_that.dailyUsage,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String subscriptionId,  String sellerId,  int month,  int year,  int freeListingsUsed,  int paidListingsCount,  double totalPaidListingFeesLD,  int quotaRemaining,  DateTime? quotaResetDate,  Map<String, int>? dailyUsage, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _MonthlyQuotaUsage():
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.month,_that.year,_that.freeListingsUsed,_that.paidListingsCount,_that.totalPaidListingFeesLD,_that.quotaRemaining,_that.quotaResetDate,_that.dailyUsage,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String subscriptionId,  String sellerId,  int month,  int year,  int freeListingsUsed,  int paidListingsCount,  double totalPaidListingFeesLD,  int quotaRemaining,  DateTime? quotaResetDate,  Map<String, int>? dailyUsage, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _MonthlyQuotaUsage() when $default != null:
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.month,_that.year,_that.freeListingsUsed,_that.paidListingsCount,_that.totalPaidListingFeesLD,_that.quotaRemaining,_that.quotaResetDate,_that.dailyUsage,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _MonthlyQuotaUsage implements MonthlyQuotaUsage {
  const _MonthlyQuotaUsage({required this.id, required this.subscriptionId, required this.sellerId, required this.month, required this.year, this.freeListingsUsed = 0, this.paidListingsCount = 0, this.totalPaidListingFeesLD = 0.0, this.quotaRemaining = 0, this.quotaResetDate, final  Map<String, int>? dailyUsage, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _dailyUsage = dailyUsage;
  factory _MonthlyQuotaUsage.fromJson(Map<String, dynamic> json) => _$MonthlyQuotaUsageFromJson(json);

@override final  String id;
@override final  String subscriptionId;
@override final  String sellerId;
@override final  int month;
@override final  int year;
@override@JsonKey() final  int freeListingsUsed;
@override@JsonKey() final  int paidListingsCount;
@override@JsonKey() final  double totalPaidListingFeesLD;
@override@JsonKey() final  int quotaRemaining;
@override final  DateTime? quotaResetDate;
 final  Map<String, int>? _dailyUsage;
@override Map<String, int>? get dailyUsage {
  final value = _dailyUsage;
  if (value == null) return null;
  if (_dailyUsage is EqualUnmodifiableMapView) return _dailyUsage;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of MonthlyQuotaUsage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MonthlyQuotaUsageCopyWith<_MonthlyQuotaUsage> get copyWith => __$MonthlyQuotaUsageCopyWithImpl<_MonthlyQuotaUsage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MonthlyQuotaUsageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MonthlyQuotaUsage&&(identical(other.id, id) || other.id == id)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.month, month) || other.month == month)&&(identical(other.year, year) || other.year == year)&&(identical(other.freeListingsUsed, freeListingsUsed) || other.freeListingsUsed == freeListingsUsed)&&(identical(other.paidListingsCount, paidListingsCount) || other.paidListingsCount == paidListingsCount)&&(identical(other.totalPaidListingFeesLD, totalPaidListingFeesLD) || other.totalPaidListingFeesLD == totalPaidListingFeesLD)&&(identical(other.quotaRemaining, quotaRemaining) || other.quotaRemaining == quotaRemaining)&&(identical(other.quotaResetDate, quotaResetDate) || other.quotaResetDate == quotaResetDate)&&const DeepCollectionEquality().equals(other._dailyUsage, _dailyUsage)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,subscriptionId,sellerId,month,year,freeListingsUsed,paidListingsCount,totalPaidListingFeesLD,quotaRemaining,quotaResetDate,const DeepCollectionEquality().hash(_dailyUsage),createdAt,updatedAt);

@override
String toString() {
  return 'MonthlyQuotaUsage(id: $id, subscriptionId: $subscriptionId, sellerId: $sellerId, month: $month, year: $year, freeListingsUsed: $freeListingsUsed, paidListingsCount: $paidListingsCount, totalPaidListingFeesLD: $totalPaidListingFeesLD, quotaRemaining: $quotaRemaining, quotaResetDate: $quotaResetDate, dailyUsage: $dailyUsage, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$MonthlyQuotaUsageCopyWith<$Res> implements $MonthlyQuotaUsageCopyWith<$Res> {
  factory _$MonthlyQuotaUsageCopyWith(_MonthlyQuotaUsage value, $Res Function(_MonthlyQuotaUsage) _then) = __$MonthlyQuotaUsageCopyWithImpl;
@override @useResult
$Res call({
 String id, String subscriptionId, String sellerId, int month, int year, int freeListingsUsed, int paidListingsCount, double totalPaidListingFeesLD, int quotaRemaining, DateTime? quotaResetDate, Map<String, int>? dailyUsage,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$MonthlyQuotaUsageCopyWithImpl<$Res>
    implements _$MonthlyQuotaUsageCopyWith<$Res> {
  __$MonthlyQuotaUsageCopyWithImpl(this._self, this._then);

  final _MonthlyQuotaUsage _self;
  final $Res Function(_MonthlyQuotaUsage) _then;

/// Create a copy of MonthlyQuotaUsage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? subscriptionId = null,Object? sellerId = null,Object? month = null,Object? year = null,Object? freeListingsUsed = null,Object? paidListingsCount = null,Object? totalPaidListingFeesLD = null,Object? quotaRemaining = null,Object? quotaResetDate = freezed,Object? dailyUsage = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_MonthlyQuotaUsage(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,month: null == month ? _self.month : month // ignore: cast_nullable_to_non_nullable
as int,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,freeListingsUsed: null == freeListingsUsed ? _self.freeListingsUsed : freeListingsUsed // ignore: cast_nullable_to_non_nullable
as int,paidListingsCount: null == paidListingsCount ? _self.paidListingsCount : paidListingsCount // ignore: cast_nullable_to_non_nullable
as int,totalPaidListingFeesLD: null == totalPaidListingFeesLD ? _self.totalPaidListingFeesLD : totalPaidListingFeesLD // ignore: cast_nullable_to_non_nullable
as double,quotaRemaining: null == quotaRemaining ? _self.quotaRemaining : quotaRemaining // ignore: cast_nullable_to_non_nullable
as int,quotaResetDate: freezed == quotaResetDate ? _self.quotaResetDate : quotaResetDate // ignore: cast_nullable_to_non_nullable
as DateTime?,dailyUsage: freezed == dailyUsage ? _self._dailyUsage : dailyUsage // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$AutoRenewalConfig {

 String get id; String get subscriptionId; String get sellerId; bool get isEnabled; BillingCycle get preferredCycle; bool get preferYearlyForDiscount; DateTime? get lastRenewalDate; DateTime? get nextRenewalDate; int get renewalAttempts; int get failedAttempts; DateTime? get lastFailureDate; String? get lastFailureReason; int get reminderDaysBefore; DateTime? get lastReminderSent;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of AutoRenewalConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AutoRenewalConfigCopyWith<AutoRenewalConfig> get copyWith => _$AutoRenewalConfigCopyWithImpl<AutoRenewalConfig>(this as AutoRenewalConfig, _$identity);

  /// Serializes this AutoRenewalConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AutoRenewalConfig&&(identical(other.id, id) || other.id == id)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.preferredCycle, preferredCycle) || other.preferredCycle == preferredCycle)&&(identical(other.preferYearlyForDiscount, preferYearlyForDiscount) || other.preferYearlyForDiscount == preferYearlyForDiscount)&&(identical(other.lastRenewalDate, lastRenewalDate) || other.lastRenewalDate == lastRenewalDate)&&(identical(other.nextRenewalDate, nextRenewalDate) || other.nextRenewalDate == nextRenewalDate)&&(identical(other.renewalAttempts, renewalAttempts) || other.renewalAttempts == renewalAttempts)&&(identical(other.failedAttempts, failedAttempts) || other.failedAttempts == failedAttempts)&&(identical(other.lastFailureDate, lastFailureDate) || other.lastFailureDate == lastFailureDate)&&(identical(other.lastFailureReason, lastFailureReason) || other.lastFailureReason == lastFailureReason)&&(identical(other.reminderDaysBefore, reminderDaysBefore) || other.reminderDaysBefore == reminderDaysBefore)&&(identical(other.lastReminderSent, lastReminderSent) || other.lastReminderSent == lastReminderSent)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,subscriptionId,sellerId,isEnabled,preferredCycle,preferYearlyForDiscount,lastRenewalDate,nextRenewalDate,renewalAttempts,failedAttempts,lastFailureDate,lastFailureReason,reminderDaysBefore,lastReminderSent,createdAt,updatedAt);

@override
String toString() {
  return 'AutoRenewalConfig(id: $id, subscriptionId: $subscriptionId, sellerId: $sellerId, isEnabled: $isEnabled, preferredCycle: $preferredCycle, preferYearlyForDiscount: $preferYearlyForDiscount, lastRenewalDate: $lastRenewalDate, nextRenewalDate: $nextRenewalDate, renewalAttempts: $renewalAttempts, failedAttempts: $failedAttempts, lastFailureDate: $lastFailureDate, lastFailureReason: $lastFailureReason, reminderDaysBefore: $reminderDaysBefore, lastReminderSent: $lastReminderSent, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $AutoRenewalConfigCopyWith<$Res>  {
  factory $AutoRenewalConfigCopyWith(AutoRenewalConfig value, $Res Function(AutoRenewalConfig) _then) = _$AutoRenewalConfigCopyWithImpl;
@useResult
$Res call({
 String id, String subscriptionId, String sellerId, bool isEnabled, BillingCycle preferredCycle, bool preferYearlyForDiscount, DateTime? lastRenewalDate, DateTime? nextRenewalDate, int renewalAttempts, int failedAttempts, DateTime? lastFailureDate, String? lastFailureReason, int reminderDaysBefore, DateTime? lastReminderSent,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$AutoRenewalConfigCopyWithImpl<$Res>
    implements $AutoRenewalConfigCopyWith<$Res> {
  _$AutoRenewalConfigCopyWithImpl(this._self, this._then);

  final AutoRenewalConfig _self;
  final $Res Function(AutoRenewalConfig) _then;

/// Create a copy of AutoRenewalConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? subscriptionId = null,Object? sellerId = null,Object? isEnabled = null,Object? preferredCycle = null,Object? preferYearlyForDiscount = null,Object? lastRenewalDate = freezed,Object? nextRenewalDate = freezed,Object? renewalAttempts = null,Object? failedAttempts = null,Object? lastFailureDate = freezed,Object? lastFailureReason = freezed,Object? reminderDaysBefore = null,Object? lastReminderSent = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,preferredCycle: null == preferredCycle ? _self.preferredCycle : preferredCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,preferYearlyForDiscount: null == preferYearlyForDiscount ? _self.preferYearlyForDiscount : preferYearlyForDiscount // ignore: cast_nullable_to_non_nullable
as bool,lastRenewalDate: freezed == lastRenewalDate ? _self.lastRenewalDate : lastRenewalDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextRenewalDate: freezed == nextRenewalDate ? _self.nextRenewalDate : nextRenewalDate // ignore: cast_nullable_to_non_nullable
as DateTime?,renewalAttempts: null == renewalAttempts ? _self.renewalAttempts : renewalAttempts // ignore: cast_nullable_to_non_nullable
as int,failedAttempts: null == failedAttempts ? _self.failedAttempts : failedAttempts // ignore: cast_nullable_to_non_nullable
as int,lastFailureDate: freezed == lastFailureDate ? _self.lastFailureDate : lastFailureDate // ignore: cast_nullable_to_non_nullable
as DateTime?,lastFailureReason: freezed == lastFailureReason ? _self.lastFailureReason : lastFailureReason // ignore: cast_nullable_to_non_nullable
as String?,reminderDaysBefore: null == reminderDaysBefore ? _self.reminderDaysBefore : reminderDaysBefore // ignore: cast_nullable_to_non_nullable
as int,lastReminderSent: freezed == lastReminderSent ? _self.lastReminderSent : lastReminderSent // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [AutoRenewalConfig].
extension AutoRenewalConfigPatterns on AutoRenewalConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AutoRenewalConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AutoRenewalConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AutoRenewalConfig value)  $default,){
final _that = this;
switch (_that) {
case _AutoRenewalConfig():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AutoRenewalConfig value)?  $default,){
final _that = this;
switch (_that) {
case _AutoRenewalConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String subscriptionId,  String sellerId,  bool isEnabled,  BillingCycle preferredCycle,  bool preferYearlyForDiscount,  DateTime? lastRenewalDate,  DateTime? nextRenewalDate,  int renewalAttempts,  int failedAttempts,  DateTime? lastFailureDate,  String? lastFailureReason,  int reminderDaysBefore,  DateTime? lastReminderSent, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AutoRenewalConfig() when $default != null:
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.isEnabled,_that.preferredCycle,_that.preferYearlyForDiscount,_that.lastRenewalDate,_that.nextRenewalDate,_that.renewalAttempts,_that.failedAttempts,_that.lastFailureDate,_that.lastFailureReason,_that.reminderDaysBefore,_that.lastReminderSent,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String subscriptionId,  String sellerId,  bool isEnabled,  BillingCycle preferredCycle,  bool preferYearlyForDiscount,  DateTime? lastRenewalDate,  DateTime? nextRenewalDate,  int renewalAttempts,  int failedAttempts,  DateTime? lastFailureDate,  String? lastFailureReason,  int reminderDaysBefore,  DateTime? lastReminderSent, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _AutoRenewalConfig():
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.isEnabled,_that.preferredCycle,_that.preferYearlyForDiscount,_that.lastRenewalDate,_that.nextRenewalDate,_that.renewalAttempts,_that.failedAttempts,_that.lastFailureDate,_that.lastFailureReason,_that.reminderDaysBefore,_that.lastReminderSent,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String subscriptionId,  String sellerId,  bool isEnabled,  BillingCycle preferredCycle,  bool preferYearlyForDiscount,  DateTime? lastRenewalDate,  DateTime? nextRenewalDate,  int renewalAttempts,  int failedAttempts,  DateTime? lastFailureDate,  String? lastFailureReason,  int reminderDaysBefore,  DateTime? lastReminderSent, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _AutoRenewalConfig() when $default != null:
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.isEnabled,_that.preferredCycle,_that.preferYearlyForDiscount,_that.lastRenewalDate,_that.nextRenewalDate,_that.renewalAttempts,_that.failedAttempts,_that.lastFailureDate,_that.lastFailureReason,_that.reminderDaysBefore,_that.lastReminderSent,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _AutoRenewalConfig implements AutoRenewalConfig {
  const _AutoRenewalConfig({required this.id, required this.subscriptionId, required this.sellerId, this.isEnabled = true, required this.preferredCycle, this.preferYearlyForDiscount = false, this.lastRenewalDate, this.nextRenewalDate, this.renewalAttempts = 0, this.failedAttempts = 0, this.lastFailureDate, this.lastFailureReason, this.reminderDaysBefore = 7, this.lastReminderSent, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _AutoRenewalConfig.fromJson(Map<String, dynamic> json) => _$AutoRenewalConfigFromJson(json);

@override final  String id;
@override final  String subscriptionId;
@override final  String sellerId;
@override@JsonKey() final  bool isEnabled;
@override final  BillingCycle preferredCycle;
@override@JsonKey() final  bool preferYearlyForDiscount;
@override final  DateTime? lastRenewalDate;
@override final  DateTime? nextRenewalDate;
@override@JsonKey() final  int renewalAttempts;
@override@JsonKey() final  int failedAttempts;
@override final  DateTime? lastFailureDate;
@override final  String? lastFailureReason;
@override@JsonKey() final  int reminderDaysBefore;
@override final  DateTime? lastReminderSent;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of AutoRenewalConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AutoRenewalConfigCopyWith<_AutoRenewalConfig> get copyWith => __$AutoRenewalConfigCopyWithImpl<_AutoRenewalConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AutoRenewalConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AutoRenewalConfig&&(identical(other.id, id) || other.id == id)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.preferredCycle, preferredCycle) || other.preferredCycle == preferredCycle)&&(identical(other.preferYearlyForDiscount, preferYearlyForDiscount) || other.preferYearlyForDiscount == preferYearlyForDiscount)&&(identical(other.lastRenewalDate, lastRenewalDate) || other.lastRenewalDate == lastRenewalDate)&&(identical(other.nextRenewalDate, nextRenewalDate) || other.nextRenewalDate == nextRenewalDate)&&(identical(other.renewalAttempts, renewalAttempts) || other.renewalAttempts == renewalAttempts)&&(identical(other.failedAttempts, failedAttempts) || other.failedAttempts == failedAttempts)&&(identical(other.lastFailureDate, lastFailureDate) || other.lastFailureDate == lastFailureDate)&&(identical(other.lastFailureReason, lastFailureReason) || other.lastFailureReason == lastFailureReason)&&(identical(other.reminderDaysBefore, reminderDaysBefore) || other.reminderDaysBefore == reminderDaysBefore)&&(identical(other.lastReminderSent, lastReminderSent) || other.lastReminderSent == lastReminderSent)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,subscriptionId,sellerId,isEnabled,preferredCycle,preferYearlyForDiscount,lastRenewalDate,nextRenewalDate,renewalAttempts,failedAttempts,lastFailureDate,lastFailureReason,reminderDaysBefore,lastReminderSent,createdAt,updatedAt);

@override
String toString() {
  return 'AutoRenewalConfig(id: $id, subscriptionId: $subscriptionId, sellerId: $sellerId, isEnabled: $isEnabled, preferredCycle: $preferredCycle, preferYearlyForDiscount: $preferYearlyForDiscount, lastRenewalDate: $lastRenewalDate, nextRenewalDate: $nextRenewalDate, renewalAttempts: $renewalAttempts, failedAttempts: $failedAttempts, lastFailureDate: $lastFailureDate, lastFailureReason: $lastFailureReason, reminderDaysBefore: $reminderDaysBefore, lastReminderSent: $lastReminderSent, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$AutoRenewalConfigCopyWith<$Res> implements $AutoRenewalConfigCopyWith<$Res> {
  factory _$AutoRenewalConfigCopyWith(_AutoRenewalConfig value, $Res Function(_AutoRenewalConfig) _then) = __$AutoRenewalConfigCopyWithImpl;
@override @useResult
$Res call({
 String id, String subscriptionId, String sellerId, bool isEnabled, BillingCycle preferredCycle, bool preferYearlyForDiscount, DateTime? lastRenewalDate, DateTime? nextRenewalDate, int renewalAttempts, int failedAttempts, DateTime? lastFailureDate, String? lastFailureReason, int reminderDaysBefore, DateTime? lastReminderSent,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$AutoRenewalConfigCopyWithImpl<$Res>
    implements _$AutoRenewalConfigCopyWith<$Res> {
  __$AutoRenewalConfigCopyWithImpl(this._self, this._then);

  final _AutoRenewalConfig _self;
  final $Res Function(_AutoRenewalConfig) _then;

/// Create a copy of AutoRenewalConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? subscriptionId = null,Object? sellerId = null,Object? isEnabled = null,Object? preferredCycle = null,Object? preferYearlyForDiscount = null,Object? lastRenewalDate = freezed,Object? nextRenewalDate = freezed,Object? renewalAttempts = null,Object? failedAttempts = null,Object? lastFailureDate = freezed,Object? lastFailureReason = freezed,Object? reminderDaysBefore = null,Object? lastReminderSent = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_AutoRenewalConfig(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,preferredCycle: null == preferredCycle ? _self.preferredCycle : preferredCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,preferYearlyForDiscount: null == preferYearlyForDiscount ? _self.preferYearlyForDiscount : preferYearlyForDiscount // ignore: cast_nullable_to_non_nullable
as bool,lastRenewalDate: freezed == lastRenewalDate ? _self.lastRenewalDate : lastRenewalDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextRenewalDate: freezed == nextRenewalDate ? _self.nextRenewalDate : nextRenewalDate // ignore: cast_nullable_to_non_nullable
as DateTime?,renewalAttempts: null == renewalAttempts ? _self.renewalAttempts : renewalAttempts // ignore: cast_nullable_to_non_nullable
as int,failedAttempts: null == failedAttempts ? _self.failedAttempts : failedAttempts // ignore: cast_nullable_to_non_nullable
as int,lastFailureDate: freezed == lastFailureDate ? _self.lastFailureDate : lastFailureDate // ignore: cast_nullable_to_non_nullable
as DateTime?,lastFailureReason: freezed == lastFailureReason ? _self.lastFailureReason : lastFailureReason // ignore: cast_nullable_to_non_nullable
as String?,reminderDaysBefore: null == reminderDaysBefore ? _self.reminderDaysBefore : reminderDaysBefore // ignore: cast_nullable_to_non_nullable
as int,lastReminderSent: freezed == lastReminderSent ? _self.lastReminderSent : lastReminderSent // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$SubscriptionChangeHistory {

 String get id; String get subscriptionId; String get sellerId; DateTime get changeDate; SubscriptionTier? get fromTier; SubscriptionTier? get toTier; BillingCycle? get fromCycle; BillingCycle? get toCycle; SubscriptionStatus? get fromStatus; SubscriptionStatus? get toStatus; String get changeType;// upgrade, downgrade, cancel, renew, etc.
 String? get changeReason; double? get proratedAmount; double? get refundAmount; String? get processedBy;// system, user, admin
 Map<String, dynamic>? get metadata;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of SubscriptionChangeHistory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionChangeHistoryCopyWith<SubscriptionChangeHistory> get copyWith => _$SubscriptionChangeHistoryCopyWithImpl<SubscriptionChangeHistory>(this as SubscriptionChangeHistory, _$identity);

  /// Serializes this SubscriptionChangeHistory to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubscriptionChangeHistory&&(identical(other.id, id) || other.id == id)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.changeDate, changeDate) || other.changeDate == changeDate)&&(identical(other.fromTier, fromTier) || other.fromTier == fromTier)&&(identical(other.toTier, toTier) || other.toTier == toTier)&&(identical(other.fromCycle, fromCycle) || other.fromCycle == fromCycle)&&(identical(other.toCycle, toCycle) || other.toCycle == toCycle)&&(identical(other.fromStatus, fromStatus) || other.fromStatus == fromStatus)&&(identical(other.toStatus, toStatus) || other.toStatus == toStatus)&&(identical(other.changeType, changeType) || other.changeType == changeType)&&(identical(other.changeReason, changeReason) || other.changeReason == changeReason)&&(identical(other.proratedAmount, proratedAmount) || other.proratedAmount == proratedAmount)&&(identical(other.refundAmount, refundAmount) || other.refundAmount == refundAmount)&&(identical(other.processedBy, processedBy) || other.processedBy == processedBy)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,subscriptionId,sellerId,changeDate,fromTier,toTier,fromCycle,toCycle,fromStatus,toStatus,changeType,changeReason,proratedAmount,refundAmount,processedBy,const DeepCollectionEquality().hash(metadata),createdAt,updatedAt);

@override
String toString() {
  return 'SubscriptionChangeHistory(id: $id, subscriptionId: $subscriptionId, sellerId: $sellerId, changeDate: $changeDate, fromTier: $fromTier, toTier: $toTier, fromCycle: $fromCycle, toCycle: $toCycle, fromStatus: $fromStatus, toStatus: $toStatus, changeType: $changeType, changeReason: $changeReason, proratedAmount: $proratedAmount, refundAmount: $refundAmount, processedBy: $processedBy, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SubscriptionChangeHistoryCopyWith<$Res>  {
  factory $SubscriptionChangeHistoryCopyWith(SubscriptionChangeHistory value, $Res Function(SubscriptionChangeHistory) _then) = _$SubscriptionChangeHistoryCopyWithImpl;
@useResult
$Res call({
 String id, String subscriptionId, String sellerId, DateTime changeDate, SubscriptionTier? fromTier, SubscriptionTier? toTier, BillingCycle? fromCycle, BillingCycle? toCycle, SubscriptionStatus? fromStatus, SubscriptionStatus? toStatus, String changeType, String? changeReason, double? proratedAmount, double? refundAmount, String? processedBy, Map<String, dynamic>? metadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$SubscriptionChangeHistoryCopyWithImpl<$Res>
    implements $SubscriptionChangeHistoryCopyWith<$Res> {
  _$SubscriptionChangeHistoryCopyWithImpl(this._self, this._then);

  final SubscriptionChangeHistory _self;
  final $Res Function(SubscriptionChangeHistory) _then;

/// Create a copy of SubscriptionChangeHistory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? subscriptionId = null,Object? sellerId = null,Object? changeDate = null,Object? fromTier = freezed,Object? toTier = freezed,Object? fromCycle = freezed,Object? toCycle = freezed,Object? fromStatus = freezed,Object? toStatus = freezed,Object? changeType = null,Object? changeReason = freezed,Object? proratedAmount = freezed,Object? refundAmount = freezed,Object? processedBy = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,changeDate: null == changeDate ? _self.changeDate : changeDate // ignore: cast_nullable_to_non_nullable
as DateTime,fromTier: freezed == fromTier ? _self.fromTier : fromTier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier?,toTier: freezed == toTier ? _self.toTier : toTier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier?,fromCycle: freezed == fromCycle ? _self.fromCycle : fromCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle?,toCycle: freezed == toCycle ? _self.toCycle : toCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle?,fromStatus: freezed == fromStatus ? _self.fromStatus : fromStatus // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus?,toStatus: freezed == toStatus ? _self.toStatus : toStatus // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus?,changeType: null == changeType ? _self.changeType : changeType // ignore: cast_nullable_to_non_nullable
as String,changeReason: freezed == changeReason ? _self.changeReason : changeReason // ignore: cast_nullable_to_non_nullable
as String?,proratedAmount: freezed == proratedAmount ? _self.proratedAmount : proratedAmount // ignore: cast_nullable_to_non_nullable
as double?,refundAmount: freezed == refundAmount ? _self.refundAmount : refundAmount // ignore: cast_nullable_to_non_nullable
as double?,processedBy: freezed == processedBy ? _self.processedBy : processedBy // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SubscriptionChangeHistory].
extension SubscriptionChangeHistoryPatterns on SubscriptionChangeHistory {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SubscriptionChangeHistory value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SubscriptionChangeHistory() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SubscriptionChangeHistory value)  $default,){
final _that = this;
switch (_that) {
case _SubscriptionChangeHistory():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SubscriptionChangeHistory value)?  $default,){
final _that = this;
switch (_that) {
case _SubscriptionChangeHistory() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String subscriptionId,  String sellerId,  DateTime changeDate,  SubscriptionTier? fromTier,  SubscriptionTier? toTier,  BillingCycle? fromCycle,  BillingCycle? toCycle,  SubscriptionStatus? fromStatus,  SubscriptionStatus? toStatus,  String changeType,  String? changeReason,  double? proratedAmount,  double? refundAmount,  String? processedBy,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SubscriptionChangeHistory() when $default != null:
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.changeDate,_that.fromTier,_that.toTier,_that.fromCycle,_that.toCycle,_that.fromStatus,_that.toStatus,_that.changeType,_that.changeReason,_that.proratedAmount,_that.refundAmount,_that.processedBy,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String subscriptionId,  String sellerId,  DateTime changeDate,  SubscriptionTier? fromTier,  SubscriptionTier? toTier,  BillingCycle? fromCycle,  BillingCycle? toCycle,  SubscriptionStatus? fromStatus,  SubscriptionStatus? toStatus,  String changeType,  String? changeReason,  double? proratedAmount,  double? refundAmount,  String? processedBy,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SubscriptionChangeHistory():
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.changeDate,_that.fromTier,_that.toTier,_that.fromCycle,_that.toCycle,_that.fromStatus,_that.toStatus,_that.changeType,_that.changeReason,_that.proratedAmount,_that.refundAmount,_that.processedBy,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String subscriptionId,  String sellerId,  DateTime changeDate,  SubscriptionTier? fromTier,  SubscriptionTier? toTier,  BillingCycle? fromCycle,  BillingCycle? toCycle,  SubscriptionStatus? fromStatus,  SubscriptionStatus? toStatus,  String changeType,  String? changeReason,  double? proratedAmount,  double? refundAmount,  String? processedBy,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SubscriptionChangeHistory() when $default != null:
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.changeDate,_that.fromTier,_that.toTier,_that.fromCycle,_that.toCycle,_that.fromStatus,_that.toStatus,_that.changeType,_that.changeReason,_that.proratedAmount,_that.refundAmount,_that.processedBy,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SubscriptionChangeHistory implements SubscriptionChangeHistory {
  const _SubscriptionChangeHistory({required this.id, required this.subscriptionId, required this.sellerId, required this.changeDate, this.fromTier, this.toTier, this.fromCycle, this.toCycle, this.fromStatus, this.toStatus, required this.changeType, this.changeReason, this.proratedAmount, this.refundAmount, this.processedBy, final  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _metadata = metadata;
  factory _SubscriptionChangeHistory.fromJson(Map<String, dynamic> json) => _$SubscriptionChangeHistoryFromJson(json);

@override final  String id;
@override final  String subscriptionId;
@override final  String sellerId;
@override final  DateTime changeDate;
@override final  SubscriptionTier? fromTier;
@override final  SubscriptionTier? toTier;
@override final  BillingCycle? fromCycle;
@override final  BillingCycle? toCycle;
@override final  SubscriptionStatus? fromStatus;
@override final  SubscriptionStatus? toStatus;
@override final  String changeType;
// upgrade, downgrade, cancel, renew, etc.
@override final  String? changeReason;
@override final  double? proratedAmount;
@override final  double? refundAmount;
@override final  String? processedBy;
// system, user, admin
 final  Map<String, dynamic>? _metadata;
// system, user, admin
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of SubscriptionChangeHistory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscriptionChangeHistoryCopyWith<_SubscriptionChangeHistory> get copyWith => __$SubscriptionChangeHistoryCopyWithImpl<_SubscriptionChangeHistory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubscriptionChangeHistoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscriptionChangeHistory&&(identical(other.id, id) || other.id == id)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.changeDate, changeDate) || other.changeDate == changeDate)&&(identical(other.fromTier, fromTier) || other.fromTier == fromTier)&&(identical(other.toTier, toTier) || other.toTier == toTier)&&(identical(other.fromCycle, fromCycle) || other.fromCycle == fromCycle)&&(identical(other.toCycle, toCycle) || other.toCycle == toCycle)&&(identical(other.fromStatus, fromStatus) || other.fromStatus == fromStatus)&&(identical(other.toStatus, toStatus) || other.toStatus == toStatus)&&(identical(other.changeType, changeType) || other.changeType == changeType)&&(identical(other.changeReason, changeReason) || other.changeReason == changeReason)&&(identical(other.proratedAmount, proratedAmount) || other.proratedAmount == proratedAmount)&&(identical(other.refundAmount, refundAmount) || other.refundAmount == refundAmount)&&(identical(other.processedBy, processedBy) || other.processedBy == processedBy)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,subscriptionId,sellerId,changeDate,fromTier,toTier,fromCycle,toCycle,fromStatus,toStatus,changeType,changeReason,proratedAmount,refundAmount,processedBy,const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt);

@override
String toString() {
  return 'SubscriptionChangeHistory(id: $id, subscriptionId: $subscriptionId, sellerId: $sellerId, changeDate: $changeDate, fromTier: $fromTier, toTier: $toTier, fromCycle: $fromCycle, toCycle: $toCycle, fromStatus: $fromStatus, toStatus: $toStatus, changeType: $changeType, changeReason: $changeReason, proratedAmount: $proratedAmount, refundAmount: $refundAmount, processedBy: $processedBy, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SubscriptionChangeHistoryCopyWith<$Res> implements $SubscriptionChangeHistoryCopyWith<$Res> {
  factory _$SubscriptionChangeHistoryCopyWith(_SubscriptionChangeHistory value, $Res Function(_SubscriptionChangeHistory) _then) = __$SubscriptionChangeHistoryCopyWithImpl;
@override @useResult
$Res call({
 String id, String subscriptionId, String sellerId, DateTime changeDate, SubscriptionTier? fromTier, SubscriptionTier? toTier, BillingCycle? fromCycle, BillingCycle? toCycle, SubscriptionStatus? fromStatus, SubscriptionStatus? toStatus, String changeType, String? changeReason, double? proratedAmount, double? refundAmount, String? processedBy, Map<String, dynamic>? metadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$SubscriptionChangeHistoryCopyWithImpl<$Res>
    implements _$SubscriptionChangeHistoryCopyWith<$Res> {
  __$SubscriptionChangeHistoryCopyWithImpl(this._self, this._then);

  final _SubscriptionChangeHistory _self;
  final $Res Function(_SubscriptionChangeHistory) _then;

/// Create a copy of SubscriptionChangeHistory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? subscriptionId = null,Object? sellerId = null,Object? changeDate = null,Object? fromTier = freezed,Object? toTier = freezed,Object? fromCycle = freezed,Object? toCycle = freezed,Object? fromStatus = freezed,Object? toStatus = freezed,Object? changeType = null,Object? changeReason = freezed,Object? proratedAmount = freezed,Object? refundAmount = freezed,Object? processedBy = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SubscriptionChangeHistory(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,changeDate: null == changeDate ? _self.changeDate : changeDate // ignore: cast_nullable_to_non_nullable
as DateTime,fromTier: freezed == fromTier ? _self.fromTier : fromTier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier?,toTier: freezed == toTier ? _self.toTier : toTier // ignore: cast_nullable_to_non_nullable
as SubscriptionTier?,fromCycle: freezed == fromCycle ? _self.fromCycle : fromCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle?,toCycle: freezed == toCycle ? _self.toCycle : toCycle // ignore: cast_nullable_to_non_nullable
as BillingCycle?,fromStatus: freezed == fromStatus ? _self.fromStatus : fromStatus // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus?,toStatus: freezed == toStatus ? _self.toStatus : toStatus // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus?,changeType: null == changeType ? _self.changeType : changeType // ignore: cast_nullable_to_non_nullable
as String,changeReason: freezed == changeReason ? _self.changeReason : changeReason // ignore: cast_nullable_to_non_nullable
as String?,proratedAmount: freezed == proratedAmount ? _self.proratedAmount : proratedAmount // ignore: cast_nullable_to_non_nullable
as double?,refundAmount: freezed == refundAmount ? _self.refundAmount : refundAmount // ignore: cast_nullable_to_non_nullable
as double?,processedBy: freezed == processedBy ? _self.processedBy : processedBy // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$SubscriptionBilling {

 String get id; String get subscriptionId; String get sellerId; DateTime get billingDate; double get amount; String get currency; BillingCycle get cycle; ListingPaymentStatus get status; DateTime? get paidDate; DateTime? get dueDate; DateTime? get failedDate; String? get paymentMethodId; String? get transactionId; String? get invoiceId; String? get failureReason; double get discountAmount; double get taxAmount; Map<String, dynamic>? get billingMetadata;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of SubscriptionBilling
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionBillingCopyWith<SubscriptionBilling> get copyWith => _$SubscriptionBillingCopyWithImpl<SubscriptionBilling>(this as SubscriptionBilling, _$identity);

  /// Serializes this SubscriptionBilling to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubscriptionBilling&&(identical(other.id, id) || other.id == id)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.billingDate, billingDate) || other.billingDate == billingDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.cycle, cycle) || other.cycle == cycle)&&(identical(other.status, status) || other.status == status)&&(identical(other.paidDate, paidDate) || other.paidDate == paidDate)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.failedDate, failedDate) || other.failedDate == failedDate)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.failureReason, failureReason) || other.failureReason == failureReason)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&const DeepCollectionEquality().equals(other.billingMetadata, billingMetadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,subscriptionId,sellerId,billingDate,amount,currency,cycle,status,paidDate,dueDate,failedDate,paymentMethodId,transactionId,invoiceId,failureReason,discountAmount,taxAmount,const DeepCollectionEquality().hash(billingMetadata),createdAt,updatedAt]);

@override
String toString() {
  return 'SubscriptionBilling(id: $id, subscriptionId: $subscriptionId, sellerId: $sellerId, billingDate: $billingDate, amount: $amount, currency: $currency, cycle: $cycle, status: $status, paidDate: $paidDate, dueDate: $dueDate, failedDate: $failedDate, paymentMethodId: $paymentMethodId, transactionId: $transactionId, invoiceId: $invoiceId, failureReason: $failureReason, discountAmount: $discountAmount, taxAmount: $taxAmount, billingMetadata: $billingMetadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SubscriptionBillingCopyWith<$Res>  {
  factory $SubscriptionBillingCopyWith(SubscriptionBilling value, $Res Function(SubscriptionBilling) _then) = _$SubscriptionBillingCopyWithImpl;
@useResult
$Res call({
 String id, String subscriptionId, String sellerId, DateTime billingDate, double amount, String currency, BillingCycle cycle, ListingPaymentStatus status, DateTime? paidDate, DateTime? dueDate, DateTime? failedDate, String? paymentMethodId, String? transactionId, String? invoiceId, String? failureReason, double discountAmount, double taxAmount, Map<String, dynamic>? billingMetadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$SubscriptionBillingCopyWithImpl<$Res>
    implements $SubscriptionBillingCopyWith<$Res> {
  _$SubscriptionBillingCopyWithImpl(this._self, this._then);

  final SubscriptionBilling _self;
  final $Res Function(SubscriptionBilling) _then;

/// Create a copy of SubscriptionBilling
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? subscriptionId = null,Object? sellerId = null,Object? billingDate = null,Object? amount = null,Object? currency = null,Object? cycle = null,Object? status = null,Object? paidDate = freezed,Object? dueDate = freezed,Object? failedDate = freezed,Object? paymentMethodId = freezed,Object? transactionId = freezed,Object? invoiceId = freezed,Object? failureReason = freezed,Object? discountAmount = null,Object? taxAmount = null,Object? billingMetadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,billingDate: null == billingDate ? _self.billingDate : billingDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,cycle: null == cycle ? _self.cycle : cycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ListingPaymentStatus,paidDate: freezed == paidDate ? _self.paidDate : paidDate // ignore: cast_nullable_to_non_nullable
as DateTime?,dueDate: freezed == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime?,failedDate: freezed == failedDate ? _self.failedDate : failedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,invoiceId: freezed == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as String?,failureReason: freezed == failureReason ? _self.failureReason : failureReason // ignore: cast_nullable_to_non_nullable
as String?,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,billingMetadata: freezed == billingMetadata ? _self.billingMetadata : billingMetadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SubscriptionBilling].
extension SubscriptionBillingPatterns on SubscriptionBilling {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SubscriptionBilling value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SubscriptionBilling() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SubscriptionBilling value)  $default,){
final _that = this;
switch (_that) {
case _SubscriptionBilling():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SubscriptionBilling value)?  $default,){
final _that = this;
switch (_that) {
case _SubscriptionBilling() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String subscriptionId,  String sellerId,  DateTime billingDate,  double amount,  String currency,  BillingCycle cycle,  ListingPaymentStatus status,  DateTime? paidDate,  DateTime? dueDate,  DateTime? failedDate,  String? paymentMethodId,  String? transactionId,  String? invoiceId,  String? failureReason,  double discountAmount,  double taxAmount,  Map<String, dynamic>? billingMetadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SubscriptionBilling() when $default != null:
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.billingDate,_that.amount,_that.currency,_that.cycle,_that.status,_that.paidDate,_that.dueDate,_that.failedDate,_that.paymentMethodId,_that.transactionId,_that.invoiceId,_that.failureReason,_that.discountAmount,_that.taxAmount,_that.billingMetadata,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String subscriptionId,  String sellerId,  DateTime billingDate,  double amount,  String currency,  BillingCycle cycle,  ListingPaymentStatus status,  DateTime? paidDate,  DateTime? dueDate,  DateTime? failedDate,  String? paymentMethodId,  String? transactionId,  String? invoiceId,  String? failureReason,  double discountAmount,  double taxAmount,  Map<String, dynamic>? billingMetadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SubscriptionBilling():
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.billingDate,_that.amount,_that.currency,_that.cycle,_that.status,_that.paidDate,_that.dueDate,_that.failedDate,_that.paymentMethodId,_that.transactionId,_that.invoiceId,_that.failureReason,_that.discountAmount,_that.taxAmount,_that.billingMetadata,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String subscriptionId,  String sellerId,  DateTime billingDate,  double amount,  String currency,  BillingCycle cycle,  ListingPaymentStatus status,  DateTime? paidDate,  DateTime? dueDate,  DateTime? failedDate,  String? paymentMethodId,  String? transactionId,  String? invoiceId,  String? failureReason,  double discountAmount,  double taxAmount,  Map<String, dynamic>? billingMetadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SubscriptionBilling() when $default != null:
return $default(_that.id,_that.subscriptionId,_that.sellerId,_that.billingDate,_that.amount,_that.currency,_that.cycle,_that.status,_that.paidDate,_that.dueDate,_that.failedDate,_that.paymentMethodId,_that.transactionId,_that.invoiceId,_that.failureReason,_that.discountAmount,_that.taxAmount,_that.billingMetadata,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SubscriptionBilling implements SubscriptionBilling {
  const _SubscriptionBilling({required this.id, required this.subscriptionId, required this.sellerId, required this.billingDate, required this.amount, required this.currency, required this.cycle, required this.status, this.paidDate, this.dueDate, this.failedDate, this.paymentMethodId, this.transactionId, this.invoiceId, this.failureReason, this.discountAmount = 0.0, this.taxAmount = 0.0, final  Map<String, dynamic>? billingMetadata, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _billingMetadata = billingMetadata;
  factory _SubscriptionBilling.fromJson(Map<String, dynamic> json) => _$SubscriptionBillingFromJson(json);

@override final  String id;
@override final  String subscriptionId;
@override final  String sellerId;
@override final  DateTime billingDate;
@override final  double amount;
@override final  String currency;
@override final  BillingCycle cycle;
@override final  ListingPaymentStatus status;
@override final  DateTime? paidDate;
@override final  DateTime? dueDate;
@override final  DateTime? failedDate;
@override final  String? paymentMethodId;
@override final  String? transactionId;
@override final  String? invoiceId;
@override final  String? failureReason;
@override@JsonKey() final  double discountAmount;
@override@JsonKey() final  double taxAmount;
 final  Map<String, dynamic>? _billingMetadata;
@override Map<String, dynamic>? get billingMetadata {
  final value = _billingMetadata;
  if (value == null) return null;
  if (_billingMetadata is EqualUnmodifiableMapView) return _billingMetadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of SubscriptionBilling
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscriptionBillingCopyWith<_SubscriptionBilling> get copyWith => __$SubscriptionBillingCopyWithImpl<_SubscriptionBilling>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubscriptionBillingToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscriptionBilling&&(identical(other.id, id) || other.id == id)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.billingDate, billingDate) || other.billingDate == billingDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.cycle, cycle) || other.cycle == cycle)&&(identical(other.status, status) || other.status == status)&&(identical(other.paidDate, paidDate) || other.paidDate == paidDate)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.failedDate, failedDate) || other.failedDate == failedDate)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.failureReason, failureReason) || other.failureReason == failureReason)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&const DeepCollectionEquality().equals(other._billingMetadata, _billingMetadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,subscriptionId,sellerId,billingDate,amount,currency,cycle,status,paidDate,dueDate,failedDate,paymentMethodId,transactionId,invoiceId,failureReason,discountAmount,taxAmount,const DeepCollectionEquality().hash(_billingMetadata),createdAt,updatedAt]);

@override
String toString() {
  return 'SubscriptionBilling(id: $id, subscriptionId: $subscriptionId, sellerId: $sellerId, billingDate: $billingDate, amount: $amount, currency: $currency, cycle: $cycle, status: $status, paidDate: $paidDate, dueDate: $dueDate, failedDate: $failedDate, paymentMethodId: $paymentMethodId, transactionId: $transactionId, invoiceId: $invoiceId, failureReason: $failureReason, discountAmount: $discountAmount, taxAmount: $taxAmount, billingMetadata: $billingMetadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SubscriptionBillingCopyWith<$Res> implements $SubscriptionBillingCopyWith<$Res> {
  factory _$SubscriptionBillingCopyWith(_SubscriptionBilling value, $Res Function(_SubscriptionBilling) _then) = __$SubscriptionBillingCopyWithImpl;
@override @useResult
$Res call({
 String id, String subscriptionId, String sellerId, DateTime billingDate, double amount, String currency, BillingCycle cycle, ListingPaymentStatus status, DateTime? paidDate, DateTime? dueDate, DateTime? failedDate, String? paymentMethodId, String? transactionId, String? invoiceId, String? failureReason, double discountAmount, double taxAmount, Map<String, dynamic>? billingMetadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$SubscriptionBillingCopyWithImpl<$Res>
    implements _$SubscriptionBillingCopyWith<$Res> {
  __$SubscriptionBillingCopyWithImpl(this._self, this._then);

  final _SubscriptionBilling _self;
  final $Res Function(_SubscriptionBilling) _then;

/// Create a copy of SubscriptionBilling
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? subscriptionId = null,Object? sellerId = null,Object? billingDate = null,Object? amount = null,Object? currency = null,Object? cycle = null,Object? status = null,Object? paidDate = freezed,Object? dueDate = freezed,Object? failedDate = freezed,Object? paymentMethodId = freezed,Object? transactionId = freezed,Object? invoiceId = freezed,Object? failureReason = freezed,Object? discountAmount = null,Object? taxAmount = null,Object? billingMetadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SubscriptionBilling(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: null == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,billingDate: null == billingDate ? _self.billingDate : billingDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,cycle: null == cycle ? _self.cycle : cycle // ignore: cast_nullable_to_non_nullable
as BillingCycle,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ListingPaymentStatus,paidDate: freezed == paidDate ? _self.paidDate : paidDate // ignore: cast_nullable_to_non_nullable
as DateTime?,dueDate: freezed == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime?,failedDate: freezed == failedDate ? _self.failedDate : failedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,invoiceId: freezed == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as String?,failureReason: freezed == failureReason ? _self.failureReason : failureReason // ignore: cast_nullable_to_non_nullable
as String?,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,billingMetadata: freezed == billingMetadata ? _self._billingMetadata : billingMetadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$SubscriptionUsage {

 int get productsListed; int get productsLimit; int get imagesUploaded; int get imagesLimit; int get featuredProducts; int get featuredProductsLimit; int get analyticsViews; int get supportTickets; int get apiCalls; Map<String, int>? get customUsage;
/// Create a copy of SubscriptionUsage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionUsageCopyWith<SubscriptionUsage> get copyWith => _$SubscriptionUsageCopyWithImpl<SubscriptionUsage>(this as SubscriptionUsage, _$identity);

  /// Serializes this SubscriptionUsage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubscriptionUsage&&(identical(other.productsListed, productsListed) || other.productsListed == productsListed)&&(identical(other.productsLimit, productsLimit) || other.productsLimit == productsLimit)&&(identical(other.imagesUploaded, imagesUploaded) || other.imagesUploaded == imagesUploaded)&&(identical(other.imagesLimit, imagesLimit) || other.imagesLimit == imagesLimit)&&(identical(other.featuredProducts, featuredProducts) || other.featuredProducts == featuredProducts)&&(identical(other.featuredProductsLimit, featuredProductsLimit) || other.featuredProductsLimit == featuredProductsLimit)&&(identical(other.analyticsViews, analyticsViews) || other.analyticsViews == analyticsViews)&&(identical(other.supportTickets, supportTickets) || other.supportTickets == supportTickets)&&(identical(other.apiCalls, apiCalls) || other.apiCalls == apiCalls)&&const DeepCollectionEquality().equals(other.customUsage, customUsage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productsListed,productsLimit,imagesUploaded,imagesLimit,featuredProducts,featuredProductsLimit,analyticsViews,supportTickets,apiCalls,const DeepCollectionEquality().hash(customUsage));

@override
String toString() {
  return 'SubscriptionUsage(productsListed: $productsListed, productsLimit: $productsLimit, imagesUploaded: $imagesUploaded, imagesLimit: $imagesLimit, featuredProducts: $featuredProducts, featuredProductsLimit: $featuredProductsLimit, analyticsViews: $analyticsViews, supportTickets: $supportTickets, apiCalls: $apiCalls, customUsage: $customUsage)';
}


}

/// @nodoc
abstract mixin class $SubscriptionUsageCopyWith<$Res>  {
  factory $SubscriptionUsageCopyWith(SubscriptionUsage value, $Res Function(SubscriptionUsage) _then) = _$SubscriptionUsageCopyWithImpl;
@useResult
$Res call({
 int productsListed, int productsLimit, int imagesUploaded, int imagesLimit, int featuredProducts, int featuredProductsLimit, int analyticsViews, int supportTickets, int apiCalls, Map<String, int>? customUsage
});




}
/// @nodoc
class _$SubscriptionUsageCopyWithImpl<$Res>
    implements $SubscriptionUsageCopyWith<$Res> {
  _$SubscriptionUsageCopyWithImpl(this._self, this._then);

  final SubscriptionUsage _self;
  final $Res Function(SubscriptionUsage) _then;

/// Create a copy of SubscriptionUsage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productsListed = null,Object? productsLimit = null,Object? imagesUploaded = null,Object? imagesLimit = null,Object? featuredProducts = null,Object? featuredProductsLimit = null,Object? analyticsViews = null,Object? supportTickets = null,Object? apiCalls = null,Object? customUsage = freezed,}) {
  return _then(_self.copyWith(
productsListed: null == productsListed ? _self.productsListed : productsListed // ignore: cast_nullable_to_non_nullable
as int,productsLimit: null == productsLimit ? _self.productsLimit : productsLimit // ignore: cast_nullable_to_non_nullable
as int,imagesUploaded: null == imagesUploaded ? _self.imagesUploaded : imagesUploaded // ignore: cast_nullable_to_non_nullable
as int,imagesLimit: null == imagesLimit ? _self.imagesLimit : imagesLimit // ignore: cast_nullable_to_non_nullable
as int,featuredProducts: null == featuredProducts ? _self.featuredProducts : featuredProducts // ignore: cast_nullable_to_non_nullable
as int,featuredProductsLimit: null == featuredProductsLimit ? _self.featuredProductsLimit : featuredProductsLimit // ignore: cast_nullable_to_non_nullable
as int,analyticsViews: null == analyticsViews ? _self.analyticsViews : analyticsViews // ignore: cast_nullable_to_non_nullable
as int,supportTickets: null == supportTickets ? _self.supportTickets : supportTickets // ignore: cast_nullable_to_non_nullable
as int,apiCalls: null == apiCalls ? _self.apiCalls : apiCalls // ignore: cast_nullable_to_non_nullable
as int,customUsage: freezed == customUsage ? _self.customUsage : customUsage // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,
  ));
}

}


/// Adds pattern-matching-related methods to [SubscriptionUsage].
extension SubscriptionUsagePatterns on SubscriptionUsage {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SubscriptionUsage value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SubscriptionUsage() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SubscriptionUsage value)  $default,){
final _that = this;
switch (_that) {
case _SubscriptionUsage():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SubscriptionUsage value)?  $default,){
final _that = this;
switch (_that) {
case _SubscriptionUsage() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int productsListed,  int productsLimit,  int imagesUploaded,  int imagesLimit,  int featuredProducts,  int featuredProductsLimit,  int analyticsViews,  int supportTickets,  int apiCalls,  Map<String, int>? customUsage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SubscriptionUsage() when $default != null:
return $default(_that.productsListed,_that.productsLimit,_that.imagesUploaded,_that.imagesLimit,_that.featuredProducts,_that.featuredProductsLimit,_that.analyticsViews,_that.supportTickets,_that.apiCalls,_that.customUsage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int productsListed,  int productsLimit,  int imagesUploaded,  int imagesLimit,  int featuredProducts,  int featuredProductsLimit,  int analyticsViews,  int supportTickets,  int apiCalls,  Map<String, int>? customUsage)  $default,) {final _that = this;
switch (_that) {
case _SubscriptionUsage():
return $default(_that.productsListed,_that.productsLimit,_that.imagesUploaded,_that.imagesLimit,_that.featuredProducts,_that.featuredProductsLimit,_that.analyticsViews,_that.supportTickets,_that.apiCalls,_that.customUsage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int productsListed,  int productsLimit,  int imagesUploaded,  int imagesLimit,  int featuredProducts,  int featuredProductsLimit,  int analyticsViews,  int supportTickets,  int apiCalls,  Map<String, int>? customUsage)?  $default,) {final _that = this;
switch (_that) {
case _SubscriptionUsage() when $default != null:
return $default(_that.productsListed,_that.productsLimit,_that.imagesUploaded,_that.imagesLimit,_that.featuredProducts,_that.featuredProductsLimit,_that.analyticsViews,_that.supportTickets,_that.apiCalls,_that.customUsage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SubscriptionUsage implements SubscriptionUsage {
  const _SubscriptionUsage({this.productsListed = 0, this.productsLimit = 0, this.imagesUploaded = 0, this.imagesLimit = 0, this.featuredProducts = 0, this.featuredProductsLimit = 0, this.analyticsViews = 0, this.supportTickets = 0, this.apiCalls = 0, final  Map<String, int>? customUsage}): _customUsage = customUsage;
  factory _SubscriptionUsage.fromJson(Map<String, dynamic> json) => _$SubscriptionUsageFromJson(json);

@override@JsonKey() final  int productsListed;
@override@JsonKey() final  int productsLimit;
@override@JsonKey() final  int imagesUploaded;
@override@JsonKey() final  int imagesLimit;
@override@JsonKey() final  int featuredProducts;
@override@JsonKey() final  int featuredProductsLimit;
@override@JsonKey() final  int analyticsViews;
@override@JsonKey() final  int supportTickets;
@override@JsonKey() final  int apiCalls;
 final  Map<String, int>? _customUsage;
@override Map<String, int>? get customUsage {
  final value = _customUsage;
  if (value == null) return null;
  if (_customUsage is EqualUnmodifiableMapView) return _customUsage;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionUsage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscriptionUsageCopyWith<_SubscriptionUsage> get copyWith => __$SubscriptionUsageCopyWithImpl<_SubscriptionUsage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubscriptionUsageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscriptionUsage&&(identical(other.productsListed, productsListed) || other.productsListed == productsListed)&&(identical(other.productsLimit, productsLimit) || other.productsLimit == productsLimit)&&(identical(other.imagesUploaded, imagesUploaded) || other.imagesUploaded == imagesUploaded)&&(identical(other.imagesLimit, imagesLimit) || other.imagesLimit == imagesLimit)&&(identical(other.featuredProducts, featuredProducts) || other.featuredProducts == featuredProducts)&&(identical(other.featuredProductsLimit, featuredProductsLimit) || other.featuredProductsLimit == featuredProductsLimit)&&(identical(other.analyticsViews, analyticsViews) || other.analyticsViews == analyticsViews)&&(identical(other.supportTickets, supportTickets) || other.supportTickets == supportTickets)&&(identical(other.apiCalls, apiCalls) || other.apiCalls == apiCalls)&&const DeepCollectionEquality().equals(other._customUsage, _customUsage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productsListed,productsLimit,imagesUploaded,imagesLimit,featuredProducts,featuredProductsLimit,analyticsViews,supportTickets,apiCalls,const DeepCollectionEquality().hash(_customUsage));

@override
String toString() {
  return 'SubscriptionUsage(productsListed: $productsListed, productsLimit: $productsLimit, imagesUploaded: $imagesUploaded, imagesLimit: $imagesLimit, featuredProducts: $featuredProducts, featuredProductsLimit: $featuredProductsLimit, analyticsViews: $analyticsViews, supportTickets: $supportTickets, apiCalls: $apiCalls, customUsage: $customUsage)';
}


}

/// @nodoc
abstract mixin class _$SubscriptionUsageCopyWith<$Res> implements $SubscriptionUsageCopyWith<$Res> {
  factory _$SubscriptionUsageCopyWith(_SubscriptionUsage value, $Res Function(_SubscriptionUsage) _then) = __$SubscriptionUsageCopyWithImpl;
@override @useResult
$Res call({
 int productsListed, int productsLimit, int imagesUploaded, int imagesLimit, int featuredProducts, int featuredProductsLimit, int analyticsViews, int supportTickets, int apiCalls, Map<String, int>? customUsage
});




}
/// @nodoc
class __$SubscriptionUsageCopyWithImpl<$Res>
    implements _$SubscriptionUsageCopyWith<$Res> {
  __$SubscriptionUsageCopyWithImpl(this._self, this._then);

  final _SubscriptionUsage _self;
  final $Res Function(_SubscriptionUsage) _then;

/// Create a copy of SubscriptionUsage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productsListed = null,Object? productsLimit = null,Object? imagesUploaded = null,Object? imagesLimit = null,Object? featuredProducts = null,Object? featuredProductsLimit = null,Object? analyticsViews = null,Object? supportTickets = null,Object? apiCalls = null,Object? customUsage = freezed,}) {
  return _then(_SubscriptionUsage(
productsListed: null == productsListed ? _self.productsListed : productsListed // ignore: cast_nullable_to_non_nullable
as int,productsLimit: null == productsLimit ? _self.productsLimit : productsLimit // ignore: cast_nullable_to_non_nullable
as int,imagesUploaded: null == imagesUploaded ? _self.imagesUploaded : imagesUploaded // ignore: cast_nullable_to_non_nullable
as int,imagesLimit: null == imagesLimit ? _self.imagesLimit : imagesLimit // ignore: cast_nullable_to_non_nullable
as int,featuredProducts: null == featuredProducts ? _self.featuredProducts : featuredProducts // ignore: cast_nullable_to_non_nullable
as int,featuredProductsLimit: null == featuredProductsLimit ? _self.featuredProductsLimit : featuredProductsLimit // ignore: cast_nullable_to_non_nullable
as int,analyticsViews: null == analyticsViews ? _self.analyticsViews : analyticsViews // ignore: cast_nullable_to_non_nullable
as int,supportTickets: null == supportTickets ? _self.supportTickets : supportTickets // ignore: cast_nullable_to_non_nullable
as int,apiCalls: null == apiCalls ? _self.apiCalls : apiCalls // ignore: cast_nullable_to_non_nullable
as int,customUsage: freezed == customUsage ? _self._customUsage : customUsage // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,
  ));
}


}

// dart format on
