// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerSubscription _$SellerSubscriptionFromJson(Map<String, dynamic> json) =>
    _SellerSubscription(
      id: json['id'] as String,
      sellerId: json['seller_id'] as String,
      planId: json['plan_id'] as String,
      tier: $enumDecode(_$SubscriptionTierEnumMap, json['tier']),
      billingCycle: $enumDecode(_$BillingCycleEnumMap, json['billing_cycle']),
      status: $enumDecode(_$SubscriptionStatusEnumMap, json['status']),
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: json['end_date'] == null
          ? null
          : DateTime.parse(json['end_date'] as String),
      trialEndDate: json['trial_end_date'] == null
          ? null
          : DateTime.parse(json['trial_end_date'] as String),
      isTrialUsed: json['is_trial_used'] as bool? ?? false,
      priceLD: (json['price_l_d'] as num?)?.toDouble() ?? 0.0,
      monthlyListingQuota:
          (json['monthly_listing_quota'] as num?)?.toInt() ?? 0,
      additionalListingFeeLD:
          (json['additional_listing_fee_l_d'] as num?)?.toDouble() ?? 0.0,
      paymentMethodId: json['payment_method_id'] as String?,
      lastPaymentDate: json['last_payment_date'] == null
          ? null
          : DateTime.parse(json['last_payment_date'] as String),
      nextBillingDate: json['next_billing_date'] == null
          ? null
          : DateTime.parse(json['next_billing_date'] as String),
      autoRenewal: json['auto_renewal'] as bool? ?? false,
      yearlyDiscountPercentage:
          (json['yearly_discount_percentage'] as num?)?.toDouble() ?? 0.0,
      hasPriorityListing: json['has_priority_listing'] as bool? ?? false,
      hasPrioritySupport: json['has_priority_support'] as bool? ?? false,
      hasDedicatedSupport: json['has_dedicated_support'] as bool? ?? false,
      features: json['features'] as Map<String, dynamic>?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$SellerSubscriptionToJson(_SellerSubscription instance) =>
    <String, dynamic>{
      'id': instance.id,
      'seller_id': instance.sellerId,
      'plan_id': instance.planId,
      'tier': _$SubscriptionTierEnumMap[instance.tier]!,
      'billing_cycle': _$BillingCycleEnumMap[instance.billingCycle]!,
      'status': _$SubscriptionStatusEnumMap[instance.status]!,
      'start_date': instance.startDate.toIso8601String(),
      'end_date': instance.endDate?.toIso8601String(),
      'trial_end_date': instance.trialEndDate?.toIso8601String(),
      'is_trial_used': instance.isTrialUsed,
      'price_l_d': instance.priceLD,
      'monthly_listing_quota': instance.monthlyListingQuota,
      'additional_listing_fee_l_d': instance.additionalListingFeeLD,
      'payment_method_id': instance.paymentMethodId,
      'last_payment_date': instance.lastPaymentDate?.toIso8601String(),
      'next_billing_date': instance.nextBillingDate?.toIso8601String(),
      'auto_renewal': instance.autoRenewal,
      'yearly_discount_percentage': instance.yearlyDiscountPercentage,
      'has_priority_listing': instance.hasPriorityListing,
      'has_priority_support': instance.hasPrioritySupport,
      'has_dedicated_support': instance.hasDedicatedSupport,
      'features': instance.features,
      'metadata': instance.metadata,
    };

const _$SubscriptionTierEnumMap = {
  SubscriptionTier.starter: 'starter',
  SubscriptionTier.basic: 'basic',
  SubscriptionTier.premium: 'premium',
  SubscriptionTier.anchor: 'anchor',
  SubscriptionTier.enterprise: 'enterprise',
};

const _$BillingCycleEnumMap = {
  BillingCycle.monthly: 'monthly',
  BillingCycle.yearly: 'yearly',
};

const _$SubscriptionStatusEnumMap = {
  SubscriptionStatus.pendingApproval: 'pending_approval',
  SubscriptionStatus.trial: 'trial',
  SubscriptionStatus.active: 'active',
  SubscriptionStatus.pastDue: 'past_due',
  SubscriptionStatus.canceled: 'canceled',
  SubscriptionStatus.canceledPendingExpiry: 'canceled_pending_expiry',
  SubscriptionStatus.unpaid: 'unpaid',
  SubscriptionStatus.expired: 'expired',
  SubscriptionStatus.rejected: 'rejected',
  SubscriptionStatus.frozen: 'frozen',
};

_SubscriptionPlan _$SubscriptionPlanFromJson(Map<String, dynamic> json) =>
    _SubscriptionPlan(
      id: json['id'] as String,
      name: json['name'] as String,
      nameAr: json['name_ar'] as String,
      description: json['description'] as String,
      descriptionAr: json['description_ar'] as String,
      tier: $enumDecode(_$SubscriptionTierEnumMap, json['tier']),
      monthlyPriceLD: (json['monthly_price_l_d'] as num).toDouble(),
      yearlyPriceLD: (json['yearly_price_l_d'] as num).toDouble(),
      monthlyListingQuota: (json['monthly_listing_quota'] as num).toInt(),
      additionalListingFeeLD: (json['additional_listing_fee_l_d'] as num)
          .toDouble(),
      trialDays: (json['trial_days'] as num?)?.toInt() ?? 0,
      isActive: json['is_active'] as bool? ?? true,
      isPopular: json['is_popular'] as bool? ?? false,
      hasPriorityListing: json['has_priority_listing'] as bool? ?? false,
      hasPrioritySupport: json['has_priority_support'] as bool? ?? false,
      hasDedicatedSupport: json['has_dedicated_support'] as bool? ?? false,
      benefits: (json['benefits'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      benefitsAr: (json['benefits_ar'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      features: json['features'] as Map<String, dynamic>?,
      limits: json['limits'] as Map<String, dynamic>?,
      stripePriceId: json['stripe_price_id'] as String?,
      stripeYearlyPriceId: json['stripe_yearly_price_id'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$SubscriptionPlanToJson(_SubscriptionPlan instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'name_ar': instance.nameAr,
      'description': instance.description,
      'description_ar': instance.descriptionAr,
      'tier': _$SubscriptionTierEnumMap[instance.tier]!,
      'monthly_price_l_d': instance.monthlyPriceLD,
      'yearly_price_l_d': instance.yearlyPriceLD,
      'monthly_listing_quota': instance.monthlyListingQuota,
      'additional_listing_fee_l_d': instance.additionalListingFeeLD,
      'trial_days': instance.trialDays,
      'is_active': instance.isActive,
      'is_popular': instance.isPopular,
      'has_priority_listing': instance.hasPriorityListing,
      'has_priority_support': instance.hasPrioritySupport,
      'has_dedicated_support': instance.hasDedicatedSupport,
      'benefits': instance.benefits,
      'benefits_ar': instance.benefitsAr,
      'features': instance.features,
      'limits': instance.limits,
      'stripe_price_id': instance.stripePriceId,
      'stripe_yearly_price_id': instance.stripeYearlyPriceId,
    };

_MonthlyQuotaUsage _$MonthlyQuotaUsageFromJson(Map<String, dynamic> json) =>
    _MonthlyQuotaUsage(
      id: json['id'] as String,
      subscriptionId: json['subscription_id'] as String,
      sellerId: json['seller_id'] as String,
      month: (json['month'] as num).toInt(),
      year: (json['year'] as num).toInt(),
      freeListingsUsed: (json['free_listings_used'] as num?)?.toInt() ?? 0,
      paidListingsCount: (json['paid_listings_count'] as num?)?.toInt() ?? 0,
      totalPaidListingFeesLD:
          (json['total_paid_listing_fees_l_d'] as num?)?.toDouble() ?? 0.0,
      quotaRemaining: (json['quota_remaining'] as num?)?.toInt() ?? 0,
      quotaResetDate: json['quota_reset_date'] == null
          ? null
          : DateTime.parse(json['quota_reset_date'] as String),
      dailyUsage: (json['daily_usage'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$MonthlyQuotaUsageToJson(_MonthlyQuotaUsage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'subscription_id': instance.subscriptionId,
      'seller_id': instance.sellerId,
      'month': instance.month,
      'year': instance.year,
      'free_listings_used': instance.freeListingsUsed,
      'paid_listings_count': instance.paidListingsCount,
      'total_paid_listing_fees_l_d': instance.totalPaidListingFeesLD,
      'quota_remaining': instance.quotaRemaining,
      'quota_reset_date': instance.quotaResetDate?.toIso8601String(),
      'daily_usage': instance.dailyUsage,
    };

_AutoRenewalConfig _$AutoRenewalConfigFromJson(
  Map<String, dynamic> json,
) => _AutoRenewalConfig(
  id: json['id'] as String,
  subscriptionId: json['subscription_id'] as String,
  sellerId: json['seller_id'] as String,
  isEnabled: json['is_enabled'] as bool? ?? true,
  preferredCycle: $enumDecode(_$BillingCycleEnumMap, json['preferred_cycle']),
  preferYearlyForDiscount: json['prefer_yearly_for_discount'] as bool? ?? false,
  lastRenewalDate: json['last_renewal_date'] == null
      ? null
      : DateTime.parse(json['last_renewal_date'] as String),
  nextRenewalDate: json['next_renewal_date'] == null
      ? null
      : DateTime.parse(json['next_renewal_date'] as String),
  renewalAttempts: (json['renewal_attempts'] as num?)?.toInt() ?? 0,
  failedAttempts: (json['failed_attempts'] as num?)?.toInt() ?? 0,
  lastFailureDate: json['last_failure_date'] == null
      ? null
      : DateTime.parse(json['last_failure_date'] as String),
  lastFailureReason: json['last_failure_reason'] as String?,
  reminderDaysBefore: (json['reminder_days_before'] as num?)?.toInt() ?? 7,
  lastReminderSent: json['last_reminder_sent'] == null
      ? null
      : DateTime.parse(json['last_reminder_sent'] as String),
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$AutoRenewalConfigToJson(_AutoRenewalConfig instance) =>
    <String, dynamic>{
      'id': instance.id,
      'subscription_id': instance.subscriptionId,
      'seller_id': instance.sellerId,
      'is_enabled': instance.isEnabled,
      'preferred_cycle': _$BillingCycleEnumMap[instance.preferredCycle]!,
      'prefer_yearly_for_discount': instance.preferYearlyForDiscount,
      'last_renewal_date': instance.lastRenewalDate?.toIso8601String(),
      'next_renewal_date': instance.nextRenewalDate?.toIso8601String(),
      'renewal_attempts': instance.renewalAttempts,
      'failed_attempts': instance.failedAttempts,
      'last_failure_date': instance.lastFailureDate?.toIso8601String(),
      'last_failure_reason': instance.lastFailureReason,
      'reminder_days_before': instance.reminderDaysBefore,
      'last_reminder_sent': instance.lastReminderSent?.toIso8601String(),
    };

_SubscriptionChangeHistory _$SubscriptionChangeHistoryFromJson(
  Map<String, dynamic> json,
) => _SubscriptionChangeHistory(
  id: json['id'] as String,
  subscriptionId: json['subscription_id'] as String,
  sellerId: json['seller_id'] as String,
  changeDate: DateTime.parse(json['change_date'] as String),
  fromTier: $enumDecodeNullable(_$SubscriptionTierEnumMap, json['from_tier']),
  toTier: $enumDecodeNullable(_$SubscriptionTierEnumMap, json['to_tier']),
  fromCycle: $enumDecodeNullable(_$BillingCycleEnumMap, json['from_cycle']),
  toCycle: $enumDecodeNullable(_$BillingCycleEnumMap, json['to_cycle']),
  fromStatus: $enumDecodeNullable(
    _$SubscriptionStatusEnumMap,
    json['from_status'],
  ),
  toStatus: $enumDecodeNullable(_$SubscriptionStatusEnumMap, json['to_status']),
  changeType: json['change_type'] as String,
  changeReason: json['change_reason'] as String?,
  proratedAmount: (json['prorated_amount'] as num?)?.toDouble(),
  refundAmount: (json['refund_amount'] as num?)?.toDouble(),
  processedBy: json['processed_by'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$SubscriptionChangeHistoryToJson(
  _SubscriptionChangeHistory instance,
) => <String, dynamic>{
  'id': instance.id,
  'subscription_id': instance.subscriptionId,
  'seller_id': instance.sellerId,
  'change_date': instance.changeDate.toIso8601String(),
  'from_tier': _$SubscriptionTierEnumMap[instance.fromTier],
  'to_tier': _$SubscriptionTierEnumMap[instance.toTier],
  'from_cycle': _$BillingCycleEnumMap[instance.fromCycle],
  'to_cycle': _$BillingCycleEnumMap[instance.toCycle],
  'from_status': _$SubscriptionStatusEnumMap[instance.fromStatus],
  'to_status': _$SubscriptionStatusEnumMap[instance.toStatus],
  'change_type': instance.changeType,
  'change_reason': instance.changeReason,
  'prorated_amount': instance.proratedAmount,
  'refund_amount': instance.refundAmount,
  'processed_by': instance.processedBy,
  'metadata': instance.metadata,
};

_SubscriptionBilling _$SubscriptionBillingFromJson(Map<String, dynamic> json) =>
    _SubscriptionBilling(
      id: json['id'] as String,
      subscriptionId: json['subscription_id'] as String,
      sellerId: json['seller_id'] as String,
      billingDate: DateTime.parse(json['billing_date'] as String),
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      cycle: $enumDecode(_$BillingCycleEnumMap, json['cycle']),
      status: $enumDecode(_$ListingPaymentStatusEnumMap, json['status']),
      paidDate: json['paid_date'] == null
          ? null
          : DateTime.parse(json['paid_date'] as String),
      dueDate: json['due_date'] == null
          ? null
          : DateTime.parse(json['due_date'] as String),
      failedDate: json['failed_date'] == null
          ? null
          : DateTime.parse(json['failed_date'] as String),
      paymentMethodId: json['payment_method_id'] as String?,
      transactionId: json['transaction_id'] as String?,
      invoiceId: json['invoice_id'] as String?,
      failureReason: json['failure_reason'] as String?,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      taxAmount: (json['tax_amount'] as num?)?.toDouble() ?? 0.0,
      billingMetadata: json['billing_metadata'] as Map<String, dynamic>?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$SubscriptionBillingToJson(
  _SubscriptionBilling instance,
) => <String, dynamic>{
  'id': instance.id,
  'subscription_id': instance.subscriptionId,
  'seller_id': instance.sellerId,
  'billing_date': instance.billingDate.toIso8601String(),
  'amount': instance.amount,
  'currency': instance.currency,
  'cycle': _$BillingCycleEnumMap[instance.cycle]!,
  'status': _$ListingPaymentStatusEnumMap[instance.status]!,
  'paid_date': instance.paidDate?.toIso8601String(),
  'due_date': instance.dueDate?.toIso8601String(),
  'failed_date': instance.failedDate?.toIso8601String(),
  'payment_method_id': instance.paymentMethodId,
  'transaction_id': instance.transactionId,
  'invoice_id': instance.invoiceId,
  'failure_reason': instance.failureReason,
  'discount_amount': instance.discountAmount,
  'tax_amount': instance.taxAmount,
  'billing_metadata': instance.billingMetadata,
};

const _$ListingPaymentStatusEnumMap = {
  ListingPaymentStatus.pending: 'pending',
  ListingPaymentStatus.paid: 'paid',
  ListingPaymentStatus.failed: 'failed',
  ListingPaymentStatus.refunded: 'refunded',
  ListingPaymentStatus.waived: 'waived',
  ListingPaymentStatus.overdue: 'overdue',
};

_SubscriptionUsage _$SubscriptionUsageFromJson(Map<String, dynamic> json) =>
    _SubscriptionUsage(
      productsListed: (json['productsListed'] as num?)?.toInt() ?? 0,
      productsLimit: (json['productsLimit'] as num?)?.toInt() ?? 0,
      imagesUploaded: (json['imagesUploaded'] as num?)?.toInt() ?? 0,
      imagesLimit: (json['imagesLimit'] as num?)?.toInt() ?? 0,
      featuredProducts: (json['featuredProducts'] as num?)?.toInt() ?? 0,
      featuredProductsLimit:
          (json['featuredProductsLimit'] as num?)?.toInt() ?? 0,
      analyticsViews: (json['analyticsViews'] as num?)?.toInt() ?? 0,
      supportTickets: (json['supportTickets'] as num?)?.toInt() ?? 0,
      apiCalls: (json['apiCalls'] as num?)?.toInt() ?? 0,
      customUsage: (json['customUsage'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
    );

Map<String, dynamic> _$SubscriptionUsageToJson(_SubscriptionUsage instance) =>
    <String, dynamic>{
      'productsListed': instance.productsListed,
      'productsLimit': instance.productsLimit,
      'imagesUploaded': instance.imagesUploaded,
      'imagesLimit': instance.imagesLimit,
      'featuredProducts': instance.featuredProducts,
      'featuredProductsLimit': instance.featuredProductsLimit,
      'analyticsViews': instance.analyticsViews,
      'supportTickets': instance.supportTickets,
      'apiCalls': instance.apiCalls,
      'customUsage': instance.customUsage,
    };
