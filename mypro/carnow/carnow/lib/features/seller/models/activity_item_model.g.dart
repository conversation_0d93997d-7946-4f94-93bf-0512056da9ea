// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ActivityItemModel _$ActivityItemModelFromJson(Map<String, dynamic> json) =>
    _ActivityItemModel(
      productId: json['productId'] as String,
      type: $enumDecode(_$ActivityTypeEnumMap, json['type']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      id: json['id'] as String?,
      productName: json['productName'] as String?,
      userId: json['userId'] as String?,
      userName: json['userName'] as String?,
      details: json['details'] as String?,
    );

Map<String, dynamic> _$ActivityItemModelToJson(_ActivityItemModel instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'type': _$ActivityTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'id': instance.id,
      'productName': instance.productName,
      'userId': instance.userId,
      'userName': instance.userName,
      'details': instance.details,
    };

const _$ActivityTypeEnumMap = {
  ActivityType.productView: 'productView',
  ActivityType.productInquiry: 'productInquiry',
  ActivityType.productSale: 'productSale',
  ActivityType.statusChange: 'statusChange',
};
