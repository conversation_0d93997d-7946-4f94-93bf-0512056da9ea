// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_performance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductPerformanceModel _$ProductPerformanceModelFromJson(
  Map<String, dynamic> json,
) => ProductPerformanceModel(
  productId: json['product_id'] as String,
  productName: json['product_name'] as String,
  totalViews: (json['total_views'] as num?)?.toInt() ?? 0,
  totalClicks: (json['total_clicks'] as num?)?.toInt() ?? 0,
  totalUnitsSold: (json['total_units_sold'] as num?)?.toInt() ?? 0,
  totalRevenue: (json['total_revenue'] as num?)?.toDouble() ?? 0.0,
  conversionRate: (json['conversion_rate'] as num?)?.toDouble() ?? 0.0,
  averageOrderValue: (json['average_order_value'] as num?)?.toDouble() ?? 0.0,
  topSearchKeywords:
      (json['top_search_keywords'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  viewsByRegion:
      (json['views_by_region'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ) ??
      const {},
  salesByRegion:
      (json['sales_by_region'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ) ??
      const {},
  performanceOverTime:
      (json['performance_over_time'] as List<dynamic>?)
          ?.map((e) => PerformanceDataPoint.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$ProductPerformanceModelToJson(
  ProductPerformanceModel instance,
) => <String, dynamic>{
  'product_id': instance.productId,
  'product_name': instance.productName,
  'total_views': instance.totalViews,
  'total_clicks': instance.totalClicks,
  'total_units_sold': instance.totalUnitsSold,
  'total_revenue': instance.totalRevenue,
  'conversion_rate': instance.conversionRate,
  'average_order_value': instance.averageOrderValue,
  'top_search_keywords': instance.topSearchKeywords,
  'views_by_region': instance.viewsByRegion,
  'sales_by_region': instance.salesByRegion,
  'performance_over_time': instance.performanceOverTime,
};

PerformanceDataPoint _$PerformanceDataPointFromJson(
  Map<String, dynamic> json,
) => PerformanceDataPoint(
  date: DateTime.parse(json['date'] as String),
  views: (json['views'] as num).toInt(),
  clicks: (json['clicks'] as num).toInt(),
  sales: (json['sales'] as num).toInt(),
  revenue: (json['revenue'] as num).toDouble(),
);

Map<String, dynamic> _$PerformanceDataPointToJson(
  PerformanceDataPoint instance,
) => <String, dynamic>{
  'date': instance.date.toIso8601String(),
  'views': instance.views,
  'clicks': instance.clicks,
  'sales': instance.sales,
  'revenue': instance.revenue,
};
