// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_notification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerNotification {

 String get id; String get sellerId; String get title; String get message; String get notificationType; DateTime get createdAt; String? get relatedId; DateTime? get readAt;
/// Create a copy of SellerNotification
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerNotificationCopyWith<SellerNotification> get copyWith => _$SellerNotificationCopyWithImpl<SellerNotification>(this as SellerNotification, _$identity);

  /// Serializes this SellerNotification to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerNotification&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.title, title) || other.title == title)&&(identical(other.message, message) || other.message == message)&&(identical(other.notificationType, notificationType) || other.notificationType == notificationType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.relatedId, relatedId) || other.relatedId == relatedId)&&(identical(other.readAt, readAt) || other.readAt == readAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,title,message,notificationType,createdAt,relatedId,readAt);

@override
String toString() {
  return 'SellerNotification(id: $id, sellerId: $sellerId, title: $title, message: $message, notificationType: $notificationType, createdAt: $createdAt, relatedId: $relatedId, readAt: $readAt)';
}


}

/// @nodoc
abstract mixin class $SellerNotificationCopyWith<$Res>  {
  factory $SellerNotificationCopyWith(SellerNotification value, $Res Function(SellerNotification) _then) = _$SellerNotificationCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String title, String message, String notificationType, DateTime createdAt, String? relatedId, DateTime? readAt
});




}
/// @nodoc
class _$SellerNotificationCopyWithImpl<$Res>
    implements $SellerNotificationCopyWith<$Res> {
  _$SellerNotificationCopyWithImpl(this._self, this._then);

  final SellerNotification _self;
  final $Res Function(SellerNotification) _then;

/// Create a copy of SellerNotification
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? title = null,Object? message = null,Object? notificationType = null,Object? createdAt = null,Object? relatedId = freezed,Object? readAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,notificationType: null == notificationType ? _self.notificationType : notificationType // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,relatedId: freezed == relatedId ? _self.relatedId : relatedId // ignore: cast_nullable_to_non_nullable
as String?,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerNotification].
extension SellerNotificationPatterns on SellerNotification {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerNotification value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerNotification() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerNotification value)  $default,){
final _that = this;
switch (_that) {
case _SellerNotification():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerNotification value)?  $default,){
final _that = this;
switch (_that) {
case _SellerNotification() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String title,  String message,  String notificationType,  DateTime createdAt,  String? relatedId,  DateTime? readAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerNotification() when $default != null:
return $default(_that.id,_that.sellerId,_that.title,_that.message,_that.notificationType,_that.createdAt,_that.relatedId,_that.readAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String title,  String message,  String notificationType,  DateTime createdAt,  String? relatedId,  DateTime? readAt)  $default,) {final _that = this;
switch (_that) {
case _SellerNotification():
return $default(_that.id,_that.sellerId,_that.title,_that.message,_that.notificationType,_that.createdAt,_that.relatedId,_that.readAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String title,  String message,  String notificationType,  DateTime createdAt,  String? relatedId,  DateTime? readAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerNotification() when $default != null:
return $default(_that.id,_that.sellerId,_that.title,_that.message,_that.notificationType,_that.createdAt,_that.relatedId,_that.readAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SellerNotification implements SellerNotification {
  const _SellerNotification({required this.id, required this.sellerId, required this.title, required this.message, required this.notificationType, required this.createdAt, this.relatedId, this.readAt});
  factory _SellerNotification.fromJson(Map<String, dynamic> json) => _$SellerNotificationFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String title;
@override final  String message;
@override final  String notificationType;
@override final  DateTime createdAt;
@override final  String? relatedId;
@override final  DateTime? readAt;

/// Create a copy of SellerNotification
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerNotificationCopyWith<_SellerNotification> get copyWith => __$SellerNotificationCopyWithImpl<_SellerNotification>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerNotificationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerNotification&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.title, title) || other.title == title)&&(identical(other.message, message) || other.message == message)&&(identical(other.notificationType, notificationType) || other.notificationType == notificationType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.relatedId, relatedId) || other.relatedId == relatedId)&&(identical(other.readAt, readAt) || other.readAt == readAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sellerId,title,message,notificationType,createdAt,relatedId,readAt);

@override
String toString() {
  return 'SellerNotification(id: $id, sellerId: $sellerId, title: $title, message: $message, notificationType: $notificationType, createdAt: $createdAt, relatedId: $relatedId, readAt: $readAt)';
}


}

/// @nodoc
abstract mixin class _$SellerNotificationCopyWith<$Res> implements $SellerNotificationCopyWith<$Res> {
  factory _$SellerNotificationCopyWith(_SellerNotification value, $Res Function(_SellerNotification) _then) = __$SellerNotificationCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String title, String message, String notificationType, DateTime createdAt, String? relatedId, DateTime? readAt
});




}
/// @nodoc
class __$SellerNotificationCopyWithImpl<$Res>
    implements _$SellerNotificationCopyWith<$Res> {
  __$SellerNotificationCopyWithImpl(this._self, this._then);

  final _SellerNotification _self;
  final $Res Function(_SellerNotification) _then;

/// Create a copy of SellerNotification
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? title = null,Object? message = null,Object? notificationType = null,Object? createdAt = null,Object? relatedId = freezed,Object? readAt = freezed,}) {
  return _then(_SellerNotification(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,notificationType: null == notificationType ? _self.notificationType : notificationType // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,relatedId: freezed == relatedId ? _self.relatedId : relatedId // ignore: cast_nullable_to_non_nullable
as String?,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
