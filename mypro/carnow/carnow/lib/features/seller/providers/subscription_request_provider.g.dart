// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_request_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$hasPendingSubscriptionRequestHash() =>
    r'5be0b21e3d55f46913333245cf427ea8d801ea8b';

/// Provider for checking if user has pending subscription request
///
/// Copied from [hasPendingSubscriptionRequest].
@ProviderFor(hasPendingSubscriptionRequest)
final hasPendingSubscriptionRequestProvider =
    AutoDisposeProvider<bool>.internal(
      hasPendingSubscriptionRequest,
      name: r'hasPendingSubscriptionRequestProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$hasPendingSubscriptionRequestHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HasPendingSubscriptionRequestRef = AutoDisposeProviderRef<bool>;
String _$currentSubscriptionRequestHash() =>
    r'506dc872d06af1532d90ee9fafb426d7343ae6a3';

/// Provider for current subscription request
///
/// Copied from [currentSubscriptionRequest].
@ProviderFor(currentSubscriptionRequest)
final currentSubscriptionRequestProvider =
    AutoDisposeProvider<EnhancedSubscriptionRequest?>.internal(
      currentSubscriptionRequest,
      name: r'currentSubscriptionRequestProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentSubscriptionRequestHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentSubscriptionRequestRef =
    AutoDisposeProviderRef<EnhancedSubscriptionRequest?>;
String _$isSubscriptionRequestLoadingHash() =>
    r'a809deb8649ede78b0b3c36cd88338c74f60a019';

/// Provider for subscription request loading state
///
/// Copied from [isSubscriptionRequestLoading].
@ProviderFor(isSubscriptionRequestLoading)
final isSubscriptionRequestLoadingProvider = AutoDisposeProvider<bool>.internal(
  isSubscriptionRequestLoading,
  name: r'isSubscriptionRequestLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isSubscriptionRequestLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsSubscriptionRequestLoadingRef = AutoDisposeProviderRef<bool>;
String _$subscriptionRequestErrorHash() =>
    r'fde94f3c4ed7397c2921837309c4c7cf2d2782af';

/// Provider for subscription request error message
///
/// Copied from [subscriptionRequestError].
@ProviderFor(subscriptionRequestError)
final subscriptionRequestErrorProvider = AutoDisposeProvider<String?>.internal(
  subscriptionRequestError,
  name: r'subscriptionRequestErrorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$subscriptionRequestErrorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionRequestErrorRef = AutoDisposeProviderRef<String?>;
String _$subscriptionRequestErrorArHash() =>
    r'023a86beb4dd25279d0735d3f1bcd3a68ed5e9a8';

/// Provider for subscription request error message in Arabic
///
/// Copied from [subscriptionRequestErrorAr].
@ProviderFor(subscriptionRequestErrorAr)
final subscriptionRequestErrorArProvider =
    AutoDisposeProvider<String?>.internal(
      subscriptionRequestErrorAr,
      name: r'subscriptionRequestErrorArProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionRequestErrorArHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionRequestErrorArRef = AutoDisposeProviderRef<String?>;
String _$subscriptionRequestSuccessHash() =>
    r'413f52ad32ad06d97e93b98ec67be763d22b4f4e';

/// Provider for subscription request success message
///
/// Copied from [subscriptionRequestSuccess].
@ProviderFor(subscriptionRequestSuccess)
final subscriptionRequestSuccessProvider =
    AutoDisposeProvider<String?>.internal(
      subscriptionRequestSuccess,
      name: r'subscriptionRequestSuccessProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionRequestSuccessHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionRequestSuccessRef = AutoDisposeProviderRef<String?>;
String _$subscriptionRequestSuccessArHash() =>
    r'9bbd82f39972c9bcda5a936edc29a2dcc54e244e';

/// Provider for subscription request success message in Arabic
///
/// Copied from [subscriptionRequestSuccessAr].
@ProviderFor(subscriptionRequestSuccessAr)
final subscriptionRequestSuccessArProvider =
    AutoDisposeProvider<String?>.internal(
      subscriptionRequestSuccessAr,
      name: r'subscriptionRequestSuccessArProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionRequestSuccessArHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionRequestSuccessArRef = AutoDisposeProviderRef<String?>;
String _$sellerSubscriptionRequestProviderHash() =>
    r'32279aaf0239ecfb0ff8899277d1bb602a514d5d';

/// Provider for seller subscription request operations
///
/// Copied from [SellerSubscriptionRequestProvider].
@ProviderFor(SellerSubscriptionRequestProvider)
final sellerSubscriptionRequestProviderProvider =
    AutoDisposeNotifierProvider<
      SellerSubscriptionRequestProvider,
      SellerSubscriptionRequestState
    >.internal(
      SellerSubscriptionRequestProvider.new,
      name: r'sellerSubscriptionRequestProviderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerSubscriptionRequestProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerSubscriptionRequestProvider =
    AutoDisposeNotifier<SellerSubscriptionRequestState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
