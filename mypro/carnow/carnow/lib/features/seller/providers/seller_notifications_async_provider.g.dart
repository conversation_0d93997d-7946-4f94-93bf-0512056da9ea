// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_notifications_async_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$unreadNotificationsCountHash() =>
    r'2384e110ef7f1b799b93c6545c0fb603040a5e32';

/// Provider for unread notifications count
///
/// Copied from [unreadNotificationsCount].
@ProviderFor(unreadNotificationsCount)
final unreadNotificationsCountProvider = AutoDisposeProvider<int>.internal(
  unreadNotificationsCount,
  name: r'unreadNotificationsCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$unreadNotificationsCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UnreadNotificationsCountRef = AutoDisposeProviderRef<int>;
String _$sellerNotificationsAsyncHash() =>
    r'ba8af360d4241dc2e157b930e454624e23bda21c';

/// See also [SellerNotificationsAsync].
@ProviderFor(SellerNotificationsAsync)
final sellerNotificationsAsyncProvider =
    AutoDisposeAsyncNotifierProvider<
      SellerNotificationsAsync,
      List<SellerNotification>
    >.internal(
      SellerNotificationsAsync.new,
      name: r'sellerNotificationsAsyncProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerNotificationsAsyncHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerNotificationsAsync =
    AutoDisposeAsyncNotifier<List<SellerNotification>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
