// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sales_analytics_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$salesAnalyticsHash() => r'df37538054450e6f84768baaf4b963c675e6970b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Sales analytics provider - simplified version
///
/// Copied from [salesAnalytics].
@ProviderFor(salesAnalytics)
const salesAnalyticsProvider = SalesAnalyticsFamily();

/// Sales analytics provider - simplified version
///
/// Copied from [salesAnalytics].
class SalesAnalyticsFamily
    extends Family<AsyncValue<List<SalesAnalyticsPoint>>> {
  /// Sales analytics provider - simplified version
  ///
  /// Copied from [salesAnalytics].
  const SalesAnalyticsFamily();

  /// Sales analytics provider - simplified version
  ///
  /// Copied from [salesAnalytics].
  SalesAnalyticsProvider call(SalesAnalyticsParams params) {
    return SalesAnalyticsProvider(params);
  }

  @override
  SalesAnalyticsProvider getProviderOverride(
    covariant SalesAnalyticsProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'salesAnalyticsProvider';
}

/// Sales analytics provider - simplified version
///
/// Copied from [salesAnalytics].
class SalesAnalyticsProvider
    extends AutoDisposeFutureProvider<List<SalesAnalyticsPoint>> {
  /// Sales analytics provider - simplified version
  ///
  /// Copied from [salesAnalytics].
  SalesAnalyticsProvider(SalesAnalyticsParams params)
    : this._internal(
        (ref) => salesAnalytics(ref as SalesAnalyticsRef, params),
        from: salesAnalyticsProvider,
        name: r'salesAnalyticsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$salesAnalyticsHash,
        dependencies: SalesAnalyticsFamily._dependencies,
        allTransitiveDependencies:
            SalesAnalyticsFamily._allTransitiveDependencies,
        params: params,
      );

  SalesAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final SalesAnalyticsParams params;

  @override
  Override overrideWith(
    FutureOr<List<SalesAnalyticsPoint>> Function(SalesAnalyticsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SalesAnalyticsProvider._internal(
        (ref) => create(ref as SalesAnalyticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<SalesAnalyticsPoint>> createElement() {
    return _SalesAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SalesAnalyticsProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SalesAnalyticsRef
    on AutoDisposeFutureProviderRef<List<SalesAnalyticsPoint>> {
  /// The parameter `params` of this provider.
  SalesAnalyticsParams get params;
}

class _SalesAnalyticsProviderElement
    extends AutoDisposeFutureProviderElement<List<SalesAnalyticsPoint>>
    with SalesAnalyticsRef {
  _SalesAnalyticsProviderElement(super.provider);

  @override
  SalesAnalyticsParams get params => (origin as SalesAnalyticsProvider).params;
}

String _$productPerformanceHash() =>
    r'9323b3e7cb3a1231f0c267ff9873dd436da9aab3';

/// Product performance provider - simplified
///
/// Copied from [productPerformance].
@ProviderFor(productPerformance)
const productPerformanceProvider = ProductPerformanceFamily();

/// Product performance provider - simplified
///
/// Copied from [productPerformance].
class ProductPerformanceFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// Product performance provider - simplified
  ///
  /// Copied from [productPerformance].
  const ProductPerformanceFamily();

  /// Product performance provider - simplified
  ///
  /// Copied from [productPerformance].
  ProductPerformanceProvider call(SalesAnalyticsParams params) {
    return ProductPerformanceProvider(params);
  }

  @override
  ProductPerformanceProvider getProviderOverride(
    covariant ProductPerformanceProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productPerformanceProvider';
}

/// Product performance provider - simplified
///
/// Copied from [productPerformance].
class ProductPerformanceProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// Product performance provider - simplified
  ///
  /// Copied from [productPerformance].
  ProductPerformanceProvider(SalesAnalyticsParams params)
    : this._internal(
        (ref) => productPerformance(ref as ProductPerformanceRef, params),
        from: productPerformanceProvider,
        name: r'productPerformanceProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productPerformanceHash,
        dependencies: ProductPerformanceFamily._dependencies,
        allTransitiveDependencies:
            ProductPerformanceFamily._allTransitiveDependencies,
        params: params,
      );

  ProductPerformanceProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final SalesAnalyticsParams params;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
      ProductPerformanceRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductPerformanceProvider._internal(
        (ref) => create(ref as ProductPerformanceRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _ProductPerformanceProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductPerformanceProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductPerformanceRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `params` of this provider.
  SalesAnalyticsParams get params;
}

class _ProductPerformanceProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with ProductPerformanceRef {
  _ProductPerformanceProviderElement(super.provider);

  @override
  SalesAnalyticsParams get params =>
      (origin as ProductPerformanceProvider).params;
}

String _$customerInsightsHash() => r'4451d4fb028153c0df6d16333df91f4d5280cf75';

/// Customer insights provider - simplified
///
/// Copied from [customerInsights].
@ProviderFor(customerInsights)
const customerInsightsProvider = CustomerInsightsFamily();

/// Customer insights provider - simplified
///
/// Copied from [customerInsights].
class CustomerInsightsFamily extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Customer insights provider - simplified
  ///
  /// Copied from [customerInsights].
  const CustomerInsightsFamily();

  /// Customer insights provider - simplified
  ///
  /// Copied from [customerInsights].
  CustomerInsightsProvider call(SalesAnalyticsParams params) {
    return CustomerInsightsProvider(params);
  }

  @override
  CustomerInsightsProvider getProviderOverride(
    covariant CustomerInsightsProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'customerInsightsProvider';
}

/// Customer insights provider - simplified
///
/// Copied from [customerInsights].
class CustomerInsightsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// Customer insights provider - simplified
  ///
  /// Copied from [customerInsights].
  CustomerInsightsProvider(SalesAnalyticsParams params)
    : this._internal(
        (ref) => customerInsights(ref as CustomerInsightsRef, params),
        from: customerInsightsProvider,
        name: r'customerInsightsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$customerInsightsHash,
        dependencies: CustomerInsightsFamily._dependencies,
        allTransitiveDependencies:
            CustomerInsightsFamily._allTransitiveDependencies,
        params: params,
      );

  CustomerInsightsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final SalesAnalyticsParams params;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(CustomerInsightsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CustomerInsightsProvider._internal(
        (ref) => create(ref as CustomerInsightsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _CustomerInsightsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CustomerInsightsProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CustomerInsightsRef
    on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `params` of this provider.
  SalesAnalyticsParams get params;
}

class _CustomerInsightsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with CustomerInsightsRef {
  _CustomerInsightsProviderElement(super.provider);

  @override
  SalesAnalyticsParams get params =>
      (origin as CustomerInsightsProvider).params;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
