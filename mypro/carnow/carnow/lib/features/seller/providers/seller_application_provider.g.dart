// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_application_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$canApplyAsSellerHash() => r'e9e6e62c81b653914f3c13e9f91ff7ab228453d6';

/// See also [canApplyAsSeller].
@ProviderFor(canApplyAsSeller)
final canApplyAsSellerProvider = AutoDisposeFutureProvider<bool>.internal(
  canApplyAsSeller,
  name: r'canApplyAsSellerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$canApplyAsSellerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CanApplyAsSellerRef = AutoDisposeFutureProviderRef<bool>;
String _$sellerApplicationLogsHash() =>
    r'33823c954a4f7b20b975e8bb5c1ae2b661f5f8bf';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [sellerApplicationLogs].
@ProviderFor(sellerApplicationLogs)
const sellerApplicationLogsProvider = SellerApplicationLogsFamily();

/// See also [sellerApplicationLogs].
class SellerApplicationLogsFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// See also [sellerApplicationLogs].
  const SellerApplicationLogsFamily();

  /// See also [sellerApplicationLogs].
  SellerApplicationLogsProvider call(String applicationId) {
    return SellerApplicationLogsProvider(applicationId);
  }

  @override
  SellerApplicationLogsProvider getProviderOverride(
    covariant SellerApplicationLogsProvider provider,
  ) {
    return call(provider.applicationId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sellerApplicationLogsProvider';
}

/// See also [sellerApplicationLogs].
class SellerApplicationLogsProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// See also [sellerApplicationLogs].
  SellerApplicationLogsProvider(String applicationId)
    : this._internal(
        (ref) => sellerApplicationLogs(
          ref as SellerApplicationLogsRef,
          applicationId,
        ),
        from: sellerApplicationLogsProvider,
        name: r'sellerApplicationLogsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sellerApplicationLogsHash,
        dependencies: SellerApplicationLogsFamily._dependencies,
        allTransitiveDependencies:
            SellerApplicationLogsFamily._allTransitiveDependencies,
        applicationId: applicationId,
      );

  SellerApplicationLogsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.applicationId,
  }) : super.internal();

  final String applicationId;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
      SellerApplicationLogsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SellerApplicationLogsProvider._internal(
        (ref) => create(ref as SellerApplicationLogsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        applicationId: applicationId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _SellerApplicationLogsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SellerApplicationLogsProvider &&
        other.applicationId == applicationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, applicationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SellerApplicationLogsRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `applicationId` of this provider.
  String get applicationId;
}

class _SellerApplicationLogsProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with SellerApplicationLogsRef {
  _SellerApplicationLogsProviderElement(super.provider);

  @override
  String get applicationId =>
      (origin as SellerApplicationLogsProvider).applicationId;
}

String _$sellerApplicationHash() => r'bd046a474d732ee0a15de449fd86b14a98ec071f';

/// See also [SellerApplication].
@ProviderFor(SellerApplication)
final sellerApplicationProvider =
    AutoDisposeAsyncNotifierProvider<
      SellerApplication,
      SellerApplicationModel?
    >.internal(
      SellerApplication.new,
      name: r'sellerApplicationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerApplicationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerApplication = AutoDisposeAsyncNotifier<SellerApplicationModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
