// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionPlansHash() => r'037d7331cdd911a340e13d99a4162d3706ae9cb0';

/// Provider للحصول على خطط الاشتراك
///
/// Copied from [subscriptionPlans].
@ProviderFor(subscriptionPlans)
final subscriptionPlansProvider =
    AutoDisposeFutureProvider<List<SubscriptionPlan>>.internal(
      subscriptionPlans,
      name: r'subscriptionPlansProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionPlansHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionPlansRef =
    AutoDisposeFutureProviderRef<List<SubscriptionPlan>>;
String _$subscriptionPlanHash() => r'c3e65eb5d6a56f578446835fdcb8d106bc781a39';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider للحصول على خطة اشتراك محددة
///
/// Copied from [subscriptionPlan].
@ProviderFor(subscriptionPlan)
const subscriptionPlanProvider = SubscriptionPlanFamily();

/// Provider للحصول على خطة اشتراك محددة
///
/// Copied from [subscriptionPlan].
class SubscriptionPlanFamily extends Family<AsyncValue<SubscriptionPlan>> {
  /// Provider للحصول على خطة اشتراك محددة
  ///
  /// Copied from [subscriptionPlan].
  const SubscriptionPlanFamily();

  /// Provider للحصول على خطة اشتراك محددة
  ///
  /// Copied from [subscriptionPlan].
  SubscriptionPlanProvider call(int planId) {
    return SubscriptionPlanProvider(planId);
  }

  @override
  SubscriptionPlanProvider getProviderOverride(
    covariant SubscriptionPlanProvider provider,
  ) {
    return call(provider.planId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'subscriptionPlanProvider';
}

/// Provider للحصول على خطة اشتراك محددة
///
/// Copied from [subscriptionPlan].
class SubscriptionPlanProvider
    extends AutoDisposeFutureProvider<SubscriptionPlan> {
  /// Provider للحصول على خطة اشتراك محددة
  ///
  /// Copied from [subscriptionPlan].
  SubscriptionPlanProvider(int planId)
    : this._internal(
        (ref) => subscriptionPlan(ref as SubscriptionPlanRef, planId),
        from: subscriptionPlanProvider,
        name: r'subscriptionPlanProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$subscriptionPlanHash,
        dependencies: SubscriptionPlanFamily._dependencies,
        allTransitiveDependencies:
            SubscriptionPlanFamily._allTransitiveDependencies,
        planId: planId,
      );

  SubscriptionPlanProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.planId,
  }) : super.internal();

  final int planId;

  @override
  Override overrideWith(
    FutureOr<SubscriptionPlan> Function(SubscriptionPlanRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SubscriptionPlanProvider._internal(
        (ref) => create(ref as SubscriptionPlanRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        planId: planId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SubscriptionPlan> createElement() {
    return _SubscriptionPlanProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SubscriptionPlanProvider && other.planId == planId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, planId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SubscriptionPlanRef on AutoDisposeFutureProviderRef<SubscriptionPlan> {
  /// The parameter `planId` of this provider.
  int get planId;
}

class _SubscriptionPlanProviderElement
    extends AutoDisposeFutureProviderElement<SubscriptionPlan>
    with SubscriptionPlanRef {
  _SubscriptionPlanProviderElement(super.provider);

  @override
  int get planId => (origin as SubscriptionPlanProvider).planId;
}

String _$subscriptionPriceCalculationHash() =>
    r'1a732e27b867e903df28a1f0dc693248b6b45cc8';

/// Provider لحساب سعر الاشتراك
///
/// Copied from [subscriptionPriceCalculation].
@ProviderFor(subscriptionPriceCalculation)
const subscriptionPriceCalculationProvider =
    SubscriptionPriceCalculationFamily();

/// Provider لحساب سعر الاشتراك
///
/// Copied from [subscriptionPriceCalculation].
class SubscriptionPriceCalculationFamily
    extends Family<AsyncValue<DiscountCalculation>> {
  /// Provider لحساب سعر الاشتراك
  ///
  /// Copied from [subscriptionPriceCalculation].
  const SubscriptionPriceCalculationFamily();

  /// Provider لحساب سعر الاشتراك
  ///
  /// Copied from [subscriptionPriceCalculation].
  SubscriptionPriceCalculationProvider call({
    required int planId,
    required String billingCycle,
    String? discountCode,
  }) {
    return SubscriptionPriceCalculationProvider(
      planId: planId,
      billingCycle: billingCycle,
      discountCode: discountCode,
    );
  }

  @override
  SubscriptionPriceCalculationProvider getProviderOverride(
    covariant SubscriptionPriceCalculationProvider provider,
  ) {
    return call(
      planId: provider.planId,
      billingCycle: provider.billingCycle,
      discountCode: provider.discountCode,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'subscriptionPriceCalculationProvider';
}

/// Provider لحساب سعر الاشتراك
///
/// Copied from [subscriptionPriceCalculation].
class SubscriptionPriceCalculationProvider
    extends AutoDisposeFutureProvider<DiscountCalculation> {
  /// Provider لحساب سعر الاشتراك
  ///
  /// Copied from [subscriptionPriceCalculation].
  SubscriptionPriceCalculationProvider({
    required int planId,
    required String billingCycle,
    String? discountCode,
  }) : this._internal(
         (ref) => subscriptionPriceCalculation(
           ref as SubscriptionPriceCalculationRef,
           planId: planId,
           billingCycle: billingCycle,
           discountCode: discountCode,
         ),
         from: subscriptionPriceCalculationProvider,
         name: r'subscriptionPriceCalculationProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$subscriptionPriceCalculationHash,
         dependencies: SubscriptionPriceCalculationFamily._dependencies,
         allTransitiveDependencies:
             SubscriptionPriceCalculationFamily._allTransitiveDependencies,
         planId: planId,
         billingCycle: billingCycle,
         discountCode: discountCode,
       );

  SubscriptionPriceCalculationProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.planId,
    required this.billingCycle,
    required this.discountCode,
  }) : super.internal();

  final int planId;
  final String billingCycle;
  final String? discountCode;

  @override
  Override overrideWith(
    FutureOr<DiscountCalculation> Function(
      SubscriptionPriceCalculationRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SubscriptionPriceCalculationProvider._internal(
        (ref) => create(ref as SubscriptionPriceCalculationRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        planId: planId,
        billingCycle: billingCycle,
        discountCode: discountCode,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<DiscountCalculation> createElement() {
    return _SubscriptionPriceCalculationProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SubscriptionPriceCalculationProvider &&
        other.planId == planId &&
        other.billingCycle == billingCycle &&
        other.discountCode == discountCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, planId.hashCode);
    hash = _SystemHash.combine(hash, billingCycle.hashCode);
    hash = _SystemHash.combine(hash, discountCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SubscriptionPriceCalculationRef
    on AutoDisposeFutureProviderRef<DiscountCalculation> {
  /// The parameter `planId` of this provider.
  int get planId;

  /// The parameter `billingCycle` of this provider.
  String get billingCycle;

  /// The parameter `discountCode` of this provider.
  String? get discountCode;
}

class _SubscriptionPriceCalculationProviderElement
    extends AutoDisposeFutureProviderElement<DiscountCalculation>
    with SubscriptionPriceCalculationRef {
  _SubscriptionPriceCalculationProviderElement(super.provider);

  @override
  int get planId => (origin as SubscriptionPriceCalculationProvider).planId;
  @override
  String get billingCycle =>
      (origin as SubscriptionPriceCalculationProvider).billingCycle;
  @override
  String? get discountCode =>
      (origin as SubscriptionPriceCalculationProvider).discountCode;
}

String _$userSubscriptionHash() => r'62a4d8f51a2585b0fa63c154e07cc1a6b9f1a87b';

/// Provider للحصول على اشتراك المستخدم
///
/// Copied from [userSubscription].
@ProviderFor(userSubscription)
final userSubscriptionProvider =
    AutoDisposeFutureProvider<SellerSubscription?>.internal(
      userSubscription,
      name: r'userSubscriptionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userSubscriptionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserSubscriptionRef = AutoDisposeFutureProviderRef<SellerSubscription?>;
String _$activeDiscountsHash() => r'48adc5e1b22a3841983652fef9e5f27678d48a42';

/// Provider للحصول على الخصومات النشطة
///
/// Copied from [activeDiscounts].
@ProviderFor(activeDiscounts)
final activeDiscountsProvider =
    AutoDisposeFutureProvider<List<Discount>>.internal(
      activeDiscounts,
      name: r'activeDiscountsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$activeDiscountsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActiveDiscountsRef = AutoDisposeFutureProviderRef<List<Discount>>;
String _$validateDiscountCodeHash() =>
    r'68d075f6b511426af916bcd2aca0240d8559a2a5';

/// Provider للتحقق من رمز الخصم
///
/// Copied from [validateDiscountCode].
@ProviderFor(validateDiscountCode)
const validateDiscountCodeProvider = ValidateDiscountCodeFamily();

/// Provider للتحقق من رمز الخصم
///
/// Copied from [validateDiscountCode].
class ValidateDiscountCodeFamily extends Family<AsyncValue<Discount?>> {
  /// Provider للتحقق من رمز الخصم
  ///
  /// Copied from [validateDiscountCode].
  const ValidateDiscountCodeFamily();

  /// Provider للتحقق من رمز الخصم
  ///
  /// Copied from [validateDiscountCode].
  ValidateDiscountCodeProvider call(String code) {
    return ValidateDiscountCodeProvider(code);
  }

  @override
  ValidateDiscountCodeProvider getProviderOverride(
    covariant ValidateDiscountCodeProvider provider,
  ) {
    return call(provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'validateDiscountCodeProvider';
}

/// Provider للتحقق من رمز الخصم
///
/// Copied from [validateDiscountCode].
class ValidateDiscountCodeProvider
    extends AutoDisposeFutureProvider<Discount?> {
  /// Provider للتحقق من رمز الخصم
  ///
  /// Copied from [validateDiscountCode].
  ValidateDiscountCodeProvider(String code)
    : this._internal(
        (ref) => validateDiscountCode(ref as ValidateDiscountCodeRef, code),
        from: validateDiscountCodeProvider,
        name: r'validateDiscountCodeProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$validateDiscountCodeHash,
        dependencies: ValidateDiscountCodeFamily._dependencies,
        allTransitiveDependencies:
            ValidateDiscountCodeFamily._allTransitiveDependencies,
        code: code,
      );

  ValidateDiscountCodeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<Discount?> Function(ValidateDiscountCodeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ValidateDiscountCodeProvider._internal(
        (ref) => create(ref as ValidateDiscountCodeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Discount?> createElement() {
    return _ValidateDiscountCodeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ValidateDiscountCodeProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ValidateDiscountCodeRef on AutoDisposeFutureProviderRef<Discount?> {
  /// The parameter `code` of this provider.
  String get code;
}

class _ValidateDiscountCodeProviderElement
    extends AutoDisposeFutureProviderElement<Discount?>
    with ValidateDiscountCodeRef {
  _ValidateDiscountCodeProviderElement(super.provider);

  @override
  String get code => (origin as ValidateDiscountCodeProvider).code;
}

String _$discountCalculationHash() =>
    r'713c6ff88acace86eb5f416b45d94608f28d039a';

/// Provider لحساب الخصم
///
/// Copied from [discountCalculation].
@ProviderFor(discountCalculation)
const discountCalculationProvider = DiscountCalculationFamily();

/// Provider لحساب الخصم
///
/// Copied from [discountCalculation].
class DiscountCalculationFamily
    extends Family<AsyncValue<DiscountCalculation>> {
  /// Provider لحساب الخصم
  ///
  /// Copied from [discountCalculation].
  const DiscountCalculationFamily();

  /// Provider لحساب الخصم
  ///
  /// Copied from [discountCalculation].
  DiscountCalculationProvider call({
    required double amount,
    String? discountCode,
  }) {
    return DiscountCalculationProvider(
      amount: amount,
      discountCode: discountCode,
    );
  }

  @override
  DiscountCalculationProvider getProviderOverride(
    covariant DiscountCalculationProvider provider,
  ) {
    return call(amount: provider.amount, discountCode: provider.discountCode);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'discountCalculationProvider';
}

/// Provider لحساب الخصم
///
/// Copied from [discountCalculation].
class DiscountCalculationProvider
    extends AutoDisposeFutureProvider<DiscountCalculation> {
  /// Provider لحساب الخصم
  ///
  /// Copied from [discountCalculation].
  DiscountCalculationProvider({required double amount, String? discountCode})
    : this._internal(
        (ref) => discountCalculation(
          ref as DiscountCalculationRef,
          amount: amount,
          discountCode: discountCode,
        ),
        from: discountCalculationProvider,
        name: r'discountCalculationProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$discountCalculationHash,
        dependencies: DiscountCalculationFamily._dependencies,
        allTransitiveDependencies:
            DiscountCalculationFamily._allTransitiveDependencies,
        amount: amount,
        discountCode: discountCode,
      );

  DiscountCalculationProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.amount,
    required this.discountCode,
  }) : super.internal();

  final double amount;
  final String? discountCode;

  @override
  Override overrideWith(
    FutureOr<DiscountCalculation> Function(DiscountCalculationRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DiscountCalculationProvider._internal(
        (ref) => create(ref as DiscountCalculationRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        amount: amount,
        discountCode: discountCode,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<DiscountCalculation> createElement() {
    return _DiscountCalculationProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DiscountCalculationProvider &&
        other.amount == amount &&
        other.discountCode == discountCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, amount.hashCode);
    hash = _SystemHash.combine(hash, discountCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin DiscountCalculationRef
    on AutoDisposeFutureProviderRef<DiscountCalculation> {
  /// The parameter `amount` of this provider.
  double get amount;

  /// The parameter `discountCode` of this provider.
  String? get discountCode;
}

class _DiscountCalculationProviderElement
    extends AutoDisposeFutureProviderElement<DiscountCalculation>
    with DiscountCalculationRef {
  _DiscountCalculationProviderElement(super.provider);

  @override
  double get amount => (origin as DiscountCalculationProvider).amount;
  @override
  String? get discountCode =>
      (origin as DiscountCalculationProvider).discountCode;
}

String _$yearlySubscriptionDiscountHash() =>
    r'653ddef5fc95d691f18b79530bac15da47359afe';

/// Provider للحصول على خصم الاشتراك السنوي
///
/// Copied from [yearlySubscriptionDiscount].
@ProviderFor(yearlySubscriptionDiscount)
final yearlySubscriptionDiscountProvider =
    AutoDisposeFutureProvider<Discount>.internal(
      yearlySubscriptionDiscount,
      name: r'yearlySubscriptionDiscountProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$yearlySubscriptionDiscountHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef YearlySubscriptionDiscountRef = AutoDisposeFutureProviderRef<Discount>;
String _$subscriptionPlansWithDiscountsHash() =>
    r'551f09e2a7a84679d04feaa601787c688284e180';

/// Provider للحصول على خطط الاشتراك مع الخصومات المحسوبة
///
/// Copied from [subscriptionPlansWithDiscounts].
@ProviderFor(subscriptionPlansWithDiscounts)
final subscriptionPlansWithDiscountsProvider =
    AutoDisposeFutureProvider<List<SubscriptionPlanWithDiscounts>>.internal(
      subscriptionPlansWithDiscounts,
      name: r'subscriptionPlansWithDiscountsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionPlansWithDiscountsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionPlansWithDiscountsRef =
    AutoDisposeFutureProviderRef<List<SubscriptionPlanWithDiscounts>>;
String _$createSubscriptionNotifierHash() =>
    r'0db330eb61654cf0eed49dc6404b8456a6d6d911';

/// Provider لإنشاء اشتراك جديد
///
/// Copied from [CreateSubscriptionNotifier].
@ProviderFor(CreateSubscriptionNotifier)
final createSubscriptionNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      CreateSubscriptionNotifier,
      SellerSubscription?
    >.internal(
      CreateSubscriptionNotifier.new,
      name: r'createSubscriptionNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$createSubscriptionNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CreateSubscriptionNotifier =
    AutoDisposeAsyncNotifier<SellerSubscription?>;
String _$cancelSubscriptionNotifierHash() =>
    r'e80a0e4977bc8e2649063490dc9bdd102f9d43a9';

/// Provider لإلغاء الاشتراك
///
/// Copied from [CancelSubscriptionNotifier].
@ProviderFor(CancelSubscriptionNotifier)
final cancelSubscriptionNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CancelSubscriptionNotifier, void>.internal(
      CancelSubscriptionNotifier.new,
      name: r'cancelSubscriptionNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$cancelSubscriptionNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CancelSubscriptionNotifier = AutoDisposeAsyncNotifier<void>;
String _$applyDiscountNotifierHash() =>
    r'edb46806d631c03740313a9cbc86c0d026693d2e';

/// Provider لتطبيق الخصم
///
/// Copied from [ApplyDiscountNotifier].
@ProviderFor(ApplyDiscountNotifier)
final applyDiscountNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      ApplyDiscountNotifier,
      DiscountCalculation?
    >.internal(
      ApplyDiscountNotifier.new,
      name: r'applyDiscountNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$applyDiscountNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ApplyDiscountNotifier =
    AutoDisposeAsyncNotifier<DiscountCalculation?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
