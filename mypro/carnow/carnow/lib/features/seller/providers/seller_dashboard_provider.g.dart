// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_dashboard_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerDashboardHash() => r'5a8604b346fd6df0fbec93909d44392e0eeab1f4';

/// See also [SellerDashboard].
@ProviderFor(SellerDashboard)
final sellerDashboardProvider =
    AutoDisposeAsyncNotifierProvider<
      SellerDashboard,
      SellerDashboardStatsModel
    >.internal(
      SellerDashboard.new,
      name: r'sellerDashboardProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerDashboardHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerDashboard = AutoDisposeAsyncNotifier<SellerDashboardStatsModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
