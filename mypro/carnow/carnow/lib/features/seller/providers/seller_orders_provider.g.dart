// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_orders_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$filteredOrdersHash() => r'15f0ecd0c980acedd5d4ed06cd9cbf1a1c0851be';

/// مزود للطلبات المصفاة
///
/// Copied from [filteredOrders].
@ProviderFor(filteredOrders)
final filteredOrdersProvider =
    AutoDisposeFutureProvider<List<OrderModel>>.internal(
      filteredOrders,
      name: r'filteredOrdersProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$filteredOrdersHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FilteredOrdersRef = AutoDisposeFutureProviderRef<List<OrderModel>>;
String _$sellerOrdersHash() => r'a0520937d8b01b866674769867559e9a59aa262b';

/// مزود للوصول إلى كافة الطلبات للبائع
///
/// Copied from [SellerOrders].
@ProviderFor(SellerOrders)
final sellerOrdersProvider =
    AutoDisposeAsyncNotifierProvider<SellerOrders, List<OrderModel>>.internal(
      SellerOrders.new,
      name: r'sellerOrdersProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerOrdersHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerOrders = AutoDisposeAsyncNotifier<List<OrderModel>>;
String _$orderStatusFilterHash() => r'064437f0004d4703cdaf696bea6f924b9838e43d';

/// مزود لتصفية الطلبات حسب الحالة
///
/// Copied from [OrderStatusFilter].
@ProviderFor(OrderStatusFilter)
final orderStatusFilterProvider =
    AutoDisposeNotifierProvider<OrderStatusFilter, OrderStatus?>.internal(
      OrderStatusFilter.new,
      name: r'orderStatusFilterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$orderStatusFilterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OrderStatusFilter = AutoDisposeNotifier<OrderStatus?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
