// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionBillingHistoryHash() =>
    r'aeae25ad34500d5ffc3063682b1701d1e205aef8';

/// Provider for subscription billing history
///
/// Copied from [subscriptionBillingHistory].
@ProviderFor(subscriptionBillingHistory)
final subscriptionBillingHistoryProvider =
    AutoDisposeFutureProvider<List<SubscriptionBilling>>.internal(
      subscriptionBillingHistory,
      name: r'subscriptionBillingHistoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionBillingHistoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionBillingHistoryRef =
    AutoDisposeFutureProviderRef<List<SubscriptionBilling>>;
String _$availableSubscriptionPlansHash() =>
    r'68bb6f4f259394af7192d7a7047fd78fd02b74d1';

/// Provider for available subscription plans
///
/// Copied from [availableSubscriptionPlans].
@ProviderFor(availableSubscriptionPlans)
final availableSubscriptionPlansProvider =
    AutoDisposeFutureProvider<List<SubscriptionPlan>>.internal(
      availableSubscriptionPlans,
      name: r'availableSubscriptionPlansProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$availableSubscriptionPlansHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AvailableSubscriptionPlansRef =
    AutoDisposeFutureProviderRef<List<SubscriptionPlan>>;
String _$monthlyQuotaUsageHash() => r'1b35c20397b44324928905fd31da7ae5d5657c00';

/// Provider for monthly quota usage (typed)
///
/// Copied from [monthlyQuotaUsage].
@ProviderFor(monthlyQuotaUsage)
final monthlyQuotaUsageProvider =
    AutoDisposeFutureProvider<MonthlyQuotaUsage?>.internal(
      monthlyQuotaUsage,
      name: r'monthlyQuotaUsageProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$monthlyQuotaUsageHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MonthlyQuotaUsageRef = AutoDisposeFutureProviderRef<MonthlyQuotaUsage?>;
String _$subscriptionUsageHash() => r'e663ac4ad8f2d3eee2428f11aabb4b6e9c6c10c7';

/// Provider for subscription usage statistics
///
/// Copied from [subscriptionUsage].
@ProviderFor(subscriptionUsage)
final subscriptionUsageProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      subscriptionUsage,
      name: r'subscriptionUsageProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionUsageHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionUsageRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$sellerSubscriptionProviderHash() =>
    r'38146fddc53b20ab0a9f521937f9fb9d819c85df';

/// Subscription Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// ✅ Uses Go Backend API ONLY for subscriptions
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns
/// Provider for current seller subscription
///
/// Copied from [SellerSubscriptionProvider].
@ProviderFor(SellerSubscriptionProvider)
final sellerSubscriptionProviderProvider =
    AutoDisposeAsyncNotifierProvider<
      SellerSubscriptionProvider,
      SellerSubscription?
    >.internal(
      SellerSubscriptionProvider.new,
      name: r'sellerSubscriptionProviderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerSubscriptionProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerSubscriptionProvider =
    AutoDisposeAsyncNotifier<SellerSubscription?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
