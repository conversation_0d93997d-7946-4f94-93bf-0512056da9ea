// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sales_stats_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$topSellingProductsHash() =>
    r'b9dd219a78aadf85bdb774244634a8a500fb18be';

/// Provider for top selling products
///
/// Copied from [topSellingProducts].
@ProviderFor(topSellingProducts)
final topSellingProductsProvider =
    AutoDisposeFutureProvider<List<ProductSaleStats>>.internal(
      topSellingProducts,
      name: r'topSellingProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$topSellingProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TopSellingProductsRef =
    AutoDisposeFutureProviderRef<List<ProductSaleStats>>;
String _$dailySalesChartDataHash() =>
    r'2c0eef62e4a994cfe812d5d41babd7258e94b0a5';

/// Provider for daily sales chart data
///
/// Copied from [dailySalesChartData].
@ProviderFor(dailySalesChartData)
final dailySalesChartDataProvider =
    AutoDisposeFutureProvider<List<DailySales>>.internal(
      dailySalesChartData,
      name: r'dailySalesChartDataProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$dailySalesChartDataHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DailySalesChartDataRef = AutoDisposeFutureProviderRef<List<DailySales>>;
String _$salesStatsHash() => r'0eb99cd97dbd8b11d1ed304ca5fbfdba12120f81';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SalesStats
    extends BuildlessAutoDisposeAsyncNotifier<SalesAnalyticsModel> {
  late final String sellerId;

  FutureOr<SalesAnalyticsModel> build(String sellerId);
}

/// Simple Sales Stats Provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [SalesStats].
@ProviderFor(SalesStats)
const salesStatsProvider = SalesStatsFamily();

/// Simple Sales Stats Provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [SalesStats].
class SalesStatsFamily extends Family<AsyncValue<SalesAnalyticsModel>> {
  /// Simple Sales Stats Provider following Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [SalesStats].
  const SalesStatsFamily();

  /// Simple Sales Stats Provider following Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [SalesStats].
  SalesStatsProvider call(String sellerId) {
    return SalesStatsProvider(sellerId);
  }

  @override
  SalesStatsProvider getProviderOverride(
    covariant SalesStatsProvider provider,
  ) {
    return call(provider.sellerId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'salesStatsProvider';
}

/// Simple Sales Stats Provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [SalesStats].
class SalesStatsProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<SalesStats, SalesAnalyticsModel> {
  /// Simple Sales Stats Provider following Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [SalesStats].
  SalesStatsProvider(String sellerId)
    : this._internal(
        () => SalesStats()..sellerId = sellerId,
        from: salesStatsProvider,
        name: r'salesStatsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$salesStatsHash,
        dependencies: SalesStatsFamily._dependencies,
        allTransitiveDependencies: SalesStatsFamily._allTransitiveDependencies,
        sellerId: sellerId,
      );

  SalesStatsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sellerId,
  }) : super.internal();

  final String sellerId;

  @override
  FutureOr<SalesAnalyticsModel> runNotifierBuild(
    covariant SalesStats notifier,
  ) {
    return notifier.build(sellerId);
  }

  @override
  Override overrideWith(SalesStats Function() create) {
    return ProviderOverride(
      origin: this,
      override: SalesStatsProvider._internal(
        () => create()..sellerId = sellerId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sellerId: sellerId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SalesStats, SalesAnalyticsModel>
  createElement() {
    return _SalesStatsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SalesStatsProvider && other.sellerId == sellerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sellerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SalesStatsRef
    on AutoDisposeAsyncNotifierProviderRef<SalesAnalyticsModel> {
  /// The parameter `sellerId` of this provider.
  String get sellerId;
}

class _SalesStatsProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<SalesStats, SalesAnalyticsModel>
    with SalesStatsRef {
  _SalesStatsProviderElement(super.provider);

  @override
  String get sellerId => (origin as SalesStatsProvider).sellerId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
