// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'universal_search_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UniversalSearchState {

 String get query; String get searchType; List<UniversalSearchResult> get results; List<String> get suggestions; Map<String, dynamic> get filters; String? get categoryFilter; CategoryInsights? get categoryInsights; bool get isLoading; String? get error; int get total; int get processingTime;
/// Create a copy of UniversalSearchState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UniversalSearchStateCopyWith<UniversalSearchState> get copyWith => _$UniversalSearchStateCopyWithImpl<UniversalSearchState>(this as UniversalSearchState, _$identity);

  /// Serializes this UniversalSearchState to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UniversalSearchState&&(identical(other.query, query) || other.query == query)&&(identical(other.searchType, searchType) || other.searchType == searchType)&&const DeepCollectionEquality().equals(other.results, results)&&const DeepCollectionEquality().equals(other.suggestions, suggestions)&&const DeepCollectionEquality().equals(other.filters, filters)&&(identical(other.categoryFilter, categoryFilter) || other.categoryFilter == categoryFilter)&&(identical(other.categoryInsights, categoryInsights) || other.categoryInsights == categoryInsights)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error)&&(identical(other.total, total) || other.total == total)&&(identical(other.processingTime, processingTime) || other.processingTime == processingTime));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,searchType,const DeepCollectionEquality().hash(results),const DeepCollectionEquality().hash(suggestions),const DeepCollectionEquality().hash(filters),categoryFilter,categoryInsights,isLoading,error,total,processingTime);

@override
String toString() {
  return 'UniversalSearchState(query: $query, searchType: $searchType, results: $results, suggestions: $suggestions, filters: $filters, categoryFilter: $categoryFilter, categoryInsights: $categoryInsights, isLoading: $isLoading, error: $error, total: $total, processingTime: $processingTime)';
}


}

/// @nodoc
abstract mixin class $UniversalSearchStateCopyWith<$Res>  {
  factory $UniversalSearchStateCopyWith(UniversalSearchState value, $Res Function(UniversalSearchState) _then) = _$UniversalSearchStateCopyWithImpl;
@useResult
$Res call({
 String query, String searchType, List<UniversalSearchResult> results, List<String> suggestions, Map<String, dynamic> filters, String? categoryFilter, CategoryInsights? categoryInsights, bool isLoading, String? error, int total, int processingTime
});


$CategoryInsightsCopyWith<$Res>? get categoryInsights;

}
/// @nodoc
class _$UniversalSearchStateCopyWithImpl<$Res>
    implements $UniversalSearchStateCopyWith<$Res> {
  _$UniversalSearchStateCopyWithImpl(this._self, this._then);

  final UniversalSearchState _self;
  final $Res Function(UniversalSearchState) _then;

/// Create a copy of UniversalSearchState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? query = null,Object? searchType = null,Object? results = null,Object? suggestions = null,Object? filters = null,Object? categoryFilter = freezed,Object? categoryInsights = freezed,Object? isLoading = null,Object? error = freezed,Object? total = null,Object? processingTime = null,}) {
  return _then(_self.copyWith(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,searchType: null == searchType ? _self.searchType : searchType // ignore: cast_nullable_to_non_nullable
as String,results: null == results ? _self.results : results // ignore: cast_nullable_to_non_nullable
as List<UniversalSearchResult>,suggestions: null == suggestions ? _self.suggestions : suggestions // ignore: cast_nullable_to_non_nullable
as List<String>,filters: null == filters ? _self.filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,categoryFilter: freezed == categoryFilter ? _self.categoryFilter : categoryFilter // ignore: cast_nullable_to_non_nullable
as String?,categoryInsights: freezed == categoryInsights ? _self.categoryInsights : categoryInsights // ignore: cast_nullable_to_non_nullable
as CategoryInsights?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,processingTime: null == processingTime ? _self.processingTime : processingTime // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of UniversalSearchState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CategoryInsightsCopyWith<$Res>? get categoryInsights {
    if (_self.categoryInsights == null) {
    return null;
  }

  return $CategoryInsightsCopyWith<$Res>(_self.categoryInsights!, (value) {
    return _then(_self.copyWith(categoryInsights: value));
  });
}
}


/// Adds pattern-matching-related methods to [UniversalSearchState].
extension UniversalSearchStatePatterns on UniversalSearchState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UniversalSearchState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UniversalSearchState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UniversalSearchState value)  $default,){
final _that = this;
switch (_that) {
case _UniversalSearchState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UniversalSearchState value)?  $default,){
final _that = this;
switch (_that) {
case _UniversalSearchState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String query,  String searchType,  List<UniversalSearchResult> results,  List<String> suggestions,  Map<String, dynamic> filters,  String? categoryFilter,  CategoryInsights? categoryInsights,  bool isLoading,  String? error,  int total,  int processingTime)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UniversalSearchState() when $default != null:
return $default(_that.query,_that.searchType,_that.results,_that.suggestions,_that.filters,_that.categoryFilter,_that.categoryInsights,_that.isLoading,_that.error,_that.total,_that.processingTime);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String query,  String searchType,  List<UniversalSearchResult> results,  List<String> suggestions,  Map<String, dynamic> filters,  String? categoryFilter,  CategoryInsights? categoryInsights,  bool isLoading,  String? error,  int total,  int processingTime)  $default,) {final _that = this;
switch (_that) {
case _UniversalSearchState():
return $default(_that.query,_that.searchType,_that.results,_that.suggestions,_that.filters,_that.categoryFilter,_that.categoryInsights,_that.isLoading,_that.error,_that.total,_that.processingTime);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String query,  String searchType,  List<UniversalSearchResult> results,  List<String> suggestions,  Map<String, dynamic> filters,  String? categoryFilter,  CategoryInsights? categoryInsights,  bool isLoading,  String? error,  int total,  int processingTime)?  $default,) {final _that = this;
switch (_that) {
case _UniversalSearchState() when $default != null:
return $default(_that.query,_that.searchType,_that.results,_that.suggestions,_that.filters,_that.categoryFilter,_that.categoryInsights,_that.isLoading,_that.error,_that.total,_that.processingTime);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UniversalSearchState implements UniversalSearchState {
  const _UniversalSearchState({this.query = '', this.searchType = 'hybrid', final  List<UniversalSearchResult> results = const [], final  List<String> suggestions = const [], final  Map<String, dynamic> filters = const {}, this.categoryFilter, this.categoryInsights, this.isLoading = false, this.error, this.total = 0, this.processingTime = 0}): _results = results,_suggestions = suggestions,_filters = filters;
  factory _UniversalSearchState.fromJson(Map<String, dynamic> json) => _$UniversalSearchStateFromJson(json);

@override@JsonKey() final  String query;
@override@JsonKey() final  String searchType;
 final  List<UniversalSearchResult> _results;
@override@JsonKey() List<UniversalSearchResult> get results {
  if (_results is EqualUnmodifiableListView) return _results;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_results);
}

 final  List<String> _suggestions;
@override@JsonKey() List<String> get suggestions {
  if (_suggestions is EqualUnmodifiableListView) return _suggestions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_suggestions);
}

 final  Map<String, dynamic> _filters;
@override@JsonKey() Map<String, dynamic> get filters {
  if (_filters is EqualUnmodifiableMapView) return _filters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_filters);
}

@override final  String? categoryFilter;
@override final  CategoryInsights? categoryInsights;
@override@JsonKey() final  bool isLoading;
@override final  String? error;
@override@JsonKey() final  int total;
@override@JsonKey() final  int processingTime;

/// Create a copy of UniversalSearchState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UniversalSearchStateCopyWith<_UniversalSearchState> get copyWith => __$UniversalSearchStateCopyWithImpl<_UniversalSearchState>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UniversalSearchStateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UniversalSearchState&&(identical(other.query, query) || other.query == query)&&(identical(other.searchType, searchType) || other.searchType == searchType)&&const DeepCollectionEquality().equals(other._results, _results)&&const DeepCollectionEquality().equals(other._suggestions, _suggestions)&&const DeepCollectionEquality().equals(other._filters, _filters)&&(identical(other.categoryFilter, categoryFilter) || other.categoryFilter == categoryFilter)&&(identical(other.categoryInsights, categoryInsights) || other.categoryInsights == categoryInsights)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error)&&(identical(other.total, total) || other.total == total)&&(identical(other.processingTime, processingTime) || other.processingTime == processingTime));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,searchType,const DeepCollectionEquality().hash(_results),const DeepCollectionEquality().hash(_suggestions),const DeepCollectionEquality().hash(_filters),categoryFilter,categoryInsights,isLoading,error,total,processingTime);

@override
String toString() {
  return 'UniversalSearchState(query: $query, searchType: $searchType, results: $results, suggestions: $suggestions, filters: $filters, categoryFilter: $categoryFilter, categoryInsights: $categoryInsights, isLoading: $isLoading, error: $error, total: $total, processingTime: $processingTime)';
}


}

/// @nodoc
abstract mixin class _$UniversalSearchStateCopyWith<$Res> implements $UniversalSearchStateCopyWith<$Res> {
  factory _$UniversalSearchStateCopyWith(_UniversalSearchState value, $Res Function(_UniversalSearchState) _then) = __$UniversalSearchStateCopyWithImpl;
@override @useResult
$Res call({
 String query, String searchType, List<UniversalSearchResult> results, List<String> suggestions, Map<String, dynamic> filters, String? categoryFilter, CategoryInsights? categoryInsights, bool isLoading, String? error, int total, int processingTime
});


@override $CategoryInsightsCopyWith<$Res>? get categoryInsights;

}
/// @nodoc
class __$UniversalSearchStateCopyWithImpl<$Res>
    implements _$UniversalSearchStateCopyWith<$Res> {
  __$UniversalSearchStateCopyWithImpl(this._self, this._then);

  final _UniversalSearchState _self;
  final $Res Function(_UniversalSearchState) _then;

/// Create a copy of UniversalSearchState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? query = null,Object? searchType = null,Object? results = null,Object? suggestions = null,Object? filters = null,Object? categoryFilter = freezed,Object? categoryInsights = freezed,Object? isLoading = null,Object? error = freezed,Object? total = null,Object? processingTime = null,}) {
  return _then(_UniversalSearchState(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,searchType: null == searchType ? _self.searchType : searchType // ignore: cast_nullable_to_non_nullable
as String,results: null == results ? _self._results : results // ignore: cast_nullable_to_non_nullable
as List<UniversalSearchResult>,suggestions: null == suggestions ? _self._suggestions : suggestions // ignore: cast_nullable_to_non_nullable
as List<String>,filters: null == filters ? _self._filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,categoryFilter: freezed == categoryFilter ? _self.categoryFilter : categoryFilter // ignore: cast_nullable_to_non_nullable
as String?,categoryInsights: freezed == categoryInsights ? _self.categoryInsights : categoryInsights // ignore: cast_nullable_to_non_nullable
as CategoryInsights?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,processingTime: null == processingTime ? _self.processingTime : processingTime // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of UniversalSearchState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CategoryInsightsCopyWith<$Res>? get categoryInsights {
    if (_self.categoryInsights == null) {
    return null;
  }

  return $CategoryInsightsCopyWith<$Res>(_self.categoryInsights!, (value) {
    return _then(_self.copyWith(categoryInsights: value));
  });
}
}


/// @nodoc
mixin _$UniversalSearchResult {

 String get id; String get productId; String get categoryType; String get title; String? get description; Map<String, dynamic> get metadata; double get similarity; double get textScore; double get semanticScore; double get combinedScore;
/// Create a copy of UniversalSearchResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UniversalSearchResultCopyWith<UniversalSearchResult> get copyWith => _$UniversalSearchResultCopyWithImpl<UniversalSearchResult>(this as UniversalSearchResult, _$identity);

  /// Serializes this UniversalSearchResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UniversalSearchResult&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.categoryType, categoryType) || other.categoryType == categoryType)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.similarity, similarity) || other.similarity == similarity)&&(identical(other.textScore, textScore) || other.textScore == textScore)&&(identical(other.semanticScore, semanticScore) || other.semanticScore == semanticScore)&&(identical(other.combinedScore, combinedScore) || other.combinedScore == combinedScore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,categoryType,title,description,const DeepCollectionEquality().hash(metadata),similarity,textScore,semanticScore,combinedScore);

@override
String toString() {
  return 'UniversalSearchResult(id: $id, productId: $productId, categoryType: $categoryType, title: $title, description: $description, metadata: $metadata, similarity: $similarity, textScore: $textScore, semanticScore: $semanticScore, combinedScore: $combinedScore)';
}


}

/// @nodoc
abstract mixin class $UniversalSearchResultCopyWith<$Res>  {
  factory $UniversalSearchResultCopyWith(UniversalSearchResult value, $Res Function(UniversalSearchResult) _then) = _$UniversalSearchResultCopyWithImpl;
@useResult
$Res call({
 String id, String productId, String categoryType, String title, String? description, Map<String, dynamic> metadata, double similarity, double textScore, double semanticScore, double combinedScore
});




}
/// @nodoc
class _$UniversalSearchResultCopyWithImpl<$Res>
    implements $UniversalSearchResultCopyWith<$Res> {
  _$UniversalSearchResultCopyWithImpl(this._self, this._then);

  final UniversalSearchResult _self;
  final $Res Function(UniversalSearchResult) _then;

/// Create a copy of UniversalSearchResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? productId = null,Object? categoryType = null,Object? title = null,Object? description = freezed,Object? metadata = null,Object? similarity = null,Object? textScore = null,Object? semanticScore = null,Object? combinedScore = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,categoryType: null == categoryType ? _self.categoryType : categoryType // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,similarity: null == similarity ? _self.similarity : similarity // ignore: cast_nullable_to_non_nullable
as double,textScore: null == textScore ? _self.textScore : textScore // ignore: cast_nullable_to_non_nullable
as double,semanticScore: null == semanticScore ? _self.semanticScore : semanticScore // ignore: cast_nullable_to_non_nullable
as double,combinedScore: null == combinedScore ? _self.combinedScore : combinedScore // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [UniversalSearchResult].
extension UniversalSearchResultPatterns on UniversalSearchResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UniversalSearchResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UniversalSearchResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UniversalSearchResult value)  $default,){
final _that = this;
switch (_that) {
case _UniversalSearchResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UniversalSearchResult value)?  $default,){
final _that = this;
switch (_that) {
case _UniversalSearchResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String productId,  String categoryType,  String title,  String? description,  Map<String, dynamic> metadata,  double similarity,  double textScore,  double semanticScore,  double combinedScore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UniversalSearchResult() when $default != null:
return $default(_that.id,_that.productId,_that.categoryType,_that.title,_that.description,_that.metadata,_that.similarity,_that.textScore,_that.semanticScore,_that.combinedScore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String productId,  String categoryType,  String title,  String? description,  Map<String, dynamic> metadata,  double similarity,  double textScore,  double semanticScore,  double combinedScore)  $default,) {final _that = this;
switch (_that) {
case _UniversalSearchResult():
return $default(_that.id,_that.productId,_that.categoryType,_that.title,_that.description,_that.metadata,_that.similarity,_that.textScore,_that.semanticScore,_that.combinedScore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String productId,  String categoryType,  String title,  String? description,  Map<String, dynamic> metadata,  double similarity,  double textScore,  double semanticScore,  double combinedScore)?  $default,) {final _that = this;
switch (_that) {
case _UniversalSearchResult() when $default != null:
return $default(_that.id,_that.productId,_that.categoryType,_that.title,_that.description,_that.metadata,_that.similarity,_that.textScore,_that.semanticScore,_that.combinedScore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UniversalSearchResult implements UniversalSearchResult {
  const _UniversalSearchResult({required this.id, required this.productId, required this.categoryType, required this.title, this.description, final  Map<String, dynamic> metadata = const {}, this.similarity = 0.0, this.textScore = 0.0, this.semanticScore = 0.0, this.combinedScore = 0.0}): _metadata = metadata;
  factory _UniversalSearchResult.fromJson(Map<String, dynamic> json) => _$UniversalSearchResultFromJson(json);

@override final  String id;
@override final  String productId;
@override final  String categoryType;
@override final  String title;
@override final  String? description;
 final  Map<String, dynamic> _metadata;
@override@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}

@override@JsonKey() final  double similarity;
@override@JsonKey() final  double textScore;
@override@JsonKey() final  double semanticScore;
@override@JsonKey() final  double combinedScore;

/// Create a copy of UniversalSearchResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UniversalSearchResultCopyWith<_UniversalSearchResult> get copyWith => __$UniversalSearchResultCopyWithImpl<_UniversalSearchResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UniversalSearchResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UniversalSearchResult&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.categoryType, categoryType) || other.categoryType == categoryType)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.similarity, similarity) || other.similarity == similarity)&&(identical(other.textScore, textScore) || other.textScore == textScore)&&(identical(other.semanticScore, semanticScore) || other.semanticScore == semanticScore)&&(identical(other.combinedScore, combinedScore) || other.combinedScore == combinedScore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,categoryType,title,description,const DeepCollectionEquality().hash(_metadata),similarity,textScore,semanticScore,combinedScore);

@override
String toString() {
  return 'UniversalSearchResult(id: $id, productId: $productId, categoryType: $categoryType, title: $title, description: $description, metadata: $metadata, similarity: $similarity, textScore: $textScore, semanticScore: $semanticScore, combinedScore: $combinedScore)';
}


}

/// @nodoc
abstract mixin class _$UniversalSearchResultCopyWith<$Res> implements $UniversalSearchResultCopyWith<$Res> {
  factory _$UniversalSearchResultCopyWith(_UniversalSearchResult value, $Res Function(_UniversalSearchResult) _then) = __$UniversalSearchResultCopyWithImpl;
@override @useResult
$Res call({
 String id, String productId, String categoryType, String title, String? description, Map<String, dynamic> metadata, double similarity, double textScore, double semanticScore, double combinedScore
});




}
/// @nodoc
class __$UniversalSearchResultCopyWithImpl<$Res>
    implements _$UniversalSearchResultCopyWith<$Res> {
  __$UniversalSearchResultCopyWithImpl(this._self, this._then);

  final _UniversalSearchResult _self;
  final $Res Function(_UniversalSearchResult) _then;

/// Create a copy of UniversalSearchResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? productId = null,Object? categoryType = null,Object? title = null,Object? description = freezed,Object? metadata = null,Object? similarity = null,Object? textScore = null,Object? semanticScore = null,Object? combinedScore = null,}) {
  return _then(_UniversalSearchResult(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,categoryType: null == categoryType ? _self.categoryType : categoryType // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,similarity: null == similarity ? _self.similarity : similarity // ignore: cast_nullable_to_non_nullable
as double,textScore: null == textScore ? _self.textScore : textScore // ignore: cast_nullable_to_non_nullable
as double,semanticScore: null == semanticScore ? _self.semanticScore : semanticScore // ignore: cast_nullable_to_non_nullable
as double,combinedScore: null == combinedScore ? _self.combinedScore : combinedScore // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$UniversalSearchRequest {

 String get query; String get searchType; String? get categoryFilter; Map<String, dynamic> get filters; int get limit; double get matchThreshold; double get textWeight; double get semanticWeight;
/// Create a copy of UniversalSearchRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UniversalSearchRequestCopyWith<UniversalSearchRequest> get copyWith => _$UniversalSearchRequestCopyWithImpl<UniversalSearchRequest>(this as UniversalSearchRequest, _$identity);

  /// Serializes this UniversalSearchRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UniversalSearchRequest&&(identical(other.query, query) || other.query == query)&&(identical(other.searchType, searchType) || other.searchType == searchType)&&(identical(other.categoryFilter, categoryFilter) || other.categoryFilter == categoryFilter)&&const DeepCollectionEquality().equals(other.filters, filters)&&(identical(other.limit, limit) || other.limit == limit)&&(identical(other.matchThreshold, matchThreshold) || other.matchThreshold == matchThreshold)&&(identical(other.textWeight, textWeight) || other.textWeight == textWeight)&&(identical(other.semanticWeight, semanticWeight) || other.semanticWeight == semanticWeight));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,searchType,categoryFilter,const DeepCollectionEquality().hash(filters),limit,matchThreshold,textWeight,semanticWeight);

@override
String toString() {
  return 'UniversalSearchRequest(query: $query, searchType: $searchType, categoryFilter: $categoryFilter, filters: $filters, limit: $limit, matchThreshold: $matchThreshold, textWeight: $textWeight, semanticWeight: $semanticWeight)';
}


}

/// @nodoc
abstract mixin class $UniversalSearchRequestCopyWith<$Res>  {
  factory $UniversalSearchRequestCopyWith(UniversalSearchRequest value, $Res Function(UniversalSearchRequest) _then) = _$UniversalSearchRequestCopyWithImpl;
@useResult
$Res call({
 String query, String searchType, String? categoryFilter, Map<String, dynamic> filters, int limit, double matchThreshold, double textWeight, double semanticWeight
});




}
/// @nodoc
class _$UniversalSearchRequestCopyWithImpl<$Res>
    implements $UniversalSearchRequestCopyWith<$Res> {
  _$UniversalSearchRequestCopyWithImpl(this._self, this._then);

  final UniversalSearchRequest _self;
  final $Res Function(UniversalSearchRequest) _then;

/// Create a copy of UniversalSearchRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? query = null,Object? searchType = null,Object? categoryFilter = freezed,Object? filters = null,Object? limit = null,Object? matchThreshold = null,Object? textWeight = null,Object? semanticWeight = null,}) {
  return _then(_self.copyWith(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,searchType: null == searchType ? _self.searchType : searchType // ignore: cast_nullable_to_non_nullable
as String,categoryFilter: freezed == categoryFilter ? _self.categoryFilter : categoryFilter // ignore: cast_nullable_to_non_nullable
as String?,filters: null == filters ? _self.filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,limit: null == limit ? _self.limit : limit // ignore: cast_nullable_to_non_nullable
as int,matchThreshold: null == matchThreshold ? _self.matchThreshold : matchThreshold // ignore: cast_nullable_to_non_nullable
as double,textWeight: null == textWeight ? _self.textWeight : textWeight // ignore: cast_nullable_to_non_nullable
as double,semanticWeight: null == semanticWeight ? _self.semanticWeight : semanticWeight // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [UniversalSearchRequest].
extension UniversalSearchRequestPatterns on UniversalSearchRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UniversalSearchRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UniversalSearchRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UniversalSearchRequest value)  $default,){
final _that = this;
switch (_that) {
case _UniversalSearchRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UniversalSearchRequest value)?  $default,){
final _that = this;
switch (_that) {
case _UniversalSearchRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String query,  String searchType,  String? categoryFilter,  Map<String, dynamic> filters,  int limit,  double matchThreshold,  double textWeight,  double semanticWeight)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UniversalSearchRequest() when $default != null:
return $default(_that.query,_that.searchType,_that.categoryFilter,_that.filters,_that.limit,_that.matchThreshold,_that.textWeight,_that.semanticWeight);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String query,  String searchType,  String? categoryFilter,  Map<String, dynamic> filters,  int limit,  double matchThreshold,  double textWeight,  double semanticWeight)  $default,) {final _that = this;
switch (_that) {
case _UniversalSearchRequest():
return $default(_that.query,_that.searchType,_that.categoryFilter,_that.filters,_that.limit,_that.matchThreshold,_that.textWeight,_that.semanticWeight);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String query,  String searchType,  String? categoryFilter,  Map<String, dynamic> filters,  int limit,  double matchThreshold,  double textWeight,  double semanticWeight)?  $default,) {final _that = this;
switch (_that) {
case _UniversalSearchRequest() when $default != null:
return $default(_that.query,_that.searchType,_that.categoryFilter,_that.filters,_that.limit,_that.matchThreshold,_that.textWeight,_that.semanticWeight);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UniversalSearchRequest implements UniversalSearchRequest {
  const _UniversalSearchRequest({required this.query, this.searchType = 'hybrid', this.categoryFilter, final  Map<String, dynamic> filters = const {}, this.limit = 20, this.matchThreshold = 0.75, this.textWeight = 0.3, this.semanticWeight = 0.7}): _filters = filters;
  factory _UniversalSearchRequest.fromJson(Map<String, dynamic> json) => _$UniversalSearchRequestFromJson(json);

@override final  String query;
@override@JsonKey() final  String searchType;
@override final  String? categoryFilter;
 final  Map<String, dynamic> _filters;
@override@JsonKey() Map<String, dynamic> get filters {
  if (_filters is EqualUnmodifiableMapView) return _filters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_filters);
}

@override@JsonKey() final  int limit;
@override@JsonKey() final  double matchThreshold;
@override@JsonKey() final  double textWeight;
@override@JsonKey() final  double semanticWeight;

/// Create a copy of UniversalSearchRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UniversalSearchRequestCopyWith<_UniversalSearchRequest> get copyWith => __$UniversalSearchRequestCopyWithImpl<_UniversalSearchRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UniversalSearchRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UniversalSearchRequest&&(identical(other.query, query) || other.query == query)&&(identical(other.searchType, searchType) || other.searchType == searchType)&&(identical(other.categoryFilter, categoryFilter) || other.categoryFilter == categoryFilter)&&const DeepCollectionEquality().equals(other._filters, _filters)&&(identical(other.limit, limit) || other.limit == limit)&&(identical(other.matchThreshold, matchThreshold) || other.matchThreshold == matchThreshold)&&(identical(other.textWeight, textWeight) || other.textWeight == textWeight)&&(identical(other.semanticWeight, semanticWeight) || other.semanticWeight == semanticWeight));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,searchType,categoryFilter,const DeepCollectionEquality().hash(_filters),limit,matchThreshold,textWeight,semanticWeight);

@override
String toString() {
  return 'UniversalSearchRequest(query: $query, searchType: $searchType, categoryFilter: $categoryFilter, filters: $filters, limit: $limit, matchThreshold: $matchThreshold, textWeight: $textWeight, semanticWeight: $semanticWeight)';
}


}

/// @nodoc
abstract mixin class _$UniversalSearchRequestCopyWith<$Res> implements $UniversalSearchRequestCopyWith<$Res> {
  factory _$UniversalSearchRequestCopyWith(_UniversalSearchRequest value, $Res Function(_UniversalSearchRequest) _then) = __$UniversalSearchRequestCopyWithImpl;
@override @useResult
$Res call({
 String query, String searchType, String? categoryFilter, Map<String, dynamic> filters, int limit, double matchThreshold, double textWeight, double semanticWeight
});




}
/// @nodoc
class __$UniversalSearchRequestCopyWithImpl<$Res>
    implements _$UniversalSearchRequestCopyWith<$Res> {
  __$UniversalSearchRequestCopyWithImpl(this._self, this._then);

  final _UniversalSearchRequest _self;
  final $Res Function(_UniversalSearchRequest) _then;

/// Create a copy of UniversalSearchRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? query = null,Object? searchType = null,Object? categoryFilter = freezed,Object? filters = null,Object? limit = null,Object? matchThreshold = null,Object? textWeight = null,Object? semanticWeight = null,}) {
  return _then(_UniversalSearchRequest(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,searchType: null == searchType ? _self.searchType : searchType // ignore: cast_nullable_to_non_nullable
as String,categoryFilter: freezed == categoryFilter ? _self.categoryFilter : categoryFilter // ignore: cast_nullable_to_non_nullable
as String?,filters: null == filters ? _self._filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,limit: null == limit ? _self.limit : limit // ignore: cast_nullable_to_non_nullable
as int,matchThreshold: null == matchThreshold ? _self.matchThreshold : matchThreshold // ignore: cast_nullable_to_non_nullable
as double,textWeight: null == textWeight ? _self.textWeight : textWeight // ignore: cast_nullable_to_non_nullable
as double,semanticWeight: null == semanticWeight ? _self.semanticWeight : semanticWeight // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$UniversalSearchResponse {

 String get query; String get searchType; List<UniversalSearchResult> get results; List<String> get suggestions; int get total; int get processingTime; CategoryInsights? get categoryInsights;
/// Create a copy of UniversalSearchResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UniversalSearchResponseCopyWith<UniversalSearchResponse> get copyWith => _$UniversalSearchResponseCopyWithImpl<UniversalSearchResponse>(this as UniversalSearchResponse, _$identity);

  /// Serializes this UniversalSearchResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UniversalSearchResponse&&(identical(other.query, query) || other.query == query)&&(identical(other.searchType, searchType) || other.searchType == searchType)&&const DeepCollectionEquality().equals(other.results, results)&&const DeepCollectionEquality().equals(other.suggestions, suggestions)&&(identical(other.total, total) || other.total == total)&&(identical(other.processingTime, processingTime) || other.processingTime == processingTime)&&(identical(other.categoryInsights, categoryInsights) || other.categoryInsights == categoryInsights));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,searchType,const DeepCollectionEquality().hash(results),const DeepCollectionEquality().hash(suggestions),total,processingTime,categoryInsights);

@override
String toString() {
  return 'UniversalSearchResponse(query: $query, searchType: $searchType, results: $results, suggestions: $suggestions, total: $total, processingTime: $processingTime, categoryInsights: $categoryInsights)';
}


}

/// @nodoc
abstract mixin class $UniversalSearchResponseCopyWith<$Res>  {
  factory $UniversalSearchResponseCopyWith(UniversalSearchResponse value, $Res Function(UniversalSearchResponse) _then) = _$UniversalSearchResponseCopyWithImpl;
@useResult
$Res call({
 String query, String searchType, List<UniversalSearchResult> results, List<String> suggestions, int total, int processingTime, CategoryInsights? categoryInsights
});


$CategoryInsightsCopyWith<$Res>? get categoryInsights;

}
/// @nodoc
class _$UniversalSearchResponseCopyWithImpl<$Res>
    implements $UniversalSearchResponseCopyWith<$Res> {
  _$UniversalSearchResponseCopyWithImpl(this._self, this._then);

  final UniversalSearchResponse _self;
  final $Res Function(UniversalSearchResponse) _then;

/// Create a copy of UniversalSearchResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? query = null,Object? searchType = null,Object? results = null,Object? suggestions = null,Object? total = null,Object? processingTime = null,Object? categoryInsights = freezed,}) {
  return _then(_self.copyWith(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,searchType: null == searchType ? _self.searchType : searchType // ignore: cast_nullable_to_non_nullable
as String,results: null == results ? _self.results : results // ignore: cast_nullable_to_non_nullable
as List<UniversalSearchResult>,suggestions: null == suggestions ? _self.suggestions : suggestions // ignore: cast_nullable_to_non_nullable
as List<String>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,processingTime: null == processingTime ? _self.processingTime : processingTime // ignore: cast_nullable_to_non_nullable
as int,categoryInsights: freezed == categoryInsights ? _self.categoryInsights : categoryInsights // ignore: cast_nullable_to_non_nullable
as CategoryInsights?,
  ));
}
/// Create a copy of UniversalSearchResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CategoryInsightsCopyWith<$Res>? get categoryInsights {
    if (_self.categoryInsights == null) {
    return null;
  }

  return $CategoryInsightsCopyWith<$Res>(_self.categoryInsights!, (value) {
    return _then(_self.copyWith(categoryInsights: value));
  });
}
}


/// Adds pattern-matching-related methods to [UniversalSearchResponse].
extension UniversalSearchResponsePatterns on UniversalSearchResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UniversalSearchResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UniversalSearchResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UniversalSearchResponse value)  $default,){
final _that = this;
switch (_that) {
case _UniversalSearchResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UniversalSearchResponse value)?  $default,){
final _that = this;
switch (_that) {
case _UniversalSearchResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String query,  String searchType,  List<UniversalSearchResult> results,  List<String> suggestions,  int total,  int processingTime,  CategoryInsights? categoryInsights)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UniversalSearchResponse() when $default != null:
return $default(_that.query,_that.searchType,_that.results,_that.suggestions,_that.total,_that.processingTime,_that.categoryInsights);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String query,  String searchType,  List<UniversalSearchResult> results,  List<String> suggestions,  int total,  int processingTime,  CategoryInsights? categoryInsights)  $default,) {final _that = this;
switch (_that) {
case _UniversalSearchResponse():
return $default(_that.query,_that.searchType,_that.results,_that.suggestions,_that.total,_that.processingTime,_that.categoryInsights);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String query,  String searchType,  List<UniversalSearchResult> results,  List<String> suggestions,  int total,  int processingTime,  CategoryInsights? categoryInsights)?  $default,) {final _that = this;
switch (_that) {
case _UniversalSearchResponse() when $default != null:
return $default(_that.query,_that.searchType,_that.results,_that.suggestions,_that.total,_that.processingTime,_that.categoryInsights);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UniversalSearchResponse implements UniversalSearchResponse {
  const _UniversalSearchResponse({required this.query, this.searchType = 'hybrid', final  List<UniversalSearchResult> results = const [], final  List<String> suggestions = const [], this.total = 0, this.processingTime = 0, this.categoryInsights}): _results = results,_suggestions = suggestions;
  factory _UniversalSearchResponse.fromJson(Map<String, dynamic> json) => _$UniversalSearchResponseFromJson(json);

@override final  String query;
@override@JsonKey() final  String searchType;
 final  List<UniversalSearchResult> _results;
@override@JsonKey() List<UniversalSearchResult> get results {
  if (_results is EqualUnmodifiableListView) return _results;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_results);
}

 final  List<String> _suggestions;
@override@JsonKey() List<String> get suggestions {
  if (_suggestions is EqualUnmodifiableListView) return _suggestions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_suggestions);
}

@override@JsonKey() final  int total;
@override@JsonKey() final  int processingTime;
@override final  CategoryInsights? categoryInsights;

/// Create a copy of UniversalSearchResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UniversalSearchResponseCopyWith<_UniversalSearchResponse> get copyWith => __$UniversalSearchResponseCopyWithImpl<_UniversalSearchResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UniversalSearchResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UniversalSearchResponse&&(identical(other.query, query) || other.query == query)&&(identical(other.searchType, searchType) || other.searchType == searchType)&&const DeepCollectionEquality().equals(other._results, _results)&&const DeepCollectionEquality().equals(other._suggestions, _suggestions)&&(identical(other.total, total) || other.total == total)&&(identical(other.processingTime, processingTime) || other.processingTime == processingTime)&&(identical(other.categoryInsights, categoryInsights) || other.categoryInsights == categoryInsights));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,searchType,const DeepCollectionEquality().hash(_results),const DeepCollectionEquality().hash(_suggestions),total,processingTime,categoryInsights);

@override
String toString() {
  return 'UniversalSearchResponse(query: $query, searchType: $searchType, results: $results, suggestions: $suggestions, total: $total, processingTime: $processingTime, categoryInsights: $categoryInsights)';
}


}

/// @nodoc
abstract mixin class _$UniversalSearchResponseCopyWith<$Res> implements $UniversalSearchResponseCopyWith<$Res> {
  factory _$UniversalSearchResponseCopyWith(_UniversalSearchResponse value, $Res Function(_UniversalSearchResponse) _then) = __$UniversalSearchResponseCopyWithImpl;
@override @useResult
$Res call({
 String query, String searchType, List<UniversalSearchResult> results, List<String> suggestions, int total, int processingTime, CategoryInsights? categoryInsights
});


@override $CategoryInsightsCopyWith<$Res>? get categoryInsights;

}
/// @nodoc
class __$UniversalSearchResponseCopyWithImpl<$Res>
    implements _$UniversalSearchResponseCopyWith<$Res> {
  __$UniversalSearchResponseCopyWithImpl(this._self, this._then);

  final _UniversalSearchResponse _self;
  final $Res Function(_UniversalSearchResponse) _then;

/// Create a copy of UniversalSearchResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? query = null,Object? searchType = null,Object? results = null,Object? suggestions = null,Object? total = null,Object? processingTime = null,Object? categoryInsights = freezed,}) {
  return _then(_UniversalSearchResponse(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,searchType: null == searchType ? _self.searchType : searchType // ignore: cast_nullable_to_non_nullable
as String,results: null == results ? _self._results : results // ignore: cast_nullable_to_non_nullable
as List<UniversalSearchResult>,suggestions: null == suggestions ? _self._suggestions : suggestions // ignore: cast_nullable_to_non_nullable
as List<String>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,processingTime: null == processingTime ? _self.processingTime : processingTime // ignore: cast_nullable_to_non_nullable
as int,categoryInsights: freezed == categoryInsights ? _self.categoryInsights : categoryInsights // ignore: cast_nullable_to_non_nullable
as CategoryInsights?,
  ));
}

/// Create a copy of UniversalSearchResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CategoryInsightsCopyWith<$Res>? get categoryInsights {
    if (_self.categoryInsights == null) {
    return null;
  }

  return $CategoryInsightsCopyWith<$Res>(_self.categoryInsights!, (value) {
    return _then(_self.copyWith(categoryInsights: value));
  });
}
}


/// @nodoc
mixin _$UniversalCategory {

 String get id; String get name; String? get nameAr; String? get nameEn; String get categoryType; String? get parentId; int get level; int get sortOrder; bool get isActive; Map<String, dynamic> get metadata; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of UniversalCategory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UniversalCategoryCopyWith<UniversalCategory> get copyWith => _$UniversalCategoryCopyWithImpl<UniversalCategory>(this as UniversalCategory, _$identity);

  /// Serializes this UniversalCategory to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UniversalCategory&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&(identical(other.categoryType, categoryType) || other.categoryType == categoryType)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.level, level) || other.level == level)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,nameEn,categoryType,parentId,level,sortOrder,isActive,const DeepCollectionEquality().hash(metadata),createdAt,updatedAt);

@override
String toString() {
  return 'UniversalCategory(id: $id, name: $name, nameAr: $nameAr, nameEn: $nameEn, categoryType: $categoryType, parentId: $parentId, level: $level, sortOrder: $sortOrder, isActive: $isActive, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $UniversalCategoryCopyWith<$Res>  {
  factory $UniversalCategoryCopyWith(UniversalCategory value, $Res Function(UniversalCategory) _then) = _$UniversalCategoryCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? nameAr, String? nameEn, String categoryType, String? parentId, int level, int sortOrder, bool isActive, Map<String, dynamic> metadata, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$UniversalCategoryCopyWithImpl<$Res>
    implements $UniversalCategoryCopyWith<$Res> {
  _$UniversalCategoryCopyWithImpl(this._self, this._then);

  final UniversalCategory _self;
  final $Res Function(UniversalCategory) _then;

/// Create a copy of UniversalCategory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? nameEn = freezed,Object? categoryType = null,Object? parentId = freezed,Object? level = null,Object? sortOrder = null,Object? isActive = null,Object? metadata = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,nameEn: freezed == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String?,categoryType: null == categoryType ? _self.categoryType : categoryType // ignore: cast_nullable_to_non_nullable
as String,parentId: freezed == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String?,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as int,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [UniversalCategory].
extension UniversalCategoryPatterns on UniversalCategory {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UniversalCategory value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UniversalCategory() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UniversalCategory value)  $default,){
final _that = this;
switch (_that) {
case _UniversalCategory():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UniversalCategory value)?  $default,){
final _that = this;
switch (_that) {
case _UniversalCategory() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? nameEn,  String categoryType,  String? parentId,  int level,  int sortOrder,  bool isActive,  Map<String, dynamic> metadata,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UniversalCategory() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.categoryType,_that.parentId,_that.level,_that.sortOrder,_that.isActive,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? nameEn,  String categoryType,  String? parentId,  int level,  int sortOrder,  bool isActive,  Map<String, dynamic> metadata,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _UniversalCategory():
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.categoryType,_that.parentId,_that.level,_that.sortOrder,_that.isActive,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? nameAr,  String? nameEn,  String categoryType,  String? parentId,  int level,  int sortOrder,  bool isActive,  Map<String, dynamic> metadata,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _UniversalCategory() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.categoryType,_that.parentId,_that.level,_that.sortOrder,_that.isActive,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UniversalCategory implements UniversalCategory {
  const _UniversalCategory({required this.id, required this.name, this.nameAr, this.nameEn, required this.categoryType, this.parentId, this.level = 0, this.sortOrder = 0, this.isActive = true, final  Map<String, dynamic> metadata = const {}, this.createdAt, this.updatedAt}): _metadata = metadata;
  factory _UniversalCategory.fromJson(Map<String, dynamic> json) => _$UniversalCategoryFromJson(json);

@override final  String id;
@override final  String name;
@override final  String? nameAr;
@override final  String? nameEn;
@override final  String categoryType;
@override final  String? parentId;
@override@JsonKey() final  int level;
@override@JsonKey() final  int sortOrder;
@override@JsonKey() final  bool isActive;
 final  Map<String, dynamic> _metadata;
@override@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}

@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of UniversalCategory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UniversalCategoryCopyWith<_UniversalCategory> get copyWith => __$UniversalCategoryCopyWithImpl<_UniversalCategory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UniversalCategoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UniversalCategory&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&(identical(other.categoryType, categoryType) || other.categoryType == categoryType)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.level, level) || other.level == level)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,nameEn,categoryType,parentId,level,sortOrder,isActive,const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt);

@override
String toString() {
  return 'UniversalCategory(id: $id, name: $name, nameAr: $nameAr, nameEn: $nameEn, categoryType: $categoryType, parentId: $parentId, level: $level, sortOrder: $sortOrder, isActive: $isActive, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$UniversalCategoryCopyWith<$Res> implements $UniversalCategoryCopyWith<$Res> {
  factory _$UniversalCategoryCopyWith(_UniversalCategory value, $Res Function(_UniversalCategory) _then) = __$UniversalCategoryCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? nameAr, String? nameEn, String categoryType, String? parentId, int level, int sortOrder, bool isActive, Map<String, dynamic> metadata, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$UniversalCategoryCopyWithImpl<$Res>
    implements _$UniversalCategoryCopyWith<$Res> {
  __$UniversalCategoryCopyWithImpl(this._self, this._then);

  final _UniversalCategory _self;
  final $Res Function(_UniversalCategory) _then;

/// Create a copy of UniversalCategory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? nameEn = freezed,Object? categoryType = null,Object? parentId = freezed,Object? level = null,Object? sortOrder = null,Object? isActive = null,Object? metadata = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_UniversalCategory(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,nameEn: freezed == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String?,categoryType: null == categoryType ? _self.categoryType : categoryType // ignore: cast_nullable_to_non_nullable
as String,parentId: freezed == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String?,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as int,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CategoryFilter {

 String get id; String get categoryId; String get filterName; String get filterType; Map<String, dynamic> get filterOptions; bool get isRequired; int get sortOrder; DateTime? get createdAt;
/// Create a copy of CategoryFilter
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CategoryFilterCopyWith<CategoryFilter> get copyWith => _$CategoryFilterCopyWithImpl<CategoryFilter>(this as CategoryFilter, _$identity);

  /// Serializes this CategoryFilter to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CategoryFilter&&(identical(other.id, id) || other.id == id)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.filterName, filterName) || other.filterName == filterName)&&(identical(other.filterType, filterType) || other.filterType == filterType)&&const DeepCollectionEquality().equals(other.filterOptions, filterOptions)&&(identical(other.isRequired, isRequired) || other.isRequired == isRequired)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,categoryId,filterName,filterType,const DeepCollectionEquality().hash(filterOptions),isRequired,sortOrder,createdAt);

@override
String toString() {
  return 'CategoryFilter(id: $id, categoryId: $categoryId, filterName: $filterName, filterType: $filterType, filterOptions: $filterOptions, isRequired: $isRequired, sortOrder: $sortOrder, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $CategoryFilterCopyWith<$Res>  {
  factory $CategoryFilterCopyWith(CategoryFilter value, $Res Function(CategoryFilter) _then) = _$CategoryFilterCopyWithImpl;
@useResult
$Res call({
 String id, String categoryId, String filterName, String filterType, Map<String, dynamic> filterOptions, bool isRequired, int sortOrder, DateTime? createdAt
});




}
/// @nodoc
class _$CategoryFilterCopyWithImpl<$Res>
    implements $CategoryFilterCopyWith<$Res> {
  _$CategoryFilterCopyWithImpl(this._self, this._then);

  final CategoryFilter _self;
  final $Res Function(CategoryFilter) _then;

/// Create a copy of CategoryFilter
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? categoryId = null,Object? filterName = null,Object? filterType = null,Object? filterOptions = null,Object? isRequired = null,Object? sortOrder = null,Object? createdAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,categoryId: null == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String,filterName: null == filterName ? _self.filterName : filterName // ignore: cast_nullable_to_non_nullable
as String,filterType: null == filterType ? _self.filterType : filterType // ignore: cast_nullable_to_non_nullable
as String,filterOptions: null == filterOptions ? _self.filterOptions : filterOptions // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,isRequired: null == isRequired ? _self.isRequired : isRequired // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [CategoryFilter].
extension CategoryFilterPatterns on CategoryFilter {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CategoryFilter value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CategoryFilter() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CategoryFilter value)  $default,){
final _that = this;
switch (_that) {
case _CategoryFilter():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CategoryFilter value)?  $default,){
final _that = this;
switch (_that) {
case _CategoryFilter() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String categoryId,  String filterName,  String filterType,  Map<String, dynamic> filterOptions,  bool isRequired,  int sortOrder,  DateTime? createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CategoryFilter() when $default != null:
return $default(_that.id,_that.categoryId,_that.filterName,_that.filterType,_that.filterOptions,_that.isRequired,_that.sortOrder,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String categoryId,  String filterName,  String filterType,  Map<String, dynamic> filterOptions,  bool isRequired,  int sortOrder,  DateTime? createdAt)  $default,) {final _that = this;
switch (_that) {
case _CategoryFilter():
return $default(_that.id,_that.categoryId,_that.filterName,_that.filterType,_that.filterOptions,_that.isRequired,_that.sortOrder,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String categoryId,  String filterName,  String filterType,  Map<String, dynamic> filterOptions,  bool isRequired,  int sortOrder,  DateTime? createdAt)?  $default,) {final _that = this;
switch (_that) {
case _CategoryFilter() when $default != null:
return $default(_that.id,_that.categoryId,_that.filterName,_that.filterType,_that.filterOptions,_that.isRequired,_that.sortOrder,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CategoryFilter implements CategoryFilter {
  const _CategoryFilter({required this.id, required this.categoryId, required this.filterName, required this.filterType, final  Map<String, dynamic> filterOptions = const {}, this.isRequired = false, this.sortOrder = 0, this.createdAt}): _filterOptions = filterOptions;
  factory _CategoryFilter.fromJson(Map<String, dynamic> json) => _$CategoryFilterFromJson(json);

@override final  String id;
@override final  String categoryId;
@override final  String filterName;
@override final  String filterType;
 final  Map<String, dynamic> _filterOptions;
@override@JsonKey() Map<String, dynamic> get filterOptions {
  if (_filterOptions is EqualUnmodifiableMapView) return _filterOptions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_filterOptions);
}

@override@JsonKey() final  bool isRequired;
@override@JsonKey() final  int sortOrder;
@override final  DateTime? createdAt;

/// Create a copy of CategoryFilter
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CategoryFilterCopyWith<_CategoryFilter> get copyWith => __$CategoryFilterCopyWithImpl<_CategoryFilter>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CategoryFilterToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CategoryFilter&&(identical(other.id, id) || other.id == id)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.filterName, filterName) || other.filterName == filterName)&&(identical(other.filterType, filterType) || other.filterType == filterType)&&const DeepCollectionEquality().equals(other._filterOptions, _filterOptions)&&(identical(other.isRequired, isRequired) || other.isRequired == isRequired)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,categoryId,filterName,filterType,const DeepCollectionEquality().hash(_filterOptions),isRequired,sortOrder,createdAt);

@override
String toString() {
  return 'CategoryFilter(id: $id, categoryId: $categoryId, filterName: $filterName, filterType: $filterType, filterOptions: $filterOptions, isRequired: $isRequired, sortOrder: $sortOrder, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$CategoryFilterCopyWith<$Res> implements $CategoryFilterCopyWith<$Res> {
  factory _$CategoryFilterCopyWith(_CategoryFilter value, $Res Function(_CategoryFilter) _then) = __$CategoryFilterCopyWithImpl;
@override @useResult
$Res call({
 String id, String categoryId, String filterName, String filterType, Map<String, dynamic> filterOptions, bool isRequired, int sortOrder, DateTime? createdAt
});




}
/// @nodoc
class __$CategoryFilterCopyWithImpl<$Res>
    implements _$CategoryFilterCopyWith<$Res> {
  __$CategoryFilterCopyWithImpl(this._self, this._then);

  final _CategoryFilter _self;
  final $Res Function(_CategoryFilter) _then;

/// Create a copy of CategoryFilter
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? categoryId = null,Object? filterName = null,Object? filterType = null,Object? filterOptions = null,Object? isRequired = null,Object? sortOrder = null,Object? createdAt = freezed,}) {
  return _then(_CategoryFilter(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,categoryId: null == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String,filterName: null == filterName ? _self.filterName : filterName // ignore: cast_nullable_to_non_nullable
as String,filterType: null == filterType ? _self.filterType : filterType // ignore: cast_nullable_to_non_nullable
as String,filterOptions: null == filterOptions ? _self._filterOptions : filterOptions // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,isRequired: null == isRequired ? _self.isRequired : isRequired // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CategoryInsights {

 Map<String, int> get distribution; List<CategoryInfo> get categoryInfo; int get totalCategories; String? get dominantCategory;
/// Create a copy of CategoryInsights
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CategoryInsightsCopyWith<CategoryInsights> get copyWith => _$CategoryInsightsCopyWithImpl<CategoryInsights>(this as CategoryInsights, _$identity);

  /// Serializes this CategoryInsights to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CategoryInsights&&const DeepCollectionEquality().equals(other.distribution, distribution)&&const DeepCollectionEquality().equals(other.categoryInfo, categoryInfo)&&(identical(other.totalCategories, totalCategories) || other.totalCategories == totalCategories)&&(identical(other.dominantCategory, dominantCategory) || other.dominantCategory == dominantCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(distribution),const DeepCollectionEquality().hash(categoryInfo),totalCategories,dominantCategory);

@override
String toString() {
  return 'CategoryInsights(distribution: $distribution, categoryInfo: $categoryInfo, totalCategories: $totalCategories, dominantCategory: $dominantCategory)';
}


}

/// @nodoc
abstract mixin class $CategoryInsightsCopyWith<$Res>  {
  factory $CategoryInsightsCopyWith(CategoryInsights value, $Res Function(CategoryInsights) _then) = _$CategoryInsightsCopyWithImpl;
@useResult
$Res call({
 Map<String, int> distribution, List<CategoryInfo> categoryInfo, int totalCategories, String? dominantCategory
});




}
/// @nodoc
class _$CategoryInsightsCopyWithImpl<$Res>
    implements $CategoryInsightsCopyWith<$Res> {
  _$CategoryInsightsCopyWithImpl(this._self, this._then);

  final CategoryInsights _self;
  final $Res Function(CategoryInsights) _then;

/// Create a copy of CategoryInsights
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? distribution = null,Object? categoryInfo = null,Object? totalCategories = null,Object? dominantCategory = freezed,}) {
  return _then(_self.copyWith(
distribution: null == distribution ? _self.distribution : distribution // ignore: cast_nullable_to_non_nullable
as Map<String, int>,categoryInfo: null == categoryInfo ? _self.categoryInfo : categoryInfo // ignore: cast_nullable_to_non_nullable
as List<CategoryInfo>,totalCategories: null == totalCategories ? _self.totalCategories : totalCategories // ignore: cast_nullable_to_non_nullable
as int,dominantCategory: freezed == dominantCategory ? _self.dominantCategory : dominantCategory // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CategoryInsights].
extension CategoryInsightsPatterns on CategoryInsights {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CategoryInsights value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CategoryInsights() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CategoryInsights value)  $default,){
final _that = this;
switch (_that) {
case _CategoryInsights():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CategoryInsights value)?  $default,){
final _that = this;
switch (_that) {
case _CategoryInsights() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Map<String, int> distribution,  List<CategoryInfo> categoryInfo,  int totalCategories,  String? dominantCategory)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CategoryInsights() when $default != null:
return $default(_that.distribution,_that.categoryInfo,_that.totalCategories,_that.dominantCategory);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Map<String, int> distribution,  List<CategoryInfo> categoryInfo,  int totalCategories,  String? dominantCategory)  $default,) {final _that = this;
switch (_that) {
case _CategoryInsights():
return $default(_that.distribution,_that.categoryInfo,_that.totalCategories,_that.dominantCategory);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Map<String, int> distribution,  List<CategoryInfo> categoryInfo,  int totalCategories,  String? dominantCategory)?  $default,) {final _that = this;
switch (_that) {
case _CategoryInsights() when $default != null:
return $default(_that.distribution,_that.categoryInfo,_that.totalCategories,_that.dominantCategory);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CategoryInsights implements CategoryInsights {
  const _CategoryInsights({final  Map<String, int> distribution = const {}, final  List<CategoryInfo> categoryInfo = const [], this.totalCategories = 0, this.dominantCategory}): _distribution = distribution,_categoryInfo = categoryInfo;
  factory _CategoryInsights.fromJson(Map<String, dynamic> json) => _$CategoryInsightsFromJson(json);

 final  Map<String, int> _distribution;
@override@JsonKey() Map<String, int> get distribution {
  if (_distribution is EqualUnmodifiableMapView) return _distribution;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_distribution);
}

 final  List<CategoryInfo> _categoryInfo;
@override@JsonKey() List<CategoryInfo> get categoryInfo {
  if (_categoryInfo is EqualUnmodifiableListView) return _categoryInfo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_categoryInfo);
}

@override@JsonKey() final  int totalCategories;
@override final  String? dominantCategory;

/// Create a copy of CategoryInsights
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CategoryInsightsCopyWith<_CategoryInsights> get copyWith => __$CategoryInsightsCopyWithImpl<_CategoryInsights>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CategoryInsightsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CategoryInsights&&const DeepCollectionEquality().equals(other._distribution, _distribution)&&const DeepCollectionEquality().equals(other._categoryInfo, _categoryInfo)&&(identical(other.totalCategories, totalCategories) || other.totalCategories == totalCategories)&&(identical(other.dominantCategory, dominantCategory) || other.dominantCategory == dominantCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_distribution),const DeepCollectionEquality().hash(_categoryInfo),totalCategories,dominantCategory);

@override
String toString() {
  return 'CategoryInsights(distribution: $distribution, categoryInfo: $categoryInfo, totalCategories: $totalCategories, dominantCategory: $dominantCategory)';
}


}

/// @nodoc
abstract mixin class _$CategoryInsightsCopyWith<$Res> implements $CategoryInsightsCopyWith<$Res> {
  factory _$CategoryInsightsCopyWith(_CategoryInsights value, $Res Function(_CategoryInsights) _then) = __$CategoryInsightsCopyWithImpl;
@override @useResult
$Res call({
 Map<String, int> distribution, List<CategoryInfo> categoryInfo, int totalCategories, String? dominantCategory
});




}
/// @nodoc
class __$CategoryInsightsCopyWithImpl<$Res>
    implements _$CategoryInsightsCopyWith<$Res> {
  __$CategoryInsightsCopyWithImpl(this._self, this._then);

  final _CategoryInsights _self;
  final $Res Function(_CategoryInsights) _then;

/// Create a copy of CategoryInsights
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? distribution = null,Object? categoryInfo = null,Object? totalCategories = null,Object? dominantCategory = freezed,}) {
  return _then(_CategoryInsights(
distribution: null == distribution ? _self._distribution : distribution // ignore: cast_nullable_to_non_nullable
as Map<String, int>,categoryInfo: null == categoryInfo ? _self._categoryInfo : categoryInfo // ignore: cast_nullable_to_non_nullable
as List<CategoryInfo>,totalCategories: null == totalCategories ? _self.totalCategories : totalCategories // ignore: cast_nullable_to_non_nullable
as int,dominantCategory: freezed == dominantCategory ? _self.dominantCategory : dominantCategory // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$CategoryInfo {

 String get categoryType; String get name; String? get nameAr; String? get nameEn; Map<String, dynamic> get metadata;
/// Create a copy of CategoryInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CategoryInfoCopyWith<CategoryInfo> get copyWith => _$CategoryInfoCopyWithImpl<CategoryInfo>(this as CategoryInfo, _$identity);

  /// Serializes this CategoryInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CategoryInfo&&(identical(other.categoryType, categoryType) || other.categoryType == categoryType)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,categoryType,name,nameAr,nameEn,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'CategoryInfo(categoryType: $categoryType, name: $name, nameAr: $nameAr, nameEn: $nameEn, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $CategoryInfoCopyWith<$Res>  {
  factory $CategoryInfoCopyWith(CategoryInfo value, $Res Function(CategoryInfo) _then) = _$CategoryInfoCopyWithImpl;
@useResult
$Res call({
 String categoryType, String name, String? nameAr, String? nameEn, Map<String, dynamic> metadata
});




}
/// @nodoc
class _$CategoryInfoCopyWithImpl<$Res>
    implements $CategoryInfoCopyWith<$Res> {
  _$CategoryInfoCopyWithImpl(this._self, this._then);

  final CategoryInfo _self;
  final $Res Function(CategoryInfo) _then;

/// Create a copy of CategoryInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? categoryType = null,Object? name = null,Object? nameAr = freezed,Object? nameEn = freezed,Object? metadata = null,}) {
  return _then(_self.copyWith(
categoryType: null == categoryType ? _self.categoryType : categoryType // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,nameEn: freezed == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [CategoryInfo].
extension CategoryInfoPatterns on CategoryInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CategoryInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CategoryInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CategoryInfo value)  $default,){
final _that = this;
switch (_that) {
case _CategoryInfo():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CategoryInfo value)?  $default,){
final _that = this;
switch (_that) {
case _CategoryInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String categoryType,  String name,  String? nameAr,  String? nameEn,  Map<String, dynamic> metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CategoryInfo() when $default != null:
return $default(_that.categoryType,_that.name,_that.nameAr,_that.nameEn,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String categoryType,  String name,  String? nameAr,  String? nameEn,  Map<String, dynamic> metadata)  $default,) {final _that = this;
switch (_that) {
case _CategoryInfo():
return $default(_that.categoryType,_that.name,_that.nameAr,_that.nameEn,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String categoryType,  String name,  String? nameAr,  String? nameEn,  Map<String, dynamic> metadata)?  $default,) {final _that = this;
switch (_that) {
case _CategoryInfo() when $default != null:
return $default(_that.categoryType,_that.name,_that.nameAr,_that.nameEn,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CategoryInfo implements CategoryInfo {
  const _CategoryInfo({required this.categoryType, required this.name, this.nameAr, this.nameEn, final  Map<String, dynamic> metadata = const {}}): _metadata = metadata;
  factory _CategoryInfo.fromJson(Map<String, dynamic> json) => _$CategoryInfoFromJson(json);

@override final  String categoryType;
@override final  String name;
@override final  String? nameAr;
@override final  String? nameEn;
 final  Map<String, dynamic> _metadata;
@override@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of CategoryInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CategoryInfoCopyWith<_CategoryInfo> get copyWith => __$CategoryInfoCopyWithImpl<_CategoryInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CategoryInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CategoryInfo&&(identical(other.categoryType, categoryType) || other.categoryType == categoryType)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,categoryType,name,nameAr,nameEn,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'CategoryInfo(categoryType: $categoryType, name: $name, nameAr: $nameAr, nameEn: $nameEn, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$CategoryInfoCopyWith<$Res> implements $CategoryInfoCopyWith<$Res> {
  factory _$CategoryInfoCopyWith(_CategoryInfo value, $Res Function(_CategoryInfo) _then) = __$CategoryInfoCopyWithImpl;
@override @useResult
$Res call({
 String categoryType, String name, String? nameAr, String? nameEn, Map<String, dynamic> metadata
});




}
/// @nodoc
class __$CategoryInfoCopyWithImpl<$Res>
    implements _$CategoryInfoCopyWith<$Res> {
  __$CategoryInfoCopyWithImpl(this._self, this._then);

  final _CategoryInfo _self;
  final $Res Function(_CategoryInfo) _then;

/// Create a copy of CategoryInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? categoryType = null,Object? name = null,Object? nameAr = freezed,Object? nameEn = freezed,Object? metadata = null,}) {
  return _then(_CategoryInfo(
categoryType: null == categoryType ? _self.categoryType : categoryType // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,nameEn: freezed == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String?,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}


/// @nodoc
mixin _$FilterState {

 Map<String, dynamic> get activeFilters; List<CategoryFilter> get availableFilters; bool get isLoading; String? get error;
/// Create a copy of FilterState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FilterStateCopyWith<FilterState> get copyWith => _$FilterStateCopyWithImpl<FilterState>(this as FilterState, _$identity);

  /// Serializes this FilterState to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FilterState&&const DeepCollectionEquality().equals(other.activeFilters, activeFilters)&&const DeepCollectionEquality().equals(other.availableFilters, availableFilters)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(activeFilters),const DeepCollectionEquality().hash(availableFilters),isLoading,error);

@override
String toString() {
  return 'FilterState(activeFilters: $activeFilters, availableFilters: $availableFilters, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class $FilterStateCopyWith<$Res>  {
  factory $FilterStateCopyWith(FilterState value, $Res Function(FilterState) _then) = _$FilterStateCopyWithImpl;
@useResult
$Res call({
 Map<String, dynamic> activeFilters, List<CategoryFilter> availableFilters, bool isLoading, String? error
});




}
/// @nodoc
class _$FilterStateCopyWithImpl<$Res>
    implements $FilterStateCopyWith<$Res> {
  _$FilterStateCopyWithImpl(this._self, this._then);

  final FilterState _self;
  final $Res Function(FilterState) _then;

/// Create a copy of FilterState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? activeFilters = null,Object? availableFilters = null,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_self.copyWith(
activeFilters: null == activeFilters ? _self.activeFilters : activeFilters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,availableFilters: null == availableFilters ? _self.availableFilters : availableFilters // ignore: cast_nullable_to_non_nullable
as List<CategoryFilter>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [FilterState].
extension FilterStatePatterns on FilterState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FilterState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FilterState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FilterState value)  $default,){
final _that = this;
switch (_that) {
case _FilterState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FilterState value)?  $default,){
final _that = this;
switch (_that) {
case _FilterState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Map<String, dynamic> activeFilters,  List<CategoryFilter> availableFilters,  bool isLoading,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FilterState() when $default != null:
return $default(_that.activeFilters,_that.availableFilters,_that.isLoading,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Map<String, dynamic> activeFilters,  List<CategoryFilter> availableFilters,  bool isLoading,  String? error)  $default,) {final _that = this;
switch (_that) {
case _FilterState():
return $default(_that.activeFilters,_that.availableFilters,_that.isLoading,_that.error);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Map<String, dynamic> activeFilters,  List<CategoryFilter> availableFilters,  bool isLoading,  String? error)?  $default,) {final _that = this;
switch (_that) {
case _FilterState() when $default != null:
return $default(_that.activeFilters,_that.availableFilters,_that.isLoading,_that.error);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FilterState implements FilterState {
  const _FilterState({final  Map<String, dynamic> activeFilters = const {}, final  List<CategoryFilter> availableFilters = const [], this.isLoading = false, this.error}): _activeFilters = activeFilters,_availableFilters = availableFilters;
  factory _FilterState.fromJson(Map<String, dynamic> json) => _$FilterStateFromJson(json);

 final  Map<String, dynamic> _activeFilters;
@override@JsonKey() Map<String, dynamic> get activeFilters {
  if (_activeFilters is EqualUnmodifiableMapView) return _activeFilters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_activeFilters);
}

 final  List<CategoryFilter> _availableFilters;
@override@JsonKey() List<CategoryFilter> get availableFilters {
  if (_availableFilters is EqualUnmodifiableListView) return _availableFilters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_availableFilters);
}

@override@JsonKey() final  bool isLoading;
@override final  String? error;

/// Create a copy of FilterState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FilterStateCopyWith<_FilterState> get copyWith => __$FilterStateCopyWithImpl<_FilterState>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FilterStateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FilterState&&const DeepCollectionEquality().equals(other._activeFilters, _activeFilters)&&const DeepCollectionEquality().equals(other._availableFilters, _availableFilters)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_activeFilters),const DeepCollectionEquality().hash(_availableFilters),isLoading,error);

@override
String toString() {
  return 'FilterState(activeFilters: $activeFilters, availableFilters: $availableFilters, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class _$FilterStateCopyWith<$Res> implements $FilterStateCopyWith<$Res> {
  factory _$FilterStateCopyWith(_FilterState value, $Res Function(_FilterState) _then) = __$FilterStateCopyWithImpl;
@override @useResult
$Res call({
 Map<String, dynamic> activeFilters, List<CategoryFilter> availableFilters, bool isLoading, String? error
});




}
/// @nodoc
class __$FilterStateCopyWithImpl<$Res>
    implements _$FilterStateCopyWith<$Res> {
  __$FilterStateCopyWithImpl(this._self, this._then);

  final _FilterState _self;
  final $Res Function(_FilterState) _then;

/// Create a copy of FilterState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? activeFilters = null,Object? availableFilters = null,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_FilterState(
activeFilters: null == activeFilters ? _self._activeFilters : activeFilters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,availableFilters: null == availableFilters ? _self._availableFilters : availableFilters // ignore: cast_nullable_to_non_nullable
as List<CategoryFilter>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$SearchSettings {

 String get defaultSearchType; double get defaultMatchThreshold; double get defaultTextWeight; double get defaultSemanticWeight; int get defaultLimit; bool get enableSuggestions; bool get enableHistory; bool get enableFilters;
/// Create a copy of SearchSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SearchSettingsCopyWith<SearchSettings> get copyWith => _$SearchSettingsCopyWithImpl<SearchSettings>(this as SearchSettings, _$identity);

  /// Serializes this SearchSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SearchSettings&&(identical(other.defaultSearchType, defaultSearchType) || other.defaultSearchType == defaultSearchType)&&(identical(other.defaultMatchThreshold, defaultMatchThreshold) || other.defaultMatchThreshold == defaultMatchThreshold)&&(identical(other.defaultTextWeight, defaultTextWeight) || other.defaultTextWeight == defaultTextWeight)&&(identical(other.defaultSemanticWeight, defaultSemanticWeight) || other.defaultSemanticWeight == defaultSemanticWeight)&&(identical(other.defaultLimit, defaultLimit) || other.defaultLimit == defaultLimit)&&(identical(other.enableSuggestions, enableSuggestions) || other.enableSuggestions == enableSuggestions)&&(identical(other.enableHistory, enableHistory) || other.enableHistory == enableHistory)&&(identical(other.enableFilters, enableFilters) || other.enableFilters == enableFilters));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,defaultSearchType,defaultMatchThreshold,defaultTextWeight,defaultSemanticWeight,defaultLimit,enableSuggestions,enableHistory,enableFilters);

@override
String toString() {
  return 'SearchSettings(defaultSearchType: $defaultSearchType, defaultMatchThreshold: $defaultMatchThreshold, defaultTextWeight: $defaultTextWeight, defaultSemanticWeight: $defaultSemanticWeight, defaultLimit: $defaultLimit, enableSuggestions: $enableSuggestions, enableHistory: $enableHistory, enableFilters: $enableFilters)';
}


}

/// @nodoc
abstract mixin class $SearchSettingsCopyWith<$Res>  {
  factory $SearchSettingsCopyWith(SearchSettings value, $Res Function(SearchSettings) _then) = _$SearchSettingsCopyWithImpl;
@useResult
$Res call({
 String defaultSearchType, double defaultMatchThreshold, double defaultTextWeight, double defaultSemanticWeight, int defaultLimit, bool enableSuggestions, bool enableHistory, bool enableFilters
});




}
/// @nodoc
class _$SearchSettingsCopyWithImpl<$Res>
    implements $SearchSettingsCopyWith<$Res> {
  _$SearchSettingsCopyWithImpl(this._self, this._then);

  final SearchSettings _self;
  final $Res Function(SearchSettings) _then;

/// Create a copy of SearchSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? defaultSearchType = null,Object? defaultMatchThreshold = null,Object? defaultTextWeight = null,Object? defaultSemanticWeight = null,Object? defaultLimit = null,Object? enableSuggestions = null,Object? enableHistory = null,Object? enableFilters = null,}) {
  return _then(_self.copyWith(
defaultSearchType: null == defaultSearchType ? _self.defaultSearchType : defaultSearchType // ignore: cast_nullable_to_non_nullable
as String,defaultMatchThreshold: null == defaultMatchThreshold ? _self.defaultMatchThreshold : defaultMatchThreshold // ignore: cast_nullable_to_non_nullable
as double,defaultTextWeight: null == defaultTextWeight ? _self.defaultTextWeight : defaultTextWeight // ignore: cast_nullable_to_non_nullable
as double,defaultSemanticWeight: null == defaultSemanticWeight ? _self.defaultSemanticWeight : defaultSemanticWeight // ignore: cast_nullable_to_non_nullable
as double,defaultLimit: null == defaultLimit ? _self.defaultLimit : defaultLimit // ignore: cast_nullable_to_non_nullable
as int,enableSuggestions: null == enableSuggestions ? _self.enableSuggestions : enableSuggestions // ignore: cast_nullable_to_non_nullable
as bool,enableHistory: null == enableHistory ? _self.enableHistory : enableHistory // ignore: cast_nullable_to_non_nullable
as bool,enableFilters: null == enableFilters ? _self.enableFilters : enableFilters // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [SearchSettings].
extension SearchSettingsPatterns on SearchSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SearchSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SearchSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SearchSettings value)  $default,){
final _that = this;
switch (_that) {
case _SearchSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SearchSettings value)?  $default,){
final _that = this;
switch (_that) {
case _SearchSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String defaultSearchType,  double defaultMatchThreshold,  double defaultTextWeight,  double defaultSemanticWeight,  int defaultLimit,  bool enableSuggestions,  bool enableHistory,  bool enableFilters)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SearchSettings() when $default != null:
return $default(_that.defaultSearchType,_that.defaultMatchThreshold,_that.defaultTextWeight,_that.defaultSemanticWeight,_that.defaultLimit,_that.enableSuggestions,_that.enableHistory,_that.enableFilters);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String defaultSearchType,  double defaultMatchThreshold,  double defaultTextWeight,  double defaultSemanticWeight,  int defaultLimit,  bool enableSuggestions,  bool enableHistory,  bool enableFilters)  $default,) {final _that = this;
switch (_that) {
case _SearchSettings():
return $default(_that.defaultSearchType,_that.defaultMatchThreshold,_that.defaultTextWeight,_that.defaultSemanticWeight,_that.defaultLimit,_that.enableSuggestions,_that.enableHistory,_that.enableFilters);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String defaultSearchType,  double defaultMatchThreshold,  double defaultTextWeight,  double defaultSemanticWeight,  int defaultLimit,  bool enableSuggestions,  bool enableHistory,  bool enableFilters)?  $default,) {final _that = this;
switch (_that) {
case _SearchSettings() when $default != null:
return $default(_that.defaultSearchType,_that.defaultMatchThreshold,_that.defaultTextWeight,_that.defaultSemanticWeight,_that.defaultLimit,_that.enableSuggestions,_that.enableHistory,_that.enableFilters);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SearchSettings implements SearchSettings {
  const _SearchSettings({this.defaultSearchType = 'hybrid', this.defaultMatchThreshold = 0.75, this.defaultTextWeight = 0.3, this.defaultSemanticWeight = 0.7, this.defaultLimit = 20, this.enableSuggestions = true, this.enableHistory = true, this.enableFilters = true});
  factory _SearchSettings.fromJson(Map<String, dynamic> json) => _$SearchSettingsFromJson(json);

@override@JsonKey() final  String defaultSearchType;
@override@JsonKey() final  double defaultMatchThreshold;
@override@JsonKey() final  double defaultTextWeight;
@override@JsonKey() final  double defaultSemanticWeight;
@override@JsonKey() final  int defaultLimit;
@override@JsonKey() final  bool enableSuggestions;
@override@JsonKey() final  bool enableHistory;
@override@JsonKey() final  bool enableFilters;

/// Create a copy of SearchSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SearchSettingsCopyWith<_SearchSettings> get copyWith => __$SearchSettingsCopyWithImpl<_SearchSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SearchSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SearchSettings&&(identical(other.defaultSearchType, defaultSearchType) || other.defaultSearchType == defaultSearchType)&&(identical(other.defaultMatchThreshold, defaultMatchThreshold) || other.defaultMatchThreshold == defaultMatchThreshold)&&(identical(other.defaultTextWeight, defaultTextWeight) || other.defaultTextWeight == defaultTextWeight)&&(identical(other.defaultSemanticWeight, defaultSemanticWeight) || other.defaultSemanticWeight == defaultSemanticWeight)&&(identical(other.defaultLimit, defaultLimit) || other.defaultLimit == defaultLimit)&&(identical(other.enableSuggestions, enableSuggestions) || other.enableSuggestions == enableSuggestions)&&(identical(other.enableHistory, enableHistory) || other.enableHistory == enableHistory)&&(identical(other.enableFilters, enableFilters) || other.enableFilters == enableFilters));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,defaultSearchType,defaultMatchThreshold,defaultTextWeight,defaultSemanticWeight,defaultLimit,enableSuggestions,enableHistory,enableFilters);

@override
String toString() {
  return 'SearchSettings(defaultSearchType: $defaultSearchType, defaultMatchThreshold: $defaultMatchThreshold, defaultTextWeight: $defaultTextWeight, defaultSemanticWeight: $defaultSemanticWeight, defaultLimit: $defaultLimit, enableSuggestions: $enableSuggestions, enableHistory: $enableHistory, enableFilters: $enableFilters)';
}


}

/// @nodoc
abstract mixin class _$SearchSettingsCopyWith<$Res> implements $SearchSettingsCopyWith<$Res> {
  factory _$SearchSettingsCopyWith(_SearchSettings value, $Res Function(_SearchSettings) _then) = __$SearchSettingsCopyWithImpl;
@override @useResult
$Res call({
 String defaultSearchType, double defaultMatchThreshold, double defaultTextWeight, double defaultSemanticWeight, int defaultLimit, bool enableSuggestions, bool enableHistory, bool enableFilters
});




}
/// @nodoc
class __$SearchSettingsCopyWithImpl<$Res>
    implements _$SearchSettingsCopyWith<$Res> {
  __$SearchSettingsCopyWithImpl(this._self, this._then);

  final _SearchSettings _self;
  final $Res Function(_SearchSettings) _then;

/// Create a copy of SearchSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? defaultSearchType = null,Object? defaultMatchThreshold = null,Object? defaultTextWeight = null,Object? defaultSemanticWeight = null,Object? defaultLimit = null,Object? enableSuggestions = null,Object? enableHistory = null,Object? enableFilters = null,}) {
  return _then(_SearchSettings(
defaultSearchType: null == defaultSearchType ? _self.defaultSearchType : defaultSearchType // ignore: cast_nullable_to_non_nullable
as String,defaultMatchThreshold: null == defaultMatchThreshold ? _self.defaultMatchThreshold : defaultMatchThreshold // ignore: cast_nullable_to_non_nullable
as double,defaultTextWeight: null == defaultTextWeight ? _self.defaultTextWeight : defaultTextWeight // ignore: cast_nullable_to_non_nullable
as double,defaultSemanticWeight: null == defaultSemanticWeight ? _self.defaultSemanticWeight : defaultSemanticWeight // ignore: cast_nullable_to_non_nullable
as double,defaultLimit: null == defaultLimit ? _self.defaultLimit : defaultLimit // ignore: cast_nullable_to_non_nullable
as int,enableSuggestions: null == enableSuggestions ? _self.enableSuggestions : enableSuggestions // ignore: cast_nullable_to_non_nullable
as bool,enableHistory: null == enableHistory ? _self.enableHistory : enableHistory // ignore: cast_nullable_to_non_nullable
as bool,enableFilters: null == enableFilters ? _self.enableFilters : enableFilters // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
