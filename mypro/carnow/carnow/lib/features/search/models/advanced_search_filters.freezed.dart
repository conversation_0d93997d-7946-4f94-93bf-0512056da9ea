// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'advanced_search_filters.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AdvancedSearchFilters {

// فلاتر أساسية
 String? get query; String? get category; double? get priceMin; double? get priceMax; String? get condition; String? get listingType;// فلاتر المركبة - البيانات الأمريكية الجديدة
@JsonKey(name: 'body_style_id') int? get bodyStyleId;@JsonKey(name: 'transmission_type_id') int? get transmissionTypeId;@JsonKey(name: 'brake_system_id') int? get brakeSystemId;@JsonKey(name: 'valvetrain_design_id') int? get valvetrainDesignId;@JsonKey(name: 'cab_type_id') int? get cabTypeId;@JsonKey(name: 'bed_type_id') int? get bedTypeId;@JsonKey(name: 'wheelbase_type_id') int? get wheelbaseTypeId;@JsonKey(name: 'has_turbo') bool? get hasTurbo;// فلاتر ميزات الأمان
@JsonKey(name: 'blind_spot_monitoring_id') int? get blindSpotMonitoringId;@JsonKey(name: 'blind_spot_intervention_id') int? get blindSpotInterventionId;// فلاتر المركبات الكهربائية
@JsonKey(name: 'charger_level_id') int? get chargerLevelId;// فلاتر المقطورات
@JsonKey(name: 'trailer_type_id') int? get trailerTypeId;// فلاتر التوافق التقليدية
 String? get make; String? get model; int? get yearFrom; int? get yearTo; String? get engine; String? get trim;// فلاتر الموقع والبائع
 String? get location; String? get sellerId; bool? get verifiedSeller;// فلاتر التقييم والشعبية
 double? get minRating; int? get minReviews; bool? get popularParts;// فلاتر التوفر والشحن
 bool? get inStock; bool? get freeShipping; int? get maxShippingDays;// ترتيب النتائج
 String get sortBy;// relevance, price_asc, price_desc, newest, rating
 int get limit; int get offset;
/// Create a copy of AdvancedSearchFilters
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdvancedSearchFiltersCopyWith<AdvancedSearchFilters> get copyWith => _$AdvancedSearchFiltersCopyWithImpl<AdvancedSearchFilters>(this as AdvancedSearchFilters, _$identity);

  /// Serializes this AdvancedSearchFilters to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdvancedSearchFilters&&(identical(other.query, query) || other.query == query)&&(identical(other.category, category) || other.category == category)&&(identical(other.priceMin, priceMin) || other.priceMin == priceMin)&&(identical(other.priceMax, priceMax) || other.priceMax == priceMax)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.listingType, listingType) || other.listingType == listingType)&&(identical(other.bodyStyleId, bodyStyleId) || other.bodyStyleId == bodyStyleId)&&(identical(other.transmissionTypeId, transmissionTypeId) || other.transmissionTypeId == transmissionTypeId)&&(identical(other.brakeSystemId, brakeSystemId) || other.brakeSystemId == brakeSystemId)&&(identical(other.valvetrainDesignId, valvetrainDesignId) || other.valvetrainDesignId == valvetrainDesignId)&&(identical(other.cabTypeId, cabTypeId) || other.cabTypeId == cabTypeId)&&(identical(other.bedTypeId, bedTypeId) || other.bedTypeId == bedTypeId)&&(identical(other.wheelbaseTypeId, wheelbaseTypeId) || other.wheelbaseTypeId == wheelbaseTypeId)&&(identical(other.hasTurbo, hasTurbo) || other.hasTurbo == hasTurbo)&&(identical(other.blindSpotMonitoringId, blindSpotMonitoringId) || other.blindSpotMonitoringId == blindSpotMonitoringId)&&(identical(other.blindSpotInterventionId, blindSpotInterventionId) || other.blindSpotInterventionId == blindSpotInterventionId)&&(identical(other.chargerLevelId, chargerLevelId) || other.chargerLevelId == chargerLevelId)&&(identical(other.trailerTypeId, trailerTypeId) || other.trailerTypeId == trailerTypeId)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.location, location) || other.location == location)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.verifiedSeller, verifiedSeller) || other.verifiedSeller == verifiedSeller)&&(identical(other.minRating, minRating) || other.minRating == minRating)&&(identical(other.minReviews, minReviews) || other.minReviews == minReviews)&&(identical(other.popularParts, popularParts) || other.popularParts == popularParts)&&(identical(other.inStock, inStock) || other.inStock == inStock)&&(identical(other.freeShipping, freeShipping) || other.freeShipping == freeShipping)&&(identical(other.maxShippingDays, maxShippingDays) || other.maxShippingDays == maxShippingDays)&&(identical(other.sortBy, sortBy) || other.sortBy == sortBy)&&(identical(other.limit, limit) || other.limit == limit)&&(identical(other.offset, offset) || other.offset == offset));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,query,category,priceMin,priceMax,condition,listingType,bodyStyleId,transmissionTypeId,brakeSystemId,valvetrainDesignId,cabTypeId,bedTypeId,wheelbaseTypeId,hasTurbo,blindSpotMonitoringId,blindSpotInterventionId,chargerLevelId,trailerTypeId,make,model,yearFrom,yearTo,engine,trim,location,sellerId,verifiedSeller,minRating,minReviews,popularParts,inStock,freeShipping,maxShippingDays,sortBy,limit,offset]);

@override
String toString() {
  return 'AdvancedSearchFilters(query: $query, category: $category, priceMin: $priceMin, priceMax: $priceMax, condition: $condition, listingType: $listingType, bodyStyleId: $bodyStyleId, transmissionTypeId: $transmissionTypeId, brakeSystemId: $brakeSystemId, valvetrainDesignId: $valvetrainDesignId, cabTypeId: $cabTypeId, bedTypeId: $bedTypeId, wheelbaseTypeId: $wheelbaseTypeId, hasTurbo: $hasTurbo, blindSpotMonitoringId: $blindSpotMonitoringId, blindSpotInterventionId: $blindSpotInterventionId, chargerLevelId: $chargerLevelId, trailerTypeId: $trailerTypeId, make: $make, model: $model, yearFrom: $yearFrom, yearTo: $yearTo, engine: $engine, trim: $trim, location: $location, sellerId: $sellerId, verifiedSeller: $verifiedSeller, minRating: $minRating, minReviews: $minReviews, popularParts: $popularParts, inStock: $inStock, freeShipping: $freeShipping, maxShippingDays: $maxShippingDays, sortBy: $sortBy, limit: $limit, offset: $offset)';
}


}

/// @nodoc
abstract mixin class $AdvancedSearchFiltersCopyWith<$Res>  {
  factory $AdvancedSearchFiltersCopyWith(AdvancedSearchFilters value, $Res Function(AdvancedSearchFilters) _then) = _$AdvancedSearchFiltersCopyWithImpl;
@useResult
$Res call({
 String? query, String? category, double? priceMin, double? priceMax, String? condition, String? listingType,@JsonKey(name: 'body_style_id') int? bodyStyleId,@JsonKey(name: 'transmission_type_id') int? transmissionTypeId,@JsonKey(name: 'brake_system_id') int? brakeSystemId,@JsonKey(name: 'valvetrain_design_id') int? valvetrainDesignId,@JsonKey(name: 'cab_type_id') int? cabTypeId,@JsonKey(name: 'bed_type_id') int? bedTypeId,@JsonKey(name: 'wheelbase_type_id') int? wheelbaseTypeId,@JsonKey(name: 'has_turbo') bool? hasTurbo,@JsonKey(name: 'blind_spot_monitoring_id') int? blindSpotMonitoringId,@JsonKey(name: 'blind_spot_intervention_id') int? blindSpotInterventionId,@JsonKey(name: 'charger_level_id') int? chargerLevelId,@JsonKey(name: 'trailer_type_id') int? trailerTypeId, String? make, String? model, int? yearFrom, int? yearTo, String? engine, String? trim, String? location, String? sellerId, bool? verifiedSeller, double? minRating, int? minReviews, bool? popularParts, bool? inStock, bool? freeShipping, int? maxShippingDays, String sortBy, int limit, int offset
});




}
/// @nodoc
class _$AdvancedSearchFiltersCopyWithImpl<$Res>
    implements $AdvancedSearchFiltersCopyWith<$Res> {
  _$AdvancedSearchFiltersCopyWithImpl(this._self, this._then);

  final AdvancedSearchFilters _self;
  final $Res Function(AdvancedSearchFilters) _then;

/// Create a copy of AdvancedSearchFilters
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? query = freezed,Object? category = freezed,Object? priceMin = freezed,Object? priceMax = freezed,Object? condition = freezed,Object? listingType = freezed,Object? bodyStyleId = freezed,Object? transmissionTypeId = freezed,Object? brakeSystemId = freezed,Object? valvetrainDesignId = freezed,Object? cabTypeId = freezed,Object? bedTypeId = freezed,Object? wheelbaseTypeId = freezed,Object? hasTurbo = freezed,Object? blindSpotMonitoringId = freezed,Object? blindSpotInterventionId = freezed,Object? chargerLevelId = freezed,Object? trailerTypeId = freezed,Object? make = freezed,Object? model = freezed,Object? yearFrom = freezed,Object? yearTo = freezed,Object? engine = freezed,Object? trim = freezed,Object? location = freezed,Object? sellerId = freezed,Object? verifiedSeller = freezed,Object? minRating = freezed,Object? minReviews = freezed,Object? popularParts = freezed,Object? inStock = freezed,Object? freeShipping = freezed,Object? maxShippingDays = freezed,Object? sortBy = null,Object? limit = null,Object? offset = null,}) {
  return _then(_self.copyWith(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,priceMin: freezed == priceMin ? _self.priceMin : priceMin // ignore: cast_nullable_to_non_nullable
as double?,priceMax: freezed == priceMax ? _self.priceMax : priceMax // ignore: cast_nullable_to_non_nullable
as double?,condition: freezed == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String?,listingType: freezed == listingType ? _self.listingType : listingType // ignore: cast_nullable_to_non_nullable
as String?,bodyStyleId: freezed == bodyStyleId ? _self.bodyStyleId : bodyStyleId // ignore: cast_nullable_to_non_nullable
as int?,transmissionTypeId: freezed == transmissionTypeId ? _self.transmissionTypeId : transmissionTypeId // ignore: cast_nullable_to_non_nullable
as int?,brakeSystemId: freezed == brakeSystemId ? _self.brakeSystemId : brakeSystemId // ignore: cast_nullable_to_non_nullable
as int?,valvetrainDesignId: freezed == valvetrainDesignId ? _self.valvetrainDesignId : valvetrainDesignId // ignore: cast_nullable_to_non_nullable
as int?,cabTypeId: freezed == cabTypeId ? _self.cabTypeId : cabTypeId // ignore: cast_nullable_to_non_nullable
as int?,bedTypeId: freezed == bedTypeId ? _self.bedTypeId : bedTypeId // ignore: cast_nullable_to_non_nullable
as int?,wheelbaseTypeId: freezed == wheelbaseTypeId ? _self.wheelbaseTypeId : wheelbaseTypeId // ignore: cast_nullable_to_non_nullable
as int?,hasTurbo: freezed == hasTurbo ? _self.hasTurbo : hasTurbo // ignore: cast_nullable_to_non_nullable
as bool?,blindSpotMonitoringId: freezed == blindSpotMonitoringId ? _self.blindSpotMonitoringId : blindSpotMonitoringId // ignore: cast_nullable_to_non_nullable
as int?,blindSpotInterventionId: freezed == blindSpotInterventionId ? _self.blindSpotInterventionId : blindSpotInterventionId // ignore: cast_nullable_to_non_nullable
as int?,chargerLevelId: freezed == chargerLevelId ? _self.chargerLevelId : chargerLevelId // ignore: cast_nullable_to_non_nullable
as int?,trailerTypeId: freezed == trailerTypeId ? _self.trailerTypeId : trailerTypeId // ignore: cast_nullable_to_non_nullable
as int?,make: freezed == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,verifiedSeller: freezed == verifiedSeller ? _self.verifiedSeller : verifiedSeller // ignore: cast_nullable_to_non_nullable
as bool?,minRating: freezed == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double?,minReviews: freezed == minReviews ? _self.minReviews : minReviews // ignore: cast_nullable_to_non_nullable
as int?,popularParts: freezed == popularParts ? _self.popularParts : popularParts // ignore: cast_nullable_to_non_nullable
as bool?,inStock: freezed == inStock ? _self.inStock : inStock // ignore: cast_nullable_to_non_nullable
as bool?,freeShipping: freezed == freeShipping ? _self.freeShipping : freeShipping // ignore: cast_nullable_to_non_nullable
as bool?,maxShippingDays: freezed == maxShippingDays ? _self.maxShippingDays : maxShippingDays // ignore: cast_nullable_to_non_nullable
as int?,sortBy: null == sortBy ? _self.sortBy : sortBy // ignore: cast_nullable_to_non_nullable
as String,limit: null == limit ? _self.limit : limit // ignore: cast_nullable_to_non_nullable
as int,offset: null == offset ? _self.offset : offset // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [AdvancedSearchFilters].
extension AdvancedSearchFiltersPatterns on AdvancedSearchFilters {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AdvancedSearchFilters value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AdvancedSearchFilters() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AdvancedSearchFilters value)  $default,){
final _that = this;
switch (_that) {
case _AdvancedSearchFilters():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AdvancedSearchFilters value)?  $default,){
final _that = this;
switch (_that) {
case _AdvancedSearchFilters() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? query,  String? category,  double? priceMin,  double? priceMax,  String? condition,  String? listingType, @JsonKey(name: 'body_style_id')  int? bodyStyleId, @JsonKey(name: 'transmission_type_id')  int? transmissionTypeId, @JsonKey(name: 'brake_system_id')  int? brakeSystemId, @JsonKey(name: 'valvetrain_design_id')  int? valvetrainDesignId, @JsonKey(name: 'cab_type_id')  int? cabTypeId, @JsonKey(name: 'bed_type_id')  int? bedTypeId, @JsonKey(name: 'wheelbase_type_id')  int? wheelbaseTypeId, @JsonKey(name: 'has_turbo')  bool? hasTurbo, @JsonKey(name: 'blind_spot_monitoring_id')  int? blindSpotMonitoringId, @JsonKey(name: 'blind_spot_intervention_id')  int? blindSpotInterventionId, @JsonKey(name: 'charger_level_id')  int? chargerLevelId, @JsonKey(name: 'trailer_type_id')  int? trailerTypeId,  String? make,  String? model,  int? yearFrom,  int? yearTo,  String? engine,  String? trim,  String? location,  String? sellerId,  bool? verifiedSeller,  double? minRating,  int? minReviews,  bool? popularParts,  bool? inStock,  bool? freeShipping,  int? maxShippingDays,  String sortBy,  int limit,  int offset)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AdvancedSearchFilters() when $default != null:
return $default(_that.query,_that.category,_that.priceMin,_that.priceMax,_that.condition,_that.listingType,_that.bodyStyleId,_that.transmissionTypeId,_that.brakeSystemId,_that.valvetrainDesignId,_that.cabTypeId,_that.bedTypeId,_that.wheelbaseTypeId,_that.hasTurbo,_that.blindSpotMonitoringId,_that.blindSpotInterventionId,_that.chargerLevelId,_that.trailerTypeId,_that.make,_that.model,_that.yearFrom,_that.yearTo,_that.engine,_that.trim,_that.location,_that.sellerId,_that.verifiedSeller,_that.minRating,_that.minReviews,_that.popularParts,_that.inStock,_that.freeShipping,_that.maxShippingDays,_that.sortBy,_that.limit,_that.offset);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? query,  String? category,  double? priceMin,  double? priceMax,  String? condition,  String? listingType, @JsonKey(name: 'body_style_id')  int? bodyStyleId, @JsonKey(name: 'transmission_type_id')  int? transmissionTypeId, @JsonKey(name: 'brake_system_id')  int? brakeSystemId, @JsonKey(name: 'valvetrain_design_id')  int? valvetrainDesignId, @JsonKey(name: 'cab_type_id')  int? cabTypeId, @JsonKey(name: 'bed_type_id')  int? bedTypeId, @JsonKey(name: 'wheelbase_type_id')  int? wheelbaseTypeId, @JsonKey(name: 'has_turbo')  bool? hasTurbo, @JsonKey(name: 'blind_spot_monitoring_id')  int? blindSpotMonitoringId, @JsonKey(name: 'blind_spot_intervention_id')  int? blindSpotInterventionId, @JsonKey(name: 'charger_level_id')  int? chargerLevelId, @JsonKey(name: 'trailer_type_id')  int? trailerTypeId,  String? make,  String? model,  int? yearFrom,  int? yearTo,  String? engine,  String? trim,  String? location,  String? sellerId,  bool? verifiedSeller,  double? minRating,  int? minReviews,  bool? popularParts,  bool? inStock,  bool? freeShipping,  int? maxShippingDays,  String sortBy,  int limit,  int offset)  $default,) {final _that = this;
switch (_that) {
case _AdvancedSearchFilters():
return $default(_that.query,_that.category,_that.priceMin,_that.priceMax,_that.condition,_that.listingType,_that.bodyStyleId,_that.transmissionTypeId,_that.brakeSystemId,_that.valvetrainDesignId,_that.cabTypeId,_that.bedTypeId,_that.wheelbaseTypeId,_that.hasTurbo,_that.blindSpotMonitoringId,_that.blindSpotInterventionId,_that.chargerLevelId,_that.trailerTypeId,_that.make,_that.model,_that.yearFrom,_that.yearTo,_that.engine,_that.trim,_that.location,_that.sellerId,_that.verifiedSeller,_that.minRating,_that.minReviews,_that.popularParts,_that.inStock,_that.freeShipping,_that.maxShippingDays,_that.sortBy,_that.limit,_that.offset);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? query,  String? category,  double? priceMin,  double? priceMax,  String? condition,  String? listingType, @JsonKey(name: 'body_style_id')  int? bodyStyleId, @JsonKey(name: 'transmission_type_id')  int? transmissionTypeId, @JsonKey(name: 'brake_system_id')  int? brakeSystemId, @JsonKey(name: 'valvetrain_design_id')  int? valvetrainDesignId, @JsonKey(name: 'cab_type_id')  int? cabTypeId, @JsonKey(name: 'bed_type_id')  int? bedTypeId, @JsonKey(name: 'wheelbase_type_id')  int? wheelbaseTypeId, @JsonKey(name: 'has_turbo')  bool? hasTurbo, @JsonKey(name: 'blind_spot_monitoring_id')  int? blindSpotMonitoringId, @JsonKey(name: 'blind_spot_intervention_id')  int? blindSpotInterventionId, @JsonKey(name: 'charger_level_id')  int? chargerLevelId, @JsonKey(name: 'trailer_type_id')  int? trailerTypeId,  String? make,  String? model,  int? yearFrom,  int? yearTo,  String? engine,  String? trim,  String? location,  String? sellerId,  bool? verifiedSeller,  double? minRating,  int? minReviews,  bool? popularParts,  bool? inStock,  bool? freeShipping,  int? maxShippingDays,  String sortBy,  int limit,  int offset)?  $default,) {final _that = this;
switch (_that) {
case _AdvancedSearchFilters() when $default != null:
return $default(_that.query,_that.category,_that.priceMin,_that.priceMax,_that.condition,_that.listingType,_that.bodyStyleId,_that.transmissionTypeId,_that.brakeSystemId,_that.valvetrainDesignId,_that.cabTypeId,_that.bedTypeId,_that.wheelbaseTypeId,_that.hasTurbo,_that.blindSpotMonitoringId,_that.blindSpotInterventionId,_that.chargerLevelId,_that.trailerTypeId,_that.make,_that.model,_that.yearFrom,_that.yearTo,_that.engine,_that.trim,_that.location,_that.sellerId,_that.verifiedSeller,_that.minRating,_that.minReviews,_that.popularParts,_that.inStock,_that.freeShipping,_that.maxShippingDays,_that.sortBy,_that.limit,_that.offset);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AdvancedSearchFilters implements AdvancedSearchFilters {
  const _AdvancedSearchFilters({this.query, this.category, this.priceMin, this.priceMax, this.condition, this.listingType, @JsonKey(name: 'body_style_id') this.bodyStyleId, @JsonKey(name: 'transmission_type_id') this.transmissionTypeId, @JsonKey(name: 'brake_system_id') this.brakeSystemId, @JsonKey(name: 'valvetrain_design_id') this.valvetrainDesignId, @JsonKey(name: 'cab_type_id') this.cabTypeId, @JsonKey(name: 'bed_type_id') this.bedTypeId, @JsonKey(name: 'wheelbase_type_id') this.wheelbaseTypeId, @JsonKey(name: 'has_turbo') this.hasTurbo, @JsonKey(name: 'blind_spot_monitoring_id') this.blindSpotMonitoringId, @JsonKey(name: 'blind_spot_intervention_id') this.blindSpotInterventionId, @JsonKey(name: 'charger_level_id') this.chargerLevelId, @JsonKey(name: 'trailer_type_id') this.trailerTypeId, this.make, this.model, this.yearFrom, this.yearTo, this.engine, this.trim, this.location, this.sellerId, this.verifiedSeller, this.minRating, this.minReviews, this.popularParts, this.inStock, this.freeShipping, this.maxShippingDays, this.sortBy = 'relevance', this.limit = 20, this.offset = 0});
  factory _AdvancedSearchFilters.fromJson(Map<String, dynamic> json) => _$AdvancedSearchFiltersFromJson(json);

// فلاتر أساسية
@override final  String? query;
@override final  String? category;
@override final  double? priceMin;
@override final  double? priceMax;
@override final  String? condition;
@override final  String? listingType;
// فلاتر المركبة - البيانات الأمريكية الجديدة
@override@JsonKey(name: 'body_style_id') final  int? bodyStyleId;
@override@JsonKey(name: 'transmission_type_id') final  int? transmissionTypeId;
@override@JsonKey(name: 'brake_system_id') final  int? brakeSystemId;
@override@JsonKey(name: 'valvetrain_design_id') final  int? valvetrainDesignId;
@override@JsonKey(name: 'cab_type_id') final  int? cabTypeId;
@override@JsonKey(name: 'bed_type_id') final  int? bedTypeId;
@override@JsonKey(name: 'wheelbase_type_id') final  int? wheelbaseTypeId;
@override@JsonKey(name: 'has_turbo') final  bool? hasTurbo;
// فلاتر ميزات الأمان
@override@JsonKey(name: 'blind_spot_monitoring_id') final  int? blindSpotMonitoringId;
@override@JsonKey(name: 'blind_spot_intervention_id') final  int? blindSpotInterventionId;
// فلاتر المركبات الكهربائية
@override@JsonKey(name: 'charger_level_id') final  int? chargerLevelId;
// فلاتر المقطورات
@override@JsonKey(name: 'trailer_type_id') final  int? trailerTypeId;
// فلاتر التوافق التقليدية
@override final  String? make;
@override final  String? model;
@override final  int? yearFrom;
@override final  int? yearTo;
@override final  String? engine;
@override final  String? trim;
// فلاتر الموقع والبائع
@override final  String? location;
@override final  String? sellerId;
@override final  bool? verifiedSeller;
// فلاتر التقييم والشعبية
@override final  double? minRating;
@override final  int? minReviews;
@override final  bool? popularParts;
// فلاتر التوفر والشحن
@override final  bool? inStock;
@override final  bool? freeShipping;
@override final  int? maxShippingDays;
// ترتيب النتائج
@override@JsonKey() final  String sortBy;
// relevance, price_asc, price_desc, newest, rating
@override@JsonKey() final  int limit;
@override@JsonKey() final  int offset;

/// Create a copy of AdvancedSearchFilters
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AdvancedSearchFiltersCopyWith<_AdvancedSearchFilters> get copyWith => __$AdvancedSearchFiltersCopyWithImpl<_AdvancedSearchFilters>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AdvancedSearchFiltersToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AdvancedSearchFilters&&(identical(other.query, query) || other.query == query)&&(identical(other.category, category) || other.category == category)&&(identical(other.priceMin, priceMin) || other.priceMin == priceMin)&&(identical(other.priceMax, priceMax) || other.priceMax == priceMax)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.listingType, listingType) || other.listingType == listingType)&&(identical(other.bodyStyleId, bodyStyleId) || other.bodyStyleId == bodyStyleId)&&(identical(other.transmissionTypeId, transmissionTypeId) || other.transmissionTypeId == transmissionTypeId)&&(identical(other.brakeSystemId, brakeSystemId) || other.brakeSystemId == brakeSystemId)&&(identical(other.valvetrainDesignId, valvetrainDesignId) || other.valvetrainDesignId == valvetrainDesignId)&&(identical(other.cabTypeId, cabTypeId) || other.cabTypeId == cabTypeId)&&(identical(other.bedTypeId, bedTypeId) || other.bedTypeId == bedTypeId)&&(identical(other.wheelbaseTypeId, wheelbaseTypeId) || other.wheelbaseTypeId == wheelbaseTypeId)&&(identical(other.hasTurbo, hasTurbo) || other.hasTurbo == hasTurbo)&&(identical(other.blindSpotMonitoringId, blindSpotMonitoringId) || other.blindSpotMonitoringId == blindSpotMonitoringId)&&(identical(other.blindSpotInterventionId, blindSpotInterventionId) || other.blindSpotInterventionId == blindSpotInterventionId)&&(identical(other.chargerLevelId, chargerLevelId) || other.chargerLevelId == chargerLevelId)&&(identical(other.trailerTypeId, trailerTypeId) || other.trailerTypeId == trailerTypeId)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.location, location) || other.location == location)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.verifiedSeller, verifiedSeller) || other.verifiedSeller == verifiedSeller)&&(identical(other.minRating, minRating) || other.minRating == minRating)&&(identical(other.minReviews, minReviews) || other.minReviews == minReviews)&&(identical(other.popularParts, popularParts) || other.popularParts == popularParts)&&(identical(other.inStock, inStock) || other.inStock == inStock)&&(identical(other.freeShipping, freeShipping) || other.freeShipping == freeShipping)&&(identical(other.maxShippingDays, maxShippingDays) || other.maxShippingDays == maxShippingDays)&&(identical(other.sortBy, sortBy) || other.sortBy == sortBy)&&(identical(other.limit, limit) || other.limit == limit)&&(identical(other.offset, offset) || other.offset == offset));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,query,category,priceMin,priceMax,condition,listingType,bodyStyleId,transmissionTypeId,brakeSystemId,valvetrainDesignId,cabTypeId,bedTypeId,wheelbaseTypeId,hasTurbo,blindSpotMonitoringId,blindSpotInterventionId,chargerLevelId,trailerTypeId,make,model,yearFrom,yearTo,engine,trim,location,sellerId,verifiedSeller,minRating,minReviews,popularParts,inStock,freeShipping,maxShippingDays,sortBy,limit,offset]);

@override
String toString() {
  return 'AdvancedSearchFilters(query: $query, category: $category, priceMin: $priceMin, priceMax: $priceMax, condition: $condition, listingType: $listingType, bodyStyleId: $bodyStyleId, transmissionTypeId: $transmissionTypeId, brakeSystemId: $brakeSystemId, valvetrainDesignId: $valvetrainDesignId, cabTypeId: $cabTypeId, bedTypeId: $bedTypeId, wheelbaseTypeId: $wheelbaseTypeId, hasTurbo: $hasTurbo, blindSpotMonitoringId: $blindSpotMonitoringId, blindSpotInterventionId: $blindSpotInterventionId, chargerLevelId: $chargerLevelId, trailerTypeId: $trailerTypeId, make: $make, model: $model, yearFrom: $yearFrom, yearTo: $yearTo, engine: $engine, trim: $trim, location: $location, sellerId: $sellerId, verifiedSeller: $verifiedSeller, minRating: $minRating, minReviews: $minReviews, popularParts: $popularParts, inStock: $inStock, freeShipping: $freeShipping, maxShippingDays: $maxShippingDays, sortBy: $sortBy, limit: $limit, offset: $offset)';
}


}

/// @nodoc
abstract mixin class _$AdvancedSearchFiltersCopyWith<$Res> implements $AdvancedSearchFiltersCopyWith<$Res> {
  factory _$AdvancedSearchFiltersCopyWith(_AdvancedSearchFilters value, $Res Function(_AdvancedSearchFilters) _then) = __$AdvancedSearchFiltersCopyWithImpl;
@override @useResult
$Res call({
 String? query, String? category, double? priceMin, double? priceMax, String? condition, String? listingType,@JsonKey(name: 'body_style_id') int? bodyStyleId,@JsonKey(name: 'transmission_type_id') int? transmissionTypeId,@JsonKey(name: 'brake_system_id') int? brakeSystemId,@JsonKey(name: 'valvetrain_design_id') int? valvetrainDesignId,@JsonKey(name: 'cab_type_id') int? cabTypeId,@JsonKey(name: 'bed_type_id') int? bedTypeId,@JsonKey(name: 'wheelbase_type_id') int? wheelbaseTypeId,@JsonKey(name: 'has_turbo') bool? hasTurbo,@JsonKey(name: 'blind_spot_monitoring_id') int? blindSpotMonitoringId,@JsonKey(name: 'blind_spot_intervention_id') int? blindSpotInterventionId,@JsonKey(name: 'charger_level_id') int? chargerLevelId,@JsonKey(name: 'trailer_type_id') int? trailerTypeId, String? make, String? model, int? yearFrom, int? yearTo, String? engine, String? trim, String? location, String? sellerId, bool? verifiedSeller, double? minRating, int? minReviews, bool? popularParts, bool? inStock, bool? freeShipping, int? maxShippingDays, String sortBy, int limit, int offset
});




}
/// @nodoc
class __$AdvancedSearchFiltersCopyWithImpl<$Res>
    implements _$AdvancedSearchFiltersCopyWith<$Res> {
  __$AdvancedSearchFiltersCopyWithImpl(this._self, this._then);

  final _AdvancedSearchFilters _self;
  final $Res Function(_AdvancedSearchFilters) _then;

/// Create a copy of AdvancedSearchFilters
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? query = freezed,Object? category = freezed,Object? priceMin = freezed,Object? priceMax = freezed,Object? condition = freezed,Object? listingType = freezed,Object? bodyStyleId = freezed,Object? transmissionTypeId = freezed,Object? brakeSystemId = freezed,Object? valvetrainDesignId = freezed,Object? cabTypeId = freezed,Object? bedTypeId = freezed,Object? wheelbaseTypeId = freezed,Object? hasTurbo = freezed,Object? blindSpotMonitoringId = freezed,Object? blindSpotInterventionId = freezed,Object? chargerLevelId = freezed,Object? trailerTypeId = freezed,Object? make = freezed,Object? model = freezed,Object? yearFrom = freezed,Object? yearTo = freezed,Object? engine = freezed,Object? trim = freezed,Object? location = freezed,Object? sellerId = freezed,Object? verifiedSeller = freezed,Object? minRating = freezed,Object? minReviews = freezed,Object? popularParts = freezed,Object? inStock = freezed,Object? freeShipping = freezed,Object? maxShippingDays = freezed,Object? sortBy = null,Object? limit = null,Object? offset = null,}) {
  return _then(_AdvancedSearchFilters(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,priceMin: freezed == priceMin ? _self.priceMin : priceMin // ignore: cast_nullable_to_non_nullable
as double?,priceMax: freezed == priceMax ? _self.priceMax : priceMax // ignore: cast_nullable_to_non_nullable
as double?,condition: freezed == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String?,listingType: freezed == listingType ? _self.listingType : listingType // ignore: cast_nullable_to_non_nullable
as String?,bodyStyleId: freezed == bodyStyleId ? _self.bodyStyleId : bodyStyleId // ignore: cast_nullable_to_non_nullable
as int?,transmissionTypeId: freezed == transmissionTypeId ? _self.transmissionTypeId : transmissionTypeId // ignore: cast_nullable_to_non_nullable
as int?,brakeSystemId: freezed == brakeSystemId ? _self.brakeSystemId : brakeSystemId // ignore: cast_nullable_to_non_nullable
as int?,valvetrainDesignId: freezed == valvetrainDesignId ? _self.valvetrainDesignId : valvetrainDesignId // ignore: cast_nullable_to_non_nullable
as int?,cabTypeId: freezed == cabTypeId ? _self.cabTypeId : cabTypeId // ignore: cast_nullable_to_non_nullable
as int?,bedTypeId: freezed == bedTypeId ? _self.bedTypeId : bedTypeId // ignore: cast_nullable_to_non_nullable
as int?,wheelbaseTypeId: freezed == wheelbaseTypeId ? _self.wheelbaseTypeId : wheelbaseTypeId // ignore: cast_nullable_to_non_nullable
as int?,hasTurbo: freezed == hasTurbo ? _self.hasTurbo : hasTurbo // ignore: cast_nullable_to_non_nullable
as bool?,blindSpotMonitoringId: freezed == blindSpotMonitoringId ? _self.blindSpotMonitoringId : blindSpotMonitoringId // ignore: cast_nullable_to_non_nullable
as int?,blindSpotInterventionId: freezed == blindSpotInterventionId ? _self.blindSpotInterventionId : blindSpotInterventionId // ignore: cast_nullable_to_non_nullable
as int?,chargerLevelId: freezed == chargerLevelId ? _self.chargerLevelId : chargerLevelId // ignore: cast_nullable_to_non_nullable
as int?,trailerTypeId: freezed == trailerTypeId ? _self.trailerTypeId : trailerTypeId // ignore: cast_nullable_to_non_nullable
as int?,make: freezed == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,verifiedSeller: freezed == verifiedSeller ? _self.verifiedSeller : verifiedSeller // ignore: cast_nullable_to_non_nullable
as bool?,minRating: freezed == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double?,minReviews: freezed == minReviews ? _self.minReviews : minReviews // ignore: cast_nullable_to_non_nullable
as int?,popularParts: freezed == popularParts ? _self.popularParts : popularParts // ignore: cast_nullable_to_non_nullable
as bool?,inStock: freezed == inStock ? _self.inStock : inStock // ignore: cast_nullable_to_non_nullable
as bool?,freeShipping: freezed == freeShipping ? _self.freeShipping : freeShipping // ignore: cast_nullable_to_non_nullable
as bool?,maxShippingDays: freezed == maxShippingDays ? _self.maxShippingDays : maxShippingDays // ignore: cast_nullable_to_non_nullable
as int?,sortBy: null == sortBy ? _self.sortBy : sortBy // ignore: cast_nullable_to_non_nullable
as String,limit: null == limit ? _self.limit : limit // ignore: cast_nullable_to_non_nullable
as int,offset: null == offset ? _self.offset : offset // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
