// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'smart_suggestion.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SmartSuggestion {

 String get title; String get description; AdvancedSearchFilters get filters; int get estimatedResults; double get confidence;// مستوى الثقة في الاقتراح
 String? get icon; String? get category;
/// Create a copy of SmartSuggestion
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SmartSuggestionCopyWith<SmartSuggestion> get copyWith => _$SmartSuggestionCopyWithImpl<SmartSuggestion>(this as SmartSuggestion, _$identity);

  /// Serializes this SmartSuggestion to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SmartSuggestion&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.filters, filters) || other.filters == filters)&&(identical(other.estimatedResults, estimatedResults) || other.estimatedResults == estimatedResults)&&(identical(other.confidence, confidence) || other.confidence == confidence)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.category, category) || other.category == category));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,description,filters,estimatedResults,confidence,icon,category);

@override
String toString() {
  return 'SmartSuggestion(title: $title, description: $description, filters: $filters, estimatedResults: $estimatedResults, confidence: $confidence, icon: $icon, category: $category)';
}


}

/// @nodoc
abstract mixin class $SmartSuggestionCopyWith<$Res>  {
  factory $SmartSuggestionCopyWith(SmartSuggestion value, $Res Function(SmartSuggestion) _then) = _$SmartSuggestionCopyWithImpl;
@useResult
$Res call({
 String title, String description, AdvancedSearchFilters filters, int estimatedResults, double confidence, String? icon, String? category
});


$AdvancedSearchFiltersCopyWith<$Res> get filters;

}
/// @nodoc
class _$SmartSuggestionCopyWithImpl<$Res>
    implements $SmartSuggestionCopyWith<$Res> {
  _$SmartSuggestionCopyWithImpl(this._self, this._then);

  final SmartSuggestion _self;
  final $Res Function(SmartSuggestion) _then;

/// Create a copy of SmartSuggestion
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? title = null,Object? description = null,Object? filters = null,Object? estimatedResults = null,Object? confidence = null,Object? icon = freezed,Object? category = freezed,}) {
  return _then(_self.copyWith(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,filters: null == filters ? _self.filters : filters // ignore: cast_nullable_to_non_nullable
as AdvancedSearchFilters,estimatedResults: null == estimatedResults ? _self.estimatedResults : estimatedResults // ignore: cast_nullable_to_non_nullable
as int,confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,icon: freezed == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String?,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of SmartSuggestion
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AdvancedSearchFiltersCopyWith<$Res> get filters {
  
  return $AdvancedSearchFiltersCopyWith<$Res>(_self.filters, (value) {
    return _then(_self.copyWith(filters: value));
  });
}
}


/// Adds pattern-matching-related methods to [SmartSuggestion].
extension SmartSuggestionPatterns on SmartSuggestion {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SmartSuggestion value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SmartSuggestion() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SmartSuggestion value)  $default,){
final _that = this;
switch (_that) {
case _SmartSuggestion():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SmartSuggestion value)?  $default,){
final _that = this;
switch (_that) {
case _SmartSuggestion() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String title,  String description,  AdvancedSearchFilters filters,  int estimatedResults,  double confidence,  String? icon,  String? category)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SmartSuggestion() when $default != null:
return $default(_that.title,_that.description,_that.filters,_that.estimatedResults,_that.confidence,_that.icon,_that.category);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String title,  String description,  AdvancedSearchFilters filters,  int estimatedResults,  double confidence,  String? icon,  String? category)  $default,) {final _that = this;
switch (_that) {
case _SmartSuggestion():
return $default(_that.title,_that.description,_that.filters,_that.estimatedResults,_that.confidence,_that.icon,_that.category);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String title,  String description,  AdvancedSearchFilters filters,  int estimatedResults,  double confidence,  String? icon,  String? category)?  $default,) {final _that = this;
switch (_that) {
case _SmartSuggestion() when $default != null:
return $default(_that.title,_that.description,_that.filters,_that.estimatedResults,_that.confidence,_that.icon,_that.category);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SmartSuggestion implements SmartSuggestion {
  const _SmartSuggestion({required this.title, required this.description, required this.filters, this.estimatedResults = 0, this.confidence = 0, this.icon, this.category});
  factory _SmartSuggestion.fromJson(Map<String, dynamic> json) => _$SmartSuggestionFromJson(json);

@override final  String title;
@override final  String description;
@override final  AdvancedSearchFilters filters;
@override@JsonKey() final  int estimatedResults;
@override@JsonKey() final  double confidence;
// مستوى الثقة في الاقتراح
@override final  String? icon;
@override final  String? category;

/// Create a copy of SmartSuggestion
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SmartSuggestionCopyWith<_SmartSuggestion> get copyWith => __$SmartSuggestionCopyWithImpl<_SmartSuggestion>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SmartSuggestionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SmartSuggestion&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.filters, filters) || other.filters == filters)&&(identical(other.estimatedResults, estimatedResults) || other.estimatedResults == estimatedResults)&&(identical(other.confidence, confidence) || other.confidence == confidence)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.category, category) || other.category == category));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,description,filters,estimatedResults,confidence,icon,category);

@override
String toString() {
  return 'SmartSuggestion(title: $title, description: $description, filters: $filters, estimatedResults: $estimatedResults, confidence: $confidence, icon: $icon, category: $category)';
}


}

/// @nodoc
abstract mixin class _$SmartSuggestionCopyWith<$Res> implements $SmartSuggestionCopyWith<$Res> {
  factory _$SmartSuggestionCopyWith(_SmartSuggestion value, $Res Function(_SmartSuggestion) _then) = __$SmartSuggestionCopyWithImpl;
@override @useResult
$Res call({
 String title, String description, AdvancedSearchFilters filters, int estimatedResults, double confidence, String? icon, String? category
});


@override $AdvancedSearchFiltersCopyWith<$Res> get filters;

}
/// @nodoc
class __$SmartSuggestionCopyWithImpl<$Res>
    implements _$SmartSuggestionCopyWith<$Res> {
  __$SmartSuggestionCopyWithImpl(this._self, this._then);

  final _SmartSuggestion _self;
  final $Res Function(_SmartSuggestion) _then;

/// Create a copy of SmartSuggestion
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? title = null,Object? description = null,Object? filters = null,Object? estimatedResults = null,Object? confidence = null,Object? icon = freezed,Object? category = freezed,}) {
  return _then(_SmartSuggestion(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,filters: null == filters ? _self.filters : filters // ignore: cast_nullable_to_non_nullable
as AdvancedSearchFilters,estimatedResults: null == estimatedResults ? _self.estimatedResults : estimatedResults // ignore: cast_nullable_to_non_nullable
as int,confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,icon: freezed == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String?,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of SmartSuggestion
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AdvancedSearchFiltersCopyWith<$Res> get filters {
  
  return $AdvancedSearchFiltersCopyWith<$Res>(_self.filters, (value) {
    return _then(_self.copyWith(filters: value));
  });
}
}

// dart format on
