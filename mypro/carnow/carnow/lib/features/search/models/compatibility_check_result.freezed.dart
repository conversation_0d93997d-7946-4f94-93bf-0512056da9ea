// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'compatibility_check_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CompatibilityCheckResult {

 bool get isCompatible; double get confidenceScore;// 0 - 1.0
 List<String> get matchingSpecs; List<String> get conflictingSpecs; List<String>? get warnings; List<String>? get recommendations; String? get alternativePart;
/// Create a copy of CompatibilityCheckResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CompatibilityCheckResultCopyWith<CompatibilityCheckResult> get copyWith => _$CompatibilityCheckResultCopyWithImpl<CompatibilityCheckResult>(this as CompatibilityCheckResult, _$identity);

  /// Serializes this CompatibilityCheckResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CompatibilityCheckResult&&(identical(other.isCompatible, isCompatible) || other.isCompatible == isCompatible)&&(identical(other.confidenceScore, confidenceScore) || other.confidenceScore == confidenceScore)&&const DeepCollectionEquality().equals(other.matchingSpecs, matchingSpecs)&&const DeepCollectionEquality().equals(other.conflictingSpecs, conflictingSpecs)&&const DeepCollectionEquality().equals(other.warnings, warnings)&&const DeepCollectionEquality().equals(other.recommendations, recommendations)&&(identical(other.alternativePart, alternativePart) || other.alternativePart == alternativePart));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isCompatible,confidenceScore,const DeepCollectionEquality().hash(matchingSpecs),const DeepCollectionEquality().hash(conflictingSpecs),const DeepCollectionEquality().hash(warnings),const DeepCollectionEquality().hash(recommendations),alternativePart);

@override
String toString() {
  return 'CompatibilityCheckResult(isCompatible: $isCompatible, confidenceScore: $confidenceScore, matchingSpecs: $matchingSpecs, conflictingSpecs: $conflictingSpecs, warnings: $warnings, recommendations: $recommendations, alternativePart: $alternativePart)';
}


}

/// @nodoc
abstract mixin class $CompatibilityCheckResultCopyWith<$Res>  {
  factory $CompatibilityCheckResultCopyWith(CompatibilityCheckResult value, $Res Function(CompatibilityCheckResult) _then) = _$CompatibilityCheckResultCopyWithImpl;
@useResult
$Res call({
 bool isCompatible, double confidenceScore, List<String> matchingSpecs, List<String> conflictingSpecs, List<String>? warnings, List<String>? recommendations, String? alternativePart
});




}
/// @nodoc
class _$CompatibilityCheckResultCopyWithImpl<$Res>
    implements $CompatibilityCheckResultCopyWith<$Res> {
  _$CompatibilityCheckResultCopyWithImpl(this._self, this._then);

  final CompatibilityCheckResult _self;
  final $Res Function(CompatibilityCheckResult) _then;

/// Create a copy of CompatibilityCheckResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isCompatible = null,Object? confidenceScore = null,Object? matchingSpecs = null,Object? conflictingSpecs = null,Object? warnings = freezed,Object? recommendations = freezed,Object? alternativePart = freezed,}) {
  return _then(_self.copyWith(
isCompatible: null == isCompatible ? _self.isCompatible : isCompatible // ignore: cast_nullable_to_non_nullable
as bool,confidenceScore: null == confidenceScore ? _self.confidenceScore : confidenceScore // ignore: cast_nullable_to_non_nullable
as double,matchingSpecs: null == matchingSpecs ? _self.matchingSpecs : matchingSpecs // ignore: cast_nullable_to_non_nullable
as List<String>,conflictingSpecs: null == conflictingSpecs ? _self.conflictingSpecs : conflictingSpecs // ignore: cast_nullable_to_non_nullable
as List<String>,warnings: freezed == warnings ? _self.warnings : warnings // ignore: cast_nullable_to_non_nullable
as List<String>?,recommendations: freezed == recommendations ? _self.recommendations : recommendations // ignore: cast_nullable_to_non_nullable
as List<String>?,alternativePart: freezed == alternativePart ? _self.alternativePart : alternativePart // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CompatibilityCheckResult].
extension CompatibilityCheckResultPatterns on CompatibilityCheckResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CompatibilityCheckResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CompatibilityCheckResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CompatibilityCheckResult value)  $default,){
final _that = this;
switch (_that) {
case _CompatibilityCheckResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CompatibilityCheckResult value)?  $default,){
final _that = this;
switch (_that) {
case _CompatibilityCheckResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isCompatible,  double confidenceScore,  List<String> matchingSpecs,  List<String> conflictingSpecs,  List<String>? warnings,  List<String>? recommendations,  String? alternativePart)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CompatibilityCheckResult() when $default != null:
return $default(_that.isCompatible,_that.confidenceScore,_that.matchingSpecs,_that.conflictingSpecs,_that.warnings,_that.recommendations,_that.alternativePart);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isCompatible,  double confidenceScore,  List<String> matchingSpecs,  List<String> conflictingSpecs,  List<String>? warnings,  List<String>? recommendations,  String? alternativePart)  $default,) {final _that = this;
switch (_that) {
case _CompatibilityCheckResult():
return $default(_that.isCompatible,_that.confidenceScore,_that.matchingSpecs,_that.conflictingSpecs,_that.warnings,_that.recommendations,_that.alternativePart);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isCompatible,  double confidenceScore,  List<String> matchingSpecs,  List<String> conflictingSpecs,  List<String>? warnings,  List<String>? recommendations,  String? alternativePart)?  $default,) {final _that = this;
switch (_that) {
case _CompatibilityCheckResult() when $default != null:
return $default(_that.isCompatible,_that.confidenceScore,_that.matchingSpecs,_that.conflictingSpecs,_that.warnings,_that.recommendations,_that.alternativePart);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CompatibilityCheckResult implements CompatibilityCheckResult {
  const _CompatibilityCheckResult({required this.isCompatible, required this.confidenceScore, required final  List<String> matchingSpecs, required final  List<String> conflictingSpecs, final  List<String>? warnings, final  List<String>? recommendations, this.alternativePart}): _matchingSpecs = matchingSpecs,_conflictingSpecs = conflictingSpecs,_warnings = warnings,_recommendations = recommendations;
  factory _CompatibilityCheckResult.fromJson(Map<String, dynamic> json) => _$CompatibilityCheckResultFromJson(json);

@override final  bool isCompatible;
@override final  double confidenceScore;
// 0 - 1.0
 final  List<String> _matchingSpecs;
// 0 - 1.0
@override List<String> get matchingSpecs {
  if (_matchingSpecs is EqualUnmodifiableListView) return _matchingSpecs;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_matchingSpecs);
}

 final  List<String> _conflictingSpecs;
@override List<String> get conflictingSpecs {
  if (_conflictingSpecs is EqualUnmodifiableListView) return _conflictingSpecs;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_conflictingSpecs);
}

 final  List<String>? _warnings;
@override List<String>? get warnings {
  final value = _warnings;
  if (value == null) return null;
  if (_warnings is EqualUnmodifiableListView) return _warnings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _recommendations;
@override List<String>? get recommendations {
  final value = _recommendations;
  if (value == null) return null;
  if (_recommendations is EqualUnmodifiableListView) return _recommendations;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? alternativePart;

/// Create a copy of CompatibilityCheckResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CompatibilityCheckResultCopyWith<_CompatibilityCheckResult> get copyWith => __$CompatibilityCheckResultCopyWithImpl<_CompatibilityCheckResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CompatibilityCheckResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CompatibilityCheckResult&&(identical(other.isCompatible, isCompatible) || other.isCompatible == isCompatible)&&(identical(other.confidenceScore, confidenceScore) || other.confidenceScore == confidenceScore)&&const DeepCollectionEquality().equals(other._matchingSpecs, _matchingSpecs)&&const DeepCollectionEquality().equals(other._conflictingSpecs, _conflictingSpecs)&&const DeepCollectionEquality().equals(other._warnings, _warnings)&&const DeepCollectionEquality().equals(other._recommendations, _recommendations)&&(identical(other.alternativePart, alternativePart) || other.alternativePart == alternativePart));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isCompatible,confidenceScore,const DeepCollectionEquality().hash(_matchingSpecs),const DeepCollectionEquality().hash(_conflictingSpecs),const DeepCollectionEquality().hash(_warnings),const DeepCollectionEquality().hash(_recommendations),alternativePart);

@override
String toString() {
  return 'CompatibilityCheckResult(isCompatible: $isCompatible, confidenceScore: $confidenceScore, matchingSpecs: $matchingSpecs, conflictingSpecs: $conflictingSpecs, warnings: $warnings, recommendations: $recommendations, alternativePart: $alternativePart)';
}


}

/// @nodoc
abstract mixin class _$CompatibilityCheckResultCopyWith<$Res> implements $CompatibilityCheckResultCopyWith<$Res> {
  factory _$CompatibilityCheckResultCopyWith(_CompatibilityCheckResult value, $Res Function(_CompatibilityCheckResult) _then) = __$CompatibilityCheckResultCopyWithImpl;
@override @useResult
$Res call({
 bool isCompatible, double confidenceScore, List<String> matchingSpecs, List<String> conflictingSpecs, List<String>? warnings, List<String>? recommendations, String? alternativePart
});




}
/// @nodoc
class __$CompatibilityCheckResultCopyWithImpl<$Res>
    implements _$CompatibilityCheckResultCopyWith<$Res> {
  __$CompatibilityCheckResultCopyWithImpl(this._self, this._then);

  final _CompatibilityCheckResult _self;
  final $Res Function(_CompatibilityCheckResult) _then;

/// Create a copy of CompatibilityCheckResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isCompatible = null,Object? confidenceScore = null,Object? matchingSpecs = null,Object? conflictingSpecs = null,Object? warnings = freezed,Object? recommendations = freezed,Object? alternativePart = freezed,}) {
  return _then(_CompatibilityCheckResult(
isCompatible: null == isCompatible ? _self.isCompatible : isCompatible // ignore: cast_nullable_to_non_nullable
as bool,confidenceScore: null == confidenceScore ? _self.confidenceScore : confidenceScore // ignore: cast_nullable_to_non_nullable
as double,matchingSpecs: null == matchingSpecs ? _self._matchingSpecs : matchingSpecs // ignore: cast_nullable_to_non_nullable
as List<String>,conflictingSpecs: null == conflictingSpecs ? _self._conflictingSpecs : conflictingSpecs // ignore: cast_nullable_to_non_nullable
as List<String>,warnings: freezed == warnings ? _self._warnings : warnings // ignore: cast_nullable_to_non_nullable
as List<String>?,recommendations: freezed == recommendations ? _self._recommendations : recommendations // ignore: cast_nullable_to_non_nullable
as List<String>?,alternativePart: freezed == alternativePart ? _self.alternativePart : alternativePart // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
