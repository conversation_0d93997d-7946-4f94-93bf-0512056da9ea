// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'filter_options_group.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FilterOptionsGroup {

 String get title; String get key; List<FilterOption> get options; bool get multiSelect; bool get isExpanded; String? get description;
/// Create a copy of FilterOptionsGroup
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FilterOptionsGroupCopyWith<FilterOptionsGroup> get copyWith => _$FilterOptionsGroupCopyWithImpl<FilterOptionsGroup>(this as FilterOptionsGroup, _$identity);

  /// Serializes this FilterOptionsGroup to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FilterOptionsGroup&&(identical(other.title, title) || other.title == title)&&(identical(other.key, key) || other.key == key)&&const DeepCollectionEquality().equals(other.options, options)&&(identical(other.multiSelect, multiSelect) || other.multiSelect == multiSelect)&&(identical(other.isExpanded, isExpanded) || other.isExpanded == isExpanded)&&(identical(other.description, description) || other.description == description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,key,const DeepCollectionEquality().hash(options),multiSelect,isExpanded,description);

@override
String toString() {
  return 'FilterOptionsGroup(title: $title, key: $key, options: $options, multiSelect: $multiSelect, isExpanded: $isExpanded, description: $description)';
}


}

/// @nodoc
abstract mixin class $FilterOptionsGroupCopyWith<$Res>  {
  factory $FilterOptionsGroupCopyWith(FilterOptionsGroup value, $Res Function(FilterOptionsGroup) _then) = _$FilterOptionsGroupCopyWithImpl;
@useResult
$Res call({
 String title, String key, List<FilterOption> options, bool multiSelect, bool isExpanded, String? description
});




}
/// @nodoc
class _$FilterOptionsGroupCopyWithImpl<$Res>
    implements $FilterOptionsGroupCopyWith<$Res> {
  _$FilterOptionsGroupCopyWithImpl(this._self, this._then);

  final FilterOptionsGroup _self;
  final $Res Function(FilterOptionsGroup) _then;

/// Create a copy of FilterOptionsGroup
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? title = null,Object? key = null,Object? options = null,Object? multiSelect = null,Object? isExpanded = null,Object? description = freezed,}) {
  return _then(_self.copyWith(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,key: null == key ? _self.key : key // ignore: cast_nullable_to_non_nullable
as String,options: null == options ? _self.options : options // ignore: cast_nullable_to_non_nullable
as List<FilterOption>,multiSelect: null == multiSelect ? _self.multiSelect : multiSelect // ignore: cast_nullable_to_non_nullable
as bool,isExpanded: null == isExpanded ? _self.isExpanded : isExpanded // ignore: cast_nullable_to_non_nullable
as bool,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [FilterOptionsGroup].
extension FilterOptionsGroupPatterns on FilterOptionsGroup {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FilterOptionsGroup value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FilterOptionsGroup() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FilterOptionsGroup value)  $default,){
final _that = this;
switch (_that) {
case _FilterOptionsGroup():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FilterOptionsGroup value)?  $default,){
final _that = this;
switch (_that) {
case _FilterOptionsGroup() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String title,  String key,  List<FilterOption> options,  bool multiSelect,  bool isExpanded,  String? description)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FilterOptionsGroup() when $default != null:
return $default(_that.title,_that.key,_that.options,_that.multiSelect,_that.isExpanded,_that.description);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String title,  String key,  List<FilterOption> options,  bool multiSelect,  bool isExpanded,  String? description)  $default,) {final _that = this;
switch (_that) {
case _FilterOptionsGroup():
return $default(_that.title,_that.key,_that.options,_that.multiSelect,_that.isExpanded,_that.description);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String title,  String key,  List<FilterOption> options,  bool multiSelect,  bool isExpanded,  String? description)?  $default,) {final _that = this;
switch (_that) {
case _FilterOptionsGroup() when $default != null:
return $default(_that.title,_that.key,_that.options,_that.multiSelect,_that.isExpanded,_that.description);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FilterOptionsGroup implements FilterOptionsGroup {
  const _FilterOptionsGroup({required this.title, required this.key, required final  List<FilterOption> options, this.multiSelect = false, this.isExpanded = false, this.description}): _options = options;
  factory _FilterOptionsGroup.fromJson(Map<String, dynamic> json) => _$FilterOptionsGroupFromJson(json);

@override final  String title;
@override final  String key;
 final  List<FilterOption> _options;
@override List<FilterOption> get options {
  if (_options is EqualUnmodifiableListView) return _options;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_options);
}

@override@JsonKey() final  bool multiSelect;
@override@JsonKey() final  bool isExpanded;
@override final  String? description;

/// Create a copy of FilterOptionsGroup
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FilterOptionsGroupCopyWith<_FilterOptionsGroup> get copyWith => __$FilterOptionsGroupCopyWithImpl<_FilterOptionsGroup>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FilterOptionsGroupToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FilterOptionsGroup&&(identical(other.title, title) || other.title == title)&&(identical(other.key, key) || other.key == key)&&const DeepCollectionEquality().equals(other._options, _options)&&(identical(other.multiSelect, multiSelect) || other.multiSelect == multiSelect)&&(identical(other.isExpanded, isExpanded) || other.isExpanded == isExpanded)&&(identical(other.description, description) || other.description == description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,key,const DeepCollectionEquality().hash(_options),multiSelect,isExpanded,description);

@override
String toString() {
  return 'FilterOptionsGroup(title: $title, key: $key, options: $options, multiSelect: $multiSelect, isExpanded: $isExpanded, description: $description)';
}


}

/// @nodoc
abstract mixin class _$FilterOptionsGroupCopyWith<$Res> implements $FilterOptionsGroupCopyWith<$Res> {
  factory _$FilterOptionsGroupCopyWith(_FilterOptionsGroup value, $Res Function(_FilterOptionsGroup) _then) = __$FilterOptionsGroupCopyWithImpl;
@override @useResult
$Res call({
 String title, String key, List<FilterOption> options, bool multiSelect, bool isExpanded, String? description
});




}
/// @nodoc
class __$FilterOptionsGroupCopyWithImpl<$Res>
    implements _$FilterOptionsGroupCopyWith<$Res> {
  __$FilterOptionsGroupCopyWithImpl(this._self, this._then);

  final _FilterOptionsGroup _self;
  final $Res Function(_FilterOptionsGroup) _then;

/// Create a copy of FilterOptionsGroup
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? title = null,Object? key = null,Object? options = null,Object? multiSelect = null,Object? isExpanded = null,Object? description = freezed,}) {
  return _then(_FilterOptionsGroup(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,key: null == key ? _self.key : key // ignore: cast_nullable_to_non_nullable
as String,options: null == options ? _self._options : options // ignore: cast_nullable_to_non_nullable
as List<FilterOption>,multiSelect: null == multiSelect ? _self.multiSelect : multiSelect // ignore: cast_nullable_to_non_nullable
as bool,isExpanded: null == isExpanded ? _self.isExpanded : isExpanded // ignore: cast_nullable_to_non_nullable
as bool,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
