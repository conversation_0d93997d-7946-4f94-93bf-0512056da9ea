// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'compatibility_check_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CompatibilityCheckResult _$CompatibilityCheckResultFromJson(
  Map<String, dynamic> json,
) => _CompatibilityCheckResult(
  isCompatible: json['isCompatible'] as bool,
  confidenceScore: (json['confidenceScore'] as num).toDouble(),
  matchingSpecs: (json['matchingSpecs'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  conflictingSpecs: (json['conflictingSpecs'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  warnings: (json['warnings'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  recommendations: (json['recommendations'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  alternativePart: json['alternativePart'] as String?,
);

Map<String, dynamic> _$CompatibilityCheckResultToJson(
  _CompatibilityCheckResult instance,
) => <String, dynamic>{
  'isCompatible': instance.isCompatible,
  'confidenceScore': instance.confidenceScore,
  'matchingSpecs': instance.matchingSpecs,
  'conflictingSpecs': instance.conflictingSpecs,
  'warnings': instance.warnings,
  'recommendations': instance.recommendations,
  'alternativePart': instance.alternativePart,
};
