// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchResultsHash() => r'3f7c076b1b7340c65f600bbbfdc2c7ddf3794e33';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود نتائج البحث استنادًا إلى استعلام
///
/// Copied from [searchResults].
@ProviderFor(searchResults)
const searchResultsProvider = SearchResultsFamily();

/// مزود نتائج البحث استنادًا إلى استعلام
///
/// Copied from [searchResults].
class SearchResultsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// مزود نتائج البحث استنادًا إلى استعلام
  ///
  /// Copied from [searchResults].
  const SearchResultsFamily();

  /// مزود نتائج البحث استنادًا إلى استعلام
  ///
  /// Copied from [searchResults].
  SearchResultsProvider call(String query) {
    return SearchResultsProvider(query);
  }

  @override
  SearchResultsProvider getProviderOverride(
    covariant SearchResultsProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchResultsProvider';
}

/// مزود نتائج البحث استنادًا إلى استعلام
///
/// Copied from [searchResults].
class SearchResultsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// مزود نتائج البحث استنادًا إلى استعلام
  ///
  /// Copied from [searchResults].
  SearchResultsProvider(String query)
    : this._internal(
        (ref) => searchResults(ref as SearchResultsRef, query),
        from: searchResultsProvider,
        name: r'searchResultsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$searchResultsHash,
        dependencies: SearchResultsFamily._dependencies,
        allTransitiveDependencies:
            SearchResultsFamily._allTransitiveDependencies,
        query: query,
      );

  SearchResultsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(SearchResultsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchResultsProvider._internal(
        (ref) => create(ref as SearchResultsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _SearchResultsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchResultsProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchResultsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _SearchResultsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with SearchResultsRef {
  _SearchResultsProviderElement(super.provider);

  @override
  String get query => (origin as SearchResultsProvider).query;
}

String _$vehicleSearchHash() => r'75c47db1b47c4429117e2c0133263df30e65e389';

/// مزود البحث في المركبات
///
/// Copied from [vehicleSearch].
@ProviderFor(vehicleSearch)
const vehicleSearchProvider = VehicleSearchFamily();

/// مزود البحث في المركبات
///
/// Copied from [vehicleSearch].
class VehicleSearchFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// مزود البحث في المركبات
  ///
  /// Copied from [vehicleSearch].
  const VehicleSearchFamily();

  /// مزود البحث في المركبات
  ///
  /// Copied from [vehicleSearch].
  VehicleSearchProvider call(String query) {
    return VehicleSearchProvider(query);
  }

  @override
  VehicleSearchProvider getProviderOverride(
    covariant VehicleSearchProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleSearchProvider';
}

/// مزود البحث في المركبات
///
/// Copied from [vehicleSearch].
class VehicleSearchProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// مزود البحث في المركبات
  ///
  /// Copied from [vehicleSearch].
  VehicleSearchProvider(String query)
    : this._internal(
        (ref) => vehicleSearch(ref as VehicleSearchRef, query),
        from: vehicleSearchProvider,
        name: r'vehicleSearchProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleSearchHash,
        dependencies: VehicleSearchFamily._dependencies,
        allTransitiveDependencies:
            VehicleSearchFamily._allTransitiveDependencies,
        query: query,
      );

  VehicleSearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(VehicleSearchRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleSearchProvider._internal(
        (ref) => create(ref as VehicleSearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _VehicleSearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleSearchProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleSearchRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _VehicleSearchProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with VehicleSearchRef {
  _VehicleSearchProviderElement(super.provider);

  @override
  String get query => (origin as VehicleSearchProvider).query;
}

String _$partsSearchHash() => r'5d48781c23fbcf9a58a70d9d7dd8ce046f61df16';

/// مزود البحث في قطع الغيار - Clean implementation
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [partsSearch].
@ProviderFor(partsSearch)
const partsSearchProvider = PartsSearchFamily();

/// مزود البحث في قطع الغيار - Clean implementation
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [partsSearch].
class PartsSearchFamily extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// مزود البحث في قطع الغيار - Clean implementation
  /// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [partsSearch].
  const PartsSearchFamily();

  /// مزود البحث في قطع الغيار - Clean implementation
  /// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [partsSearch].
  PartsSearchProvider call(String query) {
    return PartsSearchProvider(query);
  }

  @override
  PartsSearchProvider getProviderOverride(
    covariant PartsSearchProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'partsSearchProvider';
}

/// مزود البحث في قطع الغيار - Clean implementation
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [partsSearch].
class PartsSearchProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// مزود البحث في قطع الغيار - Clean implementation
  /// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [partsSearch].
  PartsSearchProvider(String query)
    : this._internal(
        (ref) => partsSearch(ref as PartsSearchRef, query),
        from: partsSearchProvider,
        name: r'partsSearchProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$partsSearchHash,
        dependencies: PartsSearchFamily._dependencies,
        allTransitiveDependencies: PartsSearchFamily._allTransitiveDependencies,
        query: query,
      );

  PartsSearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(PartsSearchRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PartsSearchProvider._internal(
        (ref) => create(ref as PartsSearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _PartsSearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PartsSearchProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PartsSearchRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _PartsSearchProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with PartsSearchRef {
  _PartsSearchProviderElement(super.provider);

  @override
  String get query => (origin as PartsSearchProvider).query;
}

String _$universalSearchHash() => r'b01b9eea72e44e21fdad698b1d3fc9058f583590';

/// مزود البحث العالمي
///
/// Copied from [universalSearch].
@ProviderFor(universalSearch)
const universalSearchProvider = UniversalSearchFamily();

/// مزود البحث العالمي
///
/// Copied from [universalSearch].
class UniversalSearchFamily
    extends Family<AsyncValue<Map<String, List<dynamic>>>> {
  /// مزود البحث العالمي
  ///
  /// Copied from [universalSearch].
  const UniversalSearchFamily();

  /// مزود البحث العالمي
  ///
  /// Copied from [universalSearch].
  UniversalSearchProvider call(String query) {
    return UniversalSearchProvider(query);
  }

  @override
  UniversalSearchProvider getProviderOverride(
    covariant UniversalSearchProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'universalSearchProvider';
}

/// مزود البحث العالمي
///
/// Copied from [universalSearch].
class UniversalSearchProvider
    extends AutoDisposeFutureProvider<Map<String, List<dynamic>>> {
  /// مزود البحث العالمي
  ///
  /// Copied from [universalSearch].
  UniversalSearchProvider(String query)
    : this._internal(
        (ref) => universalSearch(ref as UniversalSearchRef, query),
        from: universalSearchProvider,
        name: r'universalSearchProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$universalSearchHash,
        dependencies: UniversalSearchFamily._dependencies,
        allTransitiveDependencies:
            UniversalSearchFamily._allTransitiveDependencies,
        query: query,
      );

  UniversalSearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<Map<String, List<dynamic>>> Function(UniversalSearchRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UniversalSearchProvider._internal(
        (ref) => create(ref as UniversalSearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, List<dynamic>>> createElement() {
    return _UniversalSearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UniversalSearchProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UniversalSearchRef
    on AutoDisposeFutureProviderRef<Map<String, List<dynamic>>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _UniversalSearchProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, List<dynamic>>>
    with UniversalSearchRef {
  _UniversalSearchProviderElement(super.provider);

  @override
  String get query => (origin as UniversalSearchProvider).query;
}

String _$recentSearchesHash() => r'e0cc7aa1fb0db02c627acce019ebecf925e8f61d';

/// مزود يحفظ آخر استعلامات البحث
///
/// Copied from [RecentSearches].
@ProviderFor(RecentSearches)
final recentSearchesProvider =
    AutoDisposeNotifierProvider<RecentSearches, List<String>>.internal(
      RecentSearches.new,
      name: r'recentSearchesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$recentSearchesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$RecentSearches = AutoDisposeNotifier<List<String>>;
String _$savedSearchesHash() => r'9b6ef4ed1695cc8fda587afb9b5baa553f011032';

/// مزود يحفظ عمليات البحث المحفوظة
///
/// Copied from [SavedSearches].
@ProviderFor(SavedSearches)
final savedSearchesProvider =
    AutoDisposeNotifierProvider<SavedSearches, List<String>>.internal(
      SavedSearches.new,
      name: r'savedSearchesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$savedSearchesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SavedSearches = AutoDisposeNotifier<List<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
