// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'advanced_search_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$performAdvancedSearchHash() =>
    r'5c09a6ed6e261369a8f4da318d5796820a95a193';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for performing advanced search
///
/// Copied from [performAdvancedSearch].
@ProviderFor(performAdvancedSearch)
const performAdvancedSearchProvider = PerformAdvancedSearchFamily();

/// Provider for performing advanced search
///
/// Copied from [performAdvancedSearch].
class PerformAdvancedSearchFamily
    extends Family<AsyncValue<List<ProductModel>>> {
  /// Provider for performing advanced search
  ///
  /// Copied from [performAdvancedSearch].
  const PerformAdvancedSearchFamily();

  /// Provider for performing advanced search
  ///
  /// Copied from [performAdvancedSearch].
  PerformAdvancedSearchProvider call(AdvancedSearchFilters filter) {
    return PerformAdvancedSearchProvider(filter);
  }

  @override
  PerformAdvancedSearchProvider getProviderOverride(
    covariant PerformAdvancedSearchProvider provider,
  ) {
    return call(provider.filter);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'performAdvancedSearchProvider';
}

/// Provider for performing advanced search
///
/// Copied from [performAdvancedSearch].
class PerformAdvancedSearchProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Provider for performing advanced search
  ///
  /// Copied from [performAdvancedSearch].
  PerformAdvancedSearchProvider(AdvancedSearchFilters filter)
    : this._internal(
        (ref) => performAdvancedSearch(ref as PerformAdvancedSearchRef, filter),
        from: performAdvancedSearchProvider,
        name: r'performAdvancedSearchProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$performAdvancedSearchHash,
        dependencies: PerformAdvancedSearchFamily._dependencies,
        allTransitiveDependencies:
            PerformAdvancedSearchFamily._allTransitiveDependencies,
        filter: filter,
      );

  PerformAdvancedSearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.filter,
  }) : super.internal();

  final AdvancedSearchFilters filter;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(PerformAdvancedSearchRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PerformAdvancedSearchProvider._internal(
        (ref) => create(ref as PerformAdvancedSearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        filter: filter,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _PerformAdvancedSearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PerformAdvancedSearchProvider && other.filter == filter;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, filter.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PerformAdvancedSearchRef
    on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `filter` of this provider.
  AdvancedSearchFilters get filter;
}

class _PerformAdvancedSearchProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with PerformAdvancedSearchRef {
  _PerformAdvancedSearchProviderElement(super.provider);

  @override
  AdvancedSearchFilters get filter =>
      (origin as PerformAdvancedSearchProvider).filter;
}

String _$advancedSearchNotifierHash() =>
    r'2cca3846bda136c232fde38a1ffd5563ec36dd79';

abstract class _$AdvancedSearchNotifier
    extends BuildlessAutoDisposeAsyncNotifier<List<ProductModel>> {
  late final String? query;
  late final String? make;
  late final String? model;
  late final int? yearFrom;
  late final int? yearTo;
  late final String? categoryId;
  late final double? priceMin;
  late final double? priceMax;
  late final ProductCondition? condition;
  late final SortOption sortOption;
  late final int page;

  FutureOr<List<ProductModel>> build({
    String? query,
    String? make,
    String? model,
    int? yearFrom,
    int? yearTo,
    String? categoryId,
    double? priceMin,
    double? priceMax,
    ProductCondition? condition,
    SortOption sortOption = SortOption.relevance,
    int page = 0,
  });
}

/// Provider for advanced search with complex filtering
///
/// Copied from [AdvancedSearchNotifier].
@ProviderFor(AdvancedSearchNotifier)
const advancedSearchNotifierProvider = AdvancedSearchNotifierFamily();

/// Provider for advanced search with complex filtering
///
/// Copied from [AdvancedSearchNotifier].
class AdvancedSearchNotifierFamily
    extends Family<AsyncValue<List<ProductModel>>> {
  /// Provider for advanced search with complex filtering
  ///
  /// Copied from [AdvancedSearchNotifier].
  const AdvancedSearchNotifierFamily();

  /// Provider for advanced search with complex filtering
  ///
  /// Copied from [AdvancedSearchNotifier].
  AdvancedSearchNotifierProvider call({
    String? query,
    String? make,
    String? model,
    int? yearFrom,
    int? yearTo,
    String? categoryId,
    double? priceMin,
    double? priceMax,
    ProductCondition? condition,
    SortOption sortOption = SortOption.relevance,
    int page = 0,
  }) {
    return AdvancedSearchNotifierProvider(
      query: query,
      make: make,
      model: model,
      yearFrom: yearFrom,
      yearTo: yearTo,
      categoryId: categoryId,
      priceMin: priceMin,
      priceMax: priceMax,
      condition: condition,
      sortOption: sortOption,
      page: page,
    );
  }

  @override
  AdvancedSearchNotifierProvider getProviderOverride(
    covariant AdvancedSearchNotifierProvider provider,
  ) {
    return call(
      query: provider.query,
      make: provider.make,
      model: provider.model,
      yearFrom: provider.yearFrom,
      yearTo: provider.yearTo,
      categoryId: provider.categoryId,
      priceMin: provider.priceMin,
      priceMax: provider.priceMax,
      condition: provider.condition,
      sortOption: provider.sortOption,
      page: provider.page,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'advancedSearchNotifierProvider';
}

/// Provider for advanced search with complex filtering
///
/// Copied from [AdvancedSearchNotifier].
class AdvancedSearchNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          AdvancedSearchNotifier,
          List<ProductModel>
        > {
  /// Provider for advanced search with complex filtering
  ///
  /// Copied from [AdvancedSearchNotifier].
  AdvancedSearchNotifierProvider({
    String? query,
    String? make,
    String? model,
    int? yearFrom,
    int? yearTo,
    String? categoryId,
    double? priceMin,
    double? priceMax,
    ProductCondition? condition,
    SortOption sortOption = SortOption.relevance,
    int page = 0,
  }) : this._internal(
         () => AdvancedSearchNotifier()
           ..query = query
           ..make = make
           ..model = model
           ..yearFrom = yearFrom
           ..yearTo = yearTo
           ..categoryId = categoryId
           ..priceMin = priceMin
           ..priceMax = priceMax
           ..condition = condition
           ..sortOption = sortOption
           ..page = page,
         from: advancedSearchNotifierProvider,
         name: r'advancedSearchNotifierProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$advancedSearchNotifierHash,
         dependencies: AdvancedSearchNotifierFamily._dependencies,
         allTransitiveDependencies:
             AdvancedSearchNotifierFamily._allTransitiveDependencies,
         query: query,
         make: make,
         model: model,
         yearFrom: yearFrom,
         yearTo: yearTo,
         categoryId: categoryId,
         priceMin: priceMin,
         priceMax: priceMax,
         condition: condition,
         sortOption: sortOption,
         page: page,
       );

  AdvancedSearchNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
    required this.make,
    required this.model,
    required this.yearFrom,
    required this.yearTo,
    required this.categoryId,
    required this.priceMin,
    required this.priceMax,
    required this.condition,
    required this.sortOption,
    required this.page,
  }) : super.internal();

  final String? query;
  final String? make;
  final String? model;
  final int? yearFrom;
  final int? yearTo;
  final String? categoryId;
  final double? priceMin;
  final double? priceMax;
  final ProductCondition? condition;
  final SortOption sortOption;
  final int page;

  @override
  FutureOr<List<ProductModel>> runNotifierBuild(
    covariant AdvancedSearchNotifier notifier,
  ) {
    return notifier.build(
      query: query,
      make: make,
      model: model,
      yearFrom: yearFrom,
      yearTo: yearTo,
      categoryId: categoryId,
      priceMin: priceMin,
      priceMax: priceMax,
      condition: condition,
      sortOption: sortOption,
      page: page,
    );
  }

  @override
  Override overrideWith(AdvancedSearchNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: AdvancedSearchNotifierProvider._internal(
        () => create()
          ..query = query
          ..make = make
          ..model = model
          ..yearFrom = yearFrom
          ..yearTo = yearTo
          ..categoryId = categoryId
          ..priceMin = priceMin
          ..priceMax = priceMax
          ..condition = condition
          ..sortOption = sortOption
          ..page = page,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
        make: make,
        model: model,
        yearFrom: yearFrom,
        yearTo: yearTo,
        categoryId: categoryId,
        priceMin: priceMin,
        priceMax: priceMax,
        condition: condition,
        sortOption: sortOption,
        page: page,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    AdvancedSearchNotifier,
    List<ProductModel>
  >
  createElement() {
    return _AdvancedSearchNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AdvancedSearchNotifierProvider &&
        other.query == query &&
        other.make == make &&
        other.model == model &&
        other.yearFrom == yearFrom &&
        other.yearTo == yearTo &&
        other.categoryId == categoryId &&
        other.priceMin == priceMin &&
        other.priceMax == priceMax &&
        other.condition == condition &&
        other.sortOption == sortOption &&
        other.page == page;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);
    hash = _SystemHash.combine(hash, make.hashCode);
    hash = _SystemHash.combine(hash, model.hashCode);
    hash = _SystemHash.combine(hash, yearFrom.hashCode);
    hash = _SystemHash.combine(hash, yearTo.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);
    hash = _SystemHash.combine(hash, priceMin.hashCode);
    hash = _SystemHash.combine(hash, priceMax.hashCode);
    hash = _SystemHash.combine(hash, condition.hashCode);
    hash = _SystemHash.combine(hash, sortOption.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AdvancedSearchNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<List<ProductModel>> {
  /// The parameter `query` of this provider.
  String? get query;

  /// The parameter `make` of this provider.
  String? get make;

  /// The parameter `model` of this provider.
  String? get model;

  /// The parameter `yearFrom` of this provider.
  int? get yearFrom;

  /// The parameter `yearTo` of this provider.
  int? get yearTo;

  /// The parameter `categoryId` of this provider.
  String? get categoryId;

  /// The parameter `priceMin` of this provider.
  double? get priceMin;

  /// The parameter `priceMax` of this provider.
  double? get priceMax;

  /// The parameter `condition` of this provider.
  ProductCondition? get condition;

  /// The parameter `sortOption` of this provider.
  SortOption get sortOption;

  /// The parameter `page` of this provider.
  int get page;
}

class _AdvancedSearchNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          AdvancedSearchNotifier,
          List<ProductModel>
        >
    with AdvancedSearchNotifierRef {
  _AdvancedSearchNotifierProviderElement(super.provider);

  @override
  String? get query => (origin as AdvancedSearchNotifierProvider).query;
  @override
  String? get make => (origin as AdvancedSearchNotifierProvider).make;
  @override
  String? get model => (origin as AdvancedSearchNotifierProvider).model;
  @override
  int? get yearFrom => (origin as AdvancedSearchNotifierProvider).yearFrom;
  @override
  int? get yearTo => (origin as AdvancedSearchNotifierProvider).yearTo;
  @override
  String? get categoryId =>
      (origin as AdvancedSearchNotifierProvider).categoryId;
  @override
  double? get priceMin => (origin as AdvancedSearchNotifierProvider).priceMin;
  @override
  double? get priceMax => (origin as AdvancedSearchNotifierProvider).priceMax;
  @override
  ProductCondition? get condition =>
      (origin as AdvancedSearchNotifierProvider).condition;
  @override
  SortOption get sortOption =>
      (origin as AdvancedSearchNotifierProvider).sortOption;
  @override
  int get page => (origin as AdvancedSearchNotifierProvider).page;
}

String _$advancedSearchHash() => r'649f2a027f3dd24add761accf2d1c586d311c2c4';

/// Advanced search provider with complex filtering capabilities
///
/// Copied from [AdvancedSearch].
@ProviderFor(AdvancedSearch)
final advancedSearchProvider =
    AutoDisposeNotifierProvider<AdvancedSearch, AdvancedSearchFilters>.internal(
      AdvancedSearch.new,
      name: r'advancedSearchProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$advancedSearchHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdvancedSearch = AutoDisposeNotifier<AdvancedSearchFilters>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
