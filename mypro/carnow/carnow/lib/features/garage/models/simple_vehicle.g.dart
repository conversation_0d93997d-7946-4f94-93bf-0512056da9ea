// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'simple_vehicle.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SimpleVehicle _$SimpleVehicleFromJson(Map<String, dynamic> json) =>
    _SimpleVehicle(
      id: json['id'] as String,
      makeName: json['makeName'] as String,
      modelName: json['modelName'] as String,
      year: (json['year'] as num).toInt(),
      color: json['color'] as String? ?? '',
      plateNumber: json['plateNumber'] as String? ?? '',
      mileage: (json['mileage'] as num?)?.toInt() ?? 0,
      fuelType: json['fuelType'] as String? ?? '',
      transmission: json['transmission'] as String? ?? '',
      engineSize: json['engineSize'] as String? ?? '',
      status: json['status'] as String? ?? 'active',
      notes: json['notes'] as String? ?? '',
    );

Map<String, dynamic> _$SimpleVehicleToJson(_SimpleVehicle instance) =>
    <String, dynamic>{
      'id': instance.id,
      'makeName': instance.makeName,
      'modelName': instance.modelName,
      'year': instance.year,
      'color': instance.color,
      'plateNumber': instance.plateNumber,
      'mileage': instance.mileage,
      'fuelType': instance.fuelType,
      'transmission': instance.transmission,
      'engineSize': instance.engineSize,
      'status': instance.status,
      'notes': instance.notes,
    };

_VehicleMake _$VehicleMakeFromJson(Map<String, dynamic> json) => _VehicleMake(
  id: json['id'] as String,
  name: json['name'] as String,
  country: json['country'] as String,
  models:
      (json['models'] as List<dynamic>?)
          ?.map((e) => VehicleModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$VehicleMakeToJson(_VehicleMake instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'country': instance.country,
      'models': instance.models,
    };

_VehicleModel _$VehicleModelFromJson(Map<String, dynamic> json) =>
    _VehicleModel(
      id: json['id'] as String,
      name: json['name'] as String,
      makeId: json['makeId'] as String,
      years:
          (json['years'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      category: json['category'] as String? ?? '',
    );

Map<String, dynamic> _$VehicleModelToJson(_VehicleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'makeId': instance.makeId,
      'years': instance.years,
      'category': instance.category,
    };
