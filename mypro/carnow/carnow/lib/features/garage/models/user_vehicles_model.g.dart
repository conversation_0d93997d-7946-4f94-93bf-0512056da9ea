// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_vehicles_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserVehicleModel _$UserVehicleModelFromJson(Map<String, dynamic> json) =>
    _UserVehicleModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      makeId: json['make_id'] as String,
      modelId: json['model_id'] as String,
      year: (json['year'] as num).toInt(),
      trim: json['trim'] as String?,
      vin: json['vin'] as String?,
      licensePlate: json['license_plate'] as String?,
      color: json['color'] as String?,
      nickname: json['nickname'] as String?,
      mileage: (json['mileage'] as num?)?.toInt(),
      transmissionType: json['transmission_type'] as String?,
      fuelType: json['fuel_type'] as String?,
      engineSize: json['engine_size'] as String?,
      isPrimary: json['is_primary'] as bool? ?? true,
      isActive: json['is_active'] as bool? ?? true,
      customFields: json['custom_fields'] as Map<String, dynamic>?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$UserVehicleModelToJson(_UserVehicleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'make_id': instance.makeId,
      'model_id': instance.modelId,
      'year': instance.year,
      'trim': instance.trim,
      'vin': instance.vin,
      'license_plate': instance.licensePlate,
      'color': instance.color,
      'nickname': instance.nickname,
      'mileage': instance.mileage,
      'transmission_type': instance.transmissionType,
      'fuel_type': instance.fuelType,
      'engine_size': instance.engineSize,
      'is_primary': instance.isPrimary,
      'is_active': instance.isActive,
      'custom_fields': instance.customFields,
    };

_CompleteUserVehicleModel _$CompleteUserVehicleModelFromJson(
  Map<String, dynamic> json,
) => _CompleteUserVehicleModel(
  vehicle: UserVehicleModel.fromJson(json['vehicle'] as Map<String, dynamic>),
  makeName: json['make_name'] as String,
  modelName: json['model_name'] as String,
  makeNameAr: json['make_name_ar'] as String?,
  modelNameAr: json['model_name_ar'] as String?,
  makeLogoUrl: json['make_logo_url'] as String?,
);

Map<String, dynamic> _$CompleteUserVehicleModelToJson(
  _CompleteUserVehicleModel instance,
) => <String, dynamic>{
  'vehicle': instance.vehicle.toJson(),
  'make_name': instance.makeName,
  'model_name': instance.modelName,
  'make_name_ar': instance.makeNameAr,
  'model_name_ar': instance.modelNameAr,
  'make_logo_url': instance.makeLogoUrl,
};
