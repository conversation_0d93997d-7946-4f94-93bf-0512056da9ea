// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_vehicle.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserVehicle {

 String get userId; String get make; String get model; int? get id; int? get year; String? get trim; String? get engine; String? get transmission; String? get fuelType; int? get mileage; String? get bodyType; String? get vin; String? get imageUrl; List<String> get images; Map<String, String> get colors; Map<String, dynamic> get specifications;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt; bool get isDeleted; int? get makeRefId; int? get modelRefId; int? get fuelTypeRefId; int? get transmissionRefId; int? get engineConfigRefId;
/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserVehicleCopyWith<UserVehicle> get copyWith => _$UserVehicleCopyWithImpl<UserVehicle>(this as UserVehicle, _$identity);

  /// Serializes this UserVehicle to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserVehicle&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.id, id) || other.id == id)&&(identical(other.year, year) || other.year == year)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.transmission, transmission) || other.transmission == transmission)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.bodyType, bodyType) || other.bodyType == bodyType)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&const DeepCollectionEquality().equals(other.images, images)&&const DeepCollectionEquality().equals(other.colors, colors)&&const DeepCollectionEquality().equals(other.specifications, specifications)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.makeRefId, makeRefId) || other.makeRefId == makeRefId)&&(identical(other.modelRefId, modelRefId) || other.modelRefId == modelRefId)&&(identical(other.fuelTypeRefId, fuelTypeRefId) || other.fuelTypeRefId == fuelTypeRefId)&&(identical(other.transmissionRefId, transmissionRefId) || other.transmissionRefId == transmissionRefId)&&(identical(other.engineConfigRefId, engineConfigRefId) || other.engineConfigRefId == engineConfigRefId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,userId,make,model,id,year,trim,engine,transmission,fuelType,mileage,bodyType,vin,imageUrl,const DeepCollectionEquality().hash(images),const DeepCollectionEquality().hash(colors),const DeepCollectionEquality().hash(specifications),createdAt,updatedAt,isDeleted,makeRefId,modelRefId,fuelTypeRefId,transmissionRefId,engineConfigRefId]);

@override
String toString() {
  return 'UserVehicle(userId: $userId, make: $make, model: $model, id: $id, year: $year, trim: $trim, engine: $engine, transmission: $transmission, fuelType: $fuelType, mileage: $mileage, bodyType: $bodyType, vin: $vin, imageUrl: $imageUrl, images: $images, colors: $colors, specifications: $specifications, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, makeRefId: $makeRefId, modelRefId: $modelRefId, fuelTypeRefId: $fuelTypeRefId, transmissionRefId: $transmissionRefId, engineConfigRefId: $engineConfigRefId)';
}


}

/// @nodoc
abstract mixin class $UserVehicleCopyWith<$Res>  {
  factory $UserVehicleCopyWith(UserVehicle value, $Res Function(UserVehicle) _then) = _$UserVehicleCopyWithImpl;
@useResult
$Res call({
 String userId, String make, String model, int? id, int? year, String? trim, String? engine, String? transmission, String? fuelType, int? mileage, String? bodyType, String? vin, String? imageUrl, List<String> images, Map<String, String> colors, Map<String, dynamic> specifications,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt, bool isDeleted, int? makeRefId, int? modelRefId, int? fuelTypeRefId, int? transmissionRefId, int? engineConfigRefId
});




}
/// @nodoc
class _$UserVehicleCopyWithImpl<$Res>
    implements $UserVehicleCopyWith<$Res> {
  _$UserVehicleCopyWithImpl(this._self, this._then);

  final UserVehicle _self;
  final $Res Function(UserVehicle) _then;

/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? make = null,Object? model = null,Object? id = freezed,Object? year = freezed,Object? trim = freezed,Object? engine = freezed,Object? transmission = freezed,Object? fuelType = freezed,Object? mileage = freezed,Object? bodyType = freezed,Object? vin = freezed,Object? imageUrl = freezed,Object? images = null,Object? colors = null,Object? specifications = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? makeRefId = freezed,Object? modelRefId = freezed,Object? fuelTypeRefId = freezed,Object? transmissionRefId = freezed,Object? engineConfigRefId = freezed,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,make: null == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,year: freezed == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,transmission: freezed == transmission ? _self.transmission : transmission // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,bodyType: freezed == bodyType ? _self.bodyType : bodyType // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,images: null == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>,colors: null == colors ? _self.colors : colors // ignore: cast_nullable_to_non_nullable
as Map<String, String>,specifications: null == specifications ? _self.specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,makeRefId: freezed == makeRefId ? _self.makeRefId : makeRefId // ignore: cast_nullable_to_non_nullable
as int?,modelRefId: freezed == modelRefId ? _self.modelRefId : modelRefId // ignore: cast_nullable_to_non_nullable
as int?,fuelTypeRefId: freezed == fuelTypeRefId ? _self.fuelTypeRefId : fuelTypeRefId // ignore: cast_nullable_to_non_nullable
as int?,transmissionRefId: freezed == transmissionRefId ? _self.transmissionRefId : transmissionRefId // ignore: cast_nullable_to_non_nullable
as int?,engineConfigRefId: freezed == engineConfigRefId ? _self.engineConfigRefId : engineConfigRefId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [UserVehicle].
extension UserVehiclePatterns on UserVehicle {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserVehicle value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserVehicle() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserVehicle value)  $default,){
final _that = this;
switch (_that) {
case _UserVehicle():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserVehicle value)?  $default,){
final _that = this;
switch (_that) {
case _UserVehicle() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String make,  String model,  int? id,  int? year,  String? trim,  String? engine,  String? transmission,  String? fuelType,  int? mileage,  String? bodyType,  String? vin,  String? imageUrl,  List<String> images,  Map<String, String> colors,  Map<String, dynamic> specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt,  bool isDeleted,  int? makeRefId,  int? modelRefId,  int? fuelTypeRefId,  int? transmissionRefId,  int? engineConfigRefId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserVehicle() when $default != null:
return $default(_that.userId,_that.make,_that.model,_that.id,_that.year,_that.trim,_that.engine,_that.transmission,_that.fuelType,_that.mileage,_that.bodyType,_that.vin,_that.imageUrl,_that.images,_that.colors,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.makeRefId,_that.modelRefId,_that.fuelTypeRefId,_that.transmissionRefId,_that.engineConfigRefId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String make,  String model,  int? id,  int? year,  String? trim,  String? engine,  String? transmission,  String? fuelType,  int? mileage,  String? bodyType,  String? vin,  String? imageUrl,  List<String> images,  Map<String, String> colors,  Map<String, dynamic> specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt,  bool isDeleted,  int? makeRefId,  int? modelRefId,  int? fuelTypeRefId,  int? transmissionRefId,  int? engineConfigRefId)  $default,) {final _that = this;
switch (_that) {
case _UserVehicle():
return $default(_that.userId,_that.make,_that.model,_that.id,_that.year,_that.trim,_that.engine,_that.transmission,_that.fuelType,_that.mileage,_that.bodyType,_that.vin,_that.imageUrl,_that.images,_that.colors,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.makeRefId,_that.modelRefId,_that.fuelTypeRefId,_that.transmissionRefId,_that.engineConfigRefId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String make,  String model,  int? id,  int? year,  String? trim,  String? engine,  String? transmission,  String? fuelType,  int? mileage,  String? bodyType,  String? vin,  String? imageUrl,  List<String> images,  Map<String, String> colors,  Map<String, dynamic> specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt,  bool isDeleted,  int? makeRefId,  int? modelRefId,  int? fuelTypeRefId,  int? transmissionRefId,  int? engineConfigRefId)?  $default,) {final _that = this;
switch (_that) {
case _UserVehicle() when $default != null:
return $default(_that.userId,_that.make,_that.model,_that.id,_that.year,_that.trim,_that.engine,_that.transmission,_that.fuelType,_that.mileage,_that.bodyType,_that.vin,_that.imageUrl,_that.images,_that.colors,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.makeRefId,_that.modelRefId,_that.fuelTypeRefId,_that.transmissionRefId,_that.engineConfigRefId);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _UserVehicle implements UserVehicle {
  const _UserVehicle({required this.userId, required this.make, required this.model, this.id, this.year, this.trim, this.engine, this.transmission, this.fuelType, this.mileage, this.bodyType, this.vin, this.imageUrl, final  List<String> images = const [], final  Map<String, String> colors = const {}, final  Map<String, dynamic> specifications = const {}, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt, this.isDeleted = false, this.makeRefId, this.modelRefId, this.fuelTypeRefId, this.transmissionRefId, this.engineConfigRefId}): _images = images,_colors = colors,_specifications = specifications;
  factory _UserVehicle.fromJson(Map<String, dynamic> json) => _$UserVehicleFromJson(json);

@override final  String userId;
@override final  String make;
@override final  String model;
@override final  int? id;
@override final  int? year;
@override final  String? trim;
@override final  String? engine;
@override final  String? transmission;
@override final  String? fuelType;
@override final  int? mileage;
@override final  String? bodyType;
@override final  String? vin;
@override final  String? imageUrl;
 final  List<String> _images;
@override@JsonKey() List<String> get images {
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_images);
}

 final  Map<String, String> _colors;
@override@JsonKey() Map<String, String> get colors {
  if (_colors is EqualUnmodifiableMapView) return _colors;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_colors);
}

 final  Map<String, dynamic> _specifications;
@override@JsonKey() Map<String, dynamic> get specifications {
  if (_specifications is EqualUnmodifiableMapView) return _specifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_specifications);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;
@override@JsonKey() final  bool isDeleted;
@override final  int? makeRefId;
@override final  int? modelRefId;
@override final  int? fuelTypeRefId;
@override final  int? transmissionRefId;
@override final  int? engineConfigRefId;

/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserVehicleCopyWith<_UserVehicle> get copyWith => __$UserVehicleCopyWithImpl<_UserVehicle>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserVehicleToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserVehicle&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.id, id) || other.id == id)&&(identical(other.year, year) || other.year == year)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.transmission, transmission) || other.transmission == transmission)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.bodyType, bodyType) || other.bodyType == bodyType)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&const DeepCollectionEquality().equals(other._images, _images)&&const DeepCollectionEquality().equals(other._colors, _colors)&&const DeepCollectionEquality().equals(other._specifications, _specifications)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.makeRefId, makeRefId) || other.makeRefId == makeRefId)&&(identical(other.modelRefId, modelRefId) || other.modelRefId == modelRefId)&&(identical(other.fuelTypeRefId, fuelTypeRefId) || other.fuelTypeRefId == fuelTypeRefId)&&(identical(other.transmissionRefId, transmissionRefId) || other.transmissionRefId == transmissionRefId)&&(identical(other.engineConfigRefId, engineConfigRefId) || other.engineConfigRefId == engineConfigRefId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,userId,make,model,id,year,trim,engine,transmission,fuelType,mileage,bodyType,vin,imageUrl,const DeepCollectionEquality().hash(_images),const DeepCollectionEquality().hash(_colors),const DeepCollectionEquality().hash(_specifications),createdAt,updatedAt,isDeleted,makeRefId,modelRefId,fuelTypeRefId,transmissionRefId,engineConfigRefId]);

@override
String toString() {
  return 'UserVehicle(userId: $userId, make: $make, model: $model, id: $id, year: $year, trim: $trim, engine: $engine, transmission: $transmission, fuelType: $fuelType, mileage: $mileage, bodyType: $bodyType, vin: $vin, imageUrl: $imageUrl, images: $images, colors: $colors, specifications: $specifications, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, makeRefId: $makeRefId, modelRefId: $modelRefId, fuelTypeRefId: $fuelTypeRefId, transmissionRefId: $transmissionRefId, engineConfigRefId: $engineConfigRefId)';
}


}

/// @nodoc
abstract mixin class _$UserVehicleCopyWith<$Res> implements $UserVehicleCopyWith<$Res> {
  factory _$UserVehicleCopyWith(_UserVehicle value, $Res Function(_UserVehicle) _then) = __$UserVehicleCopyWithImpl;
@override @useResult
$Res call({
 String userId, String make, String model, int? id, int? year, String? trim, String? engine, String? transmission, String? fuelType, int? mileage, String? bodyType, String? vin, String? imageUrl, List<String> images, Map<String, String> colors, Map<String, dynamic> specifications,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt, bool isDeleted, int? makeRefId, int? modelRefId, int? fuelTypeRefId, int? transmissionRefId, int? engineConfigRefId
});




}
/// @nodoc
class __$UserVehicleCopyWithImpl<$Res>
    implements _$UserVehicleCopyWith<$Res> {
  __$UserVehicleCopyWithImpl(this._self, this._then);

  final _UserVehicle _self;
  final $Res Function(_UserVehicle) _then;

/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? make = null,Object? model = null,Object? id = freezed,Object? year = freezed,Object? trim = freezed,Object? engine = freezed,Object? transmission = freezed,Object? fuelType = freezed,Object? mileage = freezed,Object? bodyType = freezed,Object? vin = freezed,Object? imageUrl = freezed,Object? images = null,Object? colors = null,Object? specifications = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? makeRefId = freezed,Object? modelRefId = freezed,Object? fuelTypeRefId = freezed,Object? transmissionRefId = freezed,Object? engineConfigRefId = freezed,}) {
  return _then(_UserVehicle(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,make: null == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,year: freezed == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,transmission: freezed == transmission ? _self.transmission : transmission // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,bodyType: freezed == bodyType ? _self.bodyType : bodyType // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,images: null == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>,colors: null == colors ? _self._colors : colors // ignore: cast_nullable_to_non_nullable
as Map<String, String>,specifications: null == specifications ? _self._specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,makeRefId: freezed == makeRefId ? _self.makeRefId : makeRefId // ignore: cast_nullable_to_non_nullable
as int?,modelRefId: freezed == modelRefId ? _self.modelRefId : modelRefId // ignore: cast_nullable_to_non_nullable
as int?,fuelTypeRefId: freezed == fuelTypeRefId ? _self.fuelTypeRefId : fuelTypeRefId // ignore: cast_nullable_to_non_nullable
as int?,transmissionRefId: freezed == transmissionRefId ? _self.transmissionRefId : transmissionRefId // ignore: cast_nullable_to_non_nullable
as int?,engineConfigRefId: freezed == engineConfigRefId ? _self.engineConfigRefId : engineConfigRefId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
