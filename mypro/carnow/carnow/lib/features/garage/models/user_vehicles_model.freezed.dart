// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_vehicles_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserVehicleModel {

 String get id; String get userId; String get makeId; String get modelId; int get year; String? get trim; String? get vin; String? get licensePlate; String? get color; String? get nickname; int? get mileage; String? get transmissionType; String? get fuelType; String? get engineSize; bool get isPrimary; bool get isActive; Map<String, dynamic>? get customFields;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of UserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserVehicleModelCopyWith<UserVehicleModel> get copyWith => _$UserVehicleModelCopyWithImpl<UserVehicleModel>(this as UserVehicleModel, _$identity);

  /// Serializes this UserVehicleModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserVehicleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.year, year) || other.year == year)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.licensePlate, licensePlate) || other.licensePlate == licensePlate)&&(identical(other.color, color) || other.color == color)&&(identical(other.nickname, nickname) || other.nickname == nickname)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.transmissionType, transmissionType) || other.transmissionType == transmissionType)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.engineSize, engineSize) || other.engineSize == engineSize)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&const DeepCollectionEquality().equals(other.customFields, customFields)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,makeId,modelId,year,trim,vin,licensePlate,color,nickname,mileage,transmissionType,fuelType,engineSize,isPrimary,isActive,const DeepCollectionEquality().hash(customFields),createdAt,updatedAt]);

@override
String toString() {
  return 'UserVehicleModel(id: $id, userId: $userId, makeId: $makeId, modelId: $modelId, year: $year, trim: $trim, vin: $vin, licensePlate: $licensePlate, color: $color, nickname: $nickname, mileage: $mileage, transmissionType: $transmissionType, fuelType: $fuelType, engineSize: $engineSize, isPrimary: $isPrimary, isActive: $isActive, customFields: $customFields, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $UserVehicleModelCopyWith<$Res>  {
  factory $UserVehicleModelCopyWith(UserVehicleModel value, $Res Function(UserVehicleModel) _then) = _$UserVehicleModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String makeId, String modelId, int year, String? trim, String? vin, String? licensePlate, String? color, String? nickname, int? mileage, String? transmissionType, String? fuelType, String? engineSize, bool isPrimary, bool isActive, Map<String, dynamic>? customFields,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$UserVehicleModelCopyWithImpl<$Res>
    implements $UserVehicleModelCopyWith<$Res> {
  _$UserVehicleModelCopyWithImpl(this._self, this._then);

  final UserVehicleModel _self;
  final $Res Function(UserVehicleModel) _then;

/// Create a copy of UserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? makeId = null,Object? modelId = null,Object? year = null,Object? trim = freezed,Object? vin = freezed,Object? licensePlate = freezed,Object? color = freezed,Object? nickname = freezed,Object? mileage = freezed,Object? transmissionType = freezed,Object? fuelType = freezed,Object? engineSize = freezed,Object? isPrimary = null,Object? isActive = null,Object? customFields = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as String,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,licensePlate: freezed == licensePlate ? _self.licensePlate : licensePlate // ignore: cast_nullable_to_non_nullable
as String?,color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,nickname: freezed == nickname ? _self.nickname : nickname // ignore: cast_nullable_to_non_nullable
as String?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,transmissionType: freezed == transmissionType ? _self.transmissionType : transmissionType // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,engineSize: freezed == engineSize ? _self.engineSize : engineSize // ignore: cast_nullable_to_non_nullable
as String?,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,customFields: freezed == customFields ? _self.customFields : customFields // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [UserVehicleModel].
extension UserVehicleModelPatterns on UserVehicleModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserVehicleModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserVehicleModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserVehicleModel value)  $default,){
final _that = this;
switch (_that) {
case _UserVehicleModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserVehicleModel value)?  $default,){
final _that = this;
switch (_that) {
case _UserVehicleModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String makeId,  String modelId,  int year,  String? trim,  String? vin,  String? licensePlate,  String? color,  String? nickname,  int? mileage,  String? transmissionType,  String? fuelType,  String? engineSize,  bool isPrimary,  bool isActive,  Map<String, dynamic>? customFields, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserVehicleModel() when $default != null:
return $default(_that.id,_that.userId,_that.makeId,_that.modelId,_that.year,_that.trim,_that.vin,_that.licensePlate,_that.color,_that.nickname,_that.mileage,_that.transmissionType,_that.fuelType,_that.engineSize,_that.isPrimary,_that.isActive,_that.customFields,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String makeId,  String modelId,  int year,  String? trim,  String? vin,  String? licensePlate,  String? color,  String? nickname,  int? mileage,  String? transmissionType,  String? fuelType,  String? engineSize,  bool isPrimary,  bool isActive,  Map<String, dynamic>? customFields, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _UserVehicleModel():
return $default(_that.id,_that.userId,_that.makeId,_that.modelId,_that.year,_that.trim,_that.vin,_that.licensePlate,_that.color,_that.nickname,_that.mileage,_that.transmissionType,_that.fuelType,_that.engineSize,_that.isPrimary,_that.isActive,_that.customFields,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String makeId,  String modelId,  int year,  String? trim,  String? vin,  String? licensePlate,  String? color,  String? nickname,  int? mileage,  String? transmissionType,  String? fuelType,  String? engineSize,  bool isPrimary,  bool isActive,  Map<String, dynamic>? customFields, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _UserVehicleModel() when $default != null:
return $default(_that.id,_that.userId,_that.makeId,_that.modelId,_that.year,_that.trim,_that.vin,_that.licensePlate,_that.color,_that.nickname,_that.mileage,_that.transmissionType,_that.fuelType,_that.engineSize,_that.isPrimary,_that.isActive,_that.customFields,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _UserVehicleModel implements UserVehicleModel {
  const _UserVehicleModel({required this.id, required this.userId, required this.makeId, required this.modelId, required this.year, this.trim, this.vin, this.licensePlate, this.color, this.nickname, this.mileage, this.transmissionType, this.fuelType, this.engineSize, this.isPrimary = true, this.isActive = true, final  Map<String, dynamic>? customFields, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _customFields = customFields;
  factory _UserVehicleModel.fromJson(Map<String, dynamic> json) => _$UserVehicleModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String makeId;
@override final  String modelId;
@override final  int year;
@override final  String? trim;
@override final  String? vin;
@override final  String? licensePlate;
@override final  String? color;
@override final  String? nickname;
@override final  int? mileage;
@override final  String? transmissionType;
@override final  String? fuelType;
@override final  String? engineSize;
@override@JsonKey() final  bool isPrimary;
@override@JsonKey() final  bool isActive;
 final  Map<String, dynamic>? _customFields;
@override Map<String, dynamic>? get customFields {
  final value = _customFields;
  if (value == null) return null;
  if (_customFields is EqualUnmodifiableMapView) return _customFields;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of UserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserVehicleModelCopyWith<_UserVehicleModel> get copyWith => __$UserVehicleModelCopyWithImpl<_UserVehicleModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserVehicleModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserVehicleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.year, year) || other.year == year)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.licensePlate, licensePlate) || other.licensePlate == licensePlate)&&(identical(other.color, color) || other.color == color)&&(identical(other.nickname, nickname) || other.nickname == nickname)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.transmissionType, transmissionType) || other.transmissionType == transmissionType)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.engineSize, engineSize) || other.engineSize == engineSize)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&const DeepCollectionEquality().equals(other._customFields, _customFields)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,makeId,modelId,year,trim,vin,licensePlate,color,nickname,mileage,transmissionType,fuelType,engineSize,isPrimary,isActive,const DeepCollectionEquality().hash(_customFields),createdAt,updatedAt]);

@override
String toString() {
  return 'UserVehicleModel(id: $id, userId: $userId, makeId: $makeId, modelId: $modelId, year: $year, trim: $trim, vin: $vin, licensePlate: $licensePlate, color: $color, nickname: $nickname, mileage: $mileage, transmissionType: $transmissionType, fuelType: $fuelType, engineSize: $engineSize, isPrimary: $isPrimary, isActive: $isActive, customFields: $customFields, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$UserVehicleModelCopyWith<$Res> implements $UserVehicleModelCopyWith<$Res> {
  factory _$UserVehicleModelCopyWith(_UserVehicleModel value, $Res Function(_UserVehicleModel) _then) = __$UserVehicleModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String makeId, String modelId, int year, String? trim, String? vin, String? licensePlate, String? color, String? nickname, int? mileage, String? transmissionType, String? fuelType, String? engineSize, bool isPrimary, bool isActive, Map<String, dynamic>? customFields,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$UserVehicleModelCopyWithImpl<$Res>
    implements _$UserVehicleModelCopyWith<$Res> {
  __$UserVehicleModelCopyWithImpl(this._self, this._then);

  final _UserVehicleModel _self;
  final $Res Function(_UserVehicleModel) _then;

/// Create a copy of UserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? makeId = null,Object? modelId = null,Object? year = null,Object? trim = freezed,Object? vin = freezed,Object? licensePlate = freezed,Object? color = freezed,Object? nickname = freezed,Object? mileage = freezed,Object? transmissionType = freezed,Object? fuelType = freezed,Object? engineSize = freezed,Object? isPrimary = null,Object? isActive = null,Object? customFields = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_UserVehicleModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as String,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,licensePlate: freezed == licensePlate ? _self.licensePlate : licensePlate // ignore: cast_nullable_to_non_nullable
as String?,color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,nickname: freezed == nickname ? _self.nickname : nickname // ignore: cast_nullable_to_non_nullable
as String?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,transmissionType: freezed == transmissionType ? _self.transmissionType : transmissionType // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,engineSize: freezed == engineSize ? _self.engineSize : engineSize // ignore: cast_nullable_to_non_nullable
as String?,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,customFields: freezed == customFields ? _self._customFields : customFields // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CompleteUserVehicleModel {

 UserVehicleModel get vehicle; String get makeName; String get modelName; String? get makeNameAr; String? get modelNameAr; String? get makeLogoUrl;
/// Create a copy of CompleteUserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CompleteUserVehicleModelCopyWith<CompleteUserVehicleModel> get copyWith => _$CompleteUserVehicleModelCopyWithImpl<CompleteUserVehicleModel>(this as CompleteUserVehicleModel, _$identity);

  /// Serializes this CompleteUserVehicleModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CompleteUserVehicleModel&&(identical(other.vehicle, vehicle) || other.vehicle == vehicle)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.makeNameAr, makeNameAr) || other.makeNameAr == makeNameAr)&&(identical(other.modelNameAr, modelNameAr) || other.modelNameAr == modelNameAr)&&(identical(other.makeLogoUrl, makeLogoUrl) || other.makeLogoUrl == makeLogoUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,vehicle,makeName,modelName,makeNameAr,modelNameAr,makeLogoUrl);

@override
String toString() {
  return 'CompleteUserVehicleModel(vehicle: $vehicle, makeName: $makeName, modelName: $modelName, makeNameAr: $makeNameAr, modelNameAr: $modelNameAr, makeLogoUrl: $makeLogoUrl)';
}


}

/// @nodoc
abstract mixin class $CompleteUserVehicleModelCopyWith<$Res>  {
  factory $CompleteUserVehicleModelCopyWith(CompleteUserVehicleModel value, $Res Function(CompleteUserVehicleModel) _then) = _$CompleteUserVehicleModelCopyWithImpl;
@useResult
$Res call({
 UserVehicleModel vehicle, String makeName, String modelName, String? makeNameAr, String? modelNameAr, String? makeLogoUrl
});


$UserVehicleModelCopyWith<$Res> get vehicle;

}
/// @nodoc
class _$CompleteUserVehicleModelCopyWithImpl<$Res>
    implements $CompleteUserVehicleModelCopyWith<$Res> {
  _$CompleteUserVehicleModelCopyWithImpl(this._self, this._then);

  final CompleteUserVehicleModel _self;
  final $Res Function(CompleteUserVehicleModel) _then;

/// Create a copy of CompleteUserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? vehicle = null,Object? makeName = null,Object? modelName = null,Object? makeNameAr = freezed,Object? modelNameAr = freezed,Object? makeLogoUrl = freezed,}) {
  return _then(_self.copyWith(
vehicle: null == vehicle ? _self.vehicle : vehicle // ignore: cast_nullable_to_non_nullable
as UserVehicleModel,makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,makeNameAr: freezed == makeNameAr ? _self.makeNameAr : makeNameAr // ignore: cast_nullable_to_non_nullable
as String?,modelNameAr: freezed == modelNameAr ? _self.modelNameAr : modelNameAr // ignore: cast_nullable_to_non_nullable
as String?,makeLogoUrl: freezed == makeLogoUrl ? _self.makeLogoUrl : makeLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of CompleteUserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserVehicleModelCopyWith<$Res> get vehicle {
  
  return $UserVehicleModelCopyWith<$Res>(_self.vehicle, (value) {
    return _then(_self.copyWith(vehicle: value));
  });
}
}


/// Adds pattern-matching-related methods to [CompleteUserVehicleModel].
extension CompleteUserVehicleModelPatterns on CompleteUserVehicleModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CompleteUserVehicleModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CompleteUserVehicleModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CompleteUserVehicleModel value)  $default,){
final _that = this;
switch (_that) {
case _CompleteUserVehicleModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CompleteUserVehicleModel value)?  $default,){
final _that = this;
switch (_that) {
case _CompleteUserVehicleModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( UserVehicleModel vehicle,  String makeName,  String modelName,  String? makeNameAr,  String? modelNameAr,  String? makeLogoUrl)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CompleteUserVehicleModel() when $default != null:
return $default(_that.vehicle,_that.makeName,_that.modelName,_that.makeNameAr,_that.modelNameAr,_that.makeLogoUrl);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( UserVehicleModel vehicle,  String makeName,  String modelName,  String? makeNameAr,  String? modelNameAr,  String? makeLogoUrl)  $default,) {final _that = this;
switch (_that) {
case _CompleteUserVehicleModel():
return $default(_that.vehicle,_that.makeName,_that.modelName,_that.makeNameAr,_that.modelNameAr,_that.makeLogoUrl);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( UserVehicleModel vehicle,  String makeName,  String modelName,  String? makeNameAr,  String? modelNameAr,  String? makeLogoUrl)?  $default,) {final _that = this;
switch (_that) {
case _CompleteUserVehicleModel() when $default != null:
return $default(_that.vehicle,_that.makeName,_that.modelName,_that.makeNameAr,_that.modelNameAr,_that.makeLogoUrl);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _CompleteUserVehicleModel implements CompleteUserVehicleModel {
  const _CompleteUserVehicleModel({required this.vehicle, required this.makeName, required this.modelName, this.makeNameAr, this.modelNameAr, this.makeLogoUrl});
  factory _CompleteUserVehicleModel.fromJson(Map<String, dynamic> json) => _$CompleteUserVehicleModelFromJson(json);

@override final  UserVehicleModel vehicle;
@override final  String makeName;
@override final  String modelName;
@override final  String? makeNameAr;
@override final  String? modelNameAr;
@override final  String? makeLogoUrl;

/// Create a copy of CompleteUserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CompleteUserVehicleModelCopyWith<_CompleteUserVehicleModel> get copyWith => __$CompleteUserVehicleModelCopyWithImpl<_CompleteUserVehicleModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CompleteUserVehicleModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CompleteUserVehicleModel&&(identical(other.vehicle, vehicle) || other.vehicle == vehicle)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.makeNameAr, makeNameAr) || other.makeNameAr == makeNameAr)&&(identical(other.modelNameAr, modelNameAr) || other.modelNameAr == modelNameAr)&&(identical(other.makeLogoUrl, makeLogoUrl) || other.makeLogoUrl == makeLogoUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,vehicle,makeName,modelName,makeNameAr,modelNameAr,makeLogoUrl);

@override
String toString() {
  return 'CompleteUserVehicleModel(vehicle: $vehicle, makeName: $makeName, modelName: $modelName, makeNameAr: $makeNameAr, modelNameAr: $modelNameAr, makeLogoUrl: $makeLogoUrl)';
}


}

/// @nodoc
abstract mixin class _$CompleteUserVehicleModelCopyWith<$Res> implements $CompleteUserVehicleModelCopyWith<$Res> {
  factory _$CompleteUserVehicleModelCopyWith(_CompleteUserVehicleModel value, $Res Function(_CompleteUserVehicleModel) _then) = __$CompleteUserVehicleModelCopyWithImpl;
@override @useResult
$Res call({
 UserVehicleModel vehicle, String makeName, String modelName, String? makeNameAr, String? modelNameAr, String? makeLogoUrl
});


@override $UserVehicleModelCopyWith<$Res> get vehicle;

}
/// @nodoc
class __$CompleteUserVehicleModelCopyWithImpl<$Res>
    implements _$CompleteUserVehicleModelCopyWith<$Res> {
  __$CompleteUserVehicleModelCopyWithImpl(this._self, this._then);

  final _CompleteUserVehicleModel _self;
  final $Res Function(_CompleteUserVehicleModel) _then;

/// Create a copy of CompleteUserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? vehicle = null,Object? makeName = null,Object? modelName = null,Object? makeNameAr = freezed,Object? modelNameAr = freezed,Object? makeLogoUrl = freezed,}) {
  return _then(_CompleteUserVehicleModel(
vehicle: null == vehicle ? _self.vehicle : vehicle // ignore: cast_nullable_to_non_nullable
as UserVehicleModel,makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,makeNameAr: freezed == makeNameAr ? _self.makeNameAr : makeNameAr // ignore: cast_nullable_to_non_nullable
as String?,modelNameAr: freezed == modelNameAr ? _self.modelNameAr : modelNameAr // ignore: cast_nullable_to_non_nullable
as String?,makeLogoUrl: freezed == makeLogoUrl ? _self.makeLogoUrl : makeLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of CompleteUserVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserVehicleModelCopyWith<$Res> get vehicle {
  
  return $UserVehicleModelCopyWith<$Res>(_self.vehicle, (value) {
    return _then(_self.copyWith(vehicle: value));
  });
}
}

// dart format on
