// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_vehicle.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserVehicle _$UserVehicleFromJson(Map<String, dynamic> json) => _UserVehicle(
  userId: json['user_id'] as String,
  make: json['make'] as String,
  model: json['model'] as String,
  id: (json['id'] as num?)?.toInt(),
  year: (json['year'] as num?)?.toInt(),
  trim: json['trim'] as String?,
  engine: json['engine'] as String?,
  transmission: json['transmission'] as String?,
  fuelType: json['fuel_type'] as String?,
  mileage: (json['mileage'] as num?)?.toInt(),
  bodyType: json['body_type'] as String?,
  vin: json['vin'] as String?,
  imageUrl: json['image_url'] as String?,
  images:
      (json['images'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  colors:
      (json['colors'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ) ??
      const {},
  specifications: json['specifications'] as Map<String, dynamic>? ?? const {},
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
  makeRefId: (json['make_ref_id'] as num?)?.toInt(),
  modelRefId: (json['model_ref_id'] as num?)?.toInt(),
  fuelTypeRefId: (json['fuel_type_ref_id'] as num?)?.toInt(),
  transmissionRefId: (json['transmission_ref_id'] as num?)?.toInt(),
  engineConfigRefId: (json['engine_config_ref_id'] as num?)?.toInt(),
);

Map<String, dynamic> _$UserVehicleToJson(_UserVehicle instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'make': instance.make,
      'model': instance.model,
      'id': instance.id,
      'year': instance.year,
      'trim': instance.trim,
      'engine': instance.engine,
      'transmission': instance.transmission,
      'fuel_type': instance.fuelType,
      'mileage': instance.mileage,
      'body_type': instance.bodyType,
      'vin': instance.vin,
      'image_url': instance.imageUrl,
      'images': instance.images,
      'colors': instance.colors,
      'specifications': instance.specifications,
      'is_deleted': instance.isDeleted,
      'make_ref_id': instance.makeRefId,
      'model_ref_id': instance.modelRefId,
      'fuel_type_ref_id': instance.fuelTypeRefId,
      'transmission_ref_id': instance.transmissionRefId,
      'engine_config_ref_id': instance.engineConfigRefId,
    };
