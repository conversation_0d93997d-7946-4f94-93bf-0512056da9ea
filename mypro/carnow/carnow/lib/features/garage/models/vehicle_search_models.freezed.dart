// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_search_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleSuggestion implements DiagnosticableTreeMixin {

 String get type; String get name; String get display; String? get subtitle; String? get arabicName; String? get make; String? get model; int? get value; bool get isSelected; bool get isLoading;
/// Create a copy of VehicleSuggestion
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleSuggestionCopyWith<VehicleSuggestion> get copyWith => _$VehicleSuggestionCopyWithImpl<VehicleSuggestion>(this as VehicleSuggestion, _$identity);

  /// Serializes this VehicleSuggestion to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VehicleSuggestion'))
    ..add(DiagnosticsProperty('type', type))..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('display', display))..add(DiagnosticsProperty('subtitle', subtitle))..add(DiagnosticsProperty('arabicName', arabicName))..add(DiagnosticsProperty('make', make))..add(DiagnosticsProperty('model', model))..add(DiagnosticsProperty('value', value))..add(DiagnosticsProperty('isSelected', isSelected))..add(DiagnosticsProperty('isLoading', isLoading));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleSuggestion&&(identical(other.type, type) || other.type == type)&&(identical(other.name, name) || other.name == name)&&(identical(other.display, display) || other.display == display)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.arabicName, arabicName) || other.arabicName == arabicName)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.value, value) || other.value == value)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,name,display,subtitle,arabicName,make,model,value,isSelected,isLoading);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VehicleSuggestion(type: $type, name: $name, display: $display, subtitle: $subtitle, arabicName: $arabicName, make: $make, model: $model, value: $value, isSelected: $isSelected, isLoading: $isLoading)';
}


}

/// @nodoc
abstract mixin class $VehicleSuggestionCopyWith<$Res>  {
  factory $VehicleSuggestionCopyWith(VehicleSuggestion value, $Res Function(VehicleSuggestion) _then) = _$VehicleSuggestionCopyWithImpl;
@useResult
$Res call({
 String type, String name, String display, String? subtitle, String? arabicName, String? make, String? model, int? value, bool isSelected, bool isLoading
});




}
/// @nodoc
class _$VehicleSuggestionCopyWithImpl<$Res>
    implements $VehicleSuggestionCopyWith<$Res> {
  _$VehicleSuggestionCopyWithImpl(this._self, this._then);

  final VehicleSuggestion _self;
  final $Res Function(VehicleSuggestion) _then;

/// Create a copy of VehicleSuggestion
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? name = null,Object? display = null,Object? subtitle = freezed,Object? arabicName = freezed,Object? make = freezed,Object? model = freezed,Object? value = freezed,Object? isSelected = null,Object? isLoading = null,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,display: null == display ? _self.display : display // ignore: cast_nullable_to_non_nullable
as String,subtitle: freezed == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String?,arabicName: freezed == arabicName ? _self.arabicName : arabicName // ignore: cast_nullable_to_non_nullable
as String?,make: freezed == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,value: freezed == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as int?,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleSuggestion].
extension VehicleSuggestionPatterns on VehicleSuggestion {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleSuggestion value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleSuggestion() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleSuggestion value)  $default,){
final _that = this;
switch (_that) {
case _VehicleSuggestion():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleSuggestion value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleSuggestion() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String type,  String name,  String display,  String? subtitle,  String? arabicName,  String? make,  String? model,  int? value,  bool isSelected,  bool isLoading)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleSuggestion() when $default != null:
return $default(_that.type,_that.name,_that.display,_that.subtitle,_that.arabicName,_that.make,_that.model,_that.value,_that.isSelected,_that.isLoading);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String type,  String name,  String display,  String? subtitle,  String? arabicName,  String? make,  String? model,  int? value,  bool isSelected,  bool isLoading)  $default,) {final _that = this;
switch (_that) {
case _VehicleSuggestion():
return $default(_that.type,_that.name,_that.display,_that.subtitle,_that.arabicName,_that.make,_that.model,_that.value,_that.isSelected,_that.isLoading);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String type,  String name,  String display,  String? subtitle,  String? arabicName,  String? make,  String? model,  int? value,  bool isSelected,  bool isLoading)?  $default,) {final _that = this;
switch (_that) {
case _VehicleSuggestion() when $default != null:
return $default(_that.type,_that.name,_that.display,_that.subtitle,_that.arabicName,_that.make,_that.model,_that.value,_that.isSelected,_that.isLoading);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleSuggestion with DiagnosticableTreeMixin implements VehicleSuggestion {
  const _VehicleSuggestion({required this.type, required this.name, required this.display, this.subtitle, this.arabicName, this.make, this.model, this.value, this.isSelected = false, this.isLoading = false});
  factory _VehicleSuggestion.fromJson(Map<String, dynamic> json) => _$VehicleSuggestionFromJson(json);

@override final  String type;
@override final  String name;
@override final  String display;
@override final  String? subtitle;
@override final  String? arabicName;
@override final  String? make;
@override final  String? model;
@override final  int? value;
@override@JsonKey() final  bool isSelected;
@override@JsonKey() final  bool isLoading;

/// Create a copy of VehicleSuggestion
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleSuggestionCopyWith<_VehicleSuggestion> get copyWith => __$VehicleSuggestionCopyWithImpl<_VehicleSuggestion>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleSuggestionToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VehicleSuggestion'))
    ..add(DiagnosticsProperty('type', type))..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('display', display))..add(DiagnosticsProperty('subtitle', subtitle))..add(DiagnosticsProperty('arabicName', arabicName))..add(DiagnosticsProperty('make', make))..add(DiagnosticsProperty('model', model))..add(DiagnosticsProperty('value', value))..add(DiagnosticsProperty('isSelected', isSelected))..add(DiagnosticsProperty('isLoading', isLoading));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleSuggestion&&(identical(other.type, type) || other.type == type)&&(identical(other.name, name) || other.name == name)&&(identical(other.display, display) || other.display == display)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.arabicName, arabicName) || other.arabicName == arabicName)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.value, value) || other.value == value)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,name,display,subtitle,arabicName,make,model,value,isSelected,isLoading);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VehicleSuggestion(type: $type, name: $name, display: $display, subtitle: $subtitle, arabicName: $arabicName, make: $make, model: $model, value: $value, isSelected: $isSelected, isLoading: $isLoading)';
}


}

/// @nodoc
abstract mixin class _$VehicleSuggestionCopyWith<$Res> implements $VehicleSuggestionCopyWith<$Res> {
  factory _$VehicleSuggestionCopyWith(_VehicleSuggestion value, $Res Function(_VehicleSuggestion) _then) = __$VehicleSuggestionCopyWithImpl;
@override @useResult
$Res call({
 String type, String name, String display, String? subtitle, String? arabicName, String? make, String? model, int? value, bool isSelected, bool isLoading
});




}
/// @nodoc
class __$VehicleSuggestionCopyWithImpl<$Res>
    implements _$VehicleSuggestionCopyWith<$Res> {
  __$VehicleSuggestionCopyWithImpl(this._self, this._then);

  final _VehicleSuggestion _self;
  final $Res Function(_VehicleSuggestion) _then;

/// Create a copy of VehicleSuggestion
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? name = null,Object? display = null,Object? subtitle = freezed,Object? arabicName = freezed,Object? make = freezed,Object? model = freezed,Object? value = freezed,Object? isSelected = null,Object? isLoading = null,}) {
  return _then(_VehicleSuggestion(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,display: null == display ? _self.display : display // ignore: cast_nullable_to_non_nullable
as String,subtitle: freezed == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String?,arabicName: freezed == arabicName ? _self.arabicName : arabicName // ignore: cast_nullable_to_non_nullable
as String?,make: freezed == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,value: freezed == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as int?,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$VehicleSearchState implements DiagnosticableTreeMixin {

 String get query; List<VehicleSuggestion> get suggestions; bool get isLoading; bool get hasError; String? get errorMessage; VehicleSuggestion? get selectedSuggestion; int get currentStep;
/// Create a copy of VehicleSearchState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleSearchStateCopyWith<VehicleSearchState> get copyWith => _$VehicleSearchStateCopyWithImpl<VehicleSearchState>(this as VehicleSearchState, _$identity);

  /// Serializes this VehicleSearchState to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VehicleSearchState'))
    ..add(DiagnosticsProperty('query', query))..add(DiagnosticsProperty('suggestions', suggestions))..add(DiagnosticsProperty('isLoading', isLoading))..add(DiagnosticsProperty('hasError', hasError))..add(DiagnosticsProperty('errorMessage', errorMessage))..add(DiagnosticsProperty('selectedSuggestion', selectedSuggestion))..add(DiagnosticsProperty('currentStep', currentStep));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleSearchState&&(identical(other.query, query) || other.query == query)&&const DeepCollectionEquality().equals(other.suggestions, suggestions)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.selectedSuggestion, selectedSuggestion) || other.selectedSuggestion == selectedSuggestion)&&(identical(other.currentStep, currentStep) || other.currentStep == currentStep));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,const DeepCollectionEquality().hash(suggestions),isLoading,hasError,errorMessage,selectedSuggestion,currentStep);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VehicleSearchState(query: $query, suggestions: $suggestions, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, selectedSuggestion: $selectedSuggestion, currentStep: $currentStep)';
}


}

/// @nodoc
abstract mixin class $VehicleSearchStateCopyWith<$Res>  {
  factory $VehicleSearchStateCopyWith(VehicleSearchState value, $Res Function(VehicleSearchState) _then) = _$VehicleSearchStateCopyWithImpl;
@useResult
$Res call({
 String query, List<VehicleSuggestion> suggestions, bool isLoading, bool hasError, String? errorMessage, VehicleSuggestion? selectedSuggestion, int currentStep
});


$VehicleSuggestionCopyWith<$Res>? get selectedSuggestion;

}
/// @nodoc
class _$VehicleSearchStateCopyWithImpl<$Res>
    implements $VehicleSearchStateCopyWith<$Res> {
  _$VehicleSearchStateCopyWithImpl(this._self, this._then);

  final VehicleSearchState _self;
  final $Res Function(VehicleSearchState) _then;

/// Create a copy of VehicleSearchState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? query = null,Object? suggestions = null,Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? selectedSuggestion = freezed,Object? currentStep = null,}) {
  return _then(_self.copyWith(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,suggestions: null == suggestions ? _self.suggestions : suggestions // ignore: cast_nullable_to_non_nullable
as List<VehicleSuggestion>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,selectedSuggestion: freezed == selectedSuggestion ? _self.selectedSuggestion : selectedSuggestion // ignore: cast_nullable_to_non_nullable
as VehicleSuggestion?,currentStep: null == currentStep ? _self.currentStep : currentStep // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of VehicleSearchState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleSuggestionCopyWith<$Res>? get selectedSuggestion {
    if (_self.selectedSuggestion == null) {
    return null;
  }

  return $VehicleSuggestionCopyWith<$Res>(_self.selectedSuggestion!, (value) {
    return _then(_self.copyWith(selectedSuggestion: value));
  });
}
}


/// Adds pattern-matching-related methods to [VehicleSearchState].
extension VehicleSearchStatePatterns on VehicleSearchState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleSearchState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleSearchState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleSearchState value)  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleSearchState value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String query,  List<VehicleSuggestion> suggestions,  bool isLoading,  bool hasError,  String? errorMessage,  VehicleSuggestion? selectedSuggestion,  int currentStep)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleSearchState() when $default != null:
return $default(_that.query,_that.suggestions,_that.isLoading,_that.hasError,_that.errorMessage,_that.selectedSuggestion,_that.currentStep);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String query,  List<VehicleSuggestion> suggestions,  bool isLoading,  bool hasError,  String? errorMessage,  VehicleSuggestion? selectedSuggestion,  int currentStep)  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchState():
return $default(_that.query,_that.suggestions,_that.isLoading,_that.hasError,_that.errorMessage,_that.selectedSuggestion,_that.currentStep);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String query,  List<VehicleSuggestion> suggestions,  bool isLoading,  bool hasError,  String? errorMessage,  VehicleSuggestion? selectedSuggestion,  int currentStep)?  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchState() when $default != null:
return $default(_that.query,_that.suggestions,_that.isLoading,_that.hasError,_that.errorMessage,_that.selectedSuggestion,_that.currentStep);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleSearchState with DiagnosticableTreeMixin implements VehicleSearchState {
  const _VehicleSearchState({this.query = '', final  List<VehicleSuggestion> suggestions = const [], this.isLoading = false, this.hasError = false, this.errorMessage, this.selectedSuggestion, this.currentStep = 0}): _suggestions = suggestions;
  factory _VehicleSearchState.fromJson(Map<String, dynamic> json) => _$VehicleSearchStateFromJson(json);

@override@JsonKey() final  String query;
 final  List<VehicleSuggestion> _suggestions;
@override@JsonKey() List<VehicleSuggestion> get suggestions {
  if (_suggestions is EqualUnmodifiableListView) return _suggestions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_suggestions);
}

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool hasError;
@override final  String? errorMessage;
@override final  VehicleSuggestion? selectedSuggestion;
@override@JsonKey() final  int currentStep;

/// Create a copy of VehicleSearchState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleSearchStateCopyWith<_VehicleSearchState> get copyWith => __$VehicleSearchStateCopyWithImpl<_VehicleSearchState>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleSearchStateToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VehicleSearchState'))
    ..add(DiagnosticsProperty('query', query))..add(DiagnosticsProperty('suggestions', suggestions))..add(DiagnosticsProperty('isLoading', isLoading))..add(DiagnosticsProperty('hasError', hasError))..add(DiagnosticsProperty('errorMessage', errorMessage))..add(DiagnosticsProperty('selectedSuggestion', selectedSuggestion))..add(DiagnosticsProperty('currentStep', currentStep));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleSearchState&&(identical(other.query, query) || other.query == query)&&const DeepCollectionEquality().equals(other._suggestions, _suggestions)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.selectedSuggestion, selectedSuggestion) || other.selectedSuggestion == selectedSuggestion)&&(identical(other.currentStep, currentStep) || other.currentStep == currentStep));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,const DeepCollectionEquality().hash(_suggestions),isLoading,hasError,errorMessage,selectedSuggestion,currentStep);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VehicleSearchState(query: $query, suggestions: $suggestions, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, selectedSuggestion: $selectedSuggestion, currentStep: $currentStep)';
}


}

/// @nodoc
abstract mixin class _$VehicleSearchStateCopyWith<$Res> implements $VehicleSearchStateCopyWith<$Res> {
  factory _$VehicleSearchStateCopyWith(_VehicleSearchState value, $Res Function(_VehicleSearchState) _then) = __$VehicleSearchStateCopyWithImpl;
@override @useResult
$Res call({
 String query, List<VehicleSuggestion> suggestions, bool isLoading, bool hasError, String? errorMessage, VehicleSuggestion? selectedSuggestion, int currentStep
});


@override $VehicleSuggestionCopyWith<$Res>? get selectedSuggestion;

}
/// @nodoc
class __$VehicleSearchStateCopyWithImpl<$Res>
    implements _$VehicleSearchStateCopyWith<$Res> {
  __$VehicleSearchStateCopyWithImpl(this._self, this._then);

  final _VehicleSearchState _self;
  final $Res Function(_VehicleSearchState) _then;

/// Create a copy of VehicleSearchState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? query = null,Object? suggestions = null,Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? selectedSuggestion = freezed,Object? currentStep = null,}) {
  return _then(_VehicleSearchState(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,suggestions: null == suggestions ? _self._suggestions : suggestions // ignore: cast_nullable_to_non_nullable
as List<VehicleSuggestion>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,selectedSuggestion: freezed == selectedSuggestion ? _self.selectedSuggestion : selectedSuggestion // ignore: cast_nullable_to_non_nullable
as VehicleSuggestion?,currentStep: null == currentStep ? _self.currentStep : currentStep // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of VehicleSearchState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleSuggestionCopyWith<$Res>? get selectedSuggestion {
    if (_self.selectedSuggestion == null) {
    return null;
  }

  return $VehicleSuggestionCopyWith<$Res>(_self.selectedSuggestion!, (value) {
    return _then(_self.copyWith(selectedSuggestion: value));
  });
}
}


/// @nodoc
mixin _$SelectedVehicleDetails implements DiagnosticableTreeMixin {

 int get year; String get make; String get model; String? get trim; String? get engine; String? get imageUrl; String? get fullName; String? get subtitle; bool get isConfirmed; bool get isSaving; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of SelectedVehicleDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SelectedVehicleDetailsCopyWith<SelectedVehicleDetails> get copyWith => _$SelectedVehicleDetailsCopyWithImpl<SelectedVehicleDetails>(this as SelectedVehicleDetails, _$identity);

  /// Serializes this SelectedVehicleDetails to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SelectedVehicleDetails'))
    ..add(DiagnosticsProperty('year', year))..add(DiagnosticsProperty('make', make))..add(DiagnosticsProperty('model', model))..add(DiagnosticsProperty('trim', trim))..add(DiagnosticsProperty('engine', engine))..add(DiagnosticsProperty('imageUrl', imageUrl))..add(DiagnosticsProperty('fullName', fullName))..add(DiagnosticsProperty('subtitle', subtitle))..add(DiagnosticsProperty('isConfirmed', isConfirmed))..add(DiagnosticsProperty('isSaving', isSaving))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SelectedVehicleDetails&&(identical(other.year, year) || other.year == year)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.isConfirmed, isConfirmed) || other.isConfirmed == isConfirmed)&&(identical(other.isSaving, isSaving) || other.isSaving == isSaving)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,year,make,model,trim,engine,imageUrl,fullName,subtitle,isConfirmed,isSaving,createdAt,updatedAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SelectedVehicleDetails(year: $year, make: $make, model: $model, trim: $trim, engine: $engine, imageUrl: $imageUrl, fullName: $fullName, subtitle: $subtitle, isConfirmed: $isConfirmed, isSaving: $isSaving, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SelectedVehicleDetailsCopyWith<$Res>  {
  factory $SelectedVehicleDetailsCopyWith(SelectedVehicleDetails value, $Res Function(SelectedVehicleDetails) _then) = _$SelectedVehicleDetailsCopyWithImpl;
@useResult
$Res call({
 int year, String make, String model, String? trim, String? engine, String? imageUrl, String? fullName, String? subtitle, bool isConfirmed, bool isSaving, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$SelectedVehicleDetailsCopyWithImpl<$Res>
    implements $SelectedVehicleDetailsCopyWith<$Res> {
  _$SelectedVehicleDetailsCopyWithImpl(this._self, this._then);

  final SelectedVehicleDetails _self;
  final $Res Function(SelectedVehicleDetails) _then;

/// Create a copy of SelectedVehicleDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? year = null,Object? make = null,Object? model = null,Object? trim = freezed,Object? engine = freezed,Object? imageUrl = freezed,Object? fullName = freezed,Object? subtitle = freezed,Object? isConfirmed = null,Object? isSaving = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,make: null == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,fullName: freezed == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String?,subtitle: freezed == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String?,isConfirmed: null == isConfirmed ? _self.isConfirmed : isConfirmed // ignore: cast_nullable_to_non_nullable
as bool,isSaving: null == isSaving ? _self.isSaving : isSaving // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SelectedVehicleDetails].
extension SelectedVehicleDetailsPatterns on SelectedVehicleDetails {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SelectedVehicleDetails value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SelectedVehicleDetails() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SelectedVehicleDetails value)  $default,){
final _that = this;
switch (_that) {
case _SelectedVehicleDetails():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SelectedVehicleDetails value)?  $default,){
final _that = this;
switch (_that) {
case _SelectedVehicleDetails() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int year,  String make,  String model,  String? trim,  String? engine,  String? imageUrl,  String? fullName,  String? subtitle,  bool isConfirmed,  bool isSaving,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SelectedVehicleDetails() when $default != null:
return $default(_that.year,_that.make,_that.model,_that.trim,_that.engine,_that.imageUrl,_that.fullName,_that.subtitle,_that.isConfirmed,_that.isSaving,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int year,  String make,  String model,  String? trim,  String? engine,  String? imageUrl,  String? fullName,  String? subtitle,  bool isConfirmed,  bool isSaving,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SelectedVehicleDetails():
return $default(_that.year,_that.make,_that.model,_that.trim,_that.engine,_that.imageUrl,_that.fullName,_that.subtitle,_that.isConfirmed,_that.isSaving,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int year,  String make,  String model,  String? trim,  String? engine,  String? imageUrl,  String? fullName,  String? subtitle,  bool isConfirmed,  bool isSaving,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SelectedVehicleDetails() when $default != null:
return $default(_that.year,_that.make,_that.model,_that.trim,_that.engine,_that.imageUrl,_that.fullName,_that.subtitle,_that.isConfirmed,_that.isSaving,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SelectedVehicleDetails with DiagnosticableTreeMixin implements SelectedVehicleDetails {
  const _SelectedVehicleDetails({required this.year, required this.make, required this.model, this.trim, this.engine, this.imageUrl, this.fullName, this.subtitle, this.isConfirmed = false, this.isSaving = false, this.createdAt, this.updatedAt});
  factory _SelectedVehicleDetails.fromJson(Map<String, dynamic> json) => _$SelectedVehicleDetailsFromJson(json);

@override final  int year;
@override final  String make;
@override final  String model;
@override final  String? trim;
@override final  String? engine;
@override final  String? imageUrl;
@override final  String? fullName;
@override final  String? subtitle;
@override@JsonKey() final  bool isConfirmed;
@override@JsonKey() final  bool isSaving;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of SelectedVehicleDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SelectedVehicleDetailsCopyWith<_SelectedVehicleDetails> get copyWith => __$SelectedVehicleDetailsCopyWithImpl<_SelectedVehicleDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SelectedVehicleDetailsToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SelectedVehicleDetails'))
    ..add(DiagnosticsProperty('year', year))..add(DiagnosticsProperty('make', make))..add(DiagnosticsProperty('model', model))..add(DiagnosticsProperty('trim', trim))..add(DiagnosticsProperty('engine', engine))..add(DiagnosticsProperty('imageUrl', imageUrl))..add(DiagnosticsProperty('fullName', fullName))..add(DiagnosticsProperty('subtitle', subtitle))..add(DiagnosticsProperty('isConfirmed', isConfirmed))..add(DiagnosticsProperty('isSaving', isSaving))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SelectedVehicleDetails&&(identical(other.year, year) || other.year == year)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.isConfirmed, isConfirmed) || other.isConfirmed == isConfirmed)&&(identical(other.isSaving, isSaving) || other.isSaving == isSaving)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,year,make,model,trim,engine,imageUrl,fullName,subtitle,isConfirmed,isSaving,createdAt,updatedAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SelectedVehicleDetails(year: $year, make: $make, model: $model, trim: $trim, engine: $engine, imageUrl: $imageUrl, fullName: $fullName, subtitle: $subtitle, isConfirmed: $isConfirmed, isSaving: $isSaving, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SelectedVehicleDetailsCopyWith<$Res> implements $SelectedVehicleDetailsCopyWith<$Res> {
  factory _$SelectedVehicleDetailsCopyWith(_SelectedVehicleDetails value, $Res Function(_SelectedVehicleDetails) _then) = __$SelectedVehicleDetailsCopyWithImpl;
@override @useResult
$Res call({
 int year, String make, String model, String? trim, String? engine, String? imageUrl, String? fullName, String? subtitle, bool isConfirmed, bool isSaving, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$SelectedVehicleDetailsCopyWithImpl<$Res>
    implements _$SelectedVehicleDetailsCopyWith<$Res> {
  __$SelectedVehicleDetailsCopyWithImpl(this._self, this._then);

  final _SelectedVehicleDetails _self;
  final $Res Function(_SelectedVehicleDetails) _then;

/// Create a copy of SelectedVehicleDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? year = null,Object? make = null,Object? model = null,Object? trim = freezed,Object? engine = freezed,Object? imageUrl = freezed,Object? fullName = freezed,Object? subtitle = freezed,Object? isConfirmed = null,Object? isSaving = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SelectedVehicleDetails(
year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,make: null == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,fullName: freezed == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String?,subtitle: freezed == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String?,isConfirmed: null == isConfirmed ? _self.isConfirmed : isConfirmed // ignore: cast_nullable_to_non_nullable
as bool,isSaving: null == isSaving ? _self.isSaving : isSaving // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$VehicleType implements DiagnosticableTreeMixin {

 String get id; String get title; String get subtitle; String get iconName; int get colorValue; bool get isSelected;
/// Create a copy of VehicleType
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleTypeCopyWith<VehicleType> get copyWith => _$VehicleTypeCopyWithImpl<VehicleType>(this as VehicleType, _$identity);

  /// Serializes this VehicleType to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VehicleType'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('title', title))..add(DiagnosticsProperty('subtitle', subtitle))..add(DiagnosticsProperty('iconName', iconName))..add(DiagnosticsProperty('colorValue', colorValue))..add(DiagnosticsProperty('isSelected', isSelected));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleType&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.iconName, iconName) || other.iconName == iconName)&&(identical(other.colorValue, colorValue) || other.colorValue == colorValue)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,title,subtitle,iconName,colorValue,isSelected);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VehicleType(id: $id, title: $title, subtitle: $subtitle, iconName: $iconName, colorValue: $colorValue, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class $VehicleTypeCopyWith<$Res>  {
  factory $VehicleTypeCopyWith(VehicleType value, $Res Function(VehicleType) _then) = _$VehicleTypeCopyWithImpl;
@useResult
$Res call({
 String id, String title, String subtitle, String iconName, int colorValue, bool isSelected
});




}
/// @nodoc
class _$VehicleTypeCopyWithImpl<$Res>
    implements $VehicleTypeCopyWith<$Res> {
  _$VehicleTypeCopyWithImpl(this._self, this._then);

  final VehicleType _self;
  final $Res Function(VehicleType) _then;

/// Create a copy of VehicleType
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? title = null,Object? subtitle = null,Object? iconName = null,Object? colorValue = null,Object? isSelected = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,subtitle: null == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String,iconName: null == iconName ? _self.iconName : iconName // ignore: cast_nullable_to_non_nullable
as String,colorValue: null == colorValue ? _self.colorValue : colorValue // ignore: cast_nullable_to_non_nullable
as int,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleType].
extension VehicleTypePatterns on VehicleType {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleType value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleType() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleType value)  $default,){
final _that = this;
switch (_that) {
case _VehicleType():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleType value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleType() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String title,  String subtitle,  String iconName,  int colorValue,  bool isSelected)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleType() when $default != null:
return $default(_that.id,_that.title,_that.subtitle,_that.iconName,_that.colorValue,_that.isSelected);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String title,  String subtitle,  String iconName,  int colorValue,  bool isSelected)  $default,) {final _that = this;
switch (_that) {
case _VehicleType():
return $default(_that.id,_that.title,_that.subtitle,_that.iconName,_that.colorValue,_that.isSelected);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String title,  String subtitle,  String iconName,  int colorValue,  bool isSelected)?  $default,) {final _that = this;
switch (_that) {
case _VehicleType() when $default != null:
return $default(_that.id,_that.title,_that.subtitle,_that.iconName,_that.colorValue,_that.isSelected);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleType with DiagnosticableTreeMixin implements VehicleType {
  const _VehicleType({required this.id, required this.title, required this.subtitle, required this.iconName, required this.colorValue, this.isSelected = false});
  factory _VehicleType.fromJson(Map<String, dynamic> json) => _$VehicleTypeFromJson(json);

@override final  String id;
@override final  String title;
@override final  String subtitle;
@override final  String iconName;
@override final  int colorValue;
@override@JsonKey() final  bool isSelected;

/// Create a copy of VehicleType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleTypeCopyWith<_VehicleType> get copyWith => __$VehicleTypeCopyWithImpl<_VehicleType>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleTypeToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VehicleType'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('title', title))..add(DiagnosticsProperty('subtitle', subtitle))..add(DiagnosticsProperty('iconName', iconName))..add(DiagnosticsProperty('colorValue', colorValue))..add(DiagnosticsProperty('isSelected', isSelected));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleType&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.iconName, iconName) || other.iconName == iconName)&&(identical(other.colorValue, colorValue) || other.colorValue == colorValue)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,title,subtitle,iconName,colorValue,isSelected);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VehicleType(id: $id, title: $title, subtitle: $subtitle, iconName: $iconName, colorValue: $colorValue, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class _$VehicleTypeCopyWith<$Res> implements $VehicleTypeCopyWith<$Res> {
  factory _$VehicleTypeCopyWith(_VehicleType value, $Res Function(_VehicleType) _then) = __$VehicleTypeCopyWithImpl;
@override @useResult
$Res call({
 String id, String title, String subtitle, String iconName, int colorValue, bool isSelected
});




}
/// @nodoc
class __$VehicleTypeCopyWithImpl<$Res>
    implements _$VehicleTypeCopyWith<$Res> {
  __$VehicleTypeCopyWithImpl(this._self, this._then);

  final _VehicleType _self;
  final $Res Function(_VehicleType) _then;

/// Create a copy of VehicleType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? title = null,Object? subtitle = null,Object? iconName = null,Object? colorValue = null,Object? isSelected = null,}) {
  return _then(_VehicleType(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,subtitle: null == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String,iconName: null == iconName ? _self.iconName : iconName // ignore: cast_nullable_to_non_nullable
as String,colorValue: null == colorValue ? _self.colorValue : colorValue // ignore: cast_nullable_to_non_nullable
as int,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$VehicleSearchError implements DiagnosticableTreeMixin {

 String get message; String get code; String? get details; DateTime? get timestamp; bool get isRetryable;
/// Create a copy of VehicleSearchError
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleSearchErrorCopyWith<VehicleSearchError> get copyWith => _$VehicleSearchErrorCopyWithImpl<VehicleSearchError>(this as VehicleSearchError, _$identity);

  /// Serializes this VehicleSearchError to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VehicleSearchError'))
    ..add(DiagnosticsProperty('message', message))..add(DiagnosticsProperty('code', code))..add(DiagnosticsProperty('details', details))..add(DiagnosticsProperty('timestamp', timestamp))..add(DiagnosticsProperty('isRetryable', isRetryable));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleSearchError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&(identical(other.details, details) || other.details == details)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRetryable, isRetryable) || other.isRetryable == isRetryable));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,message,code,details,timestamp,isRetryable);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VehicleSearchError(message: $message, code: $code, details: $details, timestamp: $timestamp, isRetryable: $isRetryable)';
}


}

/// @nodoc
abstract mixin class $VehicleSearchErrorCopyWith<$Res>  {
  factory $VehicleSearchErrorCopyWith(VehicleSearchError value, $Res Function(VehicleSearchError) _then) = _$VehicleSearchErrorCopyWithImpl;
@useResult
$Res call({
 String message, String code, String? details, DateTime? timestamp, bool isRetryable
});




}
/// @nodoc
class _$VehicleSearchErrorCopyWithImpl<$Res>
    implements $VehicleSearchErrorCopyWith<$Res> {
  _$VehicleSearchErrorCopyWithImpl(this._self, this._then);

  final VehicleSearchError _self;
  final $Res Function(VehicleSearchError) _then;

/// Create a copy of VehicleSearchError
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? message = null,Object? code = null,Object? details = freezed,Object? timestamp = freezed,Object? isRetryable = null,}) {
  return _then(_self.copyWith(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as String?,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,isRetryable: null == isRetryable ? _self.isRetryable : isRetryable // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleSearchError].
extension VehicleSearchErrorPatterns on VehicleSearchError {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleSearchError value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleSearchError() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleSearchError value)  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchError():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleSearchError value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchError() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String message,  String code,  String? details,  DateTime? timestamp,  bool isRetryable)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleSearchError() when $default != null:
return $default(_that.message,_that.code,_that.details,_that.timestamp,_that.isRetryable);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String message,  String code,  String? details,  DateTime? timestamp,  bool isRetryable)  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchError():
return $default(_that.message,_that.code,_that.details,_that.timestamp,_that.isRetryable);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String message,  String code,  String? details,  DateTime? timestamp,  bool isRetryable)?  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchError() when $default != null:
return $default(_that.message,_that.code,_that.details,_that.timestamp,_that.isRetryable);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleSearchError with DiagnosticableTreeMixin implements VehicleSearchError {
  const _VehicleSearchError({required this.message, required this.code, this.details, this.timestamp, this.isRetryable = false});
  factory _VehicleSearchError.fromJson(Map<String, dynamic> json) => _$VehicleSearchErrorFromJson(json);

@override final  String message;
@override final  String code;
@override final  String? details;
@override final  DateTime? timestamp;
@override@JsonKey() final  bool isRetryable;

/// Create a copy of VehicleSearchError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleSearchErrorCopyWith<_VehicleSearchError> get copyWith => __$VehicleSearchErrorCopyWithImpl<_VehicleSearchError>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleSearchErrorToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VehicleSearchError'))
    ..add(DiagnosticsProperty('message', message))..add(DiagnosticsProperty('code', code))..add(DiagnosticsProperty('details', details))..add(DiagnosticsProperty('timestamp', timestamp))..add(DiagnosticsProperty('isRetryable', isRetryable));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleSearchError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&(identical(other.details, details) || other.details == details)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.isRetryable, isRetryable) || other.isRetryable == isRetryable));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,message,code,details,timestamp,isRetryable);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VehicleSearchError(message: $message, code: $code, details: $details, timestamp: $timestamp, isRetryable: $isRetryable)';
}


}

/// @nodoc
abstract mixin class _$VehicleSearchErrorCopyWith<$Res> implements $VehicleSearchErrorCopyWith<$Res> {
  factory _$VehicleSearchErrorCopyWith(_VehicleSearchError value, $Res Function(_VehicleSearchError) _then) = __$VehicleSearchErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String code, String? details, DateTime? timestamp, bool isRetryable
});




}
/// @nodoc
class __$VehicleSearchErrorCopyWithImpl<$Res>
    implements _$VehicleSearchErrorCopyWith<$Res> {
  __$VehicleSearchErrorCopyWithImpl(this._self, this._then);

  final _VehicleSearchError _self;
  final $Res Function(_VehicleSearchError) _then;

/// Create a copy of VehicleSearchError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? code = null,Object? details = freezed,Object? timestamp = freezed,Object? isRetryable = null,}) {
  return _then(_VehicleSearchError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as String?,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,isRetryable: null == isRetryable ? _self.isRetryable : isRetryable // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$SearchPreferences implements DiagnosticableTreeMixin {

 int get maxResults; int get debounceMs; bool get enableCache; bool get enableArabicSearch; List<String> get searchTypes;
/// Create a copy of SearchPreferences
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SearchPreferencesCopyWith<SearchPreferences> get copyWith => _$SearchPreferencesCopyWithImpl<SearchPreferences>(this as SearchPreferences, _$identity);

  /// Serializes this SearchPreferences to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SearchPreferences'))
    ..add(DiagnosticsProperty('maxResults', maxResults))..add(DiagnosticsProperty('debounceMs', debounceMs))..add(DiagnosticsProperty('enableCache', enableCache))..add(DiagnosticsProperty('enableArabicSearch', enableArabicSearch))..add(DiagnosticsProperty('searchTypes', searchTypes));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SearchPreferences&&(identical(other.maxResults, maxResults) || other.maxResults == maxResults)&&(identical(other.debounceMs, debounceMs) || other.debounceMs == debounceMs)&&(identical(other.enableCache, enableCache) || other.enableCache == enableCache)&&(identical(other.enableArabicSearch, enableArabicSearch) || other.enableArabicSearch == enableArabicSearch)&&const DeepCollectionEquality().equals(other.searchTypes, searchTypes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,maxResults,debounceMs,enableCache,enableArabicSearch,const DeepCollectionEquality().hash(searchTypes));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SearchPreferences(maxResults: $maxResults, debounceMs: $debounceMs, enableCache: $enableCache, enableArabicSearch: $enableArabicSearch, searchTypes: $searchTypes)';
}


}

/// @nodoc
abstract mixin class $SearchPreferencesCopyWith<$Res>  {
  factory $SearchPreferencesCopyWith(SearchPreferences value, $Res Function(SearchPreferences) _then) = _$SearchPreferencesCopyWithImpl;
@useResult
$Res call({
 int maxResults, int debounceMs, bool enableCache, bool enableArabicSearch, List<String> searchTypes
});




}
/// @nodoc
class _$SearchPreferencesCopyWithImpl<$Res>
    implements $SearchPreferencesCopyWith<$Res> {
  _$SearchPreferencesCopyWithImpl(this._self, this._then);

  final SearchPreferences _self;
  final $Res Function(SearchPreferences) _then;

/// Create a copy of SearchPreferences
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? maxResults = null,Object? debounceMs = null,Object? enableCache = null,Object? enableArabicSearch = null,Object? searchTypes = null,}) {
  return _then(_self.copyWith(
maxResults: null == maxResults ? _self.maxResults : maxResults // ignore: cast_nullable_to_non_nullable
as int,debounceMs: null == debounceMs ? _self.debounceMs : debounceMs // ignore: cast_nullable_to_non_nullable
as int,enableCache: null == enableCache ? _self.enableCache : enableCache // ignore: cast_nullable_to_non_nullable
as bool,enableArabicSearch: null == enableArabicSearch ? _self.enableArabicSearch : enableArabicSearch // ignore: cast_nullable_to_non_nullable
as bool,searchTypes: null == searchTypes ? _self.searchTypes : searchTypes // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [SearchPreferences].
extension SearchPreferencesPatterns on SearchPreferences {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SearchPreferences value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SearchPreferences() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SearchPreferences value)  $default,){
final _that = this;
switch (_that) {
case _SearchPreferences():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SearchPreferences value)?  $default,){
final _that = this;
switch (_that) {
case _SearchPreferences() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int maxResults,  int debounceMs,  bool enableCache,  bool enableArabicSearch,  List<String> searchTypes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SearchPreferences() when $default != null:
return $default(_that.maxResults,_that.debounceMs,_that.enableCache,_that.enableArabicSearch,_that.searchTypes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int maxResults,  int debounceMs,  bool enableCache,  bool enableArabicSearch,  List<String> searchTypes)  $default,) {final _that = this;
switch (_that) {
case _SearchPreferences():
return $default(_that.maxResults,_that.debounceMs,_that.enableCache,_that.enableArabicSearch,_that.searchTypes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int maxResults,  int debounceMs,  bool enableCache,  bool enableArabicSearch,  List<String> searchTypes)?  $default,) {final _that = this;
switch (_that) {
case _SearchPreferences() when $default != null:
return $default(_that.maxResults,_that.debounceMs,_that.enableCache,_that.enableArabicSearch,_that.searchTypes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SearchPreferences with DiagnosticableTreeMixin implements SearchPreferences {
  const _SearchPreferences({this.maxResults = 20, this.debounceMs = 300, this.enableCache = true, this.enableArabicSearch = true, final  List<String> searchTypes = const ['year', 'make', 'model']}): _searchTypes = searchTypes;
  factory _SearchPreferences.fromJson(Map<String, dynamic> json) => _$SearchPreferencesFromJson(json);

@override@JsonKey() final  int maxResults;
@override@JsonKey() final  int debounceMs;
@override@JsonKey() final  bool enableCache;
@override@JsonKey() final  bool enableArabicSearch;
 final  List<String> _searchTypes;
@override@JsonKey() List<String> get searchTypes {
  if (_searchTypes is EqualUnmodifiableListView) return _searchTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_searchTypes);
}


/// Create a copy of SearchPreferences
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SearchPreferencesCopyWith<_SearchPreferences> get copyWith => __$SearchPreferencesCopyWithImpl<_SearchPreferences>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SearchPreferencesToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SearchPreferences'))
    ..add(DiagnosticsProperty('maxResults', maxResults))..add(DiagnosticsProperty('debounceMs', debounceMs))..add(DiagnosticsProperty('enableCache', enableCache))..add(DiagnosticsProperty('enableArabicSearch', enableArabicSearch))..add(DiagnosticsProperty('searchTypes', searchTypes));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SearchPreferences&&(identical(other.maxResults, maxResults) || other.maxResults == maxResults)&&(identical(other.debounceMs, debounceMs) || other.debounceMs == debounceMs)&&(identical(other.enableCache, enableCache) || other.enableCache == enableCache)&&(identical(other.enableArabicSearch, enableArabicSearch) || other.enableArabicSearch == enableArabicSearch)&&const DeepCollectionEquality().equals(other._searchTypes, _searchTypes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,maxResults,debounceMs,enableCache,enableArabicSearch,const DeepCollectionEquality().hash(_searchTypes));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SearchPreferences(maxResults: $maxResults, debounceMs: $debounceMs, enableCache: $enableCache, enableArabicSearch: $enableArabicSearch, searchTypes: $searchTypes)';
}


}

/// @nodoc
abstract mixin class _$SearchPreferencesCopyWith<$Res> implements $SearchPreferencesCopyWith<$Res> {
  factory _$SearchPreferencesCopyWith(_SearchPreferences value, $Res Function(_SearchPreferences) _then) = __$SearchPreferencesCopyWithImpl;
@override @useResult
$Res call({
 int maxResults, int debounceMs, bool enableCache, bool enableArabicSearch, List<String> searchTypes
});




}
/// @nodoc
class __$SearchPreferencesCopyWithImpl<$Res>
    implements _$SearchPreferencesCopyWith<$Res> {
  __$SearchPreferencesCopyWithImpl(this._self, this._then);

  final _SearchPreferences _self;
  final $Res Function(_SearchPreferences) _then;

/// Create a copy of SearchPreferences
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? maxResults = null,Object? debounceMs = null,Object? enableCache = null,Object? enableArabicSearch = null,Object? searchTypes = null,}) {
  return _then(_SearchPreferences(
maxResults: null == maxResults ? _self.maxResults : maxResults // ignore: cast_nullable_to_non_nullable
as int,debounceMs: null == debounceMs ? _self.debounceMs : debounceMs // ignore: cast_nullable_to_non_nullable
as int,enableCache: null == enableCache ? _self.enableCache : enableCache // ignore: cast_nullable_to_non_nullable
as bool,enableArabicSearch: null == enableArabicSearch ? _self.enableArabicSearch : enableArabicSearch // ignore: cast_nullable_to_non_nullable
as bool,searchTypes: null == searchTypes ? _self._searchTypes : searchTypes // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$SearchAnalytics implements DiagnosticableTreeMixin {

 int get totalSearches; int get successfulSearches; int get failedSearches; double get averageResponseTime; List<String> get popularQueries; DateTime? get lastSearchAt;
/// Create a copy of SearchAnalytics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SearchAnalyticsCopyWith<SearchAnalytics> get copyWith => _$SearchAnalyticsCopyWithImpl<SearchAnalytics>(this as SearchAnalytics, _$identity);

  /// Serializes this SearchAnalytics to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SearchAnalytics'))
    ..add(DiagnosticsProperty('totalSearches', totalSearches))..add(DiagnosticsProperty('successfulSearches', successfulSearches))..add(DiagnosticsProperty('failedSearches', failedSearches))..add(DiagnosticsProperty('averageResponseTime', averageResponseTime))..add(DiagnosticsProperty('popularQueries', popularQueries))..add(DiagnosticsProperty('lastSearchAt', lastSearchAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SearchAnalytics&&(identical(other.totalSearches, totalSearches) || other.totalSearches == totalSearches)&&(identical(other.successfulSearches, successfulSearches) || other.successfulSearches == successfulSearches)&&(identical(other.failedSearches, failedSearches) || other.failedSearches == failedSearches)&&(identical(other.averageResponseTime, averageResponseTime) || other.averageResponseTime == averageResponseTime)&&const DeepCollectionEquality().equals(other.popularQueries, popularQueries)&&(identical(other.lastSearchAt, lastSearchAt) || other.lastSearchAt == lastSearchAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalSearches,successfulSearches,failedSearches,averageResponseTime,const DeepCollectionEquality().hash(popularQueries),lastSearchAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SearchAnalytics(totalSearches: $totalSearches, successfulSearches: $successfulSearches, failedSearches: $failedSearches, averageResponseTime: $averageResponseTime, popularQueries: $popularQueries, lastSearchAt: $lastSearchAt)';
}


}

/// @nodoc
abstract mixin class $SearchAnalyticsCopyWith<$Res>  {
  factory $SearchAnalyticsCopyWith(SearchAnalytics value, $Res Function(SearchAnalytics) _then) = _$SearchAnalyticsCopyWithImpl;
@useResult
$Res call({
 int totalSearches, int successfulSearches, int failedSearches, double averageResponseTime, List<String> popularQueries, DateTime? lastSearchAt
});




}
/// @nodoc
class _$SearchAnalyticsCopyWithImpl<$Res>
    implements $SearchAnalyticsCopyWith<$Res> {
  _$SearchAnalyticsCopyWithImpl(this._self, this._then);

  final SearchAnalytics _self;
  final $Res Function(SearchAnalytics) _then;

/// Create a copy of SearchAnalytics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalSearches = null,Object? successfulSearches = null,Object? failedSearches = null,Object? averageResponseTime = null,Object? popularQueries = null,Object? lastSearchAt = freezed,}) {
  return _then(_self.copyWith(
totalSearches: null == totalSearches ? _self.totalSearches : totalSearches // ignore: cast_nullable_to_non_nullable
as int,successfulSearches: null == successfulSearches ? _self.successfulSearches : successfulSearches // ignore: cast_nullable_to_non_nullable
as int,failedSearches: null == failedSearches ? _self.failedSearches : failedSearches // ignore: cast_nullable_to_non_nullable
as int,averageResponseTime: null == averageResponseTime ? _self.averageResponseTime : averageResponseTime // ignore: cast_nullable_to_non_nullable
as double,popularQueries: null == popularQueries ? _self.popularQueries : popularQueries // ignore: cast_nullable_to_non_nullable
as List<String>,lastSearchAt: freezed == lastSearchAt ? _self.lastSearchAt : lastSearchAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SearchAnalytics].
extension SearchAnalyticsPatterns on SearchAnalytics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SearchAnalytics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SearchAnalytics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SearchAnalytics value)  $default,){
final _that = this;
switch (_that) {
case _SearchAnalytics():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SearchAnalytics value)?  $default,){
final _that = this;
switch (_that) {
case _SearchAnalytics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalSearches,  int successfulSearches,  int failedSearches,  double averageResponseTime,  List<String> popularQueries,  DateTime? lastSearchAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SearchAnalytics() when $default != null:
return $default(_that.totalSearches,_that.successfulSearches,_that.failedSearches,_that.averageResponseTime,_that.popularQueries,_that.lastSearchAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalSearches,  int successfulSearches,  int failedSearches,  double averageResponseTime,  List<String> popularQueries,  DateTime? lastSearchAt)  $default,) {final _that = this;
switch (_that) {
case _SearchAnalytics():
return $default(_that.totalSearches,_that.successfulSearches,_that.failedSearches,_that.averageResponseTime,_that.popularQueries,_that.lastSearchAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalSearches,  int successfulSearches,  int failedSearches,  double averageResponseTime,  List<String> popularQueries,  DateTime? lastSearchAt)?  $default,) {final _that = this;
switch (_that) {
case _SearchAnalytics() when $default != null:
return $default(_that.totalSearches,_that.successfulSearches,_that.failedSearches,_that.averageResponseTime,_that.popularQueries,_that.lastSearchAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SearchAnalytics with DiagnosticableTreeMixin implements SearchAnalytics {
  const _SearchAnalytics({this.totalSearches = 0, this.successfulSearches = 0, this.failedSearches = 0, this.averageResponseTime = 0, final  List<String> popularQueries = const [], this.lastSearchAt}): _popularQueries = popularQueries;
  factory _SearchAnalytics.fromJson(Map<String, dynamic> json) => _$SearchAnalyticsFromJson(json);

@override@JsonKey() final  int totalSearches;
@override@JsonKey() final  int successfulSearches;
@override@JsonKey() final  int failedSearches;
@override@JsonKey() final  double averageResponseTime;
 final  List<String> _popularQueries;
@override@JsonKey() List<String> get popularQueries {
  if (_popularQueries is EqualUnmodifiableListView) return _popularQueries;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_popularQueries);
}

@override final  DateTime? lastSearchAt;

/// Create a copy of SearchAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SearchAnalyticsCopyWith<_SearchAnalytics> get copyWith => __$SearchAnalyticsCopyWithImpl<_SearchAnalytics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SearchAnalyticsToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SearchAnalytics'))
    ..add(DiagnosticsProperty('totalSearches', totalSearches))..add(DiagnosticsProperty('successfulSearches', successfulSearches))..add(DiagnosticsProperty('failedSearches', failedSearches))..add(DiagnosticsProperty('averageResponseTime', averageResponseTime))..add(DiagnosticsProperty('popularQueries', popularQueries))..add(DiagnosticsProperty('lastSearchAt', lastSearchAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SearchAnalytics&&(identical(other.totalSearches, totalSearches) || other.totalSearches == totalSearches)&&(identical(other.successfulSearches, successfulSearches) || other.successfulSearches == successfulSearches)&&(identical(other.failedSearches, failedSearches) || other.failedSearches == failedSearches)&&(identical(other.averageResponseTime, averageResponseTime) || other.averageResponseTime == averageResponseTime)&&const DeepCollectionEquality().equals(other._popularQueries, _popularQueries)&&(identical(other.lastSearchAt, lastSearchAt) || other.lastSearchAt == lastSearchAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalSearches,successfulSearches,failedSearches,averageResponseTime,const DeepCollectionEquality().hash(_popularQueries),lastSearchAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SearchAnalytics(totalSearches: $totalSearches, successfulSearches: $successfulSearches, failedSearches: $failedSearches, averageResponseTime: $averageResponseTime, popularQueries: $popularQueries, lastSearchAt: $lastSearchAt)';
}


}

/// @nodoc
abstract mixin class _$SearchAnalyticsCopyWith<$Res> implements $SearchAnalyticsCopyWith<$Res> {
  factory _$SearchAnalyticsCopyWith(_SearchAnalytics value, $Res Function(_SearchAnalytics) _then) = __$SearchAnalyticsCopyWithImpl;
@override @useResult
$Res call({
 int totalSearches, int successfulSearches, int failedSearches, double averageResponseTime, List<String> popularQueries, DateTime? lastSearchAt
});




}
/// @nodoc
class __$SearchAnalyticsCopyWithImpl<$Res>
    implements _$SearchAnalyticsCopyWith<$Res> {
  __$SearchAnalyticsCopyWithImpl(this._self, this._then);

  final _SearchAnalytics _self;
  final $Res Function(_SearchAnalytics) _then;

/// Create a copy of SearchAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalSearches = null,Object? successfulSearches = null,Object? failedSearches = null,Object? averageResponseTime = null,Object? popularQueries = null,Object? lastSearchAt = freezed,}) {
  return _then(_SearchAnalytics(
totalSearches: null == totalSearches ? _self.totalSearches : totalSearches // ignore: cast_nullable_to_non_nullable
as int,successfulSearches: null == successfulSearches ? _self.successfulSearches : successfulSearches // ignore: cast_nullable_to_non_nullable
as int,failedSearches: null == failedSearches ? _self.failedSearches : failedSearches // ignore: cast_nullable_to_non_nullable
as int,averageResponseTime: null == averageResponseTime ? _self.averageResponseTime : averageResponseTime // ignore: cast_nullable_to_non_nullable
as double,popularQueries: null == popularQueries ? _self._popularQueries : popularQueries // ignore: cast_nullable_to_non_nullable
as List<String>,lastSearchAt: freezed == lastSearchAt ? _self.lastSearchAt : lastSearchAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
