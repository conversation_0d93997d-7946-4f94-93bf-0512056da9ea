// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'simple_vehicle.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SimpleVehicle {

 String get id; String get makeName; String get modelName; int get year; String get color; String get plateNumber; int get mileage; String get fuelType; String get transmission; String get engineSize; String get status; String get notes;
/// Create a copy of SimpleVehicle
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SimpleVehicleCopyWith<SimpleVehicle> get copyWith => _$SimpleVehicleCopyWithImpl<SimpleVehicle>(this as SimpleVehicle, _$identity);

  /// Serializes this SimpleVehicle to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SimpleVehicle&&(identical(other.id, id) || other.id == id)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.year, year) || other.year == year)&&(identical(other.color, color) || other.color == color)&&(identical(other.plateNumber, plateNumber) || other.plateNumber == plateNumber)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.transmission, transmission) || other.transmission == transmission)&&(identical(other.engineSize, engineSize) || other.engineSize == engineSize)&&(identical(other.status, status) || other.status == status)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeName,modelName,year,color,plateNumber,mileage,fuelType,transmission,engineSize,status,notes);

@override
String toString() {
  return 'SimpleVehicle(id: $id, makeName: $makeName, modelName: $modelName, year: $year, color: $color, plateNumber: $plateNumber, mileage: $mileage, fuelType: $fuelType, transmission: $transmission, engineSize: $engineSize, status: $status, notes: $notes)';
}


}

/// @nodoc
abstract mixin class $SimpleVehicleCopyWith<$Res>  {
  factory $SimpleVehicleCopyWith(SimpleVehicle value, $Res Function(SimpleVehicle) _then) = _$SimpleVehicleCopyWithImpl;
@useResult
$Res call({
 String id, String makeName, String modelName, int year, String color, String plateNumber, int mileage, String fuelType, String transmission, String engineSize, String status, String notes
});




}
/// @nodoc
class _$SimpleVehicleCopyWithImpl<$Res>
    implements $SimpleVehicleCopyWith<$Res> {
  _$SimpleVehicleCopyWithImpl(this._self, this._then);

  final SimpleVehicle _self;
  final $Res Function(SimpleVehicle) _then;

/// Create a copy of SimpleVehicle
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? makeName = null,Object? modelName = null,Object? year = null,Object? color = null,Object? plateNumber = null,Object? mileage = null,Object? fuelType = null,Object? transmission = null,Object? engineSize = null,Object? status = null,Object? notes = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,color: null == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String,plateNumber: null == plateNumber ? _self.plateNumber : plateNumber // ignore: cast_nullable_to_non_nullable
as String,mileage: null == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int,fuelType: null == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String,transmission: null == transmission ? _self.transmission : transmission // ignore: cast_nullable_to_non_nullable
as String,engineSize: null == engineSize ? _self.engineSize : engineSize // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,notes: null == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SimpleVehicle].
extension SimpleVehiclePatterns on SimpleVehicle {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SimpleVehicle value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SimpleVehicle() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SimpleVehicle value)  $default,){
final _that = this;
switch (_that) {
case _SimpleVehicle():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SimpleVehicle value)?  $default,){
final _that = this;
switch (_that) {
case _SimpleVehicle() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String makeName,  String modelName,  int year,  String color,  String plateNumber,  int mileage,  String fuelType,  String transmission,  String engineSize,  String status,  String notes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SimpleVehicle() when $default != null:
return $default(_that.id,_that.makeName,_that.modelName,_that.year,_that.color,_that.plateNumber,_that.mileage,_that.fuelType,_that.transmission,_that.engineSize,_that.status,_that.notes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String makeName,  String modelName,  int year,  String color,  String plateNumber,  int mileage,  String fuelType,  String transmission,  String engineSize,  String status,  String notes)  $default,) {final _that = this;
switch (_that) {
case _SimpleVehicle():
return $default(_that.id,_that.makeName,_that.modelName,_that.year,_that.color,_that.plateNumber,_that.mileage,_that.fuelType,_that.transmission,_that.engineSize,_that.status,_that.notes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String makeName,  String modelName,  int year,  String color,  String plateNumber,  int mileage,  String fuelType,  String transmission,  String engineSize,  String status,  String notes)?  $default,) {final _that = this;
switch (_that) {
case _SimpleVehicle() when $default != null:
return $default(_that.id,_that.makeName,_that.modelName,_that.year,_that.color,_that.plateNumber,_that.mileage,_that.fuelType,_that.transmission,_that.engineSize,_that.status,_that.notes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SimpleVehicle implements SimpleVehicle {
  const _SimpleVehicle({required this.id, required this.makeName, required this.modelName, required this.year, this.color = '', this.plateNumber = '', this.mileage = 0, this.fuelType = '', this.transmission = '', this.engineSize = '', this.status = 'active', this.notes = ''});
  factory _SimpleVehicle.fromJson(Map<String, dynamic> json) => _$SimpleVehicleFromJson(json);

@override final  String id;
@override final  String makeName;
@override final  String modelName;
@override final  int year;
@override@JsonKey() final  String color;
@override@JsonKey() final  String plateNumber;
@override@JsonKey() final  int mileage;
@override@JsonKey() final  String fuelType;
@override@JsonKey() final  String transmission;
@override@JsonKey() final  String engineSize;
@override@JsonKey() final  String status;
@override@JsonKey() final  String notes;

/// Create a copy of SimpleVehicle
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SimpleVehicleCopyWith<_SimpleVehicle> get copyWith => __$SimpleVehicleCopyWithImpl<_SimpleVehicle>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SimpleVehicleToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SimpleVehicle&&(identical(other.id, id) || other.id == id)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.year, year) || other.year == year)&&(identical(other.color, color) || other.color == color)&&(identical(other.plateNumber, plateNumber) || other.plateNumber == plateNumber)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.transmission, transmission) || other.transmission == transmission)&&(identical(other.engineSize, engineSize) || other.engineSize == engineSize)&&(identical(other.status, status) || other.status == status)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeName,modelName,year,color,plateNumber,mileage,fuelType,transmission,engineSize,status,notes);

@override
String toString() {
  return 'SimpleVehicle(id: $id, makeName: $makeName, modelName: $modelName, year: $year, color: $color, plateNumber: $plateNumber, mileage: $mileage, fuelType: $fuelType, transmission: $transmission, engineSize: $engineSize, status: $status, notes: $notes)';
}


}

/// @nodoc
abstract mixin class _$SimpleVehicleCopyWith<$Res> implements $SimpleVehicleCopyWith<$Res> {
  factory _$SimpleVehicleCopyWith(_SimpleVehicle value, $Res Function(_SimpleVehicle) _then) = __$SimpleVehicleCopyWithImpl;
@override @useResult
$Res call({
 String id, String makeName, String modelName, int year, String color, String plateNumber, int mileage, String fuelType, String transmission, String engineSize, String status, String notes
});




}
/// @nodoc
class __$SimpleVehicleCopyWithImpl<$Res>
    implements _$SimpleVehicleCopyWith<$Res> {
  __$SimpleVehicleCopyWithImpl(this._self, this._then);

  final _SimpleVehicle _self;
  final $Res Function(_SimpleVehicle) _then;

/// Create a copy of SimpleVehicle
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? makeName = null,Object? modelName = null,Object? year = null,Object? color = null,Object? plateNumber = null,Object? mileage = null,Object? fuelType = null,Object? transmission = null,Object? engineSize = null,Object? status = null,Object? notes = null,}) {
  return _then(_SimpleVehicle(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,color: null == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String,plateNumber: null == plateNumber ? _self.plateNumber : plateNumber // ignore: cast_nullable_to_non_nullable
as String,mileage: null == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int,fuelType: null == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String,transmission: null == transmission ? _self.transmission : transmission // ignore: cast_nullable_to_non_nullable
as String,engineSize: null == engineSize ? _self.engineSize : engineSize // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,notes: null == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$VehicleMake {

 String get id; String get name; String get country; List<VehicleModel> get models;
/// Create a copy of VehicleMake
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleMakeCopyWith<VehicleMake> get copyWith => _$VehicleMakeCopyWithImpl<VehicleMake>(this as VehicleMake, _$identity);

  /// Serializes this VehicleMake to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleMake&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.country, country) || other.country == country)&&const DeepCollectionEquality().equals(other.models, models));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,country,const DeepCollectionEquality().hash(models));

@override
String toString() {
  return 'VehicleMake(id: $id, name: $name, country: $country, models: $models)';
}


}

/// @nodoc
abstract mixin class $VehicleMakeCopyWith<$Res>  {
  factory $VehicleMakeCopyWith(VehicleMake value, $Res Function(VehicleMake) _then) = _$VehicleMakeCopyWithImpl;
@useResult
$Res call({
 String id, String name, String country, List<VehicleModel> models
});




}
/// @nodoc
class _$VehicleMakeCopyWithImpl<$Res>
    implements $VehicleMakeCopyWith<$Res> {
  _$VehicleMakeCopyWithImpl(this._self, this._then);

  final VehicleMake _self;
  final $Res Function(VehicleMake) _then;

/// Create a copy of VehicleMake
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? country = null,Object? models = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,models: null == models ? _self.models : models // ignore: cast_nullable_to_non_nullable
as List<VehicleModel>,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleMake].
extension VehicleMakePatterns on VehicleMake {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleMake value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleMake() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleMake value)  $default,){
final _that = this;
switch (_that) {
case _VehicleMake():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleMake value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleMake() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String country,  List<VehicleModel> models)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleMake() when $default != null:
return $default(_that.id,_that.name,_that.country,_that.models);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String country,  List<VehicleModel> models)  $default,) {final _that = this;
switch (_that) {
case _VehicleMake():
return $default(_that.id,_that.name,_that.country,_that.models);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String country,  List<VehicleModel> models)?  $default,) {final _that = this;
switch (_that) {
case _VehicleMake() when $default != null:
return $default(_that.id,_that.name,_that.country,_that.models);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleMake implements VehicleMake {
  const _VehicleMake({required this.id, required this.name, required this.country, final  List<VehicleModel> models = const []}): _models = models;
  factory _VehicleMake.fromJson(Map<String, dynamic> json) => _$VehicleMakeFromJson(json);

@override final  String id;
@override final  String name;
@override final  String country;
 final  List<VehicleModel> _models;
@override@JsonKey() List<VehicleModel> get models {
  if (_models is EqualUnmodifiableListView) return _models;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_models);
}


/// Create a copy of VehicleMake
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleMakeCopyWith<_VehicleMake> get copyWith => __$VehicleMakeCopyWithImpl<_VehicleMake>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleMakeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleMake&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.country, country) || other.country == country)&&const DeepCollectionEquality().equals(other._models, _models));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,country,const DeepCollectionEquality().hash(_models));

@override
String toString() {
  return 'VehicleMake(id: $id, name: $name, country: $country, models: $models)';
}


}

/// @nodoc
abstract mixin class _$VehicleMakeCopyWith<$Res> implements $VehicleMakeCopyWith<$Res> {
  factory _$VehicleMakeCopyWith(_VehicleMake value, $Res Function(_VehicleMake) _then) = __$VehicleMakeCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String country, List<VehicleModel> models
});




}
/// @nodoc
class __$VehicleMakeCopyWithImpl<$Res>
    implements _$VehicleMakeCopyWith<$Res> {
  __$VehicleMakeCopyWithImpl(this._self, this._then);

  final _VehicleMake _self;
  final $Res Function(_VehicleMake) _then;

/// Create a copy of VehicleMake
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? country = null,Object? models = null,}) {
  return _then(_VehicleMake(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,models: null == models ? _self._models : models // ignore: cast_nullable_to_non_nullable
as List<VehicleModel>,
  ));
}


}


/// @nodoc
mixin _$VehicleModel {

 String get id; String get name; String get makeId; List<int> get years; String get category;
/// Create a copy of VehicleModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleModelCopyWith<VehicleModel> get copyWith => _$VehicleModelCopyWithImpl<VehicleModel>(this as VehicleModel, _$identity);

  /// Serializes this VehicleModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&const DeepCollectionEquality().equals(other.years, years)&&(identical(other.category, category) || other.category == category));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,makeId,const DeepCollectionEquality().hash(years),category);

@override
String toString() {
  return 'VehicleModel(id: $id, name: $name, makeId: $makeId, years: $years, category: $category)';
}


}

/// @nodoc
abstract mixin class $VehicleModelCopyWith<$Res>  {
  factory $VehicleModelCopyWith(VehicleModel value, $Res Function(VehicleModel) _then) = _$VehicleModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String makeId, List<int> years, String category
});




}
/// @nodoc
class _$VehicleModelCopyWithImpl<$Res>
    implements $VehicleModelCopyWith<$Res> {
  _$VehicleModelCopyWithImpl(this._self, this._then);

  final VehicleModel _self;
  final $Res Function(VehicleModel) _then;

/// Create a copy of VehicleModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? makeId = null,Object? years = null,Object? category = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as String,years: null == years ? _self.years : years // ignore: cast_nullable_to_non_nullable
as List<int>,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleModel].
extension VehicleModelPatterns on VehicleModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleModel value)  $default,){
final _that = this;
switch (_that) {
case _VehicleModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleModel value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String makeId,  List<int> years,  String category)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleModel() when $default != null:
return $default(_that.id,_that.name,_that.makeId,_that.years,_that.category);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String makeId,  List<int> years,  String category)  $default,) {final _that = this;
switch (_that) {
case _VehicleModel():
return $default(_that.id,_that.name,_that.makeId,_that.years,_that.category);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String makeId,  List<int> years,  String category)?  $default,) {final _that = this;
switch (_that) {
case _VehicleModel() when $default != null:
return $default(_that.id,_that.name,_that.makeId,_that.years,_that.category);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleModel implements VehicleModel {
  const _VehicleModel({required this.id, required this.name, required this.makeId, final  List<int> years = const [], this.category = ''}): _years = years;
  factory _VehicleModel.fromJson(Map<String, dynamic> json) => _$VehicleModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String makeId;
 final  List<int> _years;
@override@JsonKey() List<int> get years {
  if (_years is EqualUnmodifiableListView) return _years;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_years);
}

@override@JsonKey() final  String category;

/// Create a copy of VehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleModelCopyWith<_VehicleModel> get copyWith => __$VehicleModelCopyWithImpl<_VehicleModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&const DeepCollectionEquality().equals(other._years, _years)&&(identical(other.category, category) || other.category == category));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,makeId,const DeepCollectionEquality().hash(_years),category);

@override
String toString() {
  return 'VehicleModel(id: $id, name: $name, makeId: $makeId, years: $years, category: $category)';
}


}

/// @nodoc
abstract mixin class _$VehicleModelCopyWith<$Res> implements $VehicleModelCopyWith<$Res> {
  factory _$VehicleModelCopyWith(_VehicleModel value, $Res Function(_VehicleModel) _then) = __$VehicleModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String makeId, List<int> years, String category
});




}
/// @nodoc
class __$VehicleModelCopyWithImpl<$Res>
    implements _$VehicleModelCopyWith<$Res> {
  __$VehicleModelCopyWithImpl(this._self, this._then);

  final _VehicleModel _self;
  final $Res Function(_VehicleModel) _then;

/// Create a copy of VehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? makeId = null,Object? years = null,Object? category = null,}) {
  return _then(_VehicleModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as String,years: null == years ? _self._years : years // ignore: cast_nullable_to_non_nullable
as List<int>,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
