// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_data_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleMake _$VehicleMakeFromJson(Map<String, dynamic> json) =>
    _VehicleMake(id: (json['id'] as num).toInt(), name: json['name'] as String);

Map<String, dynamic> _$VehicleMakeToJson(_VehicleMake instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};

_VehicleModel _$VehicleModelFromJson(Map<String, dynamic> json) =>
    _VehicleModel(
      id: (json['id'] as num).toInt(),
      makeId: (json['make_id'] as num).toInt(),
      name: json['name'] as String,
      yearStart: (json['year_start'] as num).toInt(),
      yearEnd: (json['year_end'] as num?)?.toInt(),
    );

Map<String, dynamic> _$VehicleModelToJson(_VehicleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'make_id': instance.makeId,
      'name': instance.name,
      'year_start': instance.yearStart,
      'year_end': instance.yearEnd,
    };

_VehicleTrim _$VehicleTrimFromJson(Map<String, dynamic> json) => _VehicleTrim(
  id: (json['id'] as num).toInt(),
  modelId: (json['model_id'] as num).toInt(),
  name: json['name'] as String,
  year: (json['year'] as num).toInt(),
  engine: json['engine'] as String?,
);

Map<String, dynamic> _$VehicleTrimToJson(_VehicleTrim instance) =>
    <String, dynamic>{
      'id': instance.id,
      'model_id': instance.modelId,
      'name': instance.name,
      'year': instance.year,
      'engine': instance.engine,
    };
