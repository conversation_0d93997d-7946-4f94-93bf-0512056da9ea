// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'garage_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$garageRepositoryHash() => r'7f882eff411cb36132850c095f8fd5482f0891e9';

/// See also [garageRepository].
@ProviderFor(garageRepository)
final garageRepositoryProvider = AutoDisposeProvider<GarageRepository>.internal(
  garageRepository,
  name: r'garageRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$garageRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GarageRepositoryRef = AutoDisposeProviderRef<GarageRepository>;
String _$garageDataHash() => r'adb3b939e1fddf186f6bf084b94382816804870d';

/// See also [GarageData].
@ProviderFor(GarageData)
final garageDataProvider =
    AutoDisposeAsyncNotifierProvider<GarageData, List<UserVehicle>>.internal(
      GarageData.new,
      name: r'garageDataProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$garageDataHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GarageData = AutoDisposeAsyncNotifier<List<UserVehicle>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
