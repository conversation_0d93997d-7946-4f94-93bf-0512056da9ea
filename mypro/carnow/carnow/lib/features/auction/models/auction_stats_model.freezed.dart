// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auction_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AuctionStatsModel {

 int get bidCount; int get bidders;
/// Create a copy of AuctionStatsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuctionStatsModelCopyWith<AuctionStatsModel> get copyWith => _$AuctionStatsModelCopyWithImpl<AuctionStatsModel>(this as AuctionStatsModel, _$identity);

  /// Serializes this AuctionStatsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuctionStatsModel&&(identical(other.bidCount, bidCount) || other.bidCount == bidCount)&&(identical(other.bidders, bidders) || other.bidders == bidders));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,bidCount,bidders);

@override
String toString() {
  return 'AuctionStatsModel(bidCount: $bidCount, bidders: $bidders)';
}


}

/// @nodoc
abstract mixin class $AuctionStatsModelCopyWith<$Res>  {
  factory $AuctionStatsModelCopyWith(AuctionStatsModel value, $Res Function(AuctionStatsModel) _then) = _$AuctionStatsModelCopyWithImpl;
@useResult
$Res call({
 int bidCount, int bidders
});




}
/// @nodoc
class _$AuctionStatsModelCopyWithImpl<$Res>
    implements $AuctionStatsModelCopyWith<$Res> {
  _$AuctionStatsModelCopyWithImpl(this._self, this._then);

  final AuctionStatsModel _self;
  final $Res Function(AuctionStatsModel) _then;

/// Create a copy of AuctionStatsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? bidCount = null,Object? bidders = null,}) {
  return _then(_self.copyWith(
bidCount: null == bidCount ? _self.bidCount : bidCount // ignore: cast_nullable_to_non_nullable
as int,bidders: null == bidders ? _self.bidders : bidders // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [AuctionStatsModel].
extension AuctionStatsModelPatterns on AuctionStatsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AuctionStatsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AuctionStatsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AuctionStatsModel value)  $default,){
final _that = this;
switch (_that) {
case _AuctionStatsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AuctionStatsModel value)?  $default,){
final _that = this;
switch (_that) {
case _AuctionStatsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int bidCount,  int bidders)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AuctionStatsModel() when $default != null:
return $default(_that.bidCount,_that.bidders);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int bidCount,  int bidders)  $default,) {final _that = this;
switch (_that) {
case _AuctionStatsModel():
return $default(_that.bidCount,_that.bidders);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int bidCount,  int bidders)?  $default,) {final _that = this;
switch (_that) {
case _AuctionStatsModel() when $default != null:
return $default(_that.bidCount,_that.bidders);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AuctionStatsModel implements AuctionStatsModel {
  const _AuctionStatsModel({this.bidCount = 0, this.bidders = 0});
  factory _AuctionStatsModel.fromJson(Map<String, dynamic> json) => _$AuctionStatsModelFromJson(json);

@override@JsonKey() final  int bidCount;
@override@JsonKey() final  int bidders;

/// Create a copy of AuctionStatsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuctionStatsModelCopyWith<_AuctionStatsModel> get copyWith => __$AuctionStatsModelCopyWithImpl<_AuctionStatsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AuctionStatsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuctionStatsModel&&(identical(other.bidCount, bidCount) || other.bidCount == bidCount)&&(identical(other.bidders, bidders) || other.bidders == bidders));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,bidCount,bidders);

@override
String toString() {
  return 'AuctionStatsModel(bidCount: $bidCount, bidders: $bidders)';
}


}

/// @nodoc
abstract mixin class _$AuctionStatsModelCopyWith<$Res> implements $AuctionStatsModelCopyWith<$Res> {
  factory _$AuctionStatsModelCopyWith(_AuctionStatsModel value, $Res Function(_AuctionStatsModel) _then) = __$AuctionStatsModelCopyWithImpl;
@override @useResult
$Res call({
 int bidCount, int bidders
});




}
/// @nodoc
class __$AuctionStatsModelCopyWithImpl<$Res>
    implements _$AuctionStatsModelCopyWith<$Res> {
  __$AuctionStatsModelCopyWithImpl(this._self, this._then);

  final _AuctionStatsModel _self;
  final $Res Function(_AuctionStatsModel) _then;

/// Create a copy of AuctionStatsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? bidCount = null,Object? bidders = null,}) {
  return _then(_AuctionStatsModel(
bidCount: null == bidCount ? _self.bidCount : bidCount // ignore: cast_nullable_to_non_nullable
as int,bidders: null == bidders ? _self.bidders : bidders // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
