// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'compatibility_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CompatibilityModel _$CompatibilityModelFromJson(Map<String, dynamic> json) =>
    _CompatibilityModel(
      id: (json['id'] as num?)?.toInt(),
      partId: (json['part_id'] as num?)?.toInt(),
      yearFrom: (json['year_from'] as num?)?.toInt(),
      yearTo: (json['year_to'] as num?)?.toInt(),
      make: json['make'] as String?,
      model: json['model'] as String?,
      trim: json['trim'] as String?,
      engine: json['engine'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      isDeleted: json['is_deleted'] as bool? ?? false,
    );

Map<String, dynamic> _$CompatibilityModelToJson(_CompatibilityModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'part_id': instance.partId,
      'year_from': instance.yearFrom,
      'year_to': instance.yearTo,
      'make': instance.make,
      'model': instance.model,
      'trim': instance.trim,
      'engine': instance.engine,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
    };
