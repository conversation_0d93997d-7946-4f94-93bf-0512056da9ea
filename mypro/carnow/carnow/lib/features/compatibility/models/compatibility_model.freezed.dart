// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'compatibility_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CompatibilityModel {

 int? get id;@JsonKey(name: 'part_id') int? get partId;@Json<PERSON>ey(name: 'year_from') int? get yearFrom;@JsonKey(name: 'year_to') int? get yearTo; String? get make; String? get model; String? get trim; String? get engine;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'is_deleted') bool get isDeleted;
/// Create a copy of CompatibilityModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CompatibilityModelCopyWith<CompatibilityModel> get copyWith => _$CompatibilityModelCopyWithImpl<CompatibilityModel>(this as CompatibilityModel, _$identity);

  /// Serializes this CompatibilityModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CompatibilityModel&&(identical(other.id, id) || other.id == id)&&(identical(other.partId, partId) || other.partId == partId)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,partId,yearFrom,yearTo,make,model,trim,engine,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'CompatibilityModel(id: $id, partId: $partId, yearFrom: $yearFrom, yearTo: $yearTo, make: $make, model: $model, trim: $trim, engine: $engine, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $CompatibilityModelCopyWith<$Res>  {
  factory $CompatibilityModelCopyWith(CompatibilityModel value, $Res Function(CompatibilityModel) _then) = _$CompatibilityModelCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'part_id') int? partId,@JsonKey(name: 'year_from') int? yearFrom,@JsonKey(name: 'year_to') int? yearTo, String? make, String? model, String? trim, String? engine,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class _$CompatibilityModelCopyWithImpl<$Res>
    implements $CompatibilityModelCopyWith<$Res> {
  _$CompatibilityModelCopyWithImpl(this._self, this._then);

  final CompatibilityModel _self;
  final $Res Function(CompatibilityModel) _then;

/// Create a copy of CompatibilityModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? partId = freezed,Object? yearFrom = freezed,Object? yearTo = freezed,Object? make = freezed,Object? model = freezed,Object? trim = freezed,Object? engine = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,partId: freezed == partId ? _self.partId : partId // ignore: cast_nullable_to_non_nullable
as int?,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,make: freezed == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [CompatibilityModel].
extension CompatibilityModelPatterns on CompatibilityModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CompatibilityModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CompatibilityModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CompatibilityModel value)  $default,){
final _that = this;
switch (_that) {
case _CompatibilityModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CompatibilityModel value)?  $default,){
final _that = this;
switch (_that) {
case _CompatibilityModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id, @JsonKey(name: 'part_id')  int? partId, @JsonKey(name: 'year_from')  int? yearFrom, @JsonKey(name: 'year_to')  int? yearTo,  String? make,  String? model,  String? trim,  String? engine, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CompatibilityModel() when $default != null:
return $default(_that.id,_that.partId,_that.yearFrom,_that.yearTo,_that.make,_that.model,_that.trim,_that.engine,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id, @JsonKey(name: 'part_id')  int? partId, @JsonKey(name: 'year_from')  int? yearFrom, @JsonKey(name: 'year_to')  int? yearTo,  String? make,  String? model,  String? trim,  String? engine, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _CompatibilityModel():
return $default(_that.id,_that.partId,_that.yearFrom,_that.yearTo,_that.make,_that.model,_that.trim,_that.engine,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id, @JsonKey(name: 'part_id')  int? partId, @JsonKey(name: 'year_from')  int? yearFrom, @JsonKey(name: 'year_to')  int? yearTo,  String? make,  String? model,  String? trim,  String? engine, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _CompatibilityModel() when $default != null:
return $default(_that.id,_that.partId,_that.yearFrom,_that.yearTo,_that.make,_that.model,_that.trim,_that.engine,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CompatibilityModel implements CompatibilityModel {
  const _CompatibilityModel({this.id, @JsonKey(name: 'part_id') this.partId, @JsonKey(name: 'year_from') this.yearFrom, @JsonKey(name: 'year_to') this.yearTo, this.make, this.model, this.trim, this.engine, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'is_deleted') this.isDeleted = false});
  factory _CompatibilityModel.fromJson(Map<String, dynamic> json) => _$CompatibilityModelFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'part_id') final  int? partId;
@override@JsonKey(name: 'year_from') final  int? yearFrom;
@override@JsonKey(name: 'year_to') final  int? yearTo;
@override final  String? make;
@override final  String? model;
@override final  String? trim;
@override final  String? engine;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted') final  bool isDeleted;

/// Create a copy of CompatibilityModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CompatibilityModelCopyWith<_CompatibilityModel> get copyWith => __$CompatibilityModelCopyWithImpl<_CompatibilityModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CompatibilityModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CompatibilityModel&&(identical(other.id, id) || other.id == id)&&(identical(other.partId, partId) || other.partId == partId)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,partId,yearFrom,yearTo,make,model,trim,engine,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'CompatibilityModel(id: $id, partId: $partId, yearFrom: $yearFrom, yearTo: $yearTo, make: $make, model: $model, trim: $trim, engine: $engine, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$CompatibilityModelCopyWith<$Res> implements $CompatibilityModelCopyWith<$Res> {
  factory _$CompatibilityModelCopyWith(_CompatibilityModel value, $Res Function(_CompatibilityModel) _then) = __$CompatibilityModelCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'part_id') int? partId,@JsonKey(name: 'year_from') int? yearFrom,@JsonKey(name: 'year_to') int? yearTo, String? make, String? model, String? trim, String? engine,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class __$CompatibilityModelCopyWithImpl<$Res>
    implements _$CompatibilityModelCopyWith<$Res> {
  __$CompatibilityModelCopyWithImpl(this._self, this._then);

  final _CompatibilityModel _self;
  final $Res Function(_CompatibilityModel) _then;

/// Create a copy of CompatibilityModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? partId = freezed,Object? yearFrom = freezed,Object? yearTo = freezed,Object? make = freezed,Object? model = freezed,Object? trim = freezed,Object? engine = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_CompatibilityModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,partId: freezed == partId ? _self.partId : partId // ignore: cast_nullable_to_non_nullable
as int?,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,make: freezed == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
