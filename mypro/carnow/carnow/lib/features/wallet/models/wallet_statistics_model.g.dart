// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_statistics_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WalletStatistics _$WalletStatisticsFromJson(Map<String, dynamic> json) =>
    _WalletStatistics(
      totalDeposits: (json['totalDeposits'] as num?)?.toDouble() ?? 0.0,
      totalWithdrawals: (json['totalWithdrawals'] as num?)?.toDouble() ?? 0.0,
      totalTransfers: (json['totalTransfers'] as num?)?.toDouble() ?? 0.0,
      transactionCount: (json['transactionCount'] as num?)?.toInt() ?? 0,
      lastTransactionDate: json['lastTransactionDate'] == null
          ? null
          : DateTime.parse(json['lastTransactionDate'] as String),
      dailySpent: (json['dailySpent'] as num?)?.toDouble() ?? 0.0,
      monthlySpent: (json['monthlySpent'] as num?)?.toDouble() ?? 0.0,
      availableDailyLimit:
          (json['availableDailyLimit'] as num?)?.toDouble() ?? 0.0,
      availableMonthlyLimit:
          (json['availableMonthlyLimit'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$WalletStatisticsToJson(_WalletStatistics instance) =>
    <String, dynamic>{
      'totalDeposits': instance.totalDeposits,
      'totalWithdrawals': instance.totalWithdrawals,
      'totalTransfers': instance.totalTransfers,
      'transactionCount': instance.transactionCount,
      'lastTransactionDate': instance.lastTransactionDate?.toIso8601String(),
      'dailySpent': instance.dailySpent,
      'monthlySpent': instance.monthlySpent,
      'availableDailyLimit': instance.availableDailyLimit,
      'availableMonthlyLimit': instance.availableMonthlyLimit,
    };
