// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_transaction_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WalletTransactionModel _$WalletTransactionModelFromJson(
  Map<String, dynamic> json,
) => _WalletTransactionModel(
  id: json['id'] as String,
  walletId: json['wallet_id'] as String,
  amount: (json['amount'] as num).toDouble(),
  type: $enumDecode(_$WalletTransactionTypeEnumMap, json['type']),
  status:
      $enumDecodeNullable(_$WalletTransactionStatusEnumMap, json['status']) ??
      WalletTransactionStatus.pending,
  fromWalletId: json['fromWalletId'] as String?,
  toWalletId: json['toWalletId'] as String?,
  paymentMethod: json['paymentMethod'] as String?,
  paymentReference: json['paymentReference'] as String?,
  externalTransactionId: json['externalTransactionId'] as String?,
  description: json['description'] as String?,
  notes: json['notes'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$WalletTransactionModelToJson(
  _WalletTransactionModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'wallet_id': instance.walletId,
  'amount': instance.amount,
  'type': _$WalletTransactionTypeEnumMap[instance.type]!,
  'status': _$WalletTransactionStatusEnumMap[instance.status]!,
  'fromWalletId': instance.fromWalletId,
  'toWalletId': instance.toWalletId,
  'paymentMethod': instance.paymentMethod,
  'paymentReference': instance.paymentReference,
  'externalTransactionId': instance.externalTransactionId,
  'description': instance.description,
  'notes': instance.notes,
  'metadata': instance.metadata,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
};

const _$WalletTransactionTypeEnumMap = {
  WalletTransactionType.deposit: 'deposit',
  WalletTransactionType.withdraw: 'withdraw',
  WalletTransactionType.transferIn: 'transfer_in',
  WalletTransactionType.transferOut: 'transfer_out',
  WalletTransactionType.fee: 'fee',
  WalletTransactionType.refund: 'refund',
};

const _$WalletTransactionStatusEnumMap = {
  WalletTransactionStatus.pending: 'pending',
  WalletTransactionStatus.completed: 'completed',
  WalletTransactionStatus.failed: 'failed',
  WalletTransactionStatus.cancelled: 'cancelled',
};
