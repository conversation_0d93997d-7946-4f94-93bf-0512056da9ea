// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WalletModel {

 String get id;@JsonKey(name: 'user_id') String get userId;// البنية البسيطة متطابقة مع Backend Go - Forever Plan
 double get balance;@JsonKey(name: 'frozen_balance') double get frozenBalance; String get currency;@JsonKey(name: 'daily_limit') double get dailyLimit;@JsonKey(name: 'monthly_limit') double get monthlyLimit; String get status;@JsonKey(name: 'is_active') bool get isActive;@JsonKey(name: 'last_reset_daily') DateTime? get lastResetDaily;@JsonKey(name: 'last_reset_monthly') DateTime? get lastResetMonthly;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'is_deleted') bool get isDeleted;// حقول إضافية للواجهة (مع قيم افتراضية بسيطة)
@JsonKey(name: 'verification_level') String get verificationLevel;@JsonKey(name: 'min_withdrawal') double get minWithdrawal;
/// Create a copy of WalletModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletModelCopyWith<WalletModel> get copyWith => _$WalletModelCopyWithImpl<WalletModel>(this as WalletModel, _$identity);

  /// Serializes this WalletModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.frozenBalance, frozenBalance) || other.frozenBalance == frozenBalance)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.dailyLimit, dailyLimit) || other.dailyLimit == dailyLimit)&&(identical(other.monthlyLimit, monthlyLimit) || other.monthlyLimit == monthlyLimit)&&(identical(other.status, status) || other.status == status)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.lastResetDaily, lastResetDaily) || other.lastResetDaily == lastResetDaily)&&(identical(other.lastResetMonthly, lastResetMonthly) || other.lastResetMonthly == lastResetMonthly)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.verificationLevel, verificationLevel) || other.verificationLevel == verificationLevel)&&(identical(other.minWithdrawal, minWithdrawal) || other.minWithdrawal == minWithdrawal));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,balance,frozenBalance,currency,dailyLimit,monthlyLimit,status,isActive,lastResetDaily,lastResetMonthly,createdAt,updatedAt,isDeleted,verificationLevel,minWithdrawal);

@override
String toString() {
  return 'WalletModel(id: $id, userId: $userId, balance: $balance, frozenBalance: $frozenBalance, currency: $currency, dailyLimit: $dailyLimit, monthlyLimit: $monthlyLimit, status: $status, isActive: $isActive, lastResetDaily: $lastResetDaily, lastResetMonthly: $lastResetMonthly, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, verificationLevel: $verificationLevel, minWithdrawal: $minWithdrawal)';
}


}

/// @nodoc
abstract mixin class $WalletModelCopyWith<$Res>  {
  factory $WalletModelCopyWith(WalletModel value, $Res Function(WalletModel) _then) = _$WalletModelCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'user_id') String userId, double balance,@JsonKey(name: 'frozen_balance') double frozenBalance, String currency,@JsonKey(name: 'daily_limit') double dailyLimit,@JsonKey(name: 'monthly_limit') double monthlyLimit, String status,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'last_reset_daily') DateTime? lastResetDaily,@JsonKey(name: 'last_reset_monthly') DateTime? lastResetMonthly,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted,@JsonKey(name: 'verification_level') String verificationLevel,@JsonKey(name: 'min_withdrawal') double minWithdrawal
});




}
/// @nodoc
class _$WalletModelCopyWithImpl<$Res>
    implements $WalletModelCopyWith<$Res> {
  _$WalletModelCopyWithImpl(this._self, this._then);

  final WalletModel _self;
  final $Res Function(WalletModel) _then;

/// Create a copy of WalletModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? balance = null,Object? frozenBalance = null,Object? currency = null,Object? dailyLimit = null,Object? monthlyLimit = null,Object? status = null,Object? isActive = null,Object? lastResetDaily = freezed,Object? lastResetMonthly = freezed,Object? createdAt = null,Object? updatedAt = freezed,Object? isDeleted = null,Object? verificationLevel = null,Object? minWithdrawal = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double,frozenBalance: null == frozenBalance ? _self.frozenBalance : frozenBalance // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,dailyLimit: null == dailyLimit ? _self.dailyLimit : dailyLimit // ignore: cast_nullable_to_non_nullable
as double,monthlyLimit: null == monthlyLimit ? _self.monthlyLimit : monthlyLimit // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,lastResetDaily: freezed == lastResetDaily ? _self.lastResetDaily : lastResetDaily // ignore: cast_nullable_to_non_nullable
as DateTime?,lastResetMonthly: freezed == lastResetMonthly ? _self.lastResetMonthly : lastResetMonthly // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,verificationLevel: null == verificationLevel ? _self.verificationLevel : verificationLevel // ignore: cast_nullable_to_non_nullable
as String,minWithdrawal: null == minWithdrawal ? _self.minWithdrawal : minWithdrawal // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletModel].
extension WalletModelPatterns on WalletModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletModel value)  $default,){
final _that = this;
switch (_that) {
case _WalletModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletModel value)?  $default,){
final _that = this;
switch (_that) {
case _WalletModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'frozen_balance')  double frozenBalance,  String currency, @JsonKey(name: 'daily_limit')  double dailyLimit, @JsonKey(name: 'monthly_limit')  double monthlyLimit,  String status, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'last_reset_daily')  DateTime? lastResetDaily, @JsonKey(name: 'last_reset_monthly')  DateTime? lastResetMonthly, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted, @JsonKey(name: 'verification_level')  String verificationLevel, @JsonKey(name: 'min_withdrawal')  double minWithdrawal)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletModel() when $default != null:
return $default(_that.id,_that.userId,_that.balance,_that.frozenBalance,_that.currency,_that.dailyLimit,_that.monthlyLimit,_that.status,_that.isActive,_that.lastResetDaily,_that.lastResetMonthly,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.verificationLevel,_that.minWithdrawal);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'frozen_balance')  double frozenBalance,  String currency, @JsonKey(name: 'daily_limit')  double dailyLimit, @JsonKey(name: 'monthly_limit')  double monthlyLimit,  String status, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'last_reset_daily')  DateTime? lastResetDaily, @JsonKey(name: 'last_reset_monthly')  DateTime? lastResetMonthly, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted, @JsonKey(name: 'verification_level')  String verificationLevel, @JsonKey(name: 'min_withdrawal')  double minWithdrawal)  $default,) {final _that = this;
switch (_that) {
case _WalletModel():
return $default(_that.id,_that.userId,_that.balance,_that.frozenBalance,_that.currency,_that.dailyLimit,_that.monthlyLimit,_that.status,_that.isActive,_that.lastResetDaily,_that.lastResetMonthly,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.verificationLevel,_that.minWithdrawal);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'frozen_balance')  double frozenBalance,  String currency, @JsonKey(name: 'daily_limit')  double dailyLimit, @JsonKey(name: 'monthly_limit')  double monthlyLimit,  String status, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'last_reset_daily')  DateTime? lastResetDaily, @JsonKey(name: 'last_reset_monthly')  DateTime? lastResetMonthly, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted, @JsonKey(name: 'verification_level')  String verificationLevel, @JsonKey(name: 'min_withdrawal')  double minWithdrawal)?  $default,) {final _that = this;
switch (_that) {
case _WalletModel() when $default != null:
return $default(_that.id,_that.userId,_that.balance,_that.frozenBalance,_that.currency,_that.dailyLimit,_that.monthlyLimit,_that.status,_that.isActive,_that.lastResetDaily,_that.lastResetMonthly,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.verificationLevel,_that.minWithdrawal);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletModel extends WalletModel {
  const _WalletModel({required this.id, @JsonKey(name: 'user_id') required this.userId, this.balance = 0.0, @JsonKey(name: 'frozen_balance') this.frozenBalance = 0.0, this.currency = 'LYD', @JsonKey(name: 'daily_limit') this.dailyLimit = 1000.0, @JsonKey(name: 'monthly_limit') this.monthlyLimit = 10000.0, this.status = 'active', @JsonKey(name: 'is_active') this.isActive = true, @JsonKey(name: 'last_reset_daily') this.lastResetDaily, @JsonKey(name: 'last_reset_monthly') this.lastResetMonthly, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'is_deleted') this.isDeleted = false, @JsonKey(name: 'verification_level') this.verificationLevel = 'unverified', @JsonKey(name: 'min_withdrawal') this.minWithdrawal = 10.0}): super._();
  factory _WalletModel.fromJson(Map<String, dynamic> json) => _$WalletModelFromJson(json);

@override final  String id;
@override@JsonKey(name: 'user_id') final  String userId;
// البنية البسيطة متطابقة مع Backend Go - Forever Plan
@override@JsonKey() final  double balance;
@override@JsonKey(name: 'frozen_balance') final  double frozenBalance;
@override@JsonKey() final  String currency;
@override@JsonKey(name: 'daily_limit') final  double dailyLimit;
@override@JsonKey(name: 'monthly_limit') final  double monthlyLimit;
@override@JsonKey() final  String status;
@override@JsonKey(name: 'is_active') final  bool isActive;
@override@JsonKey(name: 'last_reset_daily') final  DateTime? lastResetDaily;
@override@JsonKey(name: 'last_reset_monthly') final  DateTime? lastResetMonthly;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted') final  bool isDeleted;
// حقول إضافية للواجهة (مع قيم افتراضية بسيطة)
@override@JsonKey(name: 'verification_level') final  String verificationLevel;
@override@JsonKey(name: 'min_withdrawal') final  double minWithdrawal;

/// Create a copy of WalletModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletModelCopyWith<_WalletModel> get copyWith => __$WalletModelCopyWithImpl<_WalletModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.frozenBalance, frozenBalance) || other.frozenBalance == frozenBalance)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.dailyLimit, dailyLimit) || other.dailyLimit == dailyLimit)&&(identical(other.monthlyLimit, monthlyLimit) || other.monthlyLimit == monthlyLimit)&&(identical(other.status, status) || other.status == status)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.lastResetDaily, lastResetDaily) || other.lastResetDaily == lastResetDaily)&&(identical(other.lastResetMonthly, lastResetMonthly) || other.lastResetMonthly == lastResetMonthly)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.verificationLevel, verificationLevel) || other.verificationLevel == verificationLevel)&&(identical(other.minWithdrawal, minWithdrawal) || other.minWithdrawal == minWithdrawal));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,balance,frozenBalance,currency,dailyLimit,monthlyLimit,status,isActive,lastResetDaily,lastResetMonthly,createdAt,updatedAt,isDeleted,verificationLevel,minWithdrawal);

@override
String toString() {
  return 'WalletModel(id: $id, userId: $userId, balance: $balance, frozenBalance: $frozenBalance, currency: $currency, dailyLimit: $dailyLimit, monthlyLimit: $monthlyLimit, status: $status, isActive: $isActive, lastResetDaily: $lastResetDaily, lastResetMonthly: $lastResetMonthly, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, verificationLevel: $verificationLevel, minWithdrawal: $minWithdrawal)';
}


}

/// @nodoc
abstract mixin class _$WalletModelCopyWith<$Res> implements $WalletModelCopyWith<$Res> {
  factory _$WalletModelCopyWith(_WalletModel value, $Res Function(_WalletModel) _then) = __$WalletModelCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'user_id') String userId, double balance,@JsonKey(name: 'frozen_balance') double frozenBalance, String currency,@JsonKey(name: 'daily_limit') double dailyLimit,@JsonKey(name: 'monthly_limit') double monthlyLimit, String status,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'last_reset_daily') DateTime? lastResetDaily,@JsonKey(name: 'last_reset_monthly') DateTime? lastResetMonthly,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted,@JsonKey(name: 'verification_level') String verificationLevel,@JsonKey(name: 'min_withdrawal') double minWithdrawal
});




}
/// @nodoc
class __$WalletModelCopyWithImpl<$Res>
    implements _$WalletModelCopyWith<$Res> {
  __$WalletModelCopyWithImpl(this._self, this._then);

  final _WalletModel _self;
  final $Res Function(_WalletModel) _then;

/// Create a copy of WalletModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? balance = null,Object? frozenBalance = null,Object? currency = null,Object? dailyLimit = null,Object? monthlyLimit = null,Object? status = null,Object? isActive = null,Object? lastResetDaily = freezed,Object? lastResetMonthly = freezed,Object? createdAt = null,Object? updatedAt = freezed,Object? isDeleted = null,Object? verificationLevel = null,Object? minWithdrawal = null,}) {
  return _then(_WalletModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double,frozenBalance: null == frozenBalance ? _self.frozenBalance : frozenBalance // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,dailyLimit: null == dailyLimit ? _self.dailyLimit : dailyLimit // ignore: cast_nullable_to_non_nullable
as double,monthlyLimit: null == monthlyLimit ? _self.monthlyLimit : monthlyLimit // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,lastResetDaily: freezed == lastResetDaily ? _self.lastResetDaily : lastResetDaily // ignore: cast_nullable_to_non_nullable
as DateTime?,lastResetMonthly: freezed == lastResetMonthly ? _self.lastResetMonthly : lastResetMonthly // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,verificationLevel: null == verificationLevel ? _self.verificationLevel : verificationLevel // ignore: cast_nullable_to_non_nullable
as String,minWithdrawal: null == minWithdrawal ? _self.minWithdrawal : minWithdrawal // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
