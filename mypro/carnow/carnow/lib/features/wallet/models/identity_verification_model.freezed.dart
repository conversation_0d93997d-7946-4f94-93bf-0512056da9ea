// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'identity_verification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$IdentityVerificationModel {

 String get id; String get userId; String get statusId; String? get documentTypeId; String? get documentNumber; DateTime? get documentExpiry; String? get documentImageUrl; String get verificationMethod; DateTime? get verifiedAt; String? get verifiedBy; String? get rejectionReason; String? get notes; Map<String, dynamic> get metadata; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of IdentityVerificationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$IdentityVerificationModelCopyWith<IdentityVerificationModel> get copyWith => _$IdentityVerificationModelCopyWithImpl<IdentityVerificationModel>(this as IdentityVerificationModel, _$identity);

  /// Serializes this IdentityVerificationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is IdentityVerificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.statusId, statusId) || other.statusId == statusId)&&(identical(other.documentTypeId, documentTypeId) || other.documentTypeId == documentTypeId)&&(identical(other.documentNumber, documentNumber) || other.documentNumber == documentNumber)&&(identical(other.documentExpiry, documentExpiry) || other.documentExpiry == documentExpiry)&&(identical(other.documentImageUrl, documentImageUrl) || other.documentImageUrl == documentImageUrl)&&(identical(other.verificationMethod, verificationMethod) || other.verificationMethod == verificationMethod)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt)&&(identical(other.verifiedBy, verifiedBy) || other.verifiedBy == verifiedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.notes, notes) || other.notes == notes)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,statusId,documentTypeId,documentNumber,documentExpiry,documentImageUrl,verificationMethod,verifiedAt,verifiedBy,rejectionReason,notes,const DeepCollectionEquality().hash(metadata),createdAt,updatedAt);

@override
String toString() {
  return 'IdentityVerificationModel(id: $id, userId: $userId, statusId: $statusId, documentTypeId: $documentTypeId, documentNumber: $documentNumber, documentExpiry: $documentExpiry, documentImageUrl: $documentImageUrl, verificationMethod: $verificationMethod, verifiedAt: $verifiedAt, verifiedBy: $verifiedBy, rejectionReason: $rejectionReason, notes: $notes, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $IdentityVerificationModelCopyWith<$Res>  {
  factory $IdentityVerificationModelCopyWith(IdentityVerificationModel value, $Res Function(IdentityVerificationModel) _then) = _$IdentityVerificationModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String statusId, String? documentTypeId, String? documentNumber, DateTime? documentExpiry, String? documentImageUrl, String verificationMethod, DateTime? verifiedAt, String? verifiedBy, String? rejectionReason, String? notes, Map<String, dynamic> metadata, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class _$IdentityVerificationModelCopyWithImpl<$Res>
    implements $IdentityVerificationModelCopyWith<$Res> {
  _$IdentityVerificationModelCopyWithImpl(this._self, this._then);

  final IdentityVerificationModel _self;
  final $Res Function(IdentityVerificationModel) _then;

/// Create a copy of IdentityVerificationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? statusId = null,Object? documentTypeId = freezed,Object? documentNumber = freezed,Object? documentExpiry = freezed,Object? documentImageUrl = freezed,Object? verificationMethod = null,Object? verifiedAt = freezed,Object? verifiedBy = freezed,Object? rejectionReason = freezed,Object? notes = freezed,Object? metadata = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,statusId: null == statusId ? _self.statusId : statusId // ignore: cast_nullable_to_non_nullable
as String,documentTypeId: freezed == documentTypeId ? _self.documentTypeId : documentTypeId // ignore: cast_nullable_to_non_nullable
as String?,documentNumber: freezed == documentNumber ? _self.documentNumber : documentNumber // ignore: cast_nullable_to_non_nullable
as String?,documentExpiry: freezed == documentExpiry ? _self.documentExpiry : documentExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,documentImageUrl: freezed == documentImageUrl ? _self.documentImageUrl : documentImageUrl // ignore: cast_nullable_to_non_nullable
as String?,verificationMethod: null == verificationMethod ? _self.verificationMethod : verificationMethod // ignore: cast_nullable_to_non_nullable
as String,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,verifiedBy: freezed == verifiedBy ? _self.verifiedBy : verifiedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [IdentityVerificationModel].
extension IdentityVerificationModelPatterns on IdentityVerificationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _IdentityVerificationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _IdentityVerificationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _IdentityVerificationModel value)  $default,){
final _that = this;
switch (_that) {
case _IdentityVerificationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _IdentityVerificationModel value)?  $default,){
final _that = this;
switch (_that) {
case _IdentityVerificationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String statusId,  String? documentTypeId,  String? documentNumber,  DateTime? documentExpiry,  String? documentImageUrl,  String verificationMethod,  DateTime? verifiedAt,  String? verifiedBy,  String? rejectionReason,  String? notes,  Map<String, dynamic> metadata,  DateTime createdAt,  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _IdentityVerificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.statusId,_that.documentTypeId,_that.documentNumber,_that.documentExpiry,_that.documentImageUrl,_that.verificationMethod,_that.verifiedAt,_that.verifiedBy,_that.rejectionReason,_that.notes,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String statusId,  String? documentTypeId,  String? documentNumber,  DateTime? documentExpiry,  String? documentImageUrl,  String verificationMethod,  DateTime? verifiedAt,  String? verifiedBy,  String? rejectionReason,  String? notes,  Map<String, dynamic> metadata,  DateTime createdAt,  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _IdentityVerificationModel():
return $default(_that.id,_that.userId,_that.statusId,_that.documentTypeId,_that.documentNumber,_that.documentExpiry,_that.documentImageUrl,_that.verificationMethod,_that.verifiedAt,_that.verifiedBy,_that.rejectionReason,_that.notes,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String statusId,  String? documentTypeId,  String? documentNumber,  DateTime? documentExpiry,  String? documentImageUrl,  String verificationMethod,  DateTime? verifiedAt,  String? verifiedBy,  String? rejectionReason,  String? notes,  Map<String, dynamic> metadata,  DateTime createdAt,  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _IdentityVerificationModel() when $default != null:
return $default(_that.id,_that.userId,_that.statusId,_that.documentTypeId,_that.documentNumber,_that.documentExpiry,_that.documentImageUrl,_that.verificationMethod,_that.verifiedAt,_that.verifiedBy,_that.rejectionReason,_that.notes,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _IdentityVerificationModel extends IdentityVerificationModel {
  const _IdentityVerificationModel({required this.id, required this.userId, required this.statusId, this.documentTypeId, this.documentNumber, this.documentExpiry, this.documentImageUrl, this.verificationMethod = 'manual', this.verifiedAt, this.verifiedBy, this.rejectionReason, this.notes, final  Map<String, dynamic> metadata = const {}, required this.createdAt, required this.updatedAt}): _metadata = metadata,super._();
  factory _IdentityVerificationModel.fromJson(Map<String, dynamic> json) => _$IdentityVerificationModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String statusId;
@override final  String? documentTypeId;
@override final  String? documentNumber;
@override final  DateTime? documentExpiry;
@override final  String? documentImageUrl;
@override@JsonKey() final  String verificationMethod;
@override final  DateTime? verifiedAt;
@override final  String? verifiedBy;
@override final  String? rejectionReason;
@override final  String? notes;
 final  Map<String, dynamic> _metadata;
@override@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}

@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of IdentityVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$IdentityVerificationModelCopyWith<_IdentityVerificationModel> get copyWith => __$IdentityVerificationModelCopyWithImpl<_IdentityVerificationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$IdentityVerificationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _IdentityVerificationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.statusId, statusId) || other.statusId == statusId)&&(identical(other.documentTypeId, documentTypeId) || other.documentTypeId == documentTypeId)&&(identical(other.documentNumber, documentNumber) || other.documentNumber == documentNumber)&&(identical(other.documentExpiry, documentExpiry) || other.documentExpiry == documentExpiry)&&(identical(other.documentImageUrl, documentImageUrl) || other.documentImageUrl == documentImageUrl)&&(identical(other.verificationMethod, verificationMethod) || other.verificationMethod == verificationMethod)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt)&&(identical(other.verifiedBy, verifiedBy) || other.verifiedBy == verifiedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.notes, notes) || other.notes == notes)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,statusId,documentTypeId,documentNumber,documentExpiry,documentImageUrl,verificationMethod,verifiedAt,verifiedBy,rejectionReason,notes,const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt);

@override
String toString() {
  return 'IdentityVerificationModel(id: $id, userId: $userId, statusId: $statusId, documentTypeId: $documentTypeId, documentNumber: $documentNumber, documentExpiry: $documentExpiry, documentImageUrl: $documentImageUrl, verificationMethod: $verificationMethod, verifiedAt: $verifiedAt, verifiedBy: $verifiedBy, rejectionReason: $rejectionReason, notes: $notes, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$IdentityVerificationModelCopyWith<$Res> implements $IdentityVerificationModelCopyWith<$Res> {
  factory _$IdentityVerificationModelCopyWith(_IdentityVerificationModel value, $Res Function(_IdentityVerificationModel) _then) = __$IdentityVerificationModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String statusId, String? documentTypeId, String? documentNumber, DateTime? documentExpiry, String? documentImageUrl, String verificationMethod, DateTime? verifiedAt, String? verifiedBy, String? rejectionReason, String? notes, Map<String, dynamic> metadata, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class __$IdentityVerificationModelCopyWithImpl<$Res>
    implements _$IdentityVerificationModelCopyWith<$Res> {
  __$IdentityVerificationModelCopyWithImpl(this._self, this._then);

  final _IdentityVerificationModel _self;
  final $Res Function(_IdentityVerificationModel) _then;

/// Create a copy of IdentityVerificationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? statusId = null,Object? documentTypeId = freezed,Object? documentNumber = freezed,Object? documentExpiry = freezed,Object? documentImageUrl = freezed,Object? verificationMethod = null,Object? verifiedAt = freezed,Object? verifiedBy = freezed,Object? rejectionReason = freezed,Object? notes = freezed,Object? metadata = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_IdentityVerificationModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,statusId: null == statusId ? _self.statusId : statusId // ignore: cast_nullable_to_non_nullable
as String,documentTypeId: freezed == documentTypeId ? _self.documentTypeId : documentTypeId // ignore: cast_nullable_to_non_nullable
as String?,documentNumber: freezed == documentNumber ? _self.documentNumber : documentNumber // ignore: cast_nullable_to_non_nullable
as String?,documentExpiry: freezed == documentExpiry ? _self.documentExpiry : documentExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,documentImageUrl: freezed == documentImageUrl ? _self.documentImageUrl : documentImageUrl // ignore: cast_nullable_to_non_nullable
as String?,verificationMethod: null == verificationMethod ? _self.verificationMethod : verificationMethod // ignore: cast_nullable_to_non_nullable
as String,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,verifiedBy: freezed == verifiedBy ? _self.verifiedBy : verifiedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$DocumentTypeModel {

 String get id; String get name; String get nameAr; String? get description; bool get isRequired; bool get isActive; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of DocumentTypeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DocumentTypeModelCopyWith<DocumentTypeModel> get copyWith => _$DocumentTypeModelCopyWithImpl<DocumentTypeModel>(this as DocumentTypeModel, _$identity);

  /// Serializes this DocumentTypeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DocumentTypeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.isRequired, isRequired) || other.isRequired == isRequired)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,isRequired,isActive,createdAt,updatedAt);

@override
String toString() {
  return 'DocumentTypeModel(id: $id, name: $name, nameAr: $nameAr, description: $description, isRequired: $isRequired, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $DocumentTypeModelCopyWith<$Res>  {
  factory $DocumentTypeModelCopyWith(DocumentTypeModel value, $Res Function(DocumentTypeModel) _then) = _$DocumentTypeModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String nameAr, String? description, bool isRequired, bool isActive, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class _$DocumentTypeModelCopyWithImpl<$Res>
    implements $DocumentTypeModelCopyWith<$Res> {
  _$DocumentTypeModelCopyWithImpl(this._self, this._then);

  final DocumentTypeModel _self;
  final $Res Function(DocumentTypeModel) _then;

/// Create a copy of DocumentTypeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = freezed,Object? isRequired = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,isRequired: null == isRequired ? _self.isRequired : isRequired // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [DocumentTypeModel].
extension DocumentTypeModelPatterns on DocumentTypeModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DocumentTypeModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DocumentTypeModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DocumentTypeModel value)  $default,){
final _that = this;
switch (_that) {
case _DocumentTypeModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DocumentTypeModel value)?  $default,){
final _that = this;
switch (_that) {
case _DocumentTypeModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String nameAr,  String? description,  bool isRequired,  bool isActive,  DateTime createdAt,  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DocumentTypeModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.isRequired,_that.isActive,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String nameAr,  String? description,  bool isRequired,  bool isActive,  DateTime createdAt,  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _DocumentTypeModel():
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.isRequired,_that.isActive,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String nameAr,  String? description,  bool isRequired,  bool isActive,  DateTime createdAt,  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _DocumentTypeModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.isRequired,_that.isActive,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DocumentTypeModel extends DocumentTypeModel {
  const _DocumentTypeModel({required this.id, required this.name, required this.nameAr, this.description, this.isRequired = true, this.isActive = true, required this.createdAt, required this.updatedAt}): super._();
  factory _DocumentTypeModel.fromJson(Map<String, dynamic> json) => _$DocumentTypeModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String nameAr;
@override final  String? description;
@override@JsonKey() final  bool isRequired;
@override@JsonKey() final  bool isActive;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of DocumentTypeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DocumentTypeModelCopyWith<_DocumentTypeModel> get copyWith => __$DocumentTypeModelCopyWithImpl<_DocumentTypeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DocumentTypeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DocumentTypeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.isRequired, isRequired) || other.isRequired == isRequired)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,isRequired,isActive,createdAt,updatedAt);

@override
String toString() {
  return 'DocumentTypeModel(id: $id, name: $name, nameAr: $nameAr, description: $description, isRequired: $isRequired, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$DocumentTypeModelCopyWith<$Res> implements $DocumentTypeModelCopyWith<$Res> {
  factory _$DocumentTypeModelCopyWith(_DocumentTypeModel value, $Res Function(_DocumentTypeModel) _then) = __$DocumentTypeModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String nameAr, String? description, bool isRequired, bool isActive, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class __$DocumentTypeModelCopyWithImpl<$Res>
    implements _$DocumentTypeModelCopyWith<$Res> {
  __$DocumentTypeModelCopyWithImpl(this._self, this._then);

  final _DocumentTypeModel _self;
  final $Res Function(_DocumentTypeModel) _then;

/// Create a copy of DocumentTypeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = freezed,Object? isRequired = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_DocumentTypeModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,isRequired: null == isRequired ? _self.isRequired : isRequired // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$VerificationStatusModel {

 String get id; String get name; String get nameAr; String? get description; int get level; DateTime get createdAt;
/// Create a copy of VerificationStatusModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VerificationStatusModelCopyWith<VerificationStatusModel> get copyWith => _$VerificationStatusModelCopyWithImpl<VerificationStatusModel>(this as VerificationStatusModel, _$identity);

  /// Serializes this VerificationStatusModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VerificationStatusModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.level, level) || other.level == level)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,level,createdAt);

@override
String toString() {
  return 'VerificationStatusModel(id: $id, name: $name, nameAr: $nameAr, description: $description, level: $level, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $VerificationStatusModelCopyWith<$Res>  {
  factory $VerificationStatusModelCopyWith(VerificationStatusModel value, $Res Function(VerificationStatusModel) _then) = _$VerificationStatusModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String nameAr, String? description, int level, DateTime createdAt
});




}
/// @nodoc
class _$VerificationStatusModelCopyWithImpl<$Res>
    implements $VerificationStatusModelCopyWith<$Res> {
  _$VerificationStatusModelCopyWithImpl(this._self, this._then);

  final VerificationStatusModel _self;
  final $Res Function(VerificationStatusModel) _then;

/// Create a copy of VerificationStatusModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = freezed,Object? level = null,Object? createdAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [VerificationStatusModel].
extension VerificationStatusModelPatterns on VerificationStatusModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VerificationStatusModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VerificationStatusModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VerificationStatusModel value)  $default,){
final _that = this;
switch (_that) {
case _VerificationStatusModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VerificationStatusModel value)?  $default,){
final _that = this;
switch (_that) {
case _VerificationStatusModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String nameAr,  String? description,  int level,  DateTime createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VerificationStatusModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.level,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String nameAr,  String? description,  int level,  DateTime createdAt)  $default,) {final _that = this;
switch (_that) {
case _VerificationStatusModel():
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.level,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String nameAr,  String? description,  int level,  DateTime createdAt)?  $default,) {final _that = this;
switch (_that) {
case _VerificationStatusModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.level,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VerificationStatusModel extends VerificationStatusModel {
  const _VerificationStatusModel({required this.id, required this.name, required this.nameAr, this.description, this.level = 0, required this.createdAt}): super._();
  factory _VerificationStatusModel.fromJson(Map<String, dynamic> json) => _$VerificationStatusModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String nameAr;
@override final  String? description;
@override@JsonKey() final  int level;
@override final  DateTime createdAt;

/// Create a copy of VerificationStatusModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VerificationStatusModelCopyWith<_VerificationStatusModel> get copyWith => __$VerificationStatusModelCopyWithImpl<_VerificationStatusModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VerificationStatusModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VerificationStatusModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.level, level) || other.level == level)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,level,createdAt);

@override
String toString() {
  return 'VerificationStatusModel(id: $id, name: $name, nameAr: $nameAr, description: $description, level: $level, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$VerificationStatusModelCopyWith<$Res> implements $VerificationStatusModelCopyWith<$Res> {
  factory _$VerificationStatusModelCopyWith(_VerificationStatusModel value, $Res Function(_VerificationStatusModel) _then) = __$VerificationStatusModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String nameAr, String? description, int level, DateTime createdAt
});




}
/// @nodoc
class __$VerificationStatusModelCopyWithImpl<$Res>
    implements _$VerificationStatusModelCopyWith<$Res> {
  __$VerificationStatusModelCopyWithImpl(this._self, this._then);

  final _VerificationStatusModel _self;
  final $Res Function(_VerificationStatusModel) _then;

/// Create a copy of VerificationStatusModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = freezed,Object? level = null,Object? createdAt = null,}) {
  return _then(_VerificationStatusModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$WithdrawalRequestModel {

 String get id; String get walletId; String? get transactionId; double get amount; WithdrawalRequestStatus get status; String? get bankName; String? get accountNumber; String? get accountHolderName; String? get iban; String? get swiftCode; DateTime? get processedAt; String? get processedBy; String? get rejectionReason; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of WithdrawalRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawalRequestModelCopyWith<WithdrawalRequestModel> get copyWith => _$WithdrawalRequestModelCopyWithImpl<WithdrawalRequestModel>(this as WithdrawalRequestModel, _$identity);

  /// Serializes this WithdrawalRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawalRequestModel&&(identical(other.id, id) || other.id == id)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.accountHolderName, accountHolderName) || other.accountHolderName == accountHolderName)&&(identical(other.iban, iban) || other.iban == iban)&&(identical(other.swiftCode, swiftCode) || other.swiftCode == swiftCode)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.processedBy, processedBy) || other.processedBy == processedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,walletId,transactionId,amount,status,bankName,accountNumber,accountHolderName,iban,swiftCode,processedAt,processedBy,rejectionReason,createdAt,updatedAt);

@override
String toString() {
  return 'WithdrawalRequestModel(id: $id, walletId: $walletId, transactionId: $transactionId, amount: $amount, status: $status, bankName: $bankName, accountNumber: $accountNumber, accountHolderName: $accountHolderName, iban: $iban, swiftCode: $swiftCode, processedAt: $processedAt, processedBy: $processedBy, rejectionReason: $rejectionReason, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $WithdrawalRequestModelCopyWith<$Res>  {
  factory $WithdrawalRequestModelCopyWith(WithdrawalRequestModel value, $Res Function(WithdrawalRequestModel) _then) = _$WithdrawalRequestModelCopyWithImpl;
@useResult
$Res call({
 String id, String walletId, String? transactionId, double amount, WithdrawalRequestStatus status, String? bankName, String? accountNumber, String? accountHolderName, String? iban, String? swiftCode, DateTime? processedAt, String? processedBy, String? rejectionReason, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class _$WithdrawalRequestModelCopyWithImpl<$Res>
    implements $WithdrawalRequestModelCopyWith<$Res> {
  _$WithdrawalRequestModelCopyWithImpl(this._self, this._then);

  final WithdrawalRequestModel _self;
  final $Res Function(WithdrawalRequestModel) _then;

/// Create a copy of WithdrawalRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? walletId = null,Object? transactionId = freezed,Object? amount = null,Object? status = null,Object? bankName = freezed,Object? accountNumber = freezed,Object? accountHolderName = freezed,Object? iban = freezed,Object? swiftCode = freezed,Object? processedAt = freezed,Object? processedBy = freezed,Object? rejectionReason = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WithdrawalRequestStatus,bankName: freezed == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,accountHolderName: freezed == accountHolderName ? _self.accountHolderName : accountHolderName // ignore: cast_nullable_to_non_nullable
as String?,iban: freezed == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as String?,swiftCode: freezed == swiftCode ? _self.swiftCode : swiftCode // ignore: cast_nullable_to_non_nullable
as String?,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,processedBy: freezed == processedBy ? _self.processedBy : processedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [WithdrawalRequestModel].
extension WithdrawalRequestModelPatterns on WithdrawalRequestModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WithdrawalRequestModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WithdrawalRequestModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WithdrawalRequestModel value)  $default,){
final _that = this;
switch (_that) {
case _WithdrawalRequestModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WithdrawalRequestModel value)?  $default,){
final _that = this;
switch (_that) {
case _WithdrawalRequestModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String walletId,  String? transactionId,  double amount,  WithdrawalRequestStatus status,  String? bankName,  String? accountNumber,  String? accountHolderName,  String? iban,  String? swiftCode,  DateTime? processedAt,  String? processedBy,  String? rejectionReason,  DateTime createdAt,  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WithdrawalRequestModel() when $default != null:
return $default(_that.id,_that.walletId,_that.transactionId,_that.amount,_that.status,_that.bankName,_that.accountNumber,_that.accountHolderName,_that.iban,_that.swiftCode,_that.processedAt,_that.processedBy,_that.rejectionReason,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String walletId,  String? transactionId,  double amount,  WithdrawalRequestStatus status,  String? bankName,  String? accountNumber,  String? accountHolderName,  String? iban,  String? swiftCode,  DateTime? processedAt,  String? processedBy,  String? rejectionReason,  DateTime createdAt,  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _WithdrawalRequestModel():
return $default(_that.id,_that.walletId,_that.transactionId,_that.amount,_that.status,_that.bankName,_that.accountNumber,_that.accountHolderName,_that.iban,_that.swiftCode,_that.processedAt,_that.processedBy,_that.rejectionReason,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String walletId,  String? transactionId,  double amount,  WithdrawalRequestStatus status,  String? bankName,  String? accountNumber,  String? accountHolderName,  String? iban,  String? swiftCode,  DateTime? processedAt,  String? processedBy,  String? rejectionReason,  DateTime createdAt,  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _WithdrawalRequestModel() when $default != null:
return $default(_that.id,_that.walletId,_that.transactionId,_that.amount,_that.status,_that.bankName,_that.accountNumber,_that.accountHolderName,_that.iban,_that.swiftCode,_that.processedAt,_that.processedBy,_that.rejectionReason,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WithdrawalRequestModel extends WithdrawalRequestModel {
  const _WithdrawalRequestModel({required this.id, required this.walletId, this.transactionId, required this.amount, this.status = WithdrawalRequestStatus.pending, this.bankName, this.accountNumber, this.accountHolderName, this.iban, this.swiftCode, this.processedAt, this.processedBy, this.rejectionReason, required this.createdAt, required this.updatedAt}): super._();
  factory _WithdrawalRequestModel.fromJson(Map<String, dynamic> json) => _$WithdrawalRequestModelFromJson(json);

@override final  String id;
@override final  String walletId;
@override final  String? transactionId;
@override final  double amount;
@override@JsonKey() final  WithdrawalRequestStatus status;
@override final  String? bankName;
@override final  String? accountNumber;
@override final  String? accountHolderName;
@override final  String? iban;
@override final  String? swiftCode;
@override final  DateTime? processedAt;
@override final  String? processedBy;
@override final  String? rejectionReason;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of WithdrawalRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawalRequestModelCopyWith<_WithdrawalRequestModel> get copyWith => __$WithdrawalRequestModelCopyWithImpl<_WithdrawalRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WithdrawalRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawalRequestModel&&(identical(other.id, id) || other.id == id)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.accountHolderName, accountHolderName) || other.accountHolderName == accountHolderName)&&(identical(other.iban, iban) || other.iban == iban)&&(identical(other.swiftCode, swiftCode) || other.swiftCode == swiftCode)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.processedBy, processedBy) || other.processedBy == processedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,walletId,transactionId,amount,status,bankName,accountNumber,accountHolderName,iban,swiftCode,processedAt,processedBy,rejectionReason,createdAt,updatedAt);

@override
String toString() {
  return 'WithdrawalRequestModel(id: $id, walletId: $walletId, transactionId: $transactionId, amount: $amount, status: $status, bankName: $bankName, accountNumber: $accountNumber, accountHolderName: $accountHolderName, iban: $iban, swiftCode: $swiftCode, processedAt: $processedAt, processedBy: $processedBy, rejectionReason: $rejectionReason, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$WithdrawalRequestModelCopyWith<$Res> implements $WithdrawalRequestModelCopyWith<$Res> {
  factory _$WithdrawalRequestModelCopyWith(_WithdrawalRequestModel value, $Res Function(_WithdrawalRequestModel) _then) = __$WithdrawalRequestModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String walletId, String? transactionId, double amount, WithdrawalRequestStatus status, String? bankName, String? accountNumber, String? accountHolderName, String? iban, String? swiftCode, DateTime? processedAt, String? processedBy, String? rejectionReason, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class __$WithdrawalRequestModelCopyWithImpl<$Res>
    implements _$WithdrawalRequestModelCopyWith<$Res> {
  __$WithdrawalRequestModelCopyWithImpl(this._self, this._then);

  final _WithdrawalRequestModel _self;
  final $Res Function(_WithdrawalRequestModel) _then;

/// Create a copy of WithdrawalRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? walletId = null,Object? transactionId = freezed,Object? amount = null,Object? status = null,Object? bankName = freezed,Object? accountNumber = freezed,Object? accountHolderName = freezed,Object? iban = freezed,Object? swiftCode = freezed,Object? processedAt = freezed,Object? processedBy = freezed,Object? rejectionReason = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_WithdrawalRequestModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WithdrawalRequestStatus,bankName: freezed == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,accountHolderName: freezed == accountHolderName ? _self.accountHolderName : accountHolderName // ignore: cast_nullable_to_non_nullable
as String?,iban: freezed == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as String?,swiftCode: freezed == swiftCode ? _self.swiftCode : swiftCode // ignore: cast_nullable_to_non_nullable
as String?,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,processedBy: freezed == processedBy ? _self.processedBy : processedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
