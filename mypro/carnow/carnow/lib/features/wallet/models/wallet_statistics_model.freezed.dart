// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_statistics_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WalletStatistics {

 double get totalDeposits; double get totalWithdrawals; double get totalTransfers; int get transactionCount; DateTime? get lastTransactionDate; double get dailySpent; double get monthlySpent; double get availableDailyLimit; double get availableMonthlyLimit;
/// Create a copy of WalletStatistics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletStatisticsCopyWith<WalletStatistics> get copyWith => _$WalletStatisticsCopyWithImpl<WalletStatistics>(this as WalletStatistics, _$identity);

  /// Serializes this WalletStatistics to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletStatistics&&(identical(other.totalDeposits, totalDeposits) || other.totalDeposits == totalDeposits)&&(identical(other.totalWithdrawals, totalWithdrawals) || other.totalWithdrawals == totalWithdrawals)&&(identical(other.totalTransfers, totalTransfers) || other.totalTransfers == totalTransfers)&&(identical(other.transactionCount, transactionCount) || other.transactionCount == transactionCount)&&(identical(other.lastTransactionDate, lastTransactionDate) || other.lastTransactionDate == lastTransactionDate)&&(identical(other.dailySpent, dailySpent) || other.dailySpent == dailySpent)&&(identical(other.monthlySpent, monthlySpent) || other.monthlySpent == monthlySpent)&&(identical(other.availableDailyLimit, availableDailyLimit) || other.availableDailyLimit == availableDailyLimit)&&(identical(other.availableMonthlyLimit, availableMonthlyLimit) || other.availableMonthlyLimit == availableMonthlyLimit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalDeposits,totalWithdrawals,totalTransfers,transactionCount,lastTransactionDate,dailySpent,monthlySpent,availableDailyLimit,availableMonthlyLimit);

@override
String toString() {
  return 'WalletStatistics(totalDeposits: $totalDeposits, totalWithdrawals: $totalWithdrawals, totalTransfers: $totalTransfers, transactionCount: $transactionCount, lastTransactionDate: $lastTransactionDate, dailySpent: $dailySpent, monthlySpent: $monthlySpent, availableDailyLimit: $availableDailyLimit, availableMonthlyLimit: $availableMonthlyLimit)';
}


}

/// @nodoc
abstract mixin class $WalletStatisticsCopyWith<$Res>  {
  factory $WalletStatisticsCopyWith(WalletStatistics value, $Res Function(WalletStatistics) _then) = _$WalletStatisticsCopyWithImpl;
@useResult
$Res call({
 double totalDeposits, double totalWithdrawals, double totalTransfers, int transactionCount, DateTime? lastTransactionDate, double dailySpent, double monthlySpent, double availableDailyLimit, double availableMonthlyLimit
});




}
/// @nodoc
class _$WalletStatisticsCopyWithImpl<$Res>
    implements $WalletStatisticsCopyWith<$Res> {
  _$WalletStatisticsCopyWithImpl(this._self, this._then);

  final WalletStatistics _self;
  final $Res Function(WalletStatistics) _then;

/// Create a copy of WalletStatistics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalDeposits = null,Object? totalWithdrawals = null,Object? totalTransfers = null,Object? transactionCount = null,Object? lastTransactionDate = freezed,Object? dailySpent = null,Object? monthlySpent = null,Object? availableDailyLimit = null,Object? availableMonthlyLimit = null,}) {
  return _then(_self.copyWith(
totalDeposits: null == totalDeposits ? _self.totalDeposits : totalDeposits // ignore: cast_nullable_to_non_nullable
as double,totalWithdrawals: null == totalWithdrawals ? _self.totalWithdrawals : totalWithdrawals // ignore: cast_nullable_to_non_nullable
as double,totalTransfers: null == totalTransfers ? _self.totalTransfers : totalTransfers // ignore: cast_nullable_to_non_nullable
as double,transactionCount: null == transactionCount ? _self.transactionCount : transactionCount // ignore: cast_nullable_to_non_nullable
as int,lastTransactionDate: freezed == lastTransactionDate ? _self.lastTransactionDate : lastTransactionDate // ignore: cast_nullable_to_non_nullable
as DateTime?,dailySpent: null == dailySpent ? _self.dailySpent : dailySpent // ignore: cast_nullable_to_non_nullable
as double,monthlySpent: null == monthlySpent ? _self.monthlySpent : monthlySpent // ignore: cast_nullable_to_non_nullable
as double,availableDailyLimit: null == availableDailyLimit ? _self.availableDailyLimit : availableDailyLimit // ignore: cast_nullable_to_non_nullable
as double,availableMonthlyLimit: null == availableMonthlyLimit ? _self.availableMonthlyLimit : availableMonthlyLimit // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletStatistics].
extension WalletStatisticsPatterns on WalletStatistics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletStatistics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletStatistics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletStatistics value)  $default,){
final _that = this;
switch (_that) {
case _WalletStatistics():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletStatistics value)?  $default,){
final _that = this;
switch (_that) {
case _WalletStatistics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double totalDeposits,  double totalWithdrawals,  double totalTransfers,  int transactionCount,  DateTime? lastTransactionDate,  double dailySpent,  double monthlySpent,  double availableDailyLimit,  double availableMonthlyLimit)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletStatistics() when $default != null:
return $default(_that.totalDeposits,_that.totalWithdrawals,_that.totalTransfers,_that.transactionCount,_that.lastTransactionDate,_that.dailySpent,_that.monthlySpent,_that.availableDailyLimit,_that.availableMonthlyLimit);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double totalDeposits,  double totalWithdrawals,  double totalTransfers,  int transactionCount,  DateTime? lastTransactionDate,  double dailySpent,  double monthlySpent,  double availableDailyLimit,  double availableMonthlyLimit)  $default,) {final _that = this;
switch (_that) {
case _WalletStatistics():
return $default(_that.totalDeposits,_that.totalWithdrawals,_that.totalTransfers,_that.transactionCount,_that.lastTransactionDate,_that.dailySpent,_that.monthlySpent,_that.availableDailyLimit,_that.availableMonthlyLimit);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double totalDeposits,  double totalWithdrawals,  double totalTransfers,  int transactionCount,  DateTime? lastTransactionDate,  double dailySpent,  double monthlySpent,  double availableDailyLimit,  double availableMonthlyLimit)?  $default,) {final _that = this;
switch (_that) {
case _WalletStatistics() when $default != null:
return $default(_that.totalDeposits,_that.totalWithdrawals,_that.totalTransfers,_that.transactionCount,_that.lastTransactionDate,_that.dailySpent,_that.monthlySpent,_that.availableDailyLimit,_that.availableMonthlyLimit);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletStatistics extends WalletStatistics {
  const _WalletStatistics({this.totalDeposits = 0.0, this.totalWithdrawals = 0.0, this.totalTransfers = 0.0, this.transactionCount = 0, this.lastTransactionDate, this.dailySpent = 0.0, this.monthlySpent = 0.0, this.availableDailyLimit = 0.0, this.availableMonthlyLimit = 0.0}): super._();
  factory _WalletStatistics.fromJson(Map<String, dynamic> json) => _$WalletStatisticsFromJson(json);

@override@JsonKey() final  double totalDeposits;
@override@JsonKey() final  double totalWithdrawals;
@override@JsonKey() final  double totalTransfers;
@override@JsonKey() final  int transactionCount;
@override final  DateTime? lastTransactionDate;
@override@JsonKey() final  double dailySpent;
@override@JsonKey() final  double monthlySpent;
@override@JsonKey() final  double availableDailyLimit;
@override@JsonKey() final  double availableMonthlyLimit;

/// Create a copy of WalletStatistics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletStatisticsCopyWith<_WalletStatistics> get copyWith => __$WalletStatisticsCopyWithImpl<_WalletStatistics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletStatisticsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletStatistics&&(identical(other.totalDeposits, totalDeposits) || other.totalDeposits == totalDeposits)&&(identical(other.totalWithdrawals, totalWithdrawals) || other.totalWithdrawals == totalWithdrawals)&&(identical(other.totalTransfers, totalTransfers) || other.totalTransfers == totalTransfers)&&(identical(other.transactionCount, transactionCount) || other.transactionCount == transactionCount)&&(identical(other.lastTransactionDate, lastTransactionDate) || other.lastTransactionDate == lastTransactionDate)&&(identical(other.dailySpent, dailySpent) || other.dailySpent == dailySpent)&&(identical(other.monthlySpent, monthlySpent) || other.monthlySpent == monthlySpent)&&(identical(other.availableDailyLimit, availableDailyLimit) || other.availableDailyLimit == availableDailyLimit)&&(identical(other.availableMonthlyLimit, availableMonthlyLimit) || other.availableMonthlyLimit == availableMonthlyLimit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalDeposits,totalWithdrawals,totalTransfers,transactionCount,lastTransactionDate,dailySpent,monthlySpent,availableDailyLimit,availableMonthlyLimit);

@override
String toString() {
  return 'WalletStatistics(totalDeposits: $totalDeposits, totalWithdrawals: $totalWithdrawals, totalTransfers: $totalTransfers, transactionCount: $transactionCount, lastTransactionDate: $lastTransactionDate, dailySpent: $dailySpent, monthlySpent: $monthlySpent, availableDailyLimit: $availableDailyLimit, availableMonthlyLimit: $availableMonthlyLimit)';
}


}

/// @nodoc
abstract mixin class _$WalletStatisticsCopyWith<$Res> implements $WalletStatisticsCopyWith<$Res> {
  factory _$WalletStatisticsCopyWith(_WalletStatistics value, $Res Function(_WalletStatistics) _then) = __$WalletStatisticsCopyWithImpl;
@override @useResult
$Res call({
 double totalDeposits, double totalWithdrawals, double totalTransfers, int transactionCount, DateTime? lastTransactionDate, double dailySpent, double monthlySpent, double availableDailyLimit, double availableMonthlyLimit
});




}
/// @nodoc
class __$WalletStatisticsCopyWithImpl<$Res>
    implements _$WalletStatisticsCopyWith<$Res> {
  __$WalletStatisticsCopyWithImpl(this._self, this._then);

  final _WalletStatistics _self;
  final $Res Function(_WalletStatistics) _then;

/// Create a copy of WalletStatistics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalDeposits = null,Object? totalWithdrawals = null,Object? totalTransfers = null,Object? transactionCount = null,Object? lastTransactionDate = freezed,Object? dailySpent = null,Object? monthlySpent = null,Object? availableDailyLimit = null,Object? availableMonthlyLimit = null,}) {
  return _then(_WalletStatistics(
totalDeposits: null == totalDeposits ? _self.totalDeposits : totalDeposits // ignore: cast_nullable_to_non_nullable
as double,totalWithdrawals: null == totalWithdrawals ? _self.totalWithdrawals : totalWithdrawals // ignore: cast_nullable_to_non_nullable
as double,totalTransfers: null == totalTransfers ? _self.totalTransfers : totalTransfers // ignore: cast_nullable_to_non_nullable
as double,transactionCount: null == transactionCount ? _self.transactionCount : transactionCount // ignore: cast_nullable_to_non_nullable
as int,lastTransactionDate: freezed == lastTransactionDate ? _self.lastTransactionDate : lastTransactionDate // ignore: cast_nullable_to_non_nullable
as DateTime?,dailySpent: null == dailySpent ? _self.dailySpent : dailySpent // ignore: cast_nullable_to_non_nullable
as double,monthlySpent: null == monthlySpent ? _self.monthlySpent : monthlySpent // ignore: cast_nullable_to_non_nullable
as double,availableDailyLimit: null == availableDailyLimit ? _self.availableDailyLimit : availableDailyLimit // ignore: cast_nullable_to_non_nullable
as double,availableMonthlyLimit: null == availableMonthlyLimit ? _self.availableMonthlyLimit : availableMonthlyLimit // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
