// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'compare_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$compareItemsHash() => r'ba89cc1bc1ee05902860677b992971dfa0626f5f';

/// See also [CompareItems].
@ProviderFor(CompareItems)
final compareItemsProvider =
    NotifierProvider<CompareItems, List<ProductModel>>.internal(
      CompareItems.new,
      name: r'compareItemsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$compareItemsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CompareItems = Notifier<List<ProductModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
