// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_comparison.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductComparison {

 String get id; String get userId;@JsonKey(includeFromJson: false, includeToJson: false) List<ProductModel>? get products; List<ComparisonCriteria> get criteria; Map<String, ComparisonScore> get scores; String? get title; String? get description; ComparisonType? get type; bool? get isPublic;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;
/// Create a copy of ProductComparison
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductComparisonCopyWith<ProductComparison> get copyWith => _$ProductComparisonCopyWithImpl<ProductComparison>(this as ProductComparison, _$identity);

  /// Serializes this ProductComparison to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductComparison&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&const DeepCollectionEquality().equals(other.products, products)&&const DeepCollectionEquality().equals(other.criteria, criteria)&&const DeepCollectionEquality().equals(other.scores, scores)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.type, type) || other.type == type)&&(identical(other.isPublic, isPublic) || other.isPublic == isPublic)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,const DeepCollectionEquality().hash(products),const DeepCollectionEquality().hash(criteria),const DeepCollectionEquality().hash(scores),title,description,type,isPublic,createdAt,updatedAt);

@override
String toString() {
  return 'ProductComparison(id: $id, userId: $userId, products: $products, criteria: $criteria, scores: $scores, title: $title, description: $description, type: $type, isPublic: $isPublic, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $ProductComparisonCopyWith<$Res>  {
  factory $ProductComparisonCopyWith(ProductComparison value, $Res Function(ProductComparison) _then) = _$ProductComparisonCopyWithImpl;
@useResult
$Res call({
 String id, String userId,@JsonKey(includeFromJson: false, includeToJson: false) List<ProductModel>? products, List<ComparisonCriteria> criteria, Map<String, ComparisonScore> scores, String? title, String? description, ComparisonType? type, bool? isPublic,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class _$ProductComparisonCopyWithImpl<$Res>
    implements $ProductComparisonCopyWith<$Res> {
  _$ProductComparisonCopyWithImpl(this._self, this._then);

  final ProductComparison _self;
  final $Res Function(ProductComparison) _then;

/// Create a copy of ProductComparison
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? products = freezed,Object? criteria = null,Object? scores = null,Object? title = freezed,Object? description = freezed,Object? type = freezed,Object? isPublic = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,products: freezed == products ? _self.products : products // ignore: cast_nullable_to_non_nullable
as List<ProductModel>?,criteria: null == criteria ? _self.criteria : criteria // ignore: cast_nullable_to_non_nullable
as List<ComparisonCriteria>,scores: null == scores ? _self.scores : scores // ignore: cast_nullable_to_non_nullable
as Map<String, ComparisonScore>,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ComparisonType?,isPublic: freezed == isPublic ? _self.isPublic : isPublic // ignore: cast_nullable_to_non_nullable
as bool?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductComparison].
extension ProductComparisonPatterns on ProductComparison {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductComparison value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductComparison() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductComparison value)  $default,){
final _that = this;
switch (_that) {
case _ProductComparison():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductComparison value)?  $default,){
final _that = this;
switch (_that) {
case _ProductComparison() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId, @JsonKey(includeFromJson: false, includeToJson: false)  List<ProductModel>? products,  List<ComparisonCriteria> criteria,  Map<String, ComparisonScore> scores,  String? title,  String? description,  ComparisonType? type,  bool? isPublic, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductComparison() when $default != null:
return $default(_that.id,_that.userId,_that.products,_that.criteria,_that.scores,_that.title,_that.description,_that.type,_that.isPublic,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId, @JsonKey(includeFromJson: false, includeToJson: false)  List<ProductModel>? products,  List<ComparisonCriteria> criteria,  Map<String, ComparisonScore> scores,  String? title,  String? description,  ComparisonType? type,  bool? isPublic, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _ProductComparison():
return $default(_that.id,_that.userId,_that.products,_that.criteria,_that.scores,_that.title,_that.description,_that.type,_that.isPublic,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId, @JsonKey(includeFromJson: false, includeToJson: false)  List<ProductModel>? products,  List<ComparisonCriteria> criteria,  Map<String, ComparisonScore> scores,  String? title,  String? description,  ComparisonType? type,  bool? isPublic, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _ProductComparison() when $default != null:
return $default(_that.id,_that.userId,_that.products,_that.criteria,_that.scores,_that.title,_that.description,_that.type,_that.isPublic,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductComparison implements ProductComparison {
  const _ProductComparison({required this.id, required this.userId, @JsonKey(includeFromJson: false, includeToJson: false) final  List<ProductModel>? products, required final  List<ComparisonCriteria> criteria, required final  Map<String, ComparisonScore> scores, this.title, this.description, this.type, this.isPublic, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt}): _products = products,_criteria = criteria,_scores = scores;
  factory _ProductComparison.fromJson(Map<String, dynamic> json) => _$ProductComparisonFromJson(json);

@override final  String id;
@override final  String userId;
 final  List<ProductModel>? _products;
@override@JsonKey(includeFromJson: false, includeToJson: false) List<ProductModel>? get products {
  final value = _products;
  if (value == null) return null;
  if (_products is EqualUnmodifiableListView) return _products;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<ComparisonCriteria> _criteria;
@override List<ComparisonCriteria> get criteria {
  if (_criteria is EqualUnmodifiableListView) return _criteria;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_criteria);
}

 final  Map<String, ComparisonScore> _scores;
@override Map<String, ComparisonScore> get scores {
  if (_scores is EqualUnmodifiableMapView) return _scores;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_scores);
}

@override final  String? title;
@override final  String? description;
@override final  ComparisonType? type;
@override final  bool? isPublic;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;

/// Create a copy of ProductComparison
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductComparisonCopyWith<_ProductComparison> get copyWith => __$ProductComparisonCopyWithImpl<_ProductComparison>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductComparisonToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductComparison&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&const DeepCollectionEquality().equals(other._products, _products)&&const DeepCollectionEquality().equals(other._criteria, _criteria)&&const DeepCollectionEquality().equals(other._scores, _scores)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.type, type) || other.type == type)&&(identical(other.isPublic, isPublic) || other.isPublic == isPublic)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,const DeepCollectionEquality().hash(_products),const DeepCollectionEquality().hash(_criteria),const DeepCollectionEquality().hash(_scores),title,description,type,isPublic,createdAt,updatedAt);

@override
String toString() {
  return 'ProductComparison(id: $id, userId: $userId, products: $products, criteria: $criteria, scores: $scores, title: $title, description: $description, type: $type, isPublic: $isPublic, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$ProductComparisonCopyWith<$Res> implements $ProductComparisonCopyWith<$Res> {
  factory _$ProductComparisonCopyWith(_ProductComparison value, $Res Function(_ProductComparison) _then) = __$ProductComparisonCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId,@JsonKey(includeFromJson: false, includeToJson: false) List<ProductModel>? products, List<ComparisonCriteria> criteria, Map<String, ComparisonScore> scores, String? title, String? description, ComparisonType? type, bool? isPublic,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class __$ProductComparisonCopyWithImpl<$Res>
    implements _$ProductComparisonCopyWith<$Res> {
  __$ProductComparisonCopyWithImpl(this._self, this._then);

  final _ProductComparison _self;
  final $Res Function(_ProductComparison) _then;

/// Create a copy of ProductComparison
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? products = freezed,Object? criteria = null,Object? scores = null,Object? title = freezed,Object? description = freezed,Object? type = freezed,Object? isPublic = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_ProductComparison(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,products: freezed == products ? _self._products : products // ignore: cast_nullable_to_non_nullable
as List<ProductModel>?,criteria: null == criteria ? _self._criteria : criteria // ignore: cast_nullable_to_non_nullable
as List<ComparisonCriteria>,scores: null == scores ? _self._scores : scores // ignore: cast_nullable_to_non_nullable
as Map<String, ComparisonScore>,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ComparisonType?,isPublic: freezed == isPublic ? _self.isPublic : isPublic // ignore: cast_nullable_to_non_nullable
as bool?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$ComparisonCriteria {

 String get id; String get name; ComparisonCriteriaType get type; double get weight; bool get isHigherBetter; String? get description; String? get unit; double? get minValue; double? get maxValue;
/// Create a copy of ComparisonCriteria
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ComparisonCriteriaCopyWith<ComparisonCriteria> get copyWith => _$ComparisonCriteriaCopyWithImpl<ComparisonCriteria>(this as ComparisonCriteria, _$identity);

  /// Serializes this ComparisonCriteria to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ComparisonCriteria&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.weight, weight) || other.weight == weight)&&(identical(other.isHigherBetter, isHigherBetter) || other.isHigherBetter == isHigherBetter)&&(identical(other.description, description) || other.description == description)&&(identical(other.unit, unit) || other.unit == unit)&&(identical(other.minValue, minValue) || other.minValue == minValue)&&(identical(other.maxValue, maxValue) || other.maxValue == maxValue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,weight,isHigherBetter,description,unit,minValue,maxValue);

@override
String toString() {
  return 'ComparisonCriteria(id: $id, name: $name, type: $type, weight: $weight, isHigherBetter: $isHigherBetter, description: $description, unit: $unit, minValue: $minValue, maxValue: $maxValue)';
}


}

/// @nodoc
abstract mixin class $ComparisonCriteriaCopyWith<$Res>  {
  factory $ComparisonCriteriaCopyWith(ComparisonCriteria value, $Res Function(ComparisonCriteria) _then) = _$ComparisonCriteriaCopyWithImpl;
@useResult
$Res call({
 String id, String name, ComparisonCriteriaType type, double weight, bool isHigherBetter, String? description, String? unit, double? minValue, double? maxValue
});




}
/// @nodoc
class _$ComparisonCriteriaCopyWithImpl<$Res>
    implements $ComparisonCriteriaCopyWith<$Res> {
  _$ComparisonCriteriaCopyWithImpl(this._self, this._then);

  final ComparisonCriteria _self;
  final $Res Function(ComparisonCriteria) _then;

/// Create a copy of ComparisonCriteria
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? type = null,Object? weight = null,Object? isHigherBetter = null,Object? description = freezed,Object? unit = freezed,Object? minValue = freezed,Object? maxValue = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ComparisonCriteriaType,weight: null == weight ? _self.weight : weight // ignore: cast_nullable_to_non_nullable
as double,isHigherBetter: null == isHigherBetter ? _self.isHigherBetter : isHigherBetter // ignore: cast_nullable_to_non_nullable
as bool,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,unit: freezed == unit ? _self.unit : unit // ignore: cast_nullable_to_non_nullable
as String?,minValue: freezed == minValue ? _self.minValue : minValue // ignore: cast_nullable_to_non_nullable
as double?,maxValue: freezed == maxValue ? _self.maxValue : maxValue // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// Adds pattern-matching-related methods to [ComparisonCriteria].
extension ComparisonCriteriaPatterns on ComparisonCriteria {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ComparisonCriteria value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ComparisonCriteria() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ComparisonCriteria value)  $default,){
final _that = this;
switch (_that) {
case _ComparisonCriteria():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ComparisonCriteria value)?  $default,){
final _that = this;
switch (_that) {
case _ComparisonCriteria() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  ComparisonCriteriaType type,  double weight,  bool isHigherBetter,  String? description,  String? unit,  double? minValue,  double? maxValue)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ComparisonCriteria() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.weight,_that.isHigherBetter,_that.description,_that.unit,_that.minValue,_that.maxValue);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  ComparisonCriteriaType type,  double weight,  bool isHigherBetter,  String? description,  String? unit,  double? minValue,  double? maxValue)  $default,) {final _that = this;
switch (_that) {
case _ComparisonCriteria():
return $default(_that.id,_that.name,_that.type,_that.weight,_that.isHigherBetter,_that.description,_that.unit,_that.minValue,_that.maxValue);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  ComparisonCriteriaType type,  double weight,  bool isHigherBetter,  String? description,  String? unit,  double? minValue,  double? maxValue)?  $default,) {final _that = this;
switch (_that) {
case _ComparisonCriteria() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.weight,_that.isHigherBetter,_that.description,_that.unit,_that.minValue,_that.maxValue);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ComparisonCriteria implements ComparisonCriteria {
  const _ComparisonCriteria({required this.id, required this.name, required this.type, required this.weight, required this.isHigherBetter, this.description, this.unit, this.minValue, this.maxValue});
  factory _ComparisonCriteria.fromJson(Map<String, dynamic> json) => _$ComparisonCriteriaFromJson(json);

@override final  String id;
@override final  String name;
@override final  ComparisonCriteriaType type;
@override final  double weight;
@override final  bool isHigherBetter;
@override final  String? description;
@override final  String? unit;
@override final  double? minValue;
@override final  double? maxValue;

/// Create a copy of ComparisonCriteria
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ComparisonCriteriaCopyWith<_ComparisonCriteria> get copyWith => __$ComparisonCriteriaCopyWithImpl<_ComparisonCriteria>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ComparisonCriteriaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ComparisonCriteria&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.weight, weight) || other.weight == weight)&&(identical(other.isHigherBetter, isHigherBetter) || other.isHigherBetter == isHigherBetter)&&(identical(other.description, description) || other.description == description)&&(identical(other.unit, unit) || other.unit == unit)&&(identical(other.minValue, minValue) || other.minValue == minValue)&&(identical(other.maxValue, maxValue) || other.maxValue == maxValue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,weight,isHigherBetter,description,unit,minValue,maxValue);

@override
String toString() {
  return 'ComparisonCriteria(id: $id, name: $name, type: $type, weight: $weight, isHigherBetter: $isHigherBetter, description: $description, unit: $unit, minValue: $minValue, maxValue: $maxValue)';
}


}

/// @nodoc
abstract mixin class _$ComparisonCriteriaCopyWith<$Res> implements $ComparisonCriteriaCopyWith<$Res> {
  factory _$ComparisonCriteriaCopyWith(_ComparisonCriteria value, $Res Function(_ComparisonCriteria) _then) = __$ComparisonCriteriaCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, ComparisonCriteriaType type, double weight, bool isHigherBetter, String? description, String? unit, double? minValue, double? maxValue
});




}
/// @nodoc
class __$ComparisonCriteriaCopyWithImpl<$Res>
    implements _$ComparisonCriteriaCopyWith<$Res> {
  __$ComparisonCriteriaCopyWithImpl(this._self, this._then);

  final _ComparisonCriteria _self;
  final $Res Function(_ComparisonCriteria) _then;

/// Create a copy of ComparisonCriteria
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? type = null,Object? weight = null,Object? isHigherBetter = null,Object? description = freezed,Object? unit = freezed,Object? minValue = freezed,Object? maxValue = freezed,}) {
  return _then(_ComparisonCriteria(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ComparisonCriteriaType,weight: null == weight ? _self.weight : weight // ignore: cast_nullable_to_non_nullable
as double,isHigherBetter: null == isHigherBetter ? _self.isHigherBetter : isHigherBetter // ignore: cast_nullable_to_non_nullable
as bool,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,unit: freezed == unit ? _self.unit : unit // ignore: cast_nullable_to_non_nullable
as String?,minValue: freezed == minValue ? _self.minValue : minValue // ignore: cast_nullable_to_non_nullable
as double?,maxValue: freezed == maxValue ? _self.maxValue : maxValue // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}


/// @nodoc
mixin _$ComparisonScore {

 String get productId; double get totalScore; Map<String, double> get criteriaScores; Map<String, dynamic> get normalizedValues; bool get isHigherBetter; String? get recommendation; List<String>? get pros; List<String>? get cons;
/// Create a copy of ComparisonScore
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ComparisonScoreCopyWith<ComparisonScore> get copyWith => _$ComparisonScoreCopyWithImpl<ComparisonScore>(this as ComparisonScore, _$identity);

  /// Serializes this ComparisonScore to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ComparisonScore&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.totalScore, totalScore) || other.totalScore == totalScore)&&const DeepCollectionEquality().equals(other.criteriaScores, criteriaScores)&&const DeepCollectionEquality().equals(other.normalizedValues, normalizedValues)&&(identical(other.isHigherBetter, isHigherBetter) || other.isHigherBetter == isHigherBetter)&&(identical(other.recommendation, recommendation) || other.recommendation == recommendation)&&const DeepCollectionEquality().equals(other.pros, pros)&&const DeepCollectionEquality().equals(other.cons, cons));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,totalScore,const DeepCollectionEquality().hash(criteriaScores),const DeepCollectionEquality().hash(normalizedValues),isHigherBetter,recommendation,const DeepCollectionEquality().hash(pros),const DeepCollectionEquality().hash(cons));

@override
String toString() {
  return 'ComparisonScore(productId: $productId, totalScore: $totalScore, criteriaScores: $criteriaScores, normalizedValues: $normalizedValues, isHigherBetter: $isHigherBetter, recommendation: $recommendation, pros: $pros, cons: $cons)';
}


}

/// @nodoc
abstract mixin class $ComparisonScoreCopyWith<$Res>  {
  factory $ComparisonScoreCopyWith(ComparisonScore value, $Res Function(ComparisonScore) _then) = _$ComparisonScoreCopyWithImpl;
@useResult
$Res call({
 String productId, double totalScore, Map<String, double> criteriaScores, Map<String, dynamic> normalizedValues, bool isHigherBetter, String? recommendation, List<String>? pros, List<String>? cons
});




}
/// @nodoc
class _$ComparisonScoreCopyWithImpl<$Res>
    implements $ComparisonScoreCopyWith<$Res> {
  _$ComparisonScoreCopyWithImpl(this._self, this._then);

  final ComparisonScore _self;
  final $Res Function(ComparisonScore) _then;

/// Create a copy of ComparisonScore
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = null,Object? totalScore = null,Object? criteriaScores = null,Object? normalizedValues = null,Object? isHigherBetter = null,Object? recommendation = freezed,Object? pros = freezed,Object? cons = freezed,}) {
  return _then(_self.copyWith(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,totalScore: null == totalScore ? _self.totalScore : totalScore // ignore: cast_nullable_to_non_nullable
as double,criteriaScores: null == criteriaScores ? _self.criteriaScores : criteriaScores // ignore: cast_nullable_to_non_nullable
as Map<String, double>,normalizedValues: null == normalizedValues ? _self.normalizedValues : normalizedValues // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,isHigherBetter: null == isHigherBetter ? _self.isHigherBetter : isHigherBetter // ignore: cast_nullable_to_non_nullable
as bool,recommendation: freezed == recommendation ? _self.recommendation : recommendation // ignore: cast_nullable_to_non_nullable
as String?,pros: freezed == pros ? _self.pros : pros // ignore: cast_nullable_to_non_nullable
as List<String>?,cons: freezed == cons ? _self.cons : cons // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}

}


/// Adds pattern-matching-related methods to [ComparisonScore].
extension ComparisonScorePatterns on ComparisonScore {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ComparisonScore value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ComparisonScore() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ComparisonScore value)  $default,){
final _that = this;
switch (_that) {
case _ComparisonScore():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ComparisonScore value)?  $default,){
final _that = this;
switch (_that) {
case _ComparisonScore() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String productId,  double totalScore,  Map<String, double> criteriaScores,  Map<String, dynamic> normalizedValues,  bool isHigherBetter,  String? recommendation,  List<String>? pros,  List<String>? cons)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ComparisonScore() when $default != null:
return $default(_that.productId,_that.totalScore,_that.criteriaScores,_that.normalizedValues,_that.isHigherBetter,_that.recommendation,_that.pros,_that.cons);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String productId,  double totalScore,  Map<String, double> criteriaScores,  Map<String, dynamic> normalizedValues,  bool isHigherBetter,  String? recommendation,  List<String>? pros,  List<String>? cons)  $default,) {final _that = this;
switch (_that) {
case _ComparisonScore():
return $default(_that.productId,_that.totalScore,_that.criteriaScores,_that.normalizedValues,_that.isHigherBetter,_that.recommendation,_that.pros,_that.cons);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String productId,  double totalScore,  Map<String, double> criteriaScores,  Map<String, dynamic> normalizedValues,  bool isHigherBetter,  String? recommendation,  List<String>? pros,  List<String>? cons)?  $default,) {final _that = this;
switch (_that) {
case _ComparisonScore() when $default != null:
return $default(_that.productId,_that.totalScore,_that.criteriaScores,_that.normalizedValues,_that.isHigherBetter,_that.recommendation,_that.pros,_that.cons);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ComparisonScore implements ComparisonScore {
  const _ComparisonScore({required this.productId, required this.totalScore, required final  Map<String, double> criteriaScores, required final  Map<String, dynamic> normalizedValues, required this.isHigherBetter, this.recommendation, final  List<String>? pros, final  List<String>? cons}): _criteriaScores = criteriaScores,_normalizedValues = normalizedValues,_pros = pros,_cons = cons;
  factory _ComparisonScore.fromJson(Map<String, dynamic> json) => _$ComparisonScoreFromJson(json);

@override final  String productId;
@override final  double totalScore;
 final  Map<String, double> _criteriaScores;
@override Map<String, double> get criteriaScores {
  if (_criteriaScores is EqualUnmodifiableMapView) return _criteriaScores;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_criteriaScores);
}

 final  Map<String, dynamic> _normalizedValues;
@override Map<String, dynamic> get normalizedValues {
  if (_normalizedValues is EqualUnmodifiableMapView) return _normalizedValues;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_normalizedValues);
}

@override final  bool isHigherBetter;
@override final  String? recommendation;
 final  List<String>? _pros;
@override List<String>? get pros {
  final value = _pros;
  if (value == null) return null;
  if (_pros is EqualUnmodifiableListView) return _pros;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _cons;
@override List<String>? get cons {
  final value = _cons;
  if (value == null) return null;
  if (_cons is EqualUnmodifiableListView) return _cons;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of ComparisonScore
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ComparisonScoreCopyWith<_ComparisonScore> get copyWith => __$ComparisonScoreCopyWithImpl<_ComparisonScore>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ComparisonScoreToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ComparisonScore&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.totalScore, totalScore) || other.totalScore == totalScore)&&const DeepCollectionEquality().equals(other._criteriaScores, _criteriaScores)&&const DeepCollectionEquality().equals(other._normalizedValues, _normalizedValues)&&(identical(other.isHigherBetter, isHigherBetter) || other.isHigherBetter == isHigherBetter)&&(identical(other.recommendation, recommendation) || other.recommendation == recommendation)&&const DeepCollectionEquality().equals(other._pros, _pros)&&const DeepCollectionEquality().equals(other._cons, _cons));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,totalScore,const DeepCollectionEquality().hash(_criteriaScores),const DeepCollectionEquality().hash(_normalizedValues),isHigherBetter,recommendation,const DeepCollectionEquality().hash(_pros),const DeepCollectionEquality().hash(_cons));

@override
String toString() {
  return 'ComparisonScore(productId: $productId, totalScore: $totalScore, criteriaScores: $criteriaScores, normalizedValues: $normalizedValues, isHigherBetter: $isHigherBetter, recommendation: $recommendation, pros: $pros, cons: $cons)';
}


}

/// @nodoc
abstract mixin class _$ComparisonScoreCopyWith<$Res> implements $ComparisonScoreCopyWith<$Res> {
  factory _$ComparisonScoreCopyWith(_ComparisonScore value, $Res Function(_ComparisonScore) _then) = __$ComparisonScoreCopyWithImpl;
@override @useResult
$Res call({
 String productId, double totalScore, Map<String, double> criteriaScores, Map<String, dynamic> normalizedValues, bool isHigherBetter, String? recommendation, List<String>? pros, List<String>? cons
});




}
/// @nodoc
class __$ComparisonScoreCopyWithImpl<$Res>
    implements _$ComparisonScoreCopyWith<$Res> {
  __$ComparisonScoreCopyWithImpl(this._self, this._then);

  final _ComparisonScore _self;
  final $Res Function(_ComparisonScore) _then;

/// Create a copy of ComparisonScore
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? totalScore = null,Object? criteriaScores = null,Object? normalizedValues = null,Object? isHigherBetter = null,Object? recommendation = freezed,Object? pros = freezed,Object? cons = freezed,}) {
  return _then(_ComparisonScore(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,totalScore: null == totalScore ? _self.totalScore : totalScore // ignore: cast_nullable_to_non_nullable
as double,criteriaScores: null == criteriaScores ? _self._criteriaScores : criteriaScores // ignore: cast_nullable_to_non_nullable
as Map<String, double>,normalizedValues: null == normalizedValues ? _self._normalizedValues : normalizedValues // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,isHigherBetter: null == isHigherBetter ? _self.isHigherBetter : isHigherBetter // ignore: cast_nullable_to_non_nullable
as bool,recommendation: freezed == recommendation ? _self.recommendation : recommendation // ignore: cast_nullable_to_non_nullable
as String?,pros: freezed == pros ? _self._pros : pros // ignore: cast_nullable_to_non_nullable
as List<String>?,cons: freezed == cons ? _self._cons : cons // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}


}


/// @nodoc
mixin _$ComparisonResult {

@JsonKey(includeFromJson: false, includeToJson: false) ProductModel? get winner;@JsonKey(includeFromJson: false, includeToJson: false) List<ProductModel>? get rankedProducts;@JsonKey(includeFromJson: false, includeToJson: false) Map<String, List<ProductModel>>? get categoryWinners; String get summary; Map<String, dynamic> get insights;
/// Create a copy of ComparisonResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ComparisonResultCopyWith<ComparisonResult> get copyWith => _$ComparisonResultCopyWithImpl<ComparisonResult>(this as ComparisonResult, _$identity);

  /// Serializes this ComparisonResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ComparisonResult&&(identical(other.winner, winner) || other.winner == winner)&&const DeepCollectionEquality().equals(other.rankedProducts, rankedProducts)&&const DeepCollectionEquality().equals(other.categoryWinners, categoryWinners)&&(identical(other.summary, summary) || other.summary == summary)&&const DeepCollectionEquality().equals(other.insights, insights));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,winner,const DeepCollectionEquality().hash(rankedProducts),const DeepCollectionEquality().hash(categoryWinners),summary,const DeepCollectionEquality().hash(insights));

@override
String toString() {
  return 'ComparisonResult(winner: $winner, rankedProducts: $rankedProducts, categoryWinners: $categoryWinners, summary: $summary, insights: $insights)';
}


}

/// @nodoc
abstract mixin class $ComparisonResultCopyWith<$Res>  {
  factory $ComparisonResultCopyWith(ComparisonResult value, $Res Function(ComparisonResult) _then) = _$ComparisonResultCopyWithImpl;
@useResult
$Res call({
@JsonKey(includeFromJson: false, includeToJson: false) ProductModel? winner,@JsonKey(includeFromJson: false, includeToJson: false) List<ProductModel>? rankedProducts,@JsonKey(includeFromJson: false, includeToJson: false) Map<String, List<ProductModel>>? categoryWinners, String summary, Map<String, dynamic> insights
});


$ProductModelCopyWith<$Res>? get winner;

}
/// @nodoc
class _$ComparisonResultCopyWithImpl<$Res>
    implements $ComparisonResultCopyWith<$Res> {
  _$ComparisonResultCopyWithImpl(this._self, this._then);

  final ComparisonResult _self;
  final $Res Function(ComparisonResult) _then;

/// Create a copy of ComparisonResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? winner = freezed,Object? rankedProducts = freezed,Object? categoryWinners = freezed,Object? summary = null,Object? insights = null,}) {
  return _then(_self.copyWith(
winner: freezed == winner ? _self.winner : winner // ignore: cast_nullable_to_non_nullable
as ProductModel?,rankedProducts: freezed == rankedProducts ? _self.rankedProducts : rankedProducts // ignore: cast_nullable_to_non_nullable
as List<ProductModel>?,categoryWinners: freezed == categoryWinners ? _self.categoryWinners : categoryWinners // ignore: cast_nullable_to_non_nullable
as Map<String, List<ProductModel>>?,summary: null == summary ? _self.summary : summary // ignore: cast_nullable_to_non_nullable
as String,insights: null == insights ? _self.insights : insights // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}
/// Create a copy of ComparisonResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductModelCopyWith<$Res>? get winner {
    if (_self.winner == null) {
    return null;
  }

  return $ProductModelCopyWith<$Res>(_self.winner!, (value) {
    return _then(_self.copyWith(winner: value));
  });
}
}


/// Adds pattern-matching-related methods to [ComparisonResult].
extension ComparisonResultPatterns on ComparisonResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ComparisonResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ComparisonResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ComparisonResult value)  $default,){
final _that = this;
switch (_that) {
case _ComparisonResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ComparisonResult value)?  $default,){
final _that = this;
switch (_that) {
case _ComparisonResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(includeFromJson: false, includeToJson: false)  ProductModel? winner, @JsonKey(includeFromJson: false, includeToJson: false)  List<ProductModel>? rankedProducts, @JsonKey(includeFromJson: false, includeToJson: false)  Map<String, List<ProductModel>>? categoryWinners,  String summary,  Map<String, dynamic> insights)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ComparisonResult() when $default != null:
return $default(_that.winner,_that.rankedProducts,_that.categoryWinners,_that.summary,_that.insights);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(includeFromJson: false, includeToJson: false)  ProductModel? winner, @JsonKey(includeFromJson: false, includeToJson: false)  List<ProductModel>? rankedProducts, @JsonKey(includeFromJson: false, includeToJson: false)  Map<String, List<ProductModel>>? categoryWinners,  String summary,  Map<String, dynamic> insights)  $default,) {final _that = this;
switch (_that) {
case _ComparisonResult():
return $default(_that.winner,_that.rankedProducts,_that.categoryWinners,_that.summary,_that.insights);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(includeFromJson: false, includeToJson: false)  ProductModel? winner, @JsonKey(includeFromJson: false, includeToJson: false)  List<ProductModel>? rankedProducts, @JsonKey(includeFromJson: false, includeToJson: false)  Map<String, List<ProductModel>>? categoryWinners,  String summary,  Map<String, dynamic> insights)?  $default,) {final _that = this;
switch (_that) {
case _ComparisonResult() when $default != null:
return $default(_that.winner,_that.rankedProducts,_that.categoryWinners,_that.summary,_that.insights);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ComparisonResult implements ComparisonResult {
  const _ComparisonResult({@JsonKey(includeFromJson: false, includeToJson: false) this.winner, @JsonKey(includeFromJson: false, includeToJson: false) final  List<ProductModel>? rankedProducts, @JsonKey(includeFromJson: false, includeToJson: false) final  Map<String, List<ProductModel>>? categoryWinners, required this.summary, required final  Map<String, dynamic> insights}): _rankedProducts = rankedProducts,_categoryWinners = categoryWinners,_insights = insights;
  factory _ComparisonResult.fromJson(Map<String, dynamic> json) => _$ComparisonResultFromJson(json);

@override@JsonKey(includeFromJson: false, includeToJson: false) final  ProductModel? winner;
 final  List<ProductModel>? _rankedProducts;
@override@JsonKey(includeFromJson: false, includeToJson: false) List<ProductModel>? get rankedProducts {
  final value = _rankedProducts;
  if (value == null) return null;
  if (_rankedProducts is EqualUnmodifiableListView) return _rankedProducts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  Map<String, List<ProductModel>>? _categoryWinners;
@override@JsonKey(includeFromJson: false, includeToJson: false) Map<String, List<ProductModel>>? get categoryWinners {
  final value = _categoryWinners;
  if (value == null) return null;
  if (_categoryWinners is EqualUnmodifiableMapView) return _categoryWinners;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String summary;
 final  Map<String, dynamic> _insights;
@override Map<String, dynamic> get insights {
  if (_insights is EqualUnmodifiableMapView) return _insights;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_insights);
}


/// Create a copy of ComparisonResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ComparisonResultCopyWith<_ComparisonResult> get copyWith => __$ComparisonResultCopyWithImpl<_ComparisonResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ComparisonResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ComparisonResult&&(identical(other.winner, winner) || other.winner == winner)&&const DeepCollectionEquality().equals(other._rankedProducts, _rankedProducts)&&const DeepCollectionEquality().equals(other._categoryWinners, _categoryWinners)&&(identical(other.summary, summary) || other.summary == summary)&&const DeepCollectionEquality().equals(other._insights, _insights));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,winner,const DeepCollectionEquality().hash(_rankedProducts),const DeepCollectionEquality().hash(_categoryWinners),summary,const DeepCollectionEquality().hash(_insights));

@override
String toString() {
  return 'ComparisonResult(winner: $winner, rankedProducts: $rankedProducts, categoryWinners: $categoryWinners, summary: $summary, insights: $insights)';
}


}

/// @nodoc
abstract mixin class _$ComparisonResultCopyWith<$Res> implements $ComparisonResultCopyWith<$Res> {
  factory _$ComparisonResultCopyWith(_ComparisonResult value, $Res Function(_ComparisonResult) _then) = __$ComparisonResultCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(includeFromJson: false, includeToJson: false) ProductModel? winner,@JsonKey(includeFromJson: false, includeToJson: false) List<ProductModel>? rankedProducts,@JsonKey(includeFromJson: false, includeToJson: false) Map<String, List<ProductModel>>? categoryWinners, String summary, Map<String, dynamic> insights
});


@override $ProductModelCopyWith<$Res>? get winner;

}
/// @nodoc
class __$ComparisonResultCopyWithImpl<$Res>
    implements _$ComparisonResultCopyWith<$Res> {
  __$ComparisonResultCopyWithImpl(this._self, this._then);

  final _ComparisonResult _self;
  final $Res Function(_ComparisonResult) _then;

/// Create a copy of ComparisonResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? winner = freezed,Object? rankedProducts = freezed,Object? categoryWinners = freezed,Object? summary = null,Object? insights = null,}) {
  return _then(_ComparisonResult(
winner: freezed == winner ? _self.winner : winner // ignore: cast_nullable_to_non_nullable
as ProductModel?,rankedProducts: freezed == rankedProducts ? _self._rankedProducts : rankedProducts // ignore: cast_nullable_to_non_nullable
as List<ProductModel>?,categoryWinners: freezed == categoryWinners ? _self._categoryWinners : categoryWinners // ignore: cast_nullable_to_non_nullable
as Map<String, List<ProductModel>>?,summary: null == summary ? _self.summary : summary // ignore: cast_nullable_to_non_nullable
as String,insights: null == insights ? _self._insights : insights // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

/// Create a copy of ComparisonResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductModelCopyWith<$Res>? get winner {
    if (_self.winner == null) {
    return null;
  }

  return $ProductModelCopyWith<$Res>(_self.winner!, (value) {
    return _then(_self.copyWith(winner: value));
  });
}
}

// dart format on
