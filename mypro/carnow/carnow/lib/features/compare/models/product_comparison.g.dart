// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_comparison.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductComparison _$ProductComparisonFromJson(Map<String, dynamic> json) =>
    _ProductComparison(
      id: json['id'] as String,
      userId: json['userId'] as String,
      criteria: (json['criteria'] as List<dynamic>)
          .map((e) => ComparisonCriteria.fromJson(e as Map<String, dynamic>))
          .toList(),
      scores: (json['scores'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry(k, ComparisonScore.fromJson(e as Map<String, dynamic>)),
      ),
      title: json['title'] as String?,
      description: json['description'] as String?,
      type: $enumDecodeNullable(_$ComparisonTypeEnumMap, json['type']),
      isPublic: json['isPublic'] as bool?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ProductComparisonToJson(_ProductComparison instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'criteria': instance.criteria,
      'scores': instance.scores,
      'title': instance.title,
      'description': instance.description,
      'type': _$ComparisonTypeEnumMap[instance.type],
      'isPublic': instance.isPublic,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

const _$ComparisonTypeEnumMap = {
  ComparisonType.vehicle: 'vehicle',
  ComparisonType.part: 'part',
  ComparisonType.price: 'price',
  ComparisonType.feature: 'feature',
  ComparisonType.custom: 'custom',
  ComparisonType.active: 'active',
  ComparisonType.saved: 'saved',
};

_ComparisonCriteria _$ComparisonCriteriaFromJson(Map<String, dynamic> json) =>
    _ComparisonCriteria(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$ComparisonCriteriaTypeEnumMap, json['type']),
      weight: (json['weight'] as num).toDouble(),
      isHigherBetter: json['isHigherBetter'] as bool,
      description: json['description'] as String?,
      unit: json['unit'] as String?,
      minValue: (json['minValue'] as num?)?.toDouble(),
      maxValue: (json['maxValue'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ComparisonCriteriaToJson(_ComparisonCriteria instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$ComparisonCriteriaTypeEnumMap[instance.type]!,
      'weight': instance.weight,
      'isHigherBetter': instance.isHigherBetter,
      'description': instance.description,
      'unit': instance.unit,
      'minValue': instance.minValue,
      'maxValue': instance.maxValue,
    };

const _$ComparisonCriteriaTypeEnumMap = {
  ComparisonCriteriaType.numeric: 'numeric',
  ComparisonCriteriaType.boolean: 'boolean',
  ComparisonCriteriaType.categorical: 'categorical',
  ComparisonCriteriaType.rating: 'rating',
  ComparisonCriteriaType.price: 'price',
};

_ComparisonScore _$ComparisonScoreFromJson(Map<String, dynamic> json) =>
    _ComparisonScore(
      productId: json['productId'] as String,
      totalScore: (json['totalScore'] as num).toDouble(),
      criteriaScores: (json['criteriaScores'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      normalizedValues: json['normalizedValues'] as Map<String, dynamic>,
      isHigherBetter: json['isHigherBetter'] as bool,
      recommendation: json['recommendation'] as String?,
      pros: (json['pros'] as List<dynamic>?)?.map((e) => e as String).toList(),
      cons: (json['cons'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$ComparisonScoreToJson(_ComparisonScore instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'totalScore': instance.totalScore,
      'criteriaScores': instance.criteriaScores,
      'normalizedValues': instance.normalizedValues,
      'isHigherBetter': instance.isHigherBetter,
      'recommendation': instance.recommendation,
      'pros': instance.pros,
      'cons': instance.cons,
    };

_ComparisonResult _$ComparisonResultFromJson(Map<String, dynamic> json) =>
    _ComparisonResult(
      summary: json['summary'] as String,
      insights: json['insights'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$ComparisonResultToJson(_ComparisonResult instance) =>
    <String, dynamic>{
      'summary': instance.summary,
      'insights': instance.insights,
    };
