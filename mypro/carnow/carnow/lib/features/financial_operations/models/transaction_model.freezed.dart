// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transaction_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TransactionModel {

 String get id; double get amount;@JsonKey(name: 'currency') String get currency;@JsonKey(name: 'type') TransactionType get type;@JsonKey(name: 'status') TransactionStatus get status;@JsonKey(name: 'payment_method') PaymentMethod get paymentMethod;@JsonKey(name: 'payer_id') String get payerId;@JsonKey(name: 'payee_id') String get payeeId;@JsonKey(name: 'order_id') String? get orderId;@JsonKey(name: 'product_id') String? get productId; String? get description;@JsonKey(name: 'transaction_metadata') Map<String, dynamic>? get metadata;@JsonKey(name: 'platform_commission_rate') double get platformCommissionRate;@JsonKey(name: 'platform_commission_amount') double get platformCommissionAmount;@JsonKey(name: 'cod_fee_rate') double get codFeeRate;@JsonKey(name: 'cod_fee_amount') double get codFeeAmount;@JsonKey(name: 'refundable_amount') double get refundableAmount;@JsonKey(name: 'refunded_amount') double get refundedAmount;@JsonKey(name: 'delivery_address') String? get deliveryAddress;@JsonKey(name: 'delivery_phone') String? get deliveryPhone;@JsonKey(name: 'delivery_notes') String? get deliveryNotes;@JsonKey(name: 'estimated_delivery_date') DateTime? get estimatedDeliveryDate;@JsonKey(name: 'actual_delivery_date') DateTime? get actualDeliveryDate;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;@JsonKey(name: 'completed_at') DateTime? get completedAt;
/// Create a copy of TransactionModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransactionModelCopyWith<TransactionModel> get copyWith => _$TransactionModelCopyWithImpl<TransactionModel>(this as TransactionModel, _$identity);

  /// Serializes this TransactionModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransactionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.payerId, payerId) || other.payerId == payerId)&&(identical(other.payeeId, payeeId) || other.payeeId == payeeId)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.platformCommissionRate, platformCommissionRate) || other.platformCommissionRate == platformCommissionRate)&&(identical(other.platformCommissionAmount, platformCommissionAmount) || other.platformCommissionAmount == platformCommissionAmount)&&(identical(other.codFeeRate, codFeeRate) || other.codFeeRate == codFeeRate)&&(identical(other.codFeeAmount, codFeeAmount) || other.codFeeAmount == codFeeAmount)&&(identical(other.refundableAmount, refundableAmount) || other.refundableAmount == refundableAmount)&&(identical(other.refundedAmount, refundedAmount) || other.refundedAmount == refundedAmount)&&(identical(other.deliveryAddress, deliveryAddress) || other.deliveryAddress == deliveryAddress)&&(identical(other.deliveryPhone, deliveryPhone) || other.deliveryPhone == deliveryPhone)&&(identical(other.deliveryNotes, deliveryNotes) || other.deliveryNotes == deliveryNotes)&&(identical(other.estimatedDeliveryDate, estimatedDeliveryDate) || other.estimatedDeliveryDate == estimatedDeliveryDate)&&(identical(other.actualDeliveryDate, actualDeliveryDate) || other.actualDeliveryDate == actualDeliveryDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,amount,currency,type,status,paymentMethod,payerId,payeeId,orderId,productId,description,const DeepCollectionEquality().hash(metadata),platformCommissionRate,platformCommissionAmount,codFeeRate,codFeeAmount,refundableAmount,refundedAmount,deliveryAddress,deliveryPhone,deliveryNotes,estimatedDeliveryDate,actualDeliveryDate,createdAt,updatedAt,completedAt]);

@override
String toString() {
  return 'TransactionModel(id: $id, amount: $amount, currency: $currency, type: $type, status: $status, paymentMethod: $paymentMethod, payerId: $payerId, payeeId: $payeeId, orderId: $orderId, productId: $productId, description: $description, metadata: $metadata, platformCommissionRate: $platformCommissionRate, platformCommissionAmount: $platformCommissionAmount, codFeeRate: $codFeeRate, codFeeAmount: $codFeeAmount, refundableAmount: $refundableAmount, refundedAmount: $refundedAmount, deliveryAddress: $deliveryAddress, deliveryPhone: $deliveryPhone, deliveryNotes: $deliveryNotes, estimatedDeliveryDate: $estimatedDeliveryDate, actualDeliveryDate: $actualDeliveryDate, createdAt: $createdAt, updatedAt: $updatedAt, completedAt: $completedAt)';
}


}

/// @nodoc
abstract mixin class $TransactionModelCopyWith<$Res>  {
  factory $TransactionModelCopyWith(TransactionModel value, $Res Function(TransactionModel) _then) = _$TransactionModelCopyWithImpl;
@useResult
$Res call({
 String id, double amount,@JsonKey(name: 'currency') String currency,@JsonKey(name: 'type') TransactionType type,@JsonKey(name: 'status') TransactionStatus status,@JsonKey(name: 'payment_method') PaymentMethod paymentMethod,@JsonKey(name: 'payer_id') String payerId,@JsonKey(name: 'payee_id') String payeeId,@JsonKey(name: 'order_id') String? orderId,@JsonKey(name: 'product_id') String? productId, String? description,@JsonKey(name: 'transaction_metadata') Map<String, dynamic>? metadata,@JsonKey(name: 'platform_commission_rate') double platformCommissionRate,@JsonKey(name: 'platform_commission_amount') double platformCommissionAmount,@JsonKey(name: 'cod_fee_rate') double codFeeRate,@JsonKey(name: 'cod_fee_amount') double codFeeAmount,@JsonKey(name: 'refundable_amount') double refundableAmount,@JsonKey(name: 'refunded_amount') double refundedAmount,@JsonKey(name: 'delivery_address') String? deliveryAddress,@JsonKey(name: 'delivery_phone') String? deliveryPhone,@JsonKey(name: 'delivery_notes') String? deliveryNotes,@JsonKey(name: 'estimated_delivery_date') DateTime? estimatedDeliveryDate,@JsonKey(name: 'actual_delivery_date') DateTime? actualDeliveryDate,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'completed_at') DateTime? completedAt
});




}
/// @nodoc
class _$TransactionModelCopyWithImpl<$Res>
    implements $TransactionModelCopyWith<$Res> {
  _$TransactionModelCopyWithImpl(this._self, this._then);

  final TransactionModel _self;
  final $Res Function(TransactionModel) _then;

/// Create a copy of TransactionModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? amount = null,Object? currency = null,Object? type = null,Object? status = null,Object? paymentMethod = null,Object? payerId = null,Object? payeeId = null,Object? orderId = freezed,Object? productId = freezed,Object? description = freezed,Object? metadata = freezed,Object? platformCommissionRate = null,Object? platformCommissionAmount = null,Object? codFeeRate = null,Object? codFeeAmount = null,Object? refundableAmount = null,Object? refundedAmount = null,Object? deliveryAddress = freezed,Object? deliveryPhone = freezed,Object? deliveryNotes = freezed,Object? estimatedDeliveryDate = freezed,Object? actualDeliveryDate = freezed,Object? createdAt = null,Object? updatedAt = null,Object? completedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as TransactionType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TransactionStatus,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethod,payerId: null == payerId ? _self.payerId : payerId // ignore: cast_nullable_to_non_nullable
as String,payeeId: null == payeeId ? _self.payeeId : payeeId // ignore: cast_nullable_to_non_nullable
as String,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,platformCommissionRate: null == platformCommissionRate ? _self.platformCommissionRate : platformCommissionRate // ignore: cast_nullable_to_non_nullable
as double,platformCommissionAmount: null == platformCommissionAmount ? _self.platformCommissionAmount : platformCommissionAmount // ignore: cast_nullable_to_non_nullable
as double,codFeeRate: null == codFeeRate ? _self.codFeeRate : codFeeRate // ignore: cast_nullable_to_non_nullable
as double,codFeeAmount: null == codFeeAmount ? _self.codFeeAmount : codFeeAmount // ignore: cast_nullable_to_non_nullable
as double,refundableAmount: null == refundableAmount ? _self.refundableAmount : refundableAmount // ignore: cast_nullable_to_non_nullable
as double,refundedAmount: null == refundedAmount ? _self.refundedAmount : refundedAmount // ignore: cast_nullable_to_non_nullable
as double,deliveryAddress: freezed == deliveryAddress ? _self.deliveryAddress : deliveryAddress // ignore: cast_nullable_to_non_nullable
as String?,deliveryPhone: freezed == deliveryPhone ? _self.deliveryPhone : deliveryPhone // ignore: cast_nullable_to_non_nullable
as String?,deliveryNotes: freezed == deliveryNotes ? _self.deliveryNotes : deliveryNotes // ignore: cast_nullable_to_non_nullable
as String?,estimatedDeliveryDate: freezed == estimatedDeliveryDate ? _self.estimatedDeliveryDate : estimatedDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,actualDeliveryDate: freezed == actualDeliveryDate ? _self.actualDeliveryDate : actualDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [TransactionModel].
extension TransactionModelPatterns on TransactionModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TransactionModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TransactionModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TransactionModel value)  $default,){
final _that = this;
switch (_that) {
case _TransactionModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TransactionModel value)?  $default,){
final _that = this;
switch (_that) {
case _TransactionModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double amount, @JsonKey(name: 'currency')  String currency, @JsonKey(name: 'type')  TransactionType type, @JsonKey(name: 'status')  TransactionStatus status, @JsonKey(name: 'payment_method')  PaymentMethod paymentMethod, @JsonKey(name: 'payer_id')  String payerId, @JsonKey(name: 'payee_id')  String payeeId, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId,  String? description, @JsonKey(name: 'transaction_metadata')  Map<String, dynamic>? metadata, @JsonKey(name: 'platform_commission_rate')  double platformCommissionRate, @JsonKey(name: 'platform_commission_amount')  double platformCommissionAmount, @JsonKey(name: 'cod_fee_rate')  double codFeeRate, @JsonKey(name: 'cod_fee_amount')  double codFeeAmount, @JsonKey(name: 'refundable_amount')  double refundableAmount, @JsonKey(name: 'refunded_amount')  double refundedAmount, @JsonKey(name: 'delivery_address')  String? deliveryAddress, @JsonKey(name: 'delivery_phone')  String? deliveryPhone, @JsonKey(name: 'delivery_notes')  String? deliveryNotes, @JsonKey(name: 'estimated_delivery_date')  DateTime? estimatedDeliveryDate, @JsonKey(name: 'actual_delivery_date')  DateTime? actualDeliveryDate, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'completed_at')  DateTime? completedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TransactionModel() when $default != null:
return $default(_that.id,_that.amount,_that.currency,_that.type,_that.status,_that.paymentMethod,_that.payerId,_that.payeeId,_that.orderId,_that.productId,_that.description,_that.metadata,_that.platformCommissionRate,_that.platformCommissionAmount,_that.codFeeRate,_that.codFeeAmount,_that.refundableAmount,_that.refundedAmount,_that.deliveryAddress,_that.deliveryPhone,_that.deliveryNotes,_that.estimatedDeliveryDate,_that.actualDeliveryDate,_that.createdAt,_that.updatedAt,_that.completedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double amount, @JsonKey(name: 'currency')  String currency, @JsonKey(name: 'type')  TransactionType type, @JsonKey(name: 'status')  TransactionStatus status, @JsonKey(name: 'payment_method')  PaymentMethod paymentMethod, @JsonKey(name: 'payer_id')  String payerId, @JsonKey(name: 'payee_id')  String payeeId, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId,  String? description, @JsonKey(name: 'transaction_metadata')  Map<String, dynamic>? metadata, @JsonKey(name: 'platform_commission_rate')  double platformCommissionRate, @JsonKey(name: 'platform_commission_amount')  double platformCommissionAmount, @JsonKey(name: 'cod_fee_rate')  double codFeeRate, @JsonKey(name: 'cod_fee_amount')  double codFeeAmount, @JsonKey(name: 'refundable_amount')  double refundableAmount, @JsonKey(name: 'refunded_amount')  double refundedAmount, @JsonKey(name: 'delivery_address')  String? deliveryAddress, @JsonKey(name: 'delivery_phone')  String? deliveryPhone, @JsonKey(name: 'delivery_notes')  String? deliveryNotes, @JsonKey(name: 'estimated_delivery_date')  DateTime? estimatedDeliveryDate, @JsonKey(name: 'actual_delivery_date')  DateTime? actualDeliveryDate, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'completed_at')  DateTime? completedAt)  $default,) {final _that = this;
switch (_that) {
case _TransactionModel():
return $default(_that.id,_that.amount,_that.currency,_that.type,_that.status,_that.paymentMethod,_that.payerId,_that.payeeId,_that.orderId,_that.productId,_that.description,_that.metadata,_that.platformCommissionRate,_that.platformCommissionAmount,_that.codFeeRate,_that.codFeeAmount,_that.refundableAmount,_that.refundedAmount,_that.deliveryAddress,_that.deliveryPhone,_that.deliveryNotes,_that.estimatedDeliveryDate,_that.actualDeliveryDate,_that.createdAt,_that.updatedAt,_that.completedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double amount, @JsonKey(name: 'currency')  String currency, @JsonKey(name: 'type')  TransactionType type, @JsonKey(name: 'status')  TransactionStatus status, @JsonKey(name: 'payment_method')  PaymentMethod paymentMethod, @JsonKey(name: 'payer_id')  String payerId, @JsonKey(name: 'payee_id')  String payeeId, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId,  String? description, @JsonKey(name: 'transaction_metadata')  Map<String, dynamic>? metadata, @JsonKey(name: 'platform_commission_rate')  double platformCommissionRate, @JsonKey(name: 'platform_commission_amount')  double platformCommissionAmount, @JsonKey(name: 'cod_fee_rate')  double codFeeRate, @JsonKey(name: 'cod_fee_amount')  double codFeeAmount, @JsonKey(name: 'refundable_amount')  double refundableAmount, @JsonKey(name: 'refunded_amount')  double refundedAmount, @JsonKey(name: 'delivery_address')  String? deliveryAddress, @JsonKey(name: 'delivery_phone')  String? deliveryPhone, @JsonKey(name: 'delivery_notes')  String? deliveryNotes, @JsonKey(name: 'estimated_delivery_date')  DateTime? estimatedDeliveryDate, @JsonKey(name: 'actual_delivery_date')  DateTime? actualDeliveryDate, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'completed_at')  DateTime? completedAt)?  $default,) {final _that = this;
switch (_that) {
case _TransactionModel() when $default != null:
return $default(_that.id,_that.amount,_that.currency,_that.type,_that.status,_that.paymentMethod,_that.payerId,_that.payeeId,_that.orderId,_that.productId,_that.description,_that.metadata,_that.platformCommissionRate,_that.platformCommissionAmount,_that.codFeeRate,_that.codFeeAmount,_that.refundableAmount,_that.refundedAmount,_that.deliveryAddress,_that.deliveryPhone,_that.deliveryNotes,_that.estimatedDeliveryDate,_that.actualDeliveryDate,_that.createdAt,_that.updatedAt,_that.completedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TransactionModel implements TransactionModel {
  const _TransactionModel({required this.id, required this.amount, @JsonKey(name: 'currency') this.currency = 'LYD', @JsonKey(name: 'type') required this.type, @JsonKey(name: 'status') required this.status, @JsonKey(name: 'payment_method') required this.paymentMethod, @JsonKey(name: 'payer_id') required this.payerId, @JsonKey(name: 'payee_id') required this.payeeId, @JsonKey(name: 'order_id') this.orderId, @JsonKey(name: 'product_id') this.productId, this.description, @JsonKey(name: 'transaction_metadata') final  Map<String, dynamic>? metadata, @JsonKey(name: 'platform_commission_rate') this.platformCommissionRate = 0.05, @JsonKey(name: 'platform_commission_amount') this.platformCommissionAmount = 0.0, @JsonKey(name: 'cod_fee_rate') this.codFeeRate = 0.02, @JsonKey(name: 'cod_fee_amount') this.codFeeAmount = 0.0, @JsonKey(name: 'refundable_amount') required this.refundableAmount, @JsonKey(name: 'refunded_amount') this.refundedAmount = 0.0, @JsonKey(name: 'delivery_address') this.deliveryAddress, @JsonKey(name: 'delivery_phone') this.deliveryPhone, @JsonKey(name: 'delivery_notes') this.deliveryNotes, @JsonKey(name: 'estimated_delivery_date') this.estimatedDeliveryDate, @JsonKey(name: 'actual_delivery_date') this.actualDeliveryDate, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt, @JsonKey(name: 'completed_at') this.completedAt}): _metadata = metadata;
  factory _TransactionModel.fromJson(Map<String, dynamic> json) => _$TransactionModelFromJson(json);

@override final  String id;
@override final  double amount;
@override@JsonKey(name: 'currency') final  String currency;
@override@JsonKey(name: 'type') final  TransactionType type;
@override@JsonKey(name: 'status') final  TransactionStatus status;
@override@JsonKey(name: 'payment_method') final  PaymentMethod paymentMethod;
@override@JsonKey(name: 'payer_id') final  String payerId;
@override@JsonKey(name: 'payee_id') final  String payeeId;
@override@JsonKey(name: 'order_id') final  String? orderId;
@override@JsonKey(name: 'product_id') final  String? productId;
@override final  String? description;
 final  Map<String, dynamic>? _metadata;
@override@JsonKey(name: 'transaction_metadata') Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'platform_commission_rate') final  double platformCommissionRate;
@override@JsonKey(name: 'platform_commission_amount') final  double platformCommissionAmount;
@override@JsonKey(name: 'cod_fee_rate') final  double codFeeRate;
@override@JsonKey(name: 'cod_fee_amount') final  double codFeeAmount;
@override@JsonKey(name: 'refundable_amount') final  double refundableAmount;
@override@JsonKey(name: 'refunded_amount') final  double refundedAmount;
@override@JsonKey(name: 'delivery_address') final  String? deliveryAddress;
@override@JsonKey(name: 'delivery_phone') final  String? deliveryPhone;
@override@JsonKey(name: 'delivery_notes') final  String? deliveryNotes;
@override@JsonKey(name: 'estimated_delivery_date') final  DateTime? estimatedDeliveryDate;
@override@JsonKey(name: 'actual_delivery_date') final  DateTime? actualDeliveryDate;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;
@override@JsonKey(name: 'completed_at') final  DateTime? completedAt;

/// Create a copy of TransactionModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransactionModelCopyWith<_TransactionModel> get copyWith => __$TransactionModelCopyWithImpl<_TransactionModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TransactionModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransactionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.payerId, payerId) || other.payerId == payerId)&&(identical(other.payeeId, payeeId) || other.payeeId == payeeId)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.platformCommissionRate, platformCommissionRate) || other.platformCommissionRate == platformCommissionRate)&&(identical(other.platformCommissionAmount, platformCommissionAmount) || other.platformCommissionAmount == platformCommissionAmount)&&(identical(other.codFeeRate, codFeeRate) || other.codFeeRate == codFeeRate)&&(identical(other.codFeeAmount, codFeeAmount) || other.codFeeAmount == codFeeAmount)&&(identical(other.refundableAmount, refundableAmount) || other.refundableAmount == refundableAmount)&&(identical(other.refundedAmount, refundedAmount) || other.refundedAmount == refundedAmount)&&(identical(other.deliveryAddress, deliveryAddress) || other.deliveryAddress == deliveryAddress)&&(identical(other.deliveryPhone, deliveryPhone) || other.deliveryPhone == deliveryPhone)&&(identical(other.deliveryNotes, deliveryNotes) || other.deliveryNotes == deliveryNotes)&&(identical(other.estimatedDeliveryDate, estimatedDeliveryDate) || other.estimatedDeliveryDate == estimatedDeliveryDate)&&(identical(other.actualDeliveryDate, actualDeliveryDate) || other.actualDeliveryDate == actualDeliveryDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,amount,currency,type,status,paymentMethod,payerId,payeeId,orderId,productId,description,const DeepCollectionEquality().hash(_metadata),platformCommissionRate,platformCommissionAmount,codFeeRate,codFeeAmount,refundableAmount,refundedAmount,deliveryAddress,deliveryPhone,deliveryNotes,estimatedDeliveryDate,actualDeliveryDate,createdAt,updatedAt,completedAt]);

@override
String toString() {
  return 'TransactionModel(id: $id, amount: $amount, currency: $currency, type: $type, status: $status, paymentMethod: $paymentMethod, payerId: $payerId, payeeId: $payeeId, orderId: $orderId, productId: $productId, description: $description, metadata: $metadata, platformCommissionRate: $platformCommissionRate, platformCommissionAmount: $platformCommissionAmount, codFeeRate: $codFeeRate, codFeeAmount: $codFeeAmount, refundableAmount: $refundableAmount, refundedAmount: $refundedAmount, deliveryAddress: $deliveryAddress, deliveryPhone: $deliveryPhone, deliveryNotes: $deliveryNotes, estimatedDeliveryDate: $estimatedDeliveryDate, actualDeliveryDate: $actualDeliveryDate, createdAt: $createdAt, updatedAt: $updatedAt, completedAt: $completedAt)';
}


}

/// @nodoc
abstract mixin class _$TransactionModelCopyWith<$Res> implements $TransactionModelCopyWith<$Res> {
  factory _$TransactionModelCopyWith(_TransactionModel value, $Res Function(_TransactionModel) _then) = __$TransactionModelCopyWithImpl;
@override @useResult
$Res call({
 String id, double amount,@JsonKey(name: 'currency') String currency,@JsonKey(name: 'type') TransactionType type,@JsonKey(name: 'status') TransactionStatus status,@JsonKey(name: 'payment_method') PaymentMethod paymentMethod,@JsonKey(name: 'payer_id') String payerId,@JsonKey(name: 'payee_id') String payeeId,@JsonKey(name: 'order_id') String? orderId,@JsonKey(name: 'product_id') String? productId, String? description,@JsonKey(name: 'transaction_metadata') Map<String, dynamic>? metadata,@JsonKey(name: 'platform_commission_rate') double platformCommissionRate,@JsonKey(name: 'platform_commission_amount') double platformCommissionAmount,@JsonKey(name: 'cod_fee_rate') double codFeeRate,@JsonKey(name: 'cod_fee_amount') double codFeeAmount,@JsonKey(name: 'refundable_amount') double refundableAmount,@JsonKey(name: 'refunded_amount') double refundedAmount,@JsonKey(name: 'delivery_address') String? deliveryAddress,@JsonKey(name: 'delivery_phone') String? deliveryPhone,@JsonKey(name: 'delivery_notes') String? deliveryNotes,@JsonKey(name: 'estimated_delivery_date') DateTime? estimatedDeliveryDate,@JsonKey(name: 'actual_delivery_date') DateTime? actualDeliveryDate,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'completed_at') DateTime? completedAt
});




}
/// @nodoc
class __$TransactionModelCopyWithImpl<$Res>
    implements _$TransactionModelCopyWith<$Res> {
  __$TransactionModelCopyWithImpl(this._self, this._then);

  final _TransactionModel _self;
  final $Res Function(_TransactionModel) _then;

/// Create a copy of TransactionModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? amount = null,Object? currency = null,Object? type = null,Object? status = null,Object? paymentMethod = null,Object? payerId = null,Object? payeeId = null,Object? orderId = freezed,Object? productId = freezed,Object? description = freezed,Object? metadata = freezed,Object? platformCommissionRate = null,Object? platformCommissionAmount = null,Object? codFeeRate = null,Object? codFeeAmount = null,Object? refundableAmount = null,Object? refundedAmount = null,Object? deliveryAddress = freezed,Object? deliveryPhone = freezed,Object? deliveryNotes = freezed,Object? estimatedDeliveryDate = freezed,Object? actualDeliveryDate = freezed,Object? createdAt = null,Object? updatedAt = null,Object? completedAt = freezed,}) {
  return _then(_TransactionModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as TransactionType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TransactionStatus,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethod,payerId: null == payerId ? _self.payerId : payerId // ignore: cast_nullable_to_non_nullable
as String,payeeId: null == payeeId ? _self.payeeId : payeeId // ignore: cast_nullable_to_non_nullable
as String,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,platformCommissionRate: null == platformCommissionRate ? _self.platformCommissionRate : platformCommissionRate // ignore: cast_nullable_to_non_nullable
as double,platformCommissionAmount: null == platformCommissionAmount ? _self.platformCommissionAmount : platformCommissionAmount // ignore: cast_nullable_to_non_nullable
as double,codFeeRate: null == codFeeRate ? _self.codFeeRate : codFeeRate // ignore: cast_nullable_to_non_nullable
as double,codFeeAmount: null == codFeeAmount ? _self.codFeeAmount : codFeeAmount // ignore: cast_nullable_to_non_nullable
as double,refundableAmount: null == refundableAmount ? _self.refundableAmount : refundableAmount // ignore: cast_nullable_to_non_nullable
as double,refundedAmount: null == refundedAmount ? _self.refundedAmount : refundedAmount // ignore: cast_nullable_to_non_nullable
as double,deliveryAddress: freezed == deliveryAddress ? _self.deliveryAddress : deliveryAddress // ignore: cast_nullable_to_non_nullable
as String?,deliveryPhone: freezed == deliveryPhone ? _self.deliveryPhone : deliveryPhone // ignore: cast_nullable_to_non_nullable
as String?,deliveryNotes: freezed == deliveryNotes ? _self.deliveryNotes : deliveryNotes // ignore: cast_nullable_to_non_nullable
as String?,estimatedDeliveryDate: freezed == estimatedDeliveryDate ? _self.estimatedDeliveryDate : estimatedDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,actualDeliveryDate: freezed == actualDeliveryDate ? _self.actualDeliveryDate : actualDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$TransactionRequest {

@JsonKey(name: 'payee_id') String get payeeId; double get amount;@JsonKey(name: 'transaction_type') TransactionType get transactionType;@JsonKey(name: 'payment_method') PaymentMethod get paymentMethod; String get description;@JsonKey(name: 'order_id') String? get orderId;@JsonKey(name: 'product_id') String? get productId;@JsonKey(name: 'delivery_data') Map<String, dynamic>? get deliveryData; Map<String, dynamic>? get metadata;
/// Create a copy of TransactionRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransactionRequestCopyWith<TransactionRequest> get copyWith => _$TransactionRequestCopyWithImpl<TransactionRequest>(this as TransactionRequest, _$identity);

  /// Serializes this TransactionRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransactionRequest&&(identical(other.payeeId, payeeId) || other.payeeId == payeeId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.transactionType, transactionType) || other.transactionType == transactionType)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.description, description) || other.description == description)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&const DeepCollectionEquality().equals(other.deliveryData, deliveryData)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,payeeId,amount,transactionType,paymentMethod,description,orderId,productId,const DeepCollectionEquality().hash(deliveryData),const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'TransactionRequest(payeeId: $payeeId, amount: $amount, transactionType: $transactionType, paymentMethod: $paymentMethod, description: $description, orderId: $orderId, productId: $productId, deliveryData: $deliveryData, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $TransactionRequestCopyWith<$Res>  {
  factory $TransactionRequestCopyWith(TransactionRequest value, $Res Function(TransactionRequest) _then) = _$TransactionRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'payee_id') String payeeId, double amount,@JsonKey(name: 'transaction_type') TransactionType transactionType,@JsonKey(name: 'payment_method') PaymentMethod paymentMethod, String description,@JsonKey(name: 'order_id') String? orderId,@JsonKey(name: 'product_id') String? productId,@JsonKey(name: 'delivery_data') Map<String, dynamic>? deliveryData, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$TransactionRequestCopyWithImpl<$Res>
    implements $TransactionRequestCopyWith<$Res> {
  _$TransactionRequestCopyWithImpl(this._self, this._then);

  final TransactionRequest _self;
  final $Res Function(TransactionRequest) _then;

/// Create a copy of TransactionRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? payeeId = null,Object? amount = null,Object? transactionType = null,Object? paymentMethod = null,Object? description = null,Object? orderId = freezed,Object? productId = freezed,Object? deliveryData = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
payeeId: null == payeeId ? _self.payeeId : payeeId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,transactionType: null == transactionType ? _self.transactionType : transactionType // ignore: cast_nullable_to_non_nullable
as TransactionType,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethod,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,deliveryData: freezed == deliveryData ? _self.deliveryData : deliveryData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [TransactionRequest].
extension TransactionRequestPatterns on TransactionRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TransactionRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TransactionRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TransactionRequest value)  $default,){
final _that = this;
switch (_that) {
case _TransactionRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TransactionRequest value)?  $default,){
final _that = this;
switch (_that) {
case _TransactionRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'payee_id')  String payeeId,  double amount, @JsonKey(name: 'transaction_type')  TransactionType transactionType, @JsonKey(name: 'payment_method')  PaymentMethod paymentMethod,  String description, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId, @JsonKey(name: 'delivery_data')  Map<String, dynamic>? deliveryData,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TransactionRequest() when $default != null:
return $default(_that.payeeId,_that.amount,_that.transactionType,_that.paymentMethod,_that.description,_that.orderId,_that.productId,_that.deliveryData,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'payee_id')  String payeeId,  double amount, @JsonKey(name: 'transaction_type')  TransactionType transactionType, @JsonKey(name: 'payment_method')  PaymentMethod paymentMethod,  String description, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId, @JsonKey(name: 'delivery_data')  Map<String, dynamic>? deliveryData,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _TransactionRequest():
return $default(_that.payeeId,_that.amount,_that.transactionType,_that.paymentMethod,_that.description,_that.orderId,_that.productId,_that.deliveryData,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'payee_id')  String payeeId,  double amount, @JsonKey(name: 'transaction_type')  TransactionType transactionType, @JsonKey(name: 'payment_method')  PaymentMethod paymentMethod,  String description, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId, @JsonKey(name: 'delivery_data')  Map<String, dynamic>? deliveryData,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _TransactionRequest() when $default != null:
return $default(_that.payeeId,_that.amount,_that.transactionType,_that.paymentMethod,_that.description,_that.orderId,_that.productId,_that.deliveryData,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TransactionRequest implements TransactionRequest {
  const _TransactionRequest({@JsonKey(name: 'payee_id') required this.payeeId, required this.amount, @JsonKey(name: 'transaction_type') required this.transactionType, @JsonKey(name: 'payment_method') required this.paymentMethod, required this.description, @JsonKey(name: 'order_id') this.orderId, @JsonKey(name: 'product_id') this.productId, @JsonKey(name: 'delivery_data') final  Map<String, dynamic>? deliveryData, final  Map<String, dynamic>? metadata}): _deliveryData = deliveryData,_metadata = metadata;
  factory _TransactionRequest.fromJson(Map<String, dynamic> json) => _$TransactionRequestFromJson(json);

@override@JsonKey(name: 'payee_id') final  String payeeId;
@override final  double amount;
@override@JsonKey(name: 'transaction_type') final  TransactionType transactionType;
@override@JsonKey(name: 'payment_method') final  PaymentMethod paymentMethod;
@override final  String description;
@override@JsonKey(name: 'order_id') final  String? orderId;
@override@JsonKey(name: 'product_id') final  String? productId;
 final  Map<String, dynamic>? _deliveryData;
@override@JsonKey(name: 'delivery_data') Map<String, dynamic>? get deliveryData {
  final value = _deliveryData;
  if (value == null) return null;
  if (_deliveryData is EqualUnmodifiableMapView) return _deliveryData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of TransactionRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransactionRequestCopyWith<_TransactionRequest> get copyWith => __$TransactionRequestCopyWithImpl<_TransactionRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TransactionRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransactionRequest&&(identical(other.payeeId, payeeId) || other.payeeId == payeeId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.transactionType, transactionType) || other.transactionType == transactionType)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.description, description) || other.description == description)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&const DeepCollectionEquality().equals(other._deliveryData, _deliveryData)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,payeeId,amount,transactionType,paymentMethod,description,orderId,productId,const DeepCollectionEquality().hash(_deliveryData),const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'TransactionRequest(payeeId: $payeeId, amount: $amount, transactionType: $transactionType, paymentMethod: $paymentMethod, description: $description, orderId: $orderId, productId: $productId, deliveryData: $deliveryData, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$TransactionRequestCopyWith<$Res> implements $TransactionRequestCopyWith<$Res> {
  factory _$TransactionRequestCopyWith(_TransactionRequest value, $Res Function(_TransactionRequest) _then) = __$TransactionRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'payee_id') String payeeId, double amount,@JsonKey(name: 'transaction_type') TransactionType transactionType,@JsonKey(name: 'payment_method') PaymentMethod paymentMethod, String description,@JsonKey(name: 'order_id') String? orderId,@JsonKey(name: 'product_id') String? productId,@JsonKey(name: 'delivery_data') Map<String, dynamic>? deliveryData, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$TransactionRequestCopyWithImpl<$Res>
    implements _$TransactionRequestCopyWith<$Res> {
  __$TransactionRequestCopyWithImpl(this._self, this._then);

  final _TransactionRequest _self;
  final $Res Function(_TransactionRequest) _then;

/// Create a copy of TransactionRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? payeeId = null,Object? amount = null,Object? transactionType = null,Object? paymentMethod = null,Object? description = null,Object? orderId = freezed,Object? productId = freezed,Object? deliveryData = freezed,Object? metadata = freezed,}) {
  return _then(_TransactionRequest(
payeeId: null == payeeId ? _self.payeeId : payeeId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,transactionType: null == transactionType ? _self.transactionType : transactionType // ignore: cast_nullable_to_non_nullable
as TransactionType,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethod,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,deliveryData: freezed == deliveryData ? _self._deliveryData : deliveryData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$TransactionResult {

 bool get success;@JsonKey(name: 'transaction_id') String? get transactionId; double get amount; double? get commission;@JsonKey(name: 'cod_fee') double? get codFee;@JsonKey(name: 'total_amount') double? get totalAmount;@JsonKey(name: 'net_amount') double? get netAmount;@JsonKey(name: 'delivery_status') String? get deliveryStatus;@JsonKey(name: 'estimated_delivery') DateTime? get estimatedDelivery;@JsonKey(name: 'tracking_info') Map<String, dynamic>? get trackingInfo;@JsonKey(name: 'completed_at') DateTime? get completedAt; String? get error;@JsonKey(name: 'error_code') String? get errorCode;
/// Create a copy of TransactionResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransactionResultCopyWith<TransactionResult> get copyWith => _$TransactionResultCopyWithImpl<TransactionResult>(this as TransactionResult, _$identity);

  /// Serializes this TransactionResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransactionResult&&(identical(other.success, success) || other.success == success)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.commission, commission) || other.commission == commission)&&(identical(other.codFee, codFee) || other.codFee == codFee)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.netAmount, netAmount) || other.netAmount == netAmount)&&(identical(other.deliveryStatus, deliveryStatus) || other.deliveryStatus == deliveryStatus)&&(identical(other.estimatedDelivery, estimatedDelivery) || other.estimatedDelivery == estimatedDelivery)&&const DeepCollectionEquality().equals(other.trackingInfo, trackingInfo)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.error, error) || other.error == error)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,transactionId,amount,commission,codFee,totalAmount,netAmount,deliveryStatus,estimatedDelivery,const DeepCollectionEquality().hash(trackingInfo),completedAt,error,errorCode);

@override
String toString() {
  return 'TransactionResult(success: $success, transactionId: $transactionId, amount: $amount, commission: $commission, codFee: $codFee, totalAmount: $totalAmount, netAmount: $netAmount, deliveryStatus: $deliveryStatus, estimatedDelivery: $estimatedDelivery, trackingInfo: $trackingInfo, completedAt: $completedAt, error: $error, errorCode: $errorCode)';
}


}

/// @nodoc
abstract mixin class $TransactionResultCopyWith<$Res>  {
  factory $TransactionResultCopyWith(TransactionResult value, $Res Function(TransactionResult) _then) = _$TransactionResultCopyWithImpl;
@useResult
$Res call({
 bool success,@JsonKey(name: 'transaction_id') String? transactionId, double amount, double? commission,@JsonKey(name: 'cod_fee') double? codFee,@JsonKey(name: 'total_amount') double? totalAmount,@JsonKey(name: 'net_amount') double? netAmount,@JsonKey(name: 'delivery_status') String? deliveryStatus,@JsonKey(name: 'estimated_delivery') DateTime? estimatedDelivery,@JsonKey(name: 'tracking_info') Map<String, dynamic>? trackingInfo,@JsonKey(name: 'completed_at') DateTime? completedAt, String? error,@JsonKey(name: 'error_code') String? errorCode
});




}
/// @nodoc
class _$TransactionResultCopyWithImpl<$Res>
    implements $TransactionResultCopyWith<$Res> {
  _$TransactionResultCopyWithImpl(this._self, this._then);

  final TransactionResult _self;
  final $Res Function(TransactionResult) _then;

/// Create a copy of TransactionResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? transactionId = freezed,Object? amount = null,Object? commission = freezed,Object? codFee = freezed,Object? totalAmount = freezed,Object? netAmount = freezed,Object? deliveryStatus = freezed,Object? estimatedDelivery = freezed,Object? trackingInfo = freezed,Object? completedAt = freezed,Object? error = freezed,Object? errorCode = freezed,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,commission: freezed == commission ? _self.commission : commission // ignore: cast_nullable_to_non_nullable
as double?,codFee: freezed == codFee ? _self.codFee : codFee // ignore: cast_nullable_to_non_nullable
as double?,totalAmount: freezed == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double?,netAmount: freezed == netAmount ? _self.netAmount : netAmount // ignore: cast_nullable_to_non_nullable
as double?,deliveryStatus: freezed == deliveryStatus ? _self.deliveryStatus : deliveryStatus // ignore: cast_nullable_to_non_nullable
as String?,estimatedDelivery: freezed == estimatedDelivery ? _self.estimatedDelivery : estimatedDelivery // ignore: cast_nullable_to_non_nullable
as DateTime?,trackingInfo: freezed == trackingInfo ? _self.trackingInfo : trackingInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [TransactionResult].
extension TransactionResultPatterns on TransactionResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TransactionResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TransactionResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TransactionResult value)  $default,){
final _that = this;
switch (_that) {
case _TransactionResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TransactionResult value)?  $default,){
final _that = this;
switch (_that) {
case _TransactionResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool success, @JsonKey(name: 'transaction_id')  String? transactionId,  double amount,  double? commission, @JsonKey(name: 'cod_fee')  double? codFee, @JsonKey(name: 'total_amount')  double? totalAmount, @JsonKey(name: 'net_amount')  double? netAmount, @JsonKey(name: 'delivery_status')  String? deliveryStatus, @JsonKey(name: 'estimated_delivery')  DateTime? estimatedDelivery, @JsonKey(name: 'tracking_info')  Map<String, dynamic>? trackingInfo, @JsonKey(name: 'completed_at')  DateTime? completedAt,  String? error, @JsonKey(name: 'error_code')  String? errorCode)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TransactionResult() when $default != null:
return $default(_that.success,_that.transactionId,_that.amount,_that.commission,_that.codFee,_that.totalAmount,_that.netAmount,_that.deliveryStatus,_that.estimatedDelivery,_that.trackingInfo,_that.completedAt,_that.error,_that.errorCode);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool success, @JsonKey(name: 'transaction_id')  String? transactionId,  double amount,  double? commission, @JsonKey(name: 'cod_fee')  double? codFee, @JsonKey(name: 'total_amount')  double? totalAmount, @JsonKey(name: 'net_amount')  double? netAmount, @JsonKey(name: 'delivery_status')  String? deliveryStatus, @JsonKey(name: 'estimated_delivery')  DateTime? estimatedDelivery, @JsonKey(name: 'tracking_info')  Map<String, dynamic>? trackingInfo, @JsonKey(name: 'completed_at')  DateTime? completedAt,  String? error, @JsonKey(name: 'error_code')  String? errorCode)  $default,) {final _that = this;
switch (_that) {
case _TransactionResult():
return $default(_that.success,_that.transactionId,_that.amount,_that.commission,_that.codFee,_that.totalAmount,_that.netAmount,_that.deliveryStatus,_that.estimatedDelivery,_that.trackingInfo,_that.completedAt,_that.error,_that.errorCode);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool success, @JsonKey(name: 'transaction_id')  String? transactionId,  double amount,  double? commission, @JsonKey(name: 'cod_fee')  double? codFee, @JsonKey(name: 'total_amount')  double? totalAmount, @JsonKey(name: 'net_amount')  double? netAmount, @JsonKey(name: 'delivery_status')  String? deliveryStatus, @JsonKey(name: 'estimated_delivery')  DateTime? estimatedDelivery, @JsonKey(name: 'tracking_info')  Map<String, dynamic>? trackingInfo, @JsonKey(name: 'completed_at')  DateTime? completedAt,  String? error, @JsonKey(name: 'error_code')  String? errorCode)?  $default,) {final _that = this;
switch (_that) {
case _TransactionResult() when $default != null:
return $default(_that.success,_that.transactionId,_that.amount,_that.commission,_that.codFee,_that.totalAmount,_that.netAmount,_that.deliveryStatus,_that.estimatedDelivery,_that.trackingInfo,_that.completedAt,_that.error,_that.errorCode);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TransactionResult implements TransactionResult {
  const _TransactionResult({required this.success, @JsonKey(name: 'transaction_id') this.transactionId, required this.amount, this.commission, @JsonKey(name: 'cod_fee') this.codFee, @JsonKey(name: 'total_amount') this.totalAmount, @JsonKey(name: 'net_amount') this.netAmount, @JsonKey(name: 'delivery_status') this.deliveryStatus, @JsonKey(name: 'estimated_delivery') this.estimatedDelivery, @JsonKey(name: 'tracking_info') final  Map<String, dynamic>? trackingInfo, @JsonKey(name: 'completed_at') this.completedAt, this.error, @JsonKey(name: 'error_code') this.errorCode}): _trackingInfo = trackingInfo;
  factory _TransactionResult.fromJson(Map<String, dynamic> json) => _$TransactionResultFromJson(json);

@override final  bool success;
@override@JsonKey(name: 'transaction_id') final  String? transactionId;
@override final  double amount;
@override final  double? commission;
@override@JsonKey(name: 'cod_fee') final  double? codFee;
@override@JsonKey(name: 'total_amount') final  double? totalAmount;
@override@JsonKey(name: 'net_amount') final  double? netAmount;
@override@JsonKey(name: 'delivery_status') final  String? deliveryStatus;
@override@JsonKey(name: 'estimated_delivery') final  DateTime? estimatedDelivery;
 final  Map<String, dynamic>? _trackingInfo;
@override@JsonKey(name: 'tracking_info') Map<String, dynamic>? get trackingInfo {
  final value = _trackingInfo;
  if (value == null) return null;
  if (_trackingInfo is EqualUnmodifiableMapView) return _trackingInfo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'completed_at') final  DateTime? completedAt;
@override final  String? error;
@override@JsonKey(name: 'error_code') final  String? errorCode;

/// Create a copy of TransactionResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransactionResultCopyWith<_TransactionResult> get copyWith => __$TransactionResultCopyWithImpl<_TransactionResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TransactionResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransactionResult&&(identical(other.success, success) || other.success == success)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.commission, commission) || other.commission == commission)&&(identical(other.codFee, codFee) || other.codFee == codFee)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.netAmount, netAmount) || other.netAmount == netAmount)&&(identical(other.deliveryStatus, deliveryStatus) || other.deliveryStatus == deliveryStatus)&&(identical(other.estimatedDelivery, estimatedDelivery) || other.estimatedDelivery == estimatedDelivery)&&const DeepCollectionEquality().equals(other._trackingInfo, _trackingInfo)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.error, error) || other.error == error)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,transactionId,amount,commission,codFee,totalAmount,netAmount,deliveryStatus,estimatedDelivery,const DeepCollectionEquality().hash(_trackingInfo),completedAt,error,errorCode);

@override
String toString() {
  return 'TransactionResult(success: $success, transactionId: $transactionId, amount: $amount, commission: $commission, codFee: $codFee, totalAmount: $totalAmount, netAmount: $netAmount, deliveryStatus: $deliveryStatus, estimatedDelivery: $estimatedDelivery, trackingInfo: $trackingInfo, completedAt: $completedAt, error: $error, errorCode: $errorCode)';
}


}

/// @nodoc
abstract mixin class _$TransactionResultCopyWith<$Res> implements $TransactionResultCopyWith<$Res> {
  factory _$TransactionResultCopyWith(_TransactionResult value, $Res Function(_TransactionResult) _then) = __$TransactionResultCopyWithImpl;
@override @useResult
$Res call({
 bool success,@JsonKey(name: 'transaction_id') String? transactionId, double amount, double? commission,@JsonKey(name: 'cod_fee') double? codFee,@JsonKey(name: 'total_amount') double? totalAmount,@JsonKey(name: 'net_amount') double? netAmount,@JsonKey(name: 'delivery_status') String? deliveryStatus,@JsonKey(name: 'estimated_delivery') DateTime? estimatedDelivery,@JsonKey(name: 'tracking_info') Map<String, dynamic>? trackingInfo,@JsonKey(name: 'completed_at') DateTime? completedAt, String? error,@JsonKey(name: 'error_code') String? errorCode
});




}
/// @nodoc
class __$TransactionResultCopyWithImpl<$Res>
    implements _$TransactionResultCopyWith<$Res> {
  __$TransactionResultCopyWithImpl(this._self, this._then);

  final _TransactionResult _self;
  final $Res Function(_TransactionResult) _then;

/// Create a copy of TransactionResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? transactionId = freezed,Object? amount = null,Object? commission = freezed,Object? codFee = freezed,Object? totalAmount = freezed,Object? netAmount = freezed,Object? deliveryStatus = freezed,Object? estimatedDelivery = freezed,Object? trackingInfo = freezed,Object? completedAt = freezed,Object? error = freezed,Object? errorCode = freezed,}) {
  return _then(_TransactionResult(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,commission: freezed == commission ? _self.commission : commission // ignore: cast_nullable_to_non_nullable
as double?,codFee: freezed == codFee ? _self.codFee : codFee // ignore: cast_nullable_to_non_nullable
as double?,totalAmount: freezed == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double?,netAmount: freezed == netAmount ? _self.netAmount : netAmount // ignore: cast_nullable_to_non_nullable
as double?,deliveryStatus: freezed == deliveryStatus ? _self.deliveryStatus : deliveryStatus // ignore: cast_nullable_to_non_nullable
as String?,estimatedDelivery: freezed == estimatedDelivery ? _self.estimatedDelivery : estimatedDelivery // ignore: cast_nullable_to_non_nullable
as DateTime?,trackingInfo: freezed == trackingInfo ? _self._trackingInfo : trackingInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$CODDeliveryInfo {

 String get id;@JsonKey(name: 'transaction_id') String get transactionId;@JsonKey(name: 'customer_name') String get customerName;@JsonKey(name: 'delivery_address') String get deliveryAddress;@JsonKey(name: 'delivery_phone') String get deliveryPhone;@JsonKey(name: 'alternative_phone') String? get alternativePhone;@JsonKey(name: 'preferred_delivery_time') String? get preferredDeliveryTime;@JsonKey(name: 'delivery_notes') String? get deliveryNotes;@JsonKey(name: 'delivery_status') String get deliveryStatus;@JsonKey(name: 'courier_id') String? get courierId;@JsonKey(name: 'tracking_number') String? get trackingNumber;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;@JsonKey(name: 'delivered_at') DateTime? get deliveredAt;
/// Create a copy of CODDeliveryInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CODDeliveryInfoCopyWith<CODDeliveryInfo> get copyWith => _$CODDeliveryInfoCopyWithImpl<CODDeliveryInfo>(this as CODDeliveryInfo, _$identity);

  /// Serializes this CODDeliveryInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CODDeliveryInfo&&(identical(other.id, id) || other.id == id)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.customerName, customerName) || other.customerName == customerName)&&(identical(other.deliveryAddress, deliveryAddress) || other.deliveryAddress == deliveryAddress)&&(identical(other.deliveryPhone, deliveryPhone) || other.deliveryPhone == deliveryPhone)&&(identical(other.alternativePhone, alternativePhone) || other.alternativePhone == alternativePhone)&&(identical(other.preferredDeliveryTime, preferredDeliveryTime) || other.preferredDeliveryTime == preferredDeliveryTime)&&(identical(other.deliveryNotes, deliveryNotes) || other.deliveryNotes == deliveryNotes)&&(identical(other.deliveryStatus, deliveryStatus) || other.deliveryStatus == deliveryStatus)&&(identical(other.courierId, courierId) || other.courierId == courierId)&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.deliveredAt, deliveredAt) || other.deliveredAt == deliveredAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,transactionId,customerName,deliveryAddress,deliveryPhone,alternativePhone,preferredDeliveryTime,deliveryNotes,deliveryStatus,courierId,trackingNumber,createdAt,updatedAt,deliveredAt);

@override
String toString() {
  return 'CODDeliveryInfo(id: $id, transactionId: $transactionId, customerName: $customerName, deliveryAddress: $deliveryAddress, deliveryPhone: $deliveryPhone, alternativePhone: $alternativePhone, preferredDeliveryTime: $preferredDeliveryTime, deliveryNotes: $deliveryNotes, deliveryStatus: $deliveryStatus, courierId: $courierId, trackingNumber: $trackingNumber, createdAt: $createdAt, updatedAt: $updatedAt, deliveredAt: $deliveredAt)';
}


}

/// @nodoc
abstract mixin class $CODDeliveryInfoCopyWith<$Res>  {
  factory $CODDeliveryInfoCopyWith(CODDeliveryInfo value, $Res Function(CODDeliveryInfo) _then) = _$CODDeliveryInfoCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'transaction_id') String transactionId,@JsonKey(name: 'customer_name') String customerName,@JsonKey(name: 'delivery_address') String deliveryAddress,@JsonKey(name: 'delivery_phone') String deliveryPhone,@JsonKey(name: 'alternative_phone') String? alternativePhone,@JsonKey(name: 'preferred_delivery_time') String? preferredDeliveryTime,@JsonKey(name: 'delivery_notes') String? deliveryNotes,@JsonKey(name: 'delivery_status') String deliveryStatus,@JsonKey(name: 'courier_id') String? courierId,@JsonKey(name: 'tracking_number') String? trackingNumber,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'delivered_at') DateTime? deliveredAt
});




}
/// @nodoc
class _$CODDeliveryInfoCopyWithImpl<$Res>
    implements $CODDeliveryInfoCopyWith<$Res> {
  _$CODDeliveryInfoCopyWithImpl(this._self, this._then);

  final CODDeliveryInfo _self;
  final $Res Function(CODDeliveryInfo) _then;

/// Create a copy of CODDeliveryInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? transactionId = null,Object? customerName = null,Object? deliveryAddress = null,Object? deliveryPhone = null,Object? alternativePhone = freezed,Object? preferredDeliveryTime = freezed,Object? deliveryNotes = freezed,Object? deliveryStatus = null,Object? courierId = freezed,Object? trackingNumber = freezed,Object? createdAt = null,Object? updatedAt = null,Object? deliveredAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,customerName: null == customerName ? _self.customerName : customerName // ignore: cast_nullable_to_non_nullable
as String,deliveryAddress: null == deliveryAddress ? _self.deliveryAddress : deliveryAddress // ignore: cast_nullable_to_non_nullable
as String,deliveryPhone: null == deliveryPhone ? _self.deliveryPhone : deliveryPhone // ignore: cast_nullable_to_non_nullable
as String,alternativePhone: freezed == alternativePhone ? _self.alternativePhone : alternativePhone // ignore: cast_nullable_to_non_nullable
as String?,preferredDeliveryTime: freezed == preferredDeliveryTime ? _self.preferredDeliveryTime : preferredDeliveryTime // ignore: cast_nullable_to_non_nullable
as String?,deliveryNotes: freezed == deliveryNotes ? _self.deliveryNotes : deliveryNotes // ignore: cast_nullable_to_non_nullable
as String?,deliveryStatus: null == deliveryStatus ? _self.deliveryStatus : deliveryStatus // ignore: cast_nullable_to_non_nullable
as String,courierId: freezed == courierId ? _self.courierId : courierId // ignore: cast_nullable_to_non_nullable
as String?,trackingNumber: freezed == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,deliveredAt: freezed == deliveredAt ? _self.deliveredAt : deliveredAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [CODDeliveryInfo].
extension CODDeliveryInfoPatterns on CODDeliveryInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CODDeliveryInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CODDeliveryInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CODDeliveryInfo value)  $default,){
final _that = this;
switch (_that) {
case _CODDeliveryInfo():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CODDeliveryInfo value)?  $default,){
final _that = this;
switch (_that) {
case _CODDeliveryInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'customer_name')  String customerName, @JsonKey(name: 'delivery_address')  String deliveryAddress, @JsonKey(name: 'delivery_phone')  String deliveryPhone, @JsonKey(name: 'alternative_phone')  String? alternativePhone, @JsonKey(name: 'preferred_delivery_time')  String? preferredDeliveryTime, @JsonKey(name: 'delivery_notes')  String? deliveryNotes, @JsonKey(name: 'delivery_status')  String deliveryStatus, @JsonKey(name: 'courier_id')  String? courierId, @JsonKey(name: 'tracking_number')  String? trackingNumber, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'delivered_at')  DateTime? deliveredAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CODDeliveryInfo() when $default != null:
return $default(_that.id,_that.transactionId,_that.customerName,_that.deliveryAddress,_that.deliveryPhone,_that.alternativePhone,_that.preferredDeliveryTime,_that.deliveryNotes,_that.deliveryStatus,_that.courierId,_that.trackingNumber,_that.createdAt,_that.updatedAt,_that.deliveredAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'customer_name')  String customerName, @JsonKey(name: 'delivery_address')  String deliveryAddress, @JsonKey(name: 'delivery_phone')  String deliveryPhone, @JsonKey(name: 'alternative_phone')  String? alternativePhone, @JsonKey(name: 'preferred_delivery_time')  String? preferredDeliveryTime, @JsonKey(name: 'delivery_notes')  String? deliveryNotes, @JsonKey(name: 'delivery_status')  String deliveryStatus, @JsonKey(name: 'courier_id')  String? courierId, @JsonKey(name: 'tracking_number')  String? trackingNumber, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'delivered_at')  DateTime? deliveredAt)  $default,) {final _that = this;
switch (_that) {
case _CODDeliveryInfo():
return $default(_that.id,_that.transactionId,_that.customerName,_that.deliveryAddress,_that.deliveryPhone,_that.alternativePhone,_that.preferredDeliveryTime,_that.deliveryNotes,_that.deliveryStatus,_that.courierId,_that.trackingNumber,_that.createdAt,_that.updatedAt,_that.deliveredAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'customer_name')  String customerName, @JsonKey(name: 'delivery_address')  String deliveryAddress, @JsonKey(name: 'delivery_phone')  String deliveryPhone, @JsonKey(name: 'alternative_phone')  String? alternativePhone, @JsonKey(name: 'preferred_delivery_time')  String? preferredDeliveryTime, @JsonKey(name: 'delivery_notes')  String? deliveryNotes, @JsonKey(name: 'delivery_status')  String deliveryStatus, @JsonKey(name: 'courier_id')  String? courierId, @JsonKey(name: 'tracking_number')  String? trackingNumber, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'delivered_at')  DateTime? deliveredAt)?  $default,) {final _that = this;
switch (_that) {
case _CODDeliveryInfo() when $default != null:
return $default(_that.id,_that.transactionId,_that.customerName,_that.deliveryAddress,_that.deliveryPhone,_that.alternativePhone,_that.preferredDeliveryTime,_that.deliveryNotes,_that.deliveryStatus,_that.courierId,_that.trackingNumber,_that.createdAt,_that.updatedAt,_that.deliveredAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CODDeliveryInfo implements CODDeliveryInfo {
  const _CODDeliveryInfo({required this.id, @JsonKey(name: 'transaction_id') required this.transactionId, @JsonKey(name: 'customer_name') required this.customerName, @JsonKey(name: 'delivery_address') required this.deliveryAddress, @JsonKey(name: 'delivery_phone') required this.deliveryPhone, @JsonKey(name: 'alternative_phone') this.alternativePhone, @JsonKey(name: 'preferred_delivery_time') this.preferredDeliveryTime, @JsonKey(name: 'delivery_notes') this.deliveryNotes, @JsonKey(name: 'delivery_status') this.deliveryStatus = 'pending', @JsonKey(name: 'courier_id') this.courierId, @JsonKey(name: 'tracking_number') this.trackingNumber, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt, @JsonKey(name: 'delivered_at') this.deliveredAt});
  factory _CODDeliveryInfo.fromJson(Map<String, dynamic> json) => _$CODDeliveryInfoFromJson(json);

@override final  String id;
@override@JsonKey(name: 'transaction_id') final  String transactionId;
@override@JsonKey(name: 'customer_name') final  String customerName;
@override@JsonKey(name: 'delivery_address') final  String deliveryAddress;
@override@JsonKey(name: 'delivery_phone') final  String deliveryPhone;
@override@JsonKey(name: 'alternative_phone') final  String? alternativePhone;
@override@JsonKey(name: 'preferred_delivery_time') final  String? preferredDeliveryTime;
@override@JsonKey(name: 'delivery_notes') final  String? deliveryNotes;
@override@JsonKey(name: 'delivery_status') final  String deliveryStatus;
@override@JsonKey(name: 'courier_id') final  String? courierId;
@override@JsonKey(name: 'tracking_number') final  String? trackingNumber;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;
@override@JsonKey(name: 'delivered_at') final  DateTime? deliveredAt;

/// Create a copy of CODDeliveryInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CODDeliveryInfoCopyWith<_CODDeliveryInfo> get copyWith => __$CODDeliveryInfoCopyWithImpl<_CODDeliveryInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CODDeliveryInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CODDeliveryInfo&&(identical(other.id, id) || other.id == id)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.customerName, customerName) || other.customerName == customerName)&&(identical(other.deliveryAddress, deliveryAddress) || other.deliveryAddress == deliveryAddress)&&(identical(other.deliveryPhone, deliveryPhone) || other.deliveryPhone == deliveryPhone)&&(identical(other.alternativePhone, alternativePhone) || other.alternativePhone == alternativePhone)&&(identical(other.preferredDeliveryTime, preferredDeliveryTime) || other.preferredDeliveryTime == preferredDeliveryTime)&&(identical(other.deliveryNotes, deliveryNotes) || other.deliveryNotes == deliveryNotes)&&(identical(other.deliveryStatus, deliveryStatus) || other.deliveryStatus == deliveryStatus)&&(identical(other.courierId, courierId) || other.courierId == courierId)&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.deliveredAt, deliveredAt) || other.deliveredAt == deliveredAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,transactionId,customerName,deliveryAddress,deliveryPhone,alternativePhone,preferredDeliveryTime,deliveryNotes,deliveryStatus,courierId,trackingNumber,createdAt,updatedAt,deliveredAt);

@override
String toString() {
  return 'CODDeliveryInfo(id: $id, transactionId: $transactionId, customerName: $customerName, deliveryAddress: $deliveryAddress, deliveryPhone: $deliveryPhone, alternativePhone: $alternativePhone, preferredDeliveryTime: $preferredDeliveryTime, deliveryNotes: $deliveryNotes, deliveryStatus: $deliveryStatus, courierId: $courierId, trackingNumber: $trackingNumber, createdAt: $createdAt, updatedAt: $updatedAt, deliveredAt: $deliveredAt)';
}


}

/// @nodoc
abstract mixin class _$CODDeliveryInfoCopyWith<$Res> implements $CODDeliveryInfoCopyWith<$Res> {
  factory _$CODDeliveryInfoCopyWith(_CODDeliveryInfo value, $Res Function(_CODDeliveryInfo) _then) = __$CODDeliveryInfoCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'transaction_id') String transactionId,@JsonKey(name: 'customer_name') String customerName,@JsonKey(name: 'delivery_address') String deliveryAddress,@JsonKey(name: 'delivery_phone') String deliveryPhone,@JsonKey(name: 'alternative_phone') String? alternativePhone,@JsonKey(name: 'preferred_delivery_time') String? preferredDeliveryTime,@JsonKey(name: 'delivery_notes') String? deliveryNotes,@JsonKey(name: 'delivery_status') String deliveryStatus,@JsonKey(name: 'courier_id') String? courierId,@JsonKey(name: 'tracking_number') String? trackingNumber,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'delivered_at') DateTime? deliveredAt
});




}
/// @nodoc
class __$CODDeliveryInfoCopyWithImpl<$Res>
    implements _$CODDeliveryInfoCopyWith<$Res> {
  __$CODDeliveryInfoCopyWithImpl(this._self, this._then);

  final _CODDeliveryInfo _self;
  final $Res Function(_CODDeliveryInfo) _then;

/// Create a copy of CODDeliveryInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? transactionId = null,Object? customerName = null,Object? deliveryAddress = null,Object? deliveryPhone = null,Object? alternativePhone = freezed,Object? preferredDeliveryTime = freezed,Object? deliveryNotes = freezed,Object? deliveryStatus = null,Object? courierId = freezed,Object? trackingNumber = freezed,Object? createdAt = null,Object? updatedAt = null,Object? deliveredAt = freezed,}) {
  return _then(_CODDeliveryInfo(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,customerName: null == customerName ? _self.customerName : customerName // ignore: cast_nullable_to_non_nullable
as String,deliveryAddress: null == deliveryAddress ? _self.deliveryAddress : deliveryAddress // ignore: cast_nullable_to_non_nullable
as String,deliveryPhone: null == deliveryPhone ? _self.deliveryPhone : deliveryPhone // ignore: cast_nullable_to_non_nullable
as String,alternativePhone: freezed == alternativePhone ? _self.alternativePhone : alternativePhone // ignore: cast_nullable_to_non_nullable
as String?,preferredDeliveryTime: freezed == preferredDeliveryTime ? _self.preferredDeliveryTime : preferredDeliveryTime // ignore: cast_nullable_to_non_nullable
as String?,deliveryNotes: freezed == deliveryNotes ? _self.deliveryNotes : deliveryNotes // ignore: cast_nullable_to_non_nullable
as String?,deliveryStatus: null == deliveryStatus ? _self.deliveryStatus : deliveryStatus // ignore: cast_nullable_to_non_nullable
as String,courierId: freezed == courierId ? _self.courierId : courierId // ignore: cast_nullable_to_non_nullable
as String?,trackingNumber: freezed == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,deliveredAt: freezed == deliveredAt ? _self.deliveredAt : deliveredAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
