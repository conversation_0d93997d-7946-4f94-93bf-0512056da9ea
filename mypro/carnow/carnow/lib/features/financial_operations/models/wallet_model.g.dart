// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WalletModel _$WalletModelFromJson(Map<String, dynamic> json) => _WalletModel(
  id: json['id'] as String,
  userId: json['user_id'] as String,
  balance: (json['balance'] as num).toDouble(),
  currency: json['currency'] as String? ?? 'LYD',
  status:
      $enumDecodeNullable(_$WalletStatusEnumMap, json['status']) ??
      WalletStatus.active,
  verificationLevel:
      $enumDecodeNullable(
        _$VerificationLevelEnumMap,
        json['verification_level'],
      ) ??
      VerificationLevel.unverified,
  dailyLimit: (json['daily_limit'] as num).toDouble(),
  monthlyLimit: (json['monthly_limit'] as num).toDouble(),
  dailySpent: (json['daily_spent'] as num?)?.toDouble() ?? 0.0,
  monthlySpent: (json['monthly_spent'] as num?)?.toDouble() ?? 0.0,
  canWithdraw: json['can_withdraw'] as bool? ?? false,
  canTransfer: json['can_transfer'] as bool? ?? false,
  phoneVerified: json['phone_verified'] as bool? ?? false,
  emailVerified: json['email_verified'] as bool? ?? false,
  idVerified: json['id_verified'] as bool? ?? false,
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  lastTransactionAt: json['last_transaction_at'] == null
      ? null
      : DateTime.parse(json['last_transaction_at'] as String),
);

Map<String, dynamic> _$WalletModelToJson(
  _WalletModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'user_id': instance.userId,
  'balance': instance.balance,
  'currency': instance.currency,
  'status': _$WalletStatusEnumMap[instance.status]!,
  'verification_level': _$VerificationLevelEnumMap[instance.verificationLevel]!,
  'daily_limit': instance.dailyLimit,
  'monthly_limit': instance.monthlyLimit,
  'daily_spent': instance.dailySpent,
  'monthly_spent': instance.monthlySpent,
  'can_withdraw': instance.canWithdraw,
  'can_transfer': instance.canTransfer,
  'phone_verified': instance.phoneVerified,
  'email_verified': instance.emailVerified,
  'id_verified': instance.idVerified,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
  'last_transaction_at': instance.lastTransactionAt?.toIso8601String(),
};

const _$WalletStatusEnumMap = {
  WalletStatus.active: 'active',
  WalletStatus.inactive: 'inactive',
  WalletStatus.suspended: 'suspended',
  WalletStatus.frozen: 'frozen',
};

const _$VerificationLevelEnumMap = {
  VerificationLevel.unverified: 'unverified',
  VerificationLevel.basic: 'basic',
  VerificationLevel.premium: 'premium',
};

_WalletTransaction _$WalletTransactionFromJson(Map<String, dynamic> json) =>
    _WalletTransaction(
      id: json['id'] as String,
      walletId: json['wallet_id'] as String,
      userId: json['user_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      type: $enumDecode(
        _$WalletTransactionTypeEnumMap,
        json['transaction_type'],
      ),
      status: $enumDecode(_$WalletTransactionStatusEnumMap, json['status']),
      referenceId: json['reference_id'] as String?,
      description: json['description'] as String,
      balanceBefore: (json['balance_before'] as num).toDouble(),
      balanceAfter: (json['balance_after'] as num).toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      processedAt: json['processed_at'] == null
          ? null
          : DateTime.parse(json['processed_at'] as String),
    );

Map<String, dynamic> _$WalletTransactionToJson(_WalletTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'wallet_id': instance.walletId,
      'user_id': instance.userId,
      'amount': instance.amount,
      'transaction_type': _$WalletTransactionTypeEnumMap[instance.type]!,
      'status': _$WalletTransactionStatusEnumMap[instance.status]!,
      'reference_id': instance.referenceId,
      'description': instance.description,
      'balance_before': instance.balanceBefore,
      'balance_after': instance.balanceAfter,
      'metadata': instance.metadata,
      'created_at': instance.createdAt.toIso8601String(),
      'processed_at': instance.processedAt?.toIso8601String(),
    };

const _$WalletTransactionTypeEnumMap = {
  WalletTransactionType.deposit: 'deposit',
  WalletTransactionType.withdrawal: 'withdrawal',
  WalletTransactionType.transferIn: 'transfer_in',
  WalletTransactionType.transferOut: 'transfer_out',
  WalletTransactionType.payment: 'payment',
  WalletTransactionType.refund: 'refund',
  WalletTransactionType.commission: 'commission',
  WalletTransactionType.fee: 'fee',
};

const _$WalletTransactionStatusEnumMap = {
  WalletTransactionStatus.pending: 'pending',
  WalletTransactionStatus.completed: 'completed',
  WalletTransactionStatus.failed: 'failed',
  WalletTransactionStatus.cancelled: 'cancelled',
};

_TransferRequest _$TransferRequestFromJson(Map<String, dynamic> json) =>
    _TransferRequest(
      toUserId: json['to_user_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      otp: json['otp'] as String?,
    );

Map<String, dynamic> _$TransferRequestToJson(_TransferRequest instance) =>
    <String, dynamic>{
      'to_user_id': instance.toUserId,
      'amount': instance.amount,
      'description': instance.description,
      'otp': instance.otp,
    };

_DepositRequest _$DepositRequestFromJson(Map<String, dynamic> json) =>
    _DepositRequest(
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      paymentMethod: json['payment_method'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$DepositRequestToJson(_DepositRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'description': instance.description,
      'payment_method': instance.paymentMethod,
      'metadata': instance.metadata,
    };

_WithdrawalRequest _$WithdrawalRequestFromJson(Map<String, dynamic> json) =>
    _WithdrawalRequest(
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      withdrawalMethod: json['withdrawal_method'] as String,
      bankAccount: json['bank_account'] as Map<String, dynamic>?,
      otp: json['otp'] as String?,
    );

Map<String, dynamic> _$WithdrawalRequestToJson(_WithdrawalRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'description': instance.description,
      'withdrawal_method': instance.withdrawalMethod,
      'bank_account': instance.bankAccount,
      'otp': instance.otp,
    };

_WalletSummary _$WalletSummaryFromJson(Map<String, dynamic> json) =>
    _WalletSummary(
      totalBalance: (json['total_balance'] as num).toDouble(),
      availableBalance: (json['available_balance'] as num).toDouble(),
      pendingBalance: (json['pending_balance'] as num).toDouble(),
      dailyRemaining: (json['daily_remaining'] as num).toDouble(),
      monthlyRemaining: (json['monthly_remaining'] as num).toDouble(),
      totalTransactions: (json['total_transactions'] as num).toInt(),
      totalSpentToday: (json['total_spent_today'] as num).toDouble(),
      totalSpentMonth: (json['total_spent_month'] as num).toDouble(),
      lastTransaction: json['last_transaction'] == null
          ? null
          : DateTime.parse(json['last_transaction'] as String),
    );

Map<String, dynamic> _$WalletSummaryToJson(_WalletSummary instance) =>
    <String, dynamic>{
      'total_balance': instance.totalBalance,
      'available_balance': instance.availableBalance,
      'pending_balance': instance.pendingBalance,
      'daily_remaining': instance.dailyRemaining,
      'monthly_remaining': instance.monthlyRemaining,
      'total_transactions': instance.totalTransactions,
      'total_spent_today': instance.totalSpentToday,
      'total_spent_month': instance.totalSpentMonth,
      'last_transaction': instance.lastTransaction?.toIso8601String(),
    };
