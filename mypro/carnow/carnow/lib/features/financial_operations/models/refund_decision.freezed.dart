// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refund_decision.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RefundDecision {

/// معرف طلب الاسترداد المستهدف
@JsonKey(name: 'refund_id') String get refundId;/// الإجراء المتخذ
@JsonKey(name: 'decision') RefundDecisionAction get decision;/// ملاحظات إدارية اختيارية
@JsonKey(name: 'admin_notes') String? get adminNotes;
/// Create a copy of RefundDecision
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RefundDecisionCopyWith<RefundDecision> get copyWith => _$RefundDecisionCopyWithImpl<RefundDecision>(this as RefundDecision, _$identity);

  /// Serializes this RefundDecision to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RefundDecision&&(identical(other.refundId, refundId) || other.refundId == refundId)&&(identical(other.decision, decision) || other.decision == decision)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,refundId,decision,adminNotes);

@override
String toString() {
  return 'RefundDecision(refundId: $refundId, decision: $decision, adminNotes: $adminNotes)';
}


}

/// @nodoc
abstract mixin class $RefundDecisionCopyWith<$Res>  {
  factory $RefundDecisionCopyWith(RefundDecision value, $Res Function(RefundDecision) _then) = _$RefundDecisionCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'refund_id') String refundId,@JsonKey(name: 'decision') RefundDecisionAction decision,@JsonKey(name: 'admin_notes') String? adminNotes
});




}
/// @nodoc
class _$RefundDecisionCopyWithImpl<$Res>
    implements $RefundDecisionCopyWith<$Res> {
  _$RefundDecisionCopyWithImpl(this._self, this._then);

  final RefundDecision _self;
  final $Res Function(RefundDecision) _then;

/// Create a copy of RefundDecision
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? refundId = null,Object? decision = null,Object? adminNotes = freezed,}) {
  return _then(_self.copyWith(
refundId: null == refundId ? _self.refundId : refundId // ignore: cast_nullable_to_non_nullable
as String,decision: null == decision ? _self.decision : decision // ignore: cast_nullable_to_non_nullable
as RefundDecisionAction,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [RefundDecision].
extension RefundDecisionPatterns on RefundDecision {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RefundDecision value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RefundDecision() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RefundDecision value)  $default,){
final _that = this;
switch (_that) {
case _RefundDecision():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RefundDecision value)?  $default,){
final _that = this;
switch (_that) {
case _RefundDecision() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'refund_id')  String refundId, @JsonKey(name: 'decision')  RefundDecisionAction decision, @JsonKey(name: 'admin_notes')  String? adminNotes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RefundDecision() when $default != null:
return $default(_that.refundId,_that.decision,_that.adminNotes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'refund_id')  String refundId, @JsonKey(name: 'decision')  RefundDecisionAction decision, @JsonKey(name: 'admin_notes')  String? adminNotes)  $default,) {final _that = this;
switch (_that) {
case _RefundDecision():
return $default(_that.refundId,_that.decision,_that.adminNotes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'refund_id')  String refundId, @JsonKey(name: 'decision')  RefundDecisionAction decision, @JsonKey(name: 'admin_notes')  String? adminNotes)?  $default,) {final _that = this;
switch (_that) {
case _RefundDecision() when $default != null:
return $default(_that.refundId,_that.decision,_that.adminNotes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RefundDecision implements RefundDecision {
  const _RefundDecision({@JsonKey(name: 'refund_id') required this.refundId, @JsonKey(name: 'decision') required this.decision, @JsonKey(name: 'admin_notes') this.adminNotes});
  factory _RefundDecision.fromJson(Map<String, dynamic> json) => _$RefundDecisionFromJson(json);

/// معرف طلب الاسترداد المستهدف
@override@JsonKey(name: 'refund_id') final  String refundId;
/// الإجراء المتخذ
@override@JsonKey(name: 'decision') final  RefundDecisionAction decision;
/// ملاحظات إدارية اختيارية
@override@JsonKey(name: 'admin_notes') final  String? adminNotes;

/// Create a copy of RefundDecision
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RefundDecisionCopyWith<_RefundDecision> get copyWith => __$RefundDecisionCopyWithImpl<_RefundDecision>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RefundDecisionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefundDecision&&(identical(other.refundId, refundId) || other.refundId == refundId)&&(identical(other.decision, decision) || other.decision == decision)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,refundId,decision,adminNotes);

@override
String toString() {
  return 'RefundDecision(refundId: $refundId, decision: $decision, adminNotes: $adminNotes)';
}


}

/// @nodoc
abstract mixin class _$RefundDecisionCopyWith<$Res> implements $RefundDecisionCopyWith<$Res> {
  factory _$RefundDecisionCopyWith(_RefundDecision value, $Res Function(_RefundDecision) _then) = __$RefundDecisionCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'refund_id') String refundId,@JsonKey(name: 'decision') RefundDecisionAction decision,@JsonKey(name: 'admin_notes') String? adminNotes
});




}
/// @nodoc
class __$RefundDecisionCopyWithImpl<$Res>
    implements _$RefundDecisionCopyWith<$Res> {
  __$RefundDecisionCopyWithImpl(this._self, this._then);

  final _RefundDecision _self;
  final $Res Function(_RefundDecision) _then;

/// Create a copy of RefundDecision
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? refundId = null,Object? decision = null,Object? adminNotes = freezed,}) {
  return _then(_RefundDecision(
refundId: null == refundId ? _self.refundId : refundId // ignore: cast_nullable_to_non_nullable
as String,decision: null == decision ? _self.decision : decision // ignore: cast_nullable_to_non_nullable
as RefundDecisionAction,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
