// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refund_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RefundResponse {

/// عملية ناجحة أم لا
 bool get success;/// معرف الاسترداد المُنشأ (فى حالة النجاح)
@JsonKey(name: 'refund_id') String? get refundId;/// رسالة بشرية مختصرة
 String? get message;/// رسالة خطأ (فى حالة الفشل)
 String? get error;
/// Create a copy of RefundResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RefundResponseCopyWith<RefundResponse> get copyWith => _$RefundResponseCopyWithImpl<RefundResponse>(this as RefundResponse, _$identity);

  /// Serializes this RefundResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RefundResponse&&(identical(other.success, success) || other.success == success)&&(identical(other.refundId, refundId) || other.refundId == refundId)&&(identical(other.message, message) || other.message == message)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,refundId,message,error);

@override
String toString() {
  return 'RefundResponse(success: $success, refundId: $refundId, message: $message, error: $error)';
}


}

/// @nodoc
abstract mixin class $RefundResponseCopyWith<$Res>  {
  factory $RefundResponseCopyWith(RefundResponse value, $Res Function(RefundResponse) _then) = _$RefundResponseCopyWithImpl;
@useResult
$Res call({
 bool success,@JsonKey(name: 'refund_id') String? refundId, String? message, String? error
});




}
/// @nodoc
class _$RefundResponseCopyWithImpl<$Res>
    implements $RefundResponseCopyWith<$Res> {
  _$RefundResponseCopyWithImpl(this._self, this._then);

  final RefundResponse _self;
  final $Res Function(RefundResponse) _then;

/// Create a copy of RefundResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? refundId = freezed,Object? message = freezed,Object? error = freezed,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,refundId: freezed == refundId ? _self.refundId : refundId // ignore: cast_nullable_to_non_nullable
as String?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [RefundResponse].
extension RefundResponsePatterns on RefundResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RefundResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RefundResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RefundResponse value)  $default,){
final _that = this;
switch (_that) {
case _RefundResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RefundResponse value)?  $default,){
final _that = this;
switch (_that) {
case _RefundResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool success, @JsonKey(name: 'refund_id')  String? refundId,  String? message,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RefundResponse() when $default != null:
return $default(_that.success,_that.refundId,_that.message,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool success, @JsonKey(name: 'refund_id')  String? refundId,  String? message,  String? error)  $default,) {final _that = this;
switch (_that) {
case _RefundResponse():
return $default(_that.success,_that.refundId,_that.message,_that.error);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool success, @JsonKey(name: 'refund_id')  String? refundId,  String? message,  String? error)?  $default,) {final _that = this;
switch (_that) {
case _RefundResponse() when $default != null:
return $default(_that.success,_that.refundId,_that.message,_that.error);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RefundResponse implements RefundResponse {
  const _RefundResponse({required this.success, @JsonKey(name: 'refund_id') this.refundId, this.message, this.error});
  factory _RefundResponse.fromJson(Map<String, dynamic> json) => _$RefundResponseFromJson(json);

/// عملية ناجحة أم لا
@override final  bool success;
/// معرف الاسترداد المُنشأ (فى حالة النجاح)
@override@JsonKey(name: 'refund_id') final  String? refundId;
/// رسالة بشرية مختصرة
@override final  String? message;
/// رسالة خطأ (فى حالة الفشل)
@override final  String? error;

/// Create a copy of RefundResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RefundResponseCopyWith<_RefundResponse> get copyWith => __$RefundResponseCopyWithImpl<_RefundResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RefundResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefundResponse&&(identical(other.success, success) || other.success == success)&&(identical(other.refundId, refundId) || other.refundId == refundId)&&(identical(other.message, message) || other.message == message)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,refundId,message,error);

@override
String toString() {
  return 'RefundResponse(success: $success, refundId: $refundId, message: $message, error: $error)';
}


}

/// @nodoc
abstract mixin class _$RefundResponseCopyWith<$Res> implements $RefundResponseCopyWith<$Res> {
  factory _$RefundResponseCopyWith(_RefundResponse value, $Res Function(_RefundResponse) _then) = __$RefundResponseCopyWithImpl;
@override @useResult
$Res call({
 bool success,@JsonKey(name: 'refund_id') String? refundId, String? message, String? error
});




}
/// @nodoc
class __$RefundResponseCopyWithImpl<$Res>
    implements _$RefundResponseCopyWith<$Res> {
  __$RefundResponseCopyWithImpl(this._self, this._then);

  final _RefundResponse _self;
  final $Res Function(_RefundResponse) _then;

/// Create a copy of RefundResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? refundId = freezed,Object? message = freezed,Object? error = freezed,}) {
  return _then(_RefundResponse(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,refundId: freezed == refundId ? _self.refundId : refundId // ignore: cast_nullable_to_non_nullable
as String?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
