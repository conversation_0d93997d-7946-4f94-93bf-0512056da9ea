// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WalletModel {

 String get id;@JsonKey(name: 'user_id') String get userId; double get balance;@JsonKey(name: 'currency') String get currency;@JsonKey(name: 'status') WalletStatus get status;@JsonKey(name: 'verification_level') VerificationLevel get verificationLevel;@JsonKey(name: 'daily_limit') double get dailyLimit;@JsonKey(name: 'monthly_limit') double get monthlyLimit;@JsonKey(name: 'daily_spent') double get dailySpent;@JsonKey(name: 'monthly_spent') double get monthlySpent;@JsonKey(name: 'can_withdraw') bool get canWithdraw;@JsonKey(name: 'can_transfer') bool get canTransfer;@JsonKey(name: 'phone_verified') bool get phoneVerified;@JsonKey(name: 'email_verified') bool get emailVerified;@JsonKey(name: 'id_verified') bool get idVerified;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'last_transaction_at') DateTime? get lastTransactionAt;
/// Create a copy of WalletModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletModelCopyWith<WalletModel> get copyWith => _$WalletModelCopyWithImpl<WalletModel>(this as WalletModel, _$identity);

  /// Serializes this WalletModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.verificationLevel, verificationLevel) || other.verificationLevel == verificationLevel)&&(identical(other.dailyLimit, dailyLimit) || other.dailyLimit == dailyLimit)&&(identical(other.monthlyLimit, monthlyLimit) || other.monthlyLimit == monthlyLimit)&&(identical(other.dailySpent, dailySpent) || other.dailySpent == dailySpent)&&(identical(other.monthlySpent, monthlySpent) || other.monthlySpent == monthlySpent)&&(identical(other.canWithdraw, canWithdraw) || other.canWithdraw == canWithdraw)&&(identical(other.canTransfer, canTransfer) || other.canTransfer == canTransfer)&&(identical(other.phoneVerified, phoneVerified) || other.phoneVerified == phoneVerified)&&(identical(other.emailVerified, emailVerified) || other.emailVerified == emailVerified)&&(identical(other.idVerified, idVerified) || other.idVerified == idVerified)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.lastTransactionAt, lastTransactionAt) || other.lastTransactionAt == lastTransactionAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,balance,currency,status,verificationLevel,dailyLimit,monthlyLimit,dailySpent,monthlySpent,canWithdraw,canTransfer,phoneVerified,emailVerified,idVerified,createdAt,updatedAt,lastTransactionAt);

@override
String toString() {
  return 'WalletModel(id: $id, userId: $userId, balance: $balance, currency: $currency, status: $status, verificationLevel: $verificationLevel, dailyLimit: $dailyLimit, monthlyLimit: $monthlyLimit, dailySpent: $dailySpent, monthlySpent: $monthlySpent, canWithdraw: $canWithdraw, canTransfer: $canTransfer, phoneVerified: $phoneVerified, emailVerified: $emailVerified, idVerified: $idVerified, createdAt: $createdAt, updatedAt: $updatedAt, lastTransactionAt: $lastTransactionAt)';
}


}

/// @nodoc
abstract mixin class $WalletModelCopyWith<$Res>  {
  factory $WalletModelCopyWith(WalletModel value, $Res Function(WalletModel) _then) = _$WalletModelCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'user_id') String userId, double balance,@JsonKey(name: 'currency') String currency,@JsonKey(name: 'status') WalletStatus status,@JsonKey(name: 'verification_level') VerificationLevel verificationLevel,@JsonKey(name: 'daily_limit') double dailyLimit,@JsonKey(name: 'monthly_limit') double monthlyLimit,@JsonKey(name: 'daily_spent') double dailySpent,@JsonKey(name: 'monthly_spent') double monthlySpent,@JsonKey(name: 'can_withdraw') bool canWithdraw,@JsonKey(name: 'can_transfer') bool canTransfer,@JsonKey(name: 'phone_verified') bool phoneVerified,@JsonKey(name: 'email_verified') bool emailVerified,@JsonKey(name: 'id_verified') bool idVerified,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'last_transaction_at') DateTime? lastTransactionAt
});




}
/// @nodoc
class _$WalletModelCopyWithImpl<$Res>
    implements $WalletModelCopyWith<$Res> {
  _$WalletModelCopyWithImpl(this._self, this._then);

  final WalletModel _self;
  final $Res Function(WalletModel) _then;

/// Create a copy of WalletModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? balance = null,Object? currency = null,Object? status = null,Object? verificationLevel = null,Object? dailyLimit = null,Object? monthlyLimit = null,Object? dailySpent = null,Object? monthlySpent = null,Object? canWithdraw = null,Object? canTransfer = null,Object? phoneVerified = null,Object? emailVerified = null,Object? idVerified = null,Object? createdAt = null,Object? updatedAt = freezed,Object? lastTransactionAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WalletStatus,verificationLevel: null == verificationLevel ? _self.verificationLevel : verificationLevel // ignore: cast_nullable_to_non_nullable
as VerificationLevel,dailyLimit: null == dailyLimit ? _self.dailyLimit : dailyLimit // ignore: cast_nullable_to_non_nullable
as double,monthlyLimit: null == monthlyLimit ? _self.monthlyLimit : monthlyLimit // ignore: cast_nullable_to_non_nullable
as double,dailySpent: null == dailySpent ? _self.dailySpent : dailySpent // ignore: cast_nullable_to_non_nullable
as double,monthlySpent: null == monthlySpent ? _self.monthlySpent : monthlySpent // ignore: cast_nullable_to_non_nullable
as double,canWithdraw: null == canWithdraw ? _self.canWithdraw : canWithdraw // ignore: cast_nullable_to_non_nullable
as bool,canTransfer: null == canTransfer ? _self.canTransfer : canTransfer // ignore: cast_nullable_to_non_nullable
as bool,phoneVerified: null == phoneVerified ? _self.phoneVerified : phoneVerified // ignore: cast_nullable_to_non_nullable
as bool,emailVerified: null == emailVerified ? _self.emailVerified : emailVerified // ignore: cast_nullable_to_non_nullable
as bool,idVerified: null == idVerified ? _self.idVerified : idVerified // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastTransactionAt: freezed == lastTransactionAt ? _self.lastTransactionAt : lastTransactionAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletModel].
extension WalletModelPatterns on WalletModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletModel value)  $default,){
final _that = this;
switch (_that) {
case _WalletModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletModel value)?  $default,){
final _that = this;
switch (_that) {
case _WalletModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'currency')  String currency, @JsonKey(name: 'status')  WalletStatus status, @JsonKey(name: 'verification_level')  VerificationLevel verificationLevel, @JsonKey(name: 'daily_limit')  double dailyLimit, @JsonKey(name: 'monthly_limit')  double monthlyLimit, @JsonKey(name: 'daily_spent')  double dailySpent, @JsonKey(name: 'monthly_spent')  double monthlySpent, @JsonKey(name: 'can_withdraw')  bool canWithdraw, @JsonKey(name: 'can_transfer')  bool canTransfer, @JsonKey(name: 'phone_verified')  bool phoneVerified, @JsonKey(name: 'email_verified')  bool emailVerified, @JsonKey(name: 'id_verified')  bool idVerified, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'last_transaction_at')  DateTime? lastTransactionAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletModel() when $default != null:
return $default(_that.id,_that.userId,_that.balance,_that.currency,_that.status,_that.verificationLevel,_that.dailyLimit,_that.monthlyLimit,_that.dailySpent,_that.monthlySpent,_that.canWithdraw,_that.canTransfer,_that.phoneVerified,_that.emailVerified,_that.idVerified,_that.createdAt,_that.updatedAt,_that.lastTransactionAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'currency')  String currency, @JsonKey(name: 'status')  WalletStatus status, @JsonKey(name: 'verification_level')  VerificationLevel verificationLevel, @JsonKey(name: 'daily_limit')  double dailyLimit, @JsonKey(name: 'monthly_limit')  double monthlyLimit, @JsonKey(name: 'daily_spent')  double dailySpent, @JsonKey(name: 'monthly_spent')  double monthlySpent, @JsonKey(name: 'can_withdraw')  bool canWithdraw, @JsonKey(name: 'can_transfer')  bool canTransfer, @JsonKey(name: 'phone_verified')  bool phoneVerified, @JsonKey(name: 'email_verified')  bool emailVerified, @JsonKey(name: 'id_verified')  bool idVerified, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'last_transaction_at')  DateTime? lastTransactionAt)  $default,) {final _that = this;
switch (_that) {
case _WalletModel():
return $default(_that.id,_that.userId,_that.balance,_that.currency,_that.status,_that.verificationLevel,_that.dailyLimit,_that.monthlyLimit,_that.dailySpent,_that.monthlySpent,_that.canWithdraw,_that.canTransfer,_that.phoneVerified,_that.emailVerified,_that.idVerified,_that.createdAt,_that.updatedAt,_that.lastTransactionAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'currency')  String currency, @JsonKey(name: 'status')  WalletStatus status, @JsonKey(name: 'verification_level')  VerificationLevel verificationLevel, @JsonKey(name: 'daily_limit')  double dailyLimit, @JsonKey(name: 'monthly_limit')  double monthlyLimit, @JsonKey(name: 'daily_spent')  double dailySpent, @JsonKey(name: 'monthly_spent')  double monthlySpent, @JsonKey(name: 'can_withdraw')  bool canWithdraw, @JsonKey(name: 'can_transfer')  bool canTransfer, @JsonKey(name: 'phone_verified')  bool phoneVerified, @JsonKey(name: 'email_verified')  bool emailVerified, @JsonKey(name: 'id_verified')  bool idVerified, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'last_transaction_at')  DateTime? lastTransactionAt)?  $default,) {final _that = this;
switch (_that) {
case _WalletModel() when $default != null:
return $default(_that.id,_that.userId,_that.balance,_that.currency,_that.status,_that.verificationLevel,_that.dailyLimit,_that.monthlyLimit,_that.dailySpent,_that.monthlySpent,_that.canWithdraw,_that.canTransfer,_that.phoneVerified,_that.emailVerified,_that.idVerified,_that.createdAt,_that.updatedAt,_that.lastTransactionAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletModel implements WalletModel {
  const _WalletModel({required this.id, @JsonKey(name: 'user_id') required this.userId, required this.balance, @JsonKey(name: 'currency') this.currency = 'LYD', @JsonKey(name: 'status') this.status = WalletStatus.active, @JsonKey(name: 'verification_level') this.verificationLevel = VerificationLevel.unverified, @JsonKey(name: 'daily_limit') required this.dailyLimit, @JsonKey(name: 'monthly_limit') required this.monthlyLimit, @JsonKey(name: 'daily_spent') this.dailySpent = 0.0, @JsonKey(name: 'monthly_spent') this.monthlySpent = 0.0, @JsonKey(name: 'can_withdraw') this.canWithdraw = false, @JsonKey(name: 'can_transfer') this.canTransfer = false, @JsonKey(name: 'phone_verified') this.phoneVerified = false, @JsonKey(name: 'email_verified') this.emailVerified = false, @JsonKey(name: 'id_verified') this.idVerified = false, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'last_transaction_at') this.lastTransactionAt});
  factory _WalletModel.fromJson(Map<String, dynamic> json) => _$WalletModelFromJson(json);

@override final  String id;
@override@JsonKey(name: 'user_id') final  String userId;
@override final  double balance;
@override@JsonKey(name: 'currency') final  String currency;
@override@JsonKey(name: 'status') final  WalletStatus status;
@override@JsonKey(name: 'verification_level') final  VerificationLevel verificationLevel;
@override@JsonKey(name: 'daily_limit') final  double dailyLimit;
@override@JsonKey(name: 'monthly_limit') final  double monthlyLimit;
@override@JsonKey(name: 'daily_spent') final  double dailySpent;
@override@JsonKey(name: 'monthly_spent') final  double monthlySpent;
@override@JsonKey(name: 'can_withdraw') final  bool canWithdraw;
@override@JsonKey(name: 'can_transfer') final  bool canTransfer;
@override@JsonKey(name: 'phone_verified') final  bool phoneVerified;
@override@JsonKey(name: 'email_verified') final  bool emailVerified;
@override@JsonKey(name: 'id_verified') final  bool idVerified;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'last_transaction_at') final  DateTime? lastTransactionAt;

/// Create a copy of WalletModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletModelCopyWith<_WalletModel> get copyWith => __$WalletModelCopyWithImpl<_WalletModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.verificationLevel, verificationLevel) || other.verificationLevel == verificationLevel)&&(identical(other.dailyLimit, dailyLimit) || other.dailyLimit == dailyLimit)&&(identical(other.monthlyLimit, monthlyLimit) || other.monthlyLimit == monthlyLimit)&&(identical(other.dailySpent, dailySpent) || other.dailySpent == dailySpent)&&(identical(other.monthlySpent, monthlySpent) || other.monthlySpent == monthlySpent)&&(identical(other.canWithdraw, canWithdraw) || other.canWithdraw == canWithdraw)&&(identical(other.canTransfer, canTransfer) || other.canTransfer == canTransfer)&&(identical(other.phoneVerified, phoneVerified) || other.phoneVerified == phoneVerified)&&(identical(other.emailVerified, emailVerified) || other.emailVerified == emailVerified)&&(identical(other.idVerified, idVerified) || other.idVerified == idVerified)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.lastTransactionAt, lastTransactionAt) || other.lastTransactionAt == lastTransactionAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,balance,currency,status,verificationLevel,dailyLimit,monthlyLimit,dailySpent,monthlySpent,canWithdraw,canTransfer,phoneVerified,emailVerified,idVerified,createdAt,updatedAt,lastTransactionAt);

@override
String toString() {
  return 'WalletModel(id: $id, userId: $userId, balance: $balance, currency: $currency, status: $status, verificationLevel: $verificationLevel, dailyLimit: $dailyLimit, monthlyLimit: $monthlyLimit, dailySpent: $dailySpent, monthlySpent: $monthlySpent, canWithdraw: $canWithdraw, canTransfer: $canTransfer, phoneVerified: $phoneVerified, emailVerified: $emailVerified, idVerified: $idVerified, createdAt: $createdAt, updatedAt: $updatedAt, lastTransactionAt: $lastTransactionAt)';
}


}

/// @nodoc
abstract mixin class _$WalletModelCopyWith<$Res> implements $WalletModelCopyWith<$Res> {
  factory _$WalletModelCopyWith(_WalletModel value, $Res Function(_WalletModel) _then) = __$WalletModelCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'user_id') String userId, double balance,@JsonKey(name: 'currency') String currency,@JsonKey(name: 'status') WalletStatus status,@JsonKey(name: 'verification_level') VerificationLevel verificationLevel,@JsonKey(name: 'daily_limit') double dailyLimit,@JsonKey(name: 'monthly_limit') double monthlyLimit,@JsonKey(name: 'daily_spent') double dailySpent,@JsonKey(name: 'monthly_spent') double monthlySpent,@JsonKey(name: 'can_withdraw') bool canWithdraw,@JsonKey(name: 'can_transfer') bool canTransfer,@JsonKey(name: 'phone_verified') bool phoneVerified,@JsonKey(name: 'email_verified') bool emailVerified,@JsonKey(name: 'id_verified') bool idVerified,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'last_transaction_at') DateTime? lastTransactionAt
});




}
/// @nodoc
class __$WalletModelCopyWithImpl<$Res>
    implements _$WalletModelCopyWith<$Res> {
  __$WalletModelCopyWithImpl(this._self, this._then);

  final _WalletModel _self;
  final $Res Function(_WalletModel) _then;

/// Create a copy of WalletModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? balance = null,Object? currency = null,Object? status = null,Object? verificationLevel = null,Object? dailyLimit = null,Object? monthlyLimit = null,Object? dailySpent = null,Object? monthlySpent = null,Object? canWithdraw = null,Object? canTransfer = null,Object? phoneVerified = null,Object? emailVerified = null,Object? idVerified = null,Object? createdAt = null,Object? updatedAt = freezed,Object? lastTransactionAt = freezed,}) {
  return _then(_WalletModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WalletStatus,verificationLevel: null == verificationLevel ? _self.verificationLevel : verificationLevel // ignore: cast_nullable_to_non_nullable
as VerificationLevel,dailyLimit: null == dailyLimit ? _self.dailyLimit : dailyLimit // ignore: cast_nullable_to_non_nullable
as double,monthlyLimit: null == monthlyLimit ? _self.monthlyLimit : monthlyLimit // ignore: cast_nullable_to_non_nullable
as double,dailySpent: null == dailySpent ? _self.dailySpent : dailySpent // ignore: cast_nullable_to_non_nullable
as double,monthlySpent: null == monthlySpent ? _self.monthlySpent : monthlySpent // ignore: cast_nullable_to_non_nullable
as double,canWithdraw: null == canWithdraw ? _self.canWithdraw : canWithdraw // ignore: cast_nullable_to_non_nullable
as bool,canTransfer: null == canTransfer ? _self.canTransfer : canTransfer // ignore: cast_nullable_to_non_nullable
as bool,phoneVerified: null == phoneVerified ? _self.phoneVerified : phoneVerified // ignore: cast_nullable_to_non_nullable
as bool,emailVerified: null == emailVerified ? _self.emailVerified : emailVerified // ignore: cast_nullable_to_non_nullable
as bool,idVerified: null == idVerified ? _self.idVerified : idVerified // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastTransactionAt: freezed == lastTransactionAt ? _self.lastTransactionAt : lastTransactionAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$WalletTransaction {

 String get id;@JsonKey(name: 'wallet_id') String get walletId;@JsonKey(name: 'user_id') String get userId; double get amount;@JsonKey(name: 'transaction_type') WalletTransactionType get type;@JsonKey(name: 'status') WalletTransactionStatus get status;@JsonKey(name: 'reference_id') String? get referenceId; String get description;@JsonKey(name: 'balance_before') double get balanceBefore;@JsonKey(name: 'balance_after') double get balanceAfter; Map<String, dynamic>? get metadata;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'processed_at') DateTime? get processedAt;
/// Create a copy of WalletTransaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletTransactionCopyWith<WalletTransaction> get copyWith => _$WalletTransactionCopyWithImpl<WalletTransaction>(this as WalletTransaction, _$identity);

  /// Serializes this WalletTransaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.referenceId, referenceId) || other.referenceId == referenceId)&&(identical(other.description, description) || other.description == description)&&(identical(other.balanceBefore, balanceBefore) || other.balanceBefore == balanceBefore)&&(identical(other.balanceAfter, balanceAfter) || other.balanceAfter == balanceAfter)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,walletId,userId,amount,type,status,referenceId,description,balanceBefore,balanceAfter,const DeepCollectionEquality().hash(metadata),createdAt,processedAt);

@override
String toString() {
  return 'WalletTransaction(id: $id, walletId: $walletId, userId: $userId, amount: $amount, type: $type, status: $status, referenceId: $referenceId, description: $description, balanceBefore: $balanceBefore, balanceAfter: $balanceAfter, metadata: $metadata, createdAt: $createdAt, processedAt: $processedAt)';
}


}

/// @nodoc
abstract mixin class $WalletTransactionCopyWith<$Res>  {
  factory $WalletTransactionCopyWith(WalletTransaction value, $Res Function(WalletTransaction) _then) = _$WalletTransactionCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_id') String userId, double amount,@JsonKey(name: 'transaction_type') WalletTransactionType type,@JsonKey(name: 'status') WalletTransactionStatus status,@JsonKey(name: 'reference_id') String? referenceId, String description,@JsonKey(name: 'balance_before') double balanceBefore,@JsonKey(name: 'balance_after') double balanceAfter, Map<String, dynamic>? metadata,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'processed_at') DateTime? processedAt
});




}
/// @nodoc
class _$WalletTransactionCopyWithImpl<$Res>
    implements $WalletTransactionCopyWith<$Res> {
  _$WalletTransactionCopyWithImpl(this._self, this._then);

  final WalletTransaction _self;
  final $Res Function(WalletTransaction) _then;

/// Create a copy of WalletTransaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? walletId = null,Object? userId = null,Object? amount = null,Object? type = null,Object? status = null,Object? referenceId = freezed,Object? description = null,Object? balanceBefore = null,Object? balanceAfter = null,Object? metadata = freezed,Object? createdAt = null,Object? processedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as WalletTransactionType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WalletTransactionStatus,referenceId: freezed == referenceId ? _self.referenceId : referenceId // ignore: cast_nullable_to_non_nullable
as String?,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,balanceBefore: null == balanceBefore ? _self.balanceBefore : balanceBefore // ignore: cast_nullable_to_non_nullable
as double,balanceAfter: null == balanceAfter ? _self.balanceAfter : balanceAfter // ignore: cast_nullable_to_non_nullable
as double,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletTransaction].
extension WalletTransactionPatterns on WalletTransaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletTransaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletTransaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletTransaction value)  $default,){
final _that = this;
switch (_that) {
case _WalletTransaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletTransaction value)?  $default,){
final _that = this;
switch (_that) {
case _WalletTransaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId,  double amount, @JsonKey(name: 'transaction_type')  WalletTransactionType type, @JsonKey(name: 'status')  WalletTransactionStatus status, @JsonKey(name: 'reference_id')  String? referenceId,  String description, @JsonKey(name: 'balance_before')  double balanceBefore, @JsonKey(name: 'balance_after')  double balanceAfter,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletTransaction() when $default != null:
return $default(_that.id,_that.walletId,_that.userId,_that.amount,_that.type,_that.status,_that.referenceId,_that.description,_that.balanceBefore,_that.balanceAfter,_that.metadata,_that.createdAt,_that.processedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId,  double amount, @JsonKey(name: 'transaction_type')  WalletTransactionType type, @JsonKey(name: 'status')  WalletTransactionStatus status, @JsonKey(name: 'reference_id')  String? referenceId,  String description, @JsonKey(name: 'balance_before')  double balanceBefore, @JsonKey(name: 'balance_after')  double balanceAfter,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)  $default,) {final _that = this;
switch (_that) {
case _WalletTransaction():
return $default(_that.id,_that.walletId,_that.userId,_that.amount,_that.type,_that.status,_that.referenceId,_that.description,_that.balanceBefore,_that.balanceAfter,_that.metadata,_that.createdAt,_that.processedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId,  double amount, @JsonKey(name: 'transaction_type')  WalletTransactionType type, @JsonKey(name: 'status')  WalletTransactionStatus status, @JsonKey(name: 'reference_id')  String? referenceId,  String description, @JsonKey(name: 'balance_before')  double balanceBefore, @JsonKey(name: 'balance_after')  double balanceAfter,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)?  $default,) {final _that = this;
switch (_that) {
case _WalletTransaction() when $default != null:
return $default(_that.id,_that.walletId,_that.userId,_that.amount,_that.type,_that.status,_that.referenceId,_that.description,_that.balanceBefore,_that.balanceAfter,_that.metadata,_that.createdAt,_that.processedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletTransaction implements WalletTransaction {
  const _WalletTransaction({required this.id, @JsonKey(name: 'wallet_id') required this.walletId, @JsonKey(name: 'user_id') required this.userId, required this.amount, @JsonKey(name: 'transaction_type') required this.type, @JsonKey(name: 'status') required this.status, @JsonKey(name: 'reference_id') this.referenceId, required this.description, @JsonKey(name: 'balance_before') required this.balanceBefore, @JsonKey(name: 'balance_after') required this.balanceAfter, final  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'processed_at') this.processedAt}): _metadata = metadata;
  factory _WalletTransaction.fromJson(Map<String, dynamic> json) => _$WalletTransactionFromJson(json);

@override final  String id;
@override@JsonKey(name: 'wallet_id') final  String walletId;
@override@JsonKey(name: 'user_id') final  String userId;
@override final  double amount;
@override@JsonKey(name: 'transaction_type') final  WalletTransactionType type;
@override@JsonKey(name: 'status') final  WalletTransactionStatus status;
@override@JsonKey(name: 'reference_id') final  String? referenceId;
@override final  String description;
@override@JsonKey(name: 'balance_before') final  double balanceBefore;
@override@JsonKey(name: 'balance_after') final  double balanceAfter;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'processed_at') final  DateTime? processedAt;

/// Create a copy of WalletTransaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletTransactionCopyWith<_WalletTransaction> get copyWith => __$WalletTransactionCopyWithImpl<_WalletTransaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletTransactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.referenceId, referenceId) || other.referenceId == referenceId)&&(identical(other.description, description) || other.description == description)&&(identical(other.balanceBefore, balanceBefore) || other.balanceBefore == balanceBefore)&&(identical(other.balanceAfter, balanceAfter) || other.balanceAfter == balanceAfter)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,walletId,userId,amount,type,status,referenceId,description,balanceBefore,balanceAfter,const DeepCollectionEquality().hash(_metadata),createdAt,processedAt);

@override
String toString() {
  return 'WalletTransaction(id: $id, walletId: $walletId, userId: $userId, amount: $amount, type: $type, status: $status, referenceId: $referenceId, description: $description, balanceBefore: $balanceBefore, balanceAfter: $balanceAfter, metadata: $metadata, createdAt: $createdAt, processedAt: $processedAt)';
}


}

/// @nodoc
abstract mixin class _$WalletTransactionCopyWith<$Res> implements $WalletTransactionCopyWith<$Res> {
  factory _$WalletTransactionCopyWith(_WalletTransaction value, $Res Function(_WalletTransaction) _then) = __$WalletTransactionCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_id') String userId, double amount,@JsonKey(name: 'transaction_type') WalletTransactionType type,@JsonKey(name: 'status') WalletTransactionStatus status,@JsonKey(name: 'reference_id') String? referenceId, String description,@JsonKey(name: 'balance_before') double balanceBefore,@JsonKey(name: 'balance_after') double balanceAfter, Map<String, dynamic>? metadata,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'processed_at') DateTime? processedAt
});




}
/// @nodoc
class __$WalletTransactionCopyWithImpl<$Res>
    implements _$WalletTransactionCopyWith<$Res> {
  __$WalletTransactionCopyWithImpl(this._self, this._then);

  final _WalletTransaction _self;
  final $Res Function(_WalletTransaction) _then;

/// Create a copy of WalletTransaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? walletId = null,Object? userId = null,Object? amount = null,Object? type = null,Object? status = null,Object? referenceId = freezed,Object? description = null,Object? balanceBefore = null,Object? balanceAfter = null,Object? metadata = freezed,Object? createdAt = null,Object? processedAt = freezed,}) {
  return _then(_WalletTransaction(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as WalletTransactionType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WalletTransactionStatus,referenceId: freezed == referenceId ? _self.referenceId : referenceId // ignore: cast_nullable_to_non_nullable
as String?,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,balanceBefore: null == balanceBefore ? _self.balanceBefore : balanceBefore // ignore: cast_nullable_to_non_nullable
as double,balanceAfter: null == balanceAfter ? _self.balanceAfter : balanceAfter // ignore: cast_nullable_to_non_nullable
as double,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$TransferRequest {

@JsonKey(name: 'to_user_id') String get toUserId; double get amount; String get description; String? get otp;
/// Create a copy of TransferRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferRequestCopyWith<TransferRequest> get copyWith => _$TransferRequestCopyWithImpl<TransferRequest>(this as TransferRequest, _$identity);

  /// Serializes this TransferRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferRequest&&(identical(other.toUserId, toUserId) || other.toUserId == toUserId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.otp, otp) || other.otp == otp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,toUserId,amount,description,otp);

@override
String toString() {
  return 'TransferRequest(toUserId: $toUserId, amount: $amount, description: $description, otp: $otp)';
}


}

/// @nodoc
abstract mixin class $TransferRequestCopyWith<$Res>  {
  factory $TransferRequestCopyWith(TransferRequest value, $Res Function(TransferRequest) _then) = _$TransferRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'to_user_id') String toUserId, double amount, String description, String? otp
});




}
/// @nodoc
class _$TransferRequestCopyWithImpl<$Res>
    implements $TransferRequestCopyWith<$Res> {
  _$TransferRequestCopyWithImpl(this._self, this._then);

  final TransferRequest _self;
  final $Res Function(TransferRequest) _then;

/// Create a copy of TransferRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? toUserId = null,Object? amount = null,Object? description = null,Object? otp = freezed,}) {
  return _then(_self.copyWith(
toUserId: null == toUserId ? _self.toUserId : toUserId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,otp: freezed == otp ? _self.otp : otp // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [TransferRequest].
extension TransferRequestPatterns on TransferRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TransferRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TransferRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TransferRequest value)  $default,){
final _that = this;
switch (_that) {
case _TransferRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TransferRequest value)?  $default,){
final _that = this;
switch (_that) {
case _TransferRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'to_user_id')  String toUserId,  double amount,  String description,  String? otp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TransferRequest() when $default != null:
return $default(_that.toUserId,_that.amount,_that.description,_that.otp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'to_user_id')  String toUserId,  double amount,  String description,  String? otp)  $default,) {final _that = this;
switch (_that) {
case _TransferRequest():
return $default(_that.toUserId,_that.amount,_that.description,_that.otp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'to_user_id')  String toUserId,  double amount,  String description,  String? otp)?  $default,) {final _that = this;
switch (_that) {
case _TransferRequest() when $default != null:
return $default(_that.toUserId,_that.amount,_that.description,_that.otp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TransferRequest implements TransferRequest {
  const _TransferRequest({@JsonKey(name: 'to_user_id') required this.toUserId, required this.amount, required this.description, this.otp});
  factory _TransferRequest.fromJson(Map<String, dynamic> json) => _$TransferRequestFromJson(json);

@override@JsonKey(name: 'to_user_id') final  String toUserId;
@override final  double amount;
@override final  String description;
@override final  String? otp;

/// Create a copy of TransferRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransferRequestCopyWith<_TransferRequest> get copyWith => __$TransferRequestCopyWithImpl<_TransferRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TransferRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransferRequest&&(identical(other.toUserId, toUserId) || other.toUserId == toUserId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.otp, otp) || other.otp == otp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,toUserId,amount,description,otp);

@override
String toString() {
  return 'TransferRequest(toUserId: $toUserId, amount: $amount, description: $description, otp: $otp)';
}


}

/// @nodoc
abstract mixin class _$TransferRequestCopyWith<$Res> implements $TransferRequestCopyWith<$Res> {
  factory _$TransferRequestCopyWith(_TransferRequest value, $Res Function(_TransferRequest) _then) = __$TransferRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'to_user_id') String toUserId, double amount, String description, String? otp
});




}
/// @nodoc
class __$TransferRequestCopyWithImpl<$Res>
    implements _$TransferRequestCopyWith<$Res> {
  __$TransferRequestCopyWithImpl(this._self, this._then);

  final _TransferRequest _self;
  final $Res Function(_TransferRequest) _then;

/// Create a copy of TransferRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? toUserId = null,Object? amount = null,Object? description = null,Object? otp = freezed,}) {
  return _then(_TransferRequest(
toUserId: null == toUserId ? _self.toUserId : toUserId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,otp: freezed == otp ? _self.otp : otp // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$DepositRequest {

 double get amount; String get description;@JsonKey(name: 'payment_method') String get paymentMethod; Map<String, dynamic>? get metadata;
/// Create a copy of DepositRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositRequestCopyWith<DepositRequest> get copyWith => _$DepositRequestCopyWithImpl<DepositRequest>(this as DepositRequest, _$identity);

  /// Serializes this DepositRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositRequest&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,description,paymentMethod,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'DepositRequest(amount: $amount, description: $description, paymentMethod: $paymentMethod, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $DepositRequestCopyWith<$Res>  {
  factory $DepositRequestCopyWith(DepositRequest value, $Res Function(DepositRequest) _then) = _$DepositRequestCopyWithImpl;
@useResult
$Res call({
 double amount, String description,@JsonKey(name: 'payment_method') String paymentMethod, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$DepositRequestCopyWithImpl<$Res>
    implements $DepositRequestCopyWith<$Res> {
  _$DepositRequestCopyWithImpl(this._self, this._then);

  final DepositRequest _self;
  final $Res Function(DepositRequest) _then;

/// Create a copy of DepositRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? amount = null,Object? description = null,Object? paymentMethod = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [DepositRequest].
extension DepositRequestPatterns on DepositRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DepositRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DepositRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DepositRequest value)  $default,){
final _that = this;
switch (_that) {
case _DepositRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DepositRequest value)?  $default,){
final _that = this;
switch (_that) {
case _DepositRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double amount,  String description, @JsonKey(name: 'payment_method')  String paymentMethod,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DepositRequest() when $default != null:
return $default(_that.amount,_that.description,_that.paymentMethod,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double amount,  String description, @JsonKey(name: 'payment_method')  String paymentMethod,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _DepositRequest():
return $default(_that.amount,_that.description,_that.paymentMethod,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double amount,  String description, @JsonKey(name: 'payment_method')  String paymentMethod,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _DepositRequest() when $default != null:
return $default(_that.amount,_that.description,_that.paymentMethod,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DepositRequest implements DepositRequest {
  const _DepositRequest({required this.amount, required this.description, @JsonKey(name: 'payment_method') required this.paymentMethod, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _DepositRequest.fromJson(Map<String, dynamic> json) => _$DepositRequestFromJson(json);

@override final  double amount;
@override final  String description;
@override@JsonKey(name: 'payment_method') final  String paymentMethod;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of DepositRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositRequestCopyWith<_DepositRequest> get copyWith => __$DepositRequestCopyWithImpl<_DepositRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepositRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositRequest&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,description,paymentMethod,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'DepositRequest(amount: $amount, description: $description, paymentMethod: $paymentMethod, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$DepositRequestCopyWith<$Res> implements $DepositRequestCopyWith<$Res> {
  factory _$DepositRequestCopyWith(_DepositRequest value, $Res Function(_DepositRequest) _then) = __$DepositRequestCopyWithImpl;
@override @useResult
$Res call({
 double amount, String description,@JsonKey(name: 'payment_method') String paymentMethod, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$DepositRequestCopyWithImpl<$Res>
    implements _$DepositRequestCopyWith<$Res> {
  __$DepositRequestCopyWithImpl(this._self, this._then);

  final _DepositRequest _self;
  final $Res Function(_DepositRequest) _then;

/// Create a copy of DepositRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? amount = null,Object? description = null,Object? paymentMethod = null,Object? metadata = freezed,}) {
  return _then(_DepositRequest(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$WithdrawalRequest {

 double get amount; String get description;@JsonKey(name: 'withdrawal_method') String get withdrawalMethod;@JsonKey(name: 'bank_account') Map<String, dynamic>? get bankAccount; String? get otp;
/// Create a copy of WithdrawalRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawalRequestCopyWith<WithdrawalRequest> get copyWith => _$WithdrawalRequestCopyWithImpl<WithdrawalRequest>(this as WithdrawalRequest, _$identity);

  /// Serializes this WithdrawalRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawalRequest&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.withdrawalMethod, withdrawalMethod) || other.withdrawalMethod == withdrawalMethod)&&const DeepCollectionEquality().equals(other.bankAccount, bankAccount)&&(identical(other.otp, otp) || other.otp == otp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,description,withdrawalMethod,const DeepCollectionEquality().hash(bankAccount),otp);

@override
String toString() {
  return 'WithdrawalRequest(amount: $amount, description: $description, withdrawalMethod: $withdrawalMethod, bankAccount: $bankAccount, otp: $otp)';
}


}

/// @nodoc
abstract mixin class $WithdrawalRequestCopyWith<$Res>  {
  factory $WithdrawalRequestCopyWith(WithdrawalRequest value, $Res Function(WithdrawalRequest) _then) = _$WithdrawalRequestCopyWithImpl;
@useResult
$Res call({
 double amount, String description,@JsonKey(name: 'withdrawal_method') String withdrawalMethod,@JsonKey(name: 'bank_account') Map<String, dynamic>? bankAccount, String? otp
});




}
/// @nodoc
class _$WithdrawalRequestCopyWithImpl<$Res>
    implements $WithdrawalRequestCopyWith<$Res> {
  _$WithdrawalRequestCopyWithImpl(this._self, this._then);

  final WithdrawalRequest _self;
  final $Res Function(WithdrawalRequest) _then;

/// Create a copy of WithdrawalRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? amount = null,Object? description = null,Object? withdrawalMethod = null,Object? bankAccount = freezed,Object? otp = freezed,}) {
  return _then(_self.copyWith(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,withdrawalMethod: null == withdrawalMethod ? _self.withdrawalMethod : withdrawalMethod // ignore: cast_nullable_to_non_nullable
as String,bankAccount: freezed == bankAccount ? _self.bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,otp: freezed == otp ? _self.otp : otp // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [WithdrawalRequest].
extension WithdrawalRequestPatterns on WithdrawalRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WithdrawalRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WithdrawalRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WithdrawalRequest value)  $default,){
final _that = this;
switch (_that) {
case _WithdrawalRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WithdrawalRequest value)?  $default,){
final _that = this;
switch (_that) {
case _WithdrawalRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double amount,  String description, @JsonKey(name: 'withdrawal_method')  String withdrawalMethod, @JsonKey(name: 'bank_account')  Map<String, dynamic>? bankAccount,  String? otp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WithdrawalRequest() when $default != null:
return $default(_that.amount,_that.description,_that.withdrawalMethod,_that.bankAccount,_that.otp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double amount,  String description, @JsonKey(name: 'withdrawal_method')  String withdrawalMethod, @JsonKey(name: 'bank_account')  Map<String, dynamic>? bankAccount,  String? otp)  $default,) {final _that = this;
switch (_that) {
case _WithdrawalRequest():
return $default(_that.amount,_that.description,_that.withdrawalMethod,_that.bankAccount,_that.otp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double amount,  String description, @JsonKey(name: 'withdrawal_method')  String withdrawalMethod, @JsonKey(name: 'bank_account')  Map<String, dynamic>? bankAccount,  String? otp)?  $default,) {final _that = this;
switch (_that) {
case _WithdrawalRequest() when $default != null:
return $default(_that.amount,_that.description,_that.withdrawalMethod,_that.bankAccount,_that.otp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WithdrawalRequest implements WithdrawalRequest {
  const _WithdrawalRequest({required this.amount, required this.description, @JsonKey(name: 'withdrawal_method') required this.withdrawalMethod, @JsonKey(name: 'bank_account') final  Map<String, dynamic>? bankAccount, this.otp}): _bankAccount = bankAccount;
  factory _WithdrawalRequest.fromJson(Map<String, dynamic> json) => _$WithdrawalRequestFromJson(json);

@override final  double amount;
@override final  String description;
@override@JsonKey(name: 'withdrawal_method') final  String withdrawalMethod;
 final  Map<String, dynamic>? _bankAccount;
@override@JsonKey(name: 'bank_account') Map<String, dynamic>? get bankAccount {
  final value = _bankAccount;
  if (value == null) return null;
  if (_bankAccount is EqualUnmodifiableMapView) return _bankAccount;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? otp;

/// Create a copy of WithdrawalRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawalRequestCopyWith<_WithdrawalRequest> get copyWith => __$WithdrawalRequestCopyWithImpl<_WithdrawalRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WithdrawalRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawalRequest&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.withdrawalMethod, withdrawalMethod) || other.withdrawalMethod == withdrawalMethod)&&const DeepCollectionEquality().equals(other._bankAccount, _bankAccount)&&(identical(other.otp, otp) || other.otp == otp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,description,withdrawalMethod,const DeepCollectionEquality().hash(_bankAccount),otp);

@override
String toString() {
  return 'WithdrawalRequest(amount: $amount, description: $description, withdrawalMethod: $withdrawalMethod, bankAccount: $bankAccount, otp: $otp)';
}


}

/// @nodoc
abstract mixin class _$WithdrawalRequestCopyWith<$Res> implements $WithdrawalRequestCopyWith<$Res> {
  factory _$WithdrawalRequestCopyWith(_WithdrawalRequest value, $Res Function(_WithdrawalRequest) _then) = __$WithdrawalRequestCopyWithImpl;
@override @useResult
$Res call({
 double amount, String description,@JsonKey(name: 'withdrawal_method') String withdrawalMethod,@JsonKey(name: 'bank_account') Map<String, dynamic>? bankAccount, String? otp
});




}
/// @nodoc
class __$WithdrawalRequestCopyWithImpl<$Res>
    implements _$WithdrawalRequestCopyWith<$Res> {
  __$WithdrawalRequestCopyWithImpl(this._self, this._then);

  final _WithdrawalRequest _self;
  final $Res Function(_WithdrawalRequest) _then;

/// Create a copy of WithdrawalRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? amount = null,Object? description = null,Object? withdrawalMethod = null,Object? bankAccount = freezed,Object? otp = freezed,}) {
  return _then(_WithdrawalRequest(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,withdrawalMethod: null == withdrawalMethod ? _self.withdrawalMethod : withdrawalMethod // ignore: cast_nullable_to_non_nullable
as String,bankAccount: freezed == bankAccount ? _self._bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,otp: freezed == otp ? _self.otp : otp // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$WalletSummary {

@JsonKey(name: 'total_balance') double get totalBalance;@JsonKey(name: 'available_balance') double get availableBalance;@JsonKey(name: 'pending_balance') double get pendingBalance;@JsonKey(name: 'daily_remaining') double get dailyRemaining;@JsonKey(name: 'monthly_remaining') double get monthlyRemaining;@JsonKey(name: 'total_transactions') int get totalTransactions;@JsonKey(name: 'total_spent_today') double get totalSpentToday;@JsonKey(name: 'total_spent_month') double get totalSpentMonth;@JsonKey(name: 'last_transaction') DateTime? get lastTransaction;
/// Create a copy of WalletSummary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletSummaryCopyWith<WalletSummary> get copyWith => _$WalletSummaryCopyWithImpl<WalletSummary>(this as WalletSummary, _$identity);

  /// Serializes this WalletSummary to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletSummary&&(identical(other.totalBalance, totalBalance) || other.totalBalance == totalBalance)&&(identical(other.availableBalance, availableBalance) || other.availableBalance == availableBalance)&&(identical(other.pendingBalance, pendingBalance) || other.pendingBalance == pendingBalance)&&(identical(other.dailyRemaining, dailyRemaining) || other.dailyRemaining == dailyRemaining)&&(identical(other.monthlyRemaining, monthlyRemaining) || other.monthlyRemaining == monthlyRemaining)&&(identical(other.totalTransactions, totalTransactions) || other.totalTransactions == totalTransactions)&&(identical(other.totalSpentToday, totalSpentToday) || other.totalSpentToday == totalSpentToday)&&(identical(other.totalSpentMonth, totalSpentMonth) || other.totalSpentMonth == totalSpentMonth)&&(identical(other.lastTransaction, lastTransaction) || other.lastTransaction == lastTransaction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalBalance,availableBalance,pendingBalance,dailyRemaining,monthlyRemaining,totalTransactions,totalSpentToday,totalSpentMonth,lastTransaction);

@override
String toString() {
  return 'WalletSummary(totalBalance: $totalBalance, availableBalance: $availableBalance, pendingBalance: $pendingBalance, dailyRemaining: $dailyRemaining, monthlyRemaining: $monthlyRemaining, totalTransactions: $totalTransactions, totalSpentToday: $totalSpentToday, totalSpentMonth: $totalSpentMonth, lastTransaction: $lastTransaction)';
}


}

/// @nodoc
abstract mixin class $WalletSummaryCopyWith<$Res>  {
  factory $WalletSummaryCopyWith(WalletSummary value, $Res Function(WalletSummary) _then) = _$WalletSummaryCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'total_balance') double totalBalance,@JsonKey(name: 'available_balance') double availableBalance,@JsonKey(name: 'pending_balance') double pendingBalance,@JsonKey(name: 'daily_remaining') double dailyRemaining,@JsonKey(name: 'monthly_remaining') double monthlyRemaining,@JsonKey(name: 'total_transactions') int totalTransactions,@JsonKey(name: 'total_spent_today') double totalSpentToday,@JsonKey(name: 'total_spent_month') double totalSpentMonth,@JsonKey(name: 'last_transaction') DateTime? lastTransaction
});




}
/// @nodoc
class _$WalletSummaryCopyWithImpl<$Res>
    implements $WalletSummaryCopyWith<$Res> {
  _$WalletSummaryCopyWithImpl(this._self, this._then);

  final WalletSummary _self;
  final $Res Function(WalletSummary) _then;

/// Create a copy of WalletSummary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalBalance = null,Object? availableBalance = null,Object? pendingBalance = null,Object? dailyRemaining = null,Object? monthlyRemaining = null,Object? totalTransactions = null,Object? totalSpentToday = null,Object? totalSpentMonth = null,Object? lastTransaction = freezed,}) {
  return _then(_self.copyWith(
totalBalance: null == totalBalance ? _self.totalBalance : totalBalance // ignore: cast_nullable_to_non_nullable
as double,availableBalance: null == availableBalance ? _self.availableBalance : availableBalance // ignore: cast_nullable_to_non_nullable
as double,pendingBalance: null == pendingBalance ? _self.pendingBalance : pendingBalance // ignore: cast_nullable_to_non_nullable
as double,dailyRemaining: null == dailyRemaining ? _self.dailyRemaining : dailyRemaining // ignore: cast_nullable_to_non_nullable
as double,monthlyRemaining: null == monthlyRemaining ? _self.monthlyRemaining : monthlyRemaining // ignore: cast_nullable_to_non_nullable
as double,totalTransactions: null == totalTransactions ? _self.totalTransactions : totalTransactions // ignore: cast_nullable_to_non_nullable
as int,totalSpentToday: null == totalSpentToday ? _self.totalSpentToday : totalSpentToday // ignore: cast_nullable_to_non_nullable
as double,totalSpentMonth: null == totalSpentMonth ? _self.totalSpentMonth : totalSpentMonth // ignore: cast_nullable_to_non_nullable
as double,lastTransaction: freezed == lastTransaction ? _self.lastTransaction : lastTransaction // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletSummary].
extension WalletSummaryPatterns on WalletSummary {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletSummary value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletSummary() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletSummary value)  $default,){
final _that = this;
switch (_that) {
case _WalletSummary():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletSummary value)?  $default,){
final _that = this;
switch (_that) {
case _WalletSummary() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'total_balance')  double totalBalance, @JsonKey(name: 'available_balance')  double availableBalance, @JsonKey(name: 'pending_balance')  double pendingBalance, @JsonKey(name: 'daily_remaining')  double dailyRemaining, @JsonKey(name: 'monthly_remaining')  double monthlyRemaining, @JsonKey(name: 'total_transactions')  int totalTransactions, @JsonKey(name: 'total_spent_today')  double totalSpentToday, @JsonKey(name: 'total_spent_month')  double totalSpentMonth, @JsonKey(name: 'last_transaction')  DateTime? lastTransaction)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletSummary() when $default != null:
return $default(_that.totalBalance,_that.availableBalance,_that.pendingBalance,_that.dailyRemaining,_that.monthlyRemaining,_that.totalTransactions,_that.totalSpentToday,_that.totalSpentMonth,_that.lastTransaction);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'total_balance')  double totalBalance, @JsonKey(name: 'available_balance')  double availableBalance, @JsonKey(name: 'pending_balance')  double pendingBalance, @JsonKey(name: 'daily_remaining')  double dailyRemaining, @JsonKey(name: 'monthly_remaining')  double monthlyRemaining, @JsonKey(name: 'total_transactions')  int totalTransactions, @JsonKey(name: 'total_spent_today')  double totalSpentToday, @JsonKey(name: 'total_spent_month')  double totalSpentMonth, @JsonKey(name: 'last_transaction')  DateTime? lastTransaction)  $default,) {final _that = this;
switch (_that) {
case _WalletSummary():
return $default(_that.totalBalance,_that.availableBalance,_that.pendingBalance,_that.dailyRemaining,_that.monthlyRemaining,_that.totalTransactions,_that.totalSpentToday,_that.totalSpentMonth,_that.lastTransaction);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'total_balance')  double totalBalance, @JsonKey(name: 'available_balance')  double availableBalance, @JsonKey(name: 'pending_balance')  double pendingBalance, @JsonKey(name: 'daily_remaining')  double dailyRemaining, @JsonKey(name: 'monthly_remaining')  double monthlyRemaining, @JsonKey(name: 'total_transactions')  int totalTransactions, @JsonKey(name: 'total_spent_today')  double totalSpentToday, @JsonKey(name: 'total_spent_month')  double totalSpentMonth, @JsonKey(name: 'last_transaction')  DateTime? lastTransaction)?  $default,) {final _that = this;
switch (_that) {
case _WalletSummary() when $default != null:
return $default(_that.totalBalance,_that.availableBalance,_that.pendingBalance,_that.dailyRemaining,_that.monthlyRemaining,_that.totalTransactions,_that.totalSpentToday,_that.totalSpentMonth,_that.lastTransaction);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletSummary implements WalletSummary {
  const _WalletSummary({@JsonKey(name: 'total_balance') required this.totalBalance, @JsonKey(name: 'available_balance') required this.availableBalance, @JsonKey(name: 'pending_balance') required this.pendingBalance, @JsonKey(name: 'daily_remaining') required this.dailyRemaining, @JsonKey(name: 'monthly_remaining') required this.monthlyRemaining, @JsonKey(name: 'total_transactions') required this.totalTransactions, @JsonKey(name: 'total_spent_today') required this.totalSpentToday, @JsonKey(name: 'total_spent_month') required this.totalSpentMonth, @JsonKey(name: 'last_transaction') this.lastTransaction});
  factory _WalletSummary.fromJson(Map<String, dynamic> json) => _$WalletSummaryFromJson(json);

@override@JsonKey(name: 'total_balance') final  double totalBalance;
@override@JsonKey(name: 'available_balance') final  double availableBalance;
@override@JsonKey(name: 'pending_balance') final  double pendingBalance;
@override@JsonKey(name: 'daily_remaining') final  double dailyRemaining;
@override@JsonKey(name: 'monthly_remaining') final  double monthlyRemaining;
@override@JsonKey(name: 'total_transactions') final  int totalTransactions;
@override@JsonKey(name: 'total_spent_today') final  double totalSpentToday;
@override@JsonKey(name: 'total_spent_month') final  double totalSpentMonth;
@override@JsonKey(name: 'last_transaction') final  DateTime? lastTransaction;

/// Create a copy of WalletSummary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletSummaryCopyWith<_WalletSummary> get copyWith => __$WalletSummaryCopyWithImpl<_WalletSummary>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletSummaryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletSummary&&(identical(other.totalBalance, totalBalance) || other.totalBalance == totalBalance)&&(identical(other.availableBalance, availableBalance) || other.availableBalance == availableBalance)&&(identical(other.pendingBalance, pendingBalance) || other.pendingBalance == pendingBalance)&&(identical(other.dailyRemaining, dailyRemaining) || other.dailyRemaining == dailyRemaining)&&(identical(other.monthlyRemaining, monthlyRemaining) || other.monthlyRemaining == monthlyRemaining)&&(identical(other.totalTransactions, totalTransactions) || other.totalTransactions == totalTransactions)&&(identical(other.totalSpentToday, totalSpentToday) || other.totalSpentToday == totalSpentToday)&&(identical(other.totalSpentMonth, totalSpentMonth) || other.totalSpentMonth == totalSpentMonth)&&(identical(other.lastTransaction, lastTransaction) || other.lastTransaction == lastTransaction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalBalance,availableBalance,pendingBalance,dailyRemaining,monthlyRemaining,totalTransactions,totalSpentToday,totalSpentMonth,lastTransaction);

@override
String toString() {
  return 'WalletSummary(totalBalance: $totalBalance, availableBalance: $availableBalance, pendingBalance: $pendingBalance, dailyRemaining: $dailyRemaining, monthlyRemaining: $monthlyRemaining, totalTransactions: $totalTransactions, totalSpentToday: $totalSpentToday, totalSpentMonth: $totalSpentMonth, lastTransaction: $lastTransaction)';
}


}

/// @nodoc
abstract mixin class _$WalletSummaryCopyWith<$Res> implements $WalletSummaryCopyWith<$Res> {
  factory _$WalletSummaryCopyWith(_WalletSummary value, $Res Function(_WalletSummary) _then) = __$WalletSummaryCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'total_balance') double totalBalance,@JsonKey(name: 'available_balance') double availableBalance,@JsonKey(name: 'pending_balance') double pendingBalance,@JsonKey(name: 'daily_remaining') double dailyRemaining,@JsonKey(name: 'monthly_remaining') double monthlyRemaining,@JsonKey(name: 'total_transactions') int totalTransactions,@JsonKey(name: 'total_spent_today') double totalSpentToday,@JsonKey(name: 'total_spent_month') double totalSpentMonth,@JsonKey(name: 'last_transaction') DateTime? lastTransaction
});




}
/// @nodoc
class __$WalletSummaryCopyWithImpl<$Res>
    implements _$WalletSummaryCopyWith<$Res> {
  __$WalletSummaryCopyWithImpl(this._self, this._then);

  final _WalletSummary _self;
  final $Res Function(_WalletSummary) _then;

/// Create a copy of WalletSummary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalBalance = null,Object? availableBalance = null,Object? pendingBalance = null,Object? dailyRemaining = null,Object? monthlyRemaining = null,Object? totalTransactions = null,Object? totalSpentToday = null,Object? totalSpentMonth = null,Object? lastTransaction = freezed,}) {
  return _then(_WalletSummary(
totalBalance: null == totalBalance ? _self.totalBalance : totalBalance // ignore: cast_nullable_to_non_nullable
as double,availableBalance: null == availableBalance ? _self.availableBalance : availableBalance // ignore: cast_nullable_to_non_nullable
as double,pendingBalance: null == pendingBalance ? _self.pendingBalance : pendingBalance // ignore: cast_nullable_to_non_nullable
as double,dailyRemaining: null == dailyRemaining ? _self.dailyRemaining : dailyRemaining // ignore: cast_nullable_to_non_nullable
as double,monthlyRemaining: null == monthlyRemaining ? _self.monthlyRemaining : monthlyRemaining // ignore: cast_nullable_to_non_nullable
as double,totalTransactions: null == totalTransactions ? _self.totalTransactions : totalTransactions // ignore: cast_nullable_to_non_nullable
as int,totalSpentToday: null == totalSpentToday ? _self.totalSpentToday : totalSpentToday // ignore: cast_nullable_to_non_nullable
as double,totalSpentMonth: null == totalSpentMonth ? _self.totalSpentMonth : totalSpentMonth // ignore: cast_nullable_to_non_nullable
as double,lastTransaction: freezed == lastTransaction ? _self.lastTransaction : lastTransaction // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
