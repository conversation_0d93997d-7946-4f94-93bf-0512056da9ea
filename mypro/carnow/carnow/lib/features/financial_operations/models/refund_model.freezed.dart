// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refund_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RefundModel {

 String get id;@JsonKey(name: 'transaction_id') String get transactionId;@JsonKey(name: 'order_id') String? get orderId;@JsonKey(name: 'product_id') String? get productId;@JsonKey(name: 'requester_id') String get requesterId;@JsonKey(name: 'seller_id') String get sellerId;@JsonKey(name: 'original_amount') double get originalAmount;@JsonKey(name: 'refund_amount') double get refundAmount;@JsonKey(name: 'refund_fee') double get refundFee;@JsonKey(name: 'net_refund_amount') double get netRefundAmount; String get currency;@JsonKey(name: 'refund_type') RefundType get refundType;@JsonKey(name: 'refund_reason') RefundReason get refundReason; RefundStatus get status;@JsonKey(name: 'reason_description') String get reasonDescription;@JsonKey(name: 'admin_notes') String? get adminNotes;@JsonKey(name: 'rejection_reason') String? get rejectionReason;@JsonKey(name: 'supporting_documents') List<String> get supportingDocuments;@JsonKey(name: 'estimated_processing_time') int? get estimatedProcessingTime;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;@JsonKey(name: 'approved_at') DateTime? get approvedAt;@JsonKey(name: 'processed_at') DateTime? get processedAt;@JsonKey(name: 'completed_at') DateTime? get completedAt;@JsonKey(name: 'approved_by') String? get approvedBy;@JsonKey(name: 'processed_by') String? get processedBy;
/// Create a copy of RefundModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RefundModelCopyWith<RefundModel> get copyWith => _$RefundModelCopyWithImpl<RefundModel>(this as RefundModel, _$identity);

  /// Serializes this RefundModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RefundModel&&(identical(other.id, id) || other.id == id)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.requesterId, requesterId) || other.requesterId == requesterId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.originalAmount, originalAmount) || other.originalAmount == originalAmount)&&(identical(other.refundAmount, refundAmount) || other.refundAmount == refundAmount)&&(identical(other.refundFee, refundFee) || other.refundFee == refundFee)&&(identical(other.netRefundAmount, netRefundAmount) || other.netRefundAmount == netRefundAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.refundType, refundType) || other.refundType == refundType)&&(identical(other.refundReason, refundReason) || other.refundReason == refundReason)&&(identical(other.status, status) || other.status == status)&&(identical(other.reasonDescription, reasonDescription) || other.reasonDescription == reasonDescription)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&const DeepCollectionEquality().equals(other.supportingDocuments, supportingDocuments)&&(identical(other.estimatedProcessingTime, estimatedProcessingTime) || other.estimatedProcessingTime == estimatedProcessingTime)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.approvedAt, approvedAt) || other.approvedAt == approvedAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy)&&(identical(other.processedBy, processedBy) || other.processedBy == processedBy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,transactionId,orderId,productId,requesterId,sellerId,originalAmount,refundAmount,refundFee,netRefundAmount,currency,refundType,refundReason,status,reasonDescription,adminNotes,rejectionReason,const DeepCollectionEquality().hash(supportingDocuments),estimatedProcessingTime,createdAt,updatedAt,approvedAt,processedAt,completedAt,approvedBy,processedBy]);

@override
String toString() {
  return 'RefundModel(id: $id, transactionId: $transactionId, orderId: $orderId, productId: $productId, requesterId: $requesterId, sellerId: $sellerId, originalAmount: $originalAmount, refundAmount: $refundAmount, refundFee: $refundFee, netRefundAmount: $netRefundAmount, currency: $currency, refundType: $refundType, refundReason: $refundReason, status: $status, reasonDescription: $reasonDescription, adminNotes: $adminNotes, rejectionReason: $rejectionReason, supportingDocuments: $supportingDocuments, estimatedProcessingTime: $estimatedProcessingTime, createdAt: $createdAt, updatedAt: $updatedAt, approvedAt: $approvedAt, processedAt: $processedAt, completedAt: $completedAt, approvedBy: $approvedBy, processedBy: $processedBy)';
}


}

/// @nodoc
abstract mixin class $RefundModelCopyWith<$Res>  {
  factory $RefundModelCopyWith(RefundModel value, $Res Function(RefundModel) _then) = _$RefundModelCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'transaction_id') String transactionId,@JsonKey(name: 'order_id') String? orderId,@JsonKey(name: 'product_id') String? productId,@JsonKey(name: 'requester_id') String requesterId,@JsonKey(name: 'seller_id') String sellerId,@JsonKey(name: 'original_amount') double originalAmount,@JsonKey(name: 'refund_amount') double refundAmount,@JsonKey(name: 'refund_fee') double refundFee,@JsonKey(name: 'net_refund_amount') double netRefundAmount, String currency,@JsonKey(name: 'refund_type') RefundType refundType,@JsonKey(name: 'refund_reason') RefundReason refundReason, RefundStatus status,@JsonKey(name: 'reason_description') String reasonDescription,@JsonKey(name: 'admin_notes') String? adminNotes,@JsonKey(name: 'rejection_reason') String? rejectionReason,@JsonKey(name: 'supporting_documents') List<String> supportingDocuments,@JsonKey(name: 'estimated_processing_time') int? estimatedProcessingTime,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'approved_at') DateTime? approvedAt,@JsonKey(name: 'processed_at') DateTime? processedAt,@JsonKey(name: 'completed_at') DateTime? completedAt,@JsonKey(name: 'approved_by') String? approvedBy,@JsonKey(name: 'processed_by') String? processedBy
});




}
/// @nodoc
class _$RefundModelCopyWithImpl<$Res>
    implements $RefundModelCopyWith<$Res> {
  _$RefundModelCopyWithImpl(this._self, this._then);

  final RefundModel _self;
  final $Res Function(RefundModel) _then;

/// Create a copy of RefundModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? transactionId = null,Object? orderId = freezed,Object? productId = freezed,Object? requesterId = null,Object? sellerId = null,Object? originalAmount = null,Object? refundAmount = null,Object? refundFee = null,Object? netRefundAmount = null,Object? currency = null,Object? refundType = null,Object? refundReason = null,Object? status = null,Object? reasonDescription = null,Object? adminNotes = freezed,Object? rejectionReason = freezed,Object? supportingDocuments = null,Object? estimatedProcessingTime = freezed,Object? createdAt = null,Object? updatedAt = null,Object? approvedAt = freezed,Object? processedAt = freezed,Object? completedAt = freezed,Object? approvedBy = freezed,Object? processedBy = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,requesterId: null == requesterId ? _self.requesterId : requesterId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,originalAmount: null == originalAmount ? _self.originalAmount : originalAmount // ignore: cast_nullable_to_non_nullable
as double,refundAmount: null == refundAmount ? _self.refundAmount : refundAmount // ignore: cast_nullable_to_non_nullable
as double,refundFee: null == refundFee ? _self.refundFee : refundFee // ignore: cast_nullable_to_non_nullable
as double,netRefundAmount: null == netRefundAmount ? _self.netRefundAmount : netRefundAmount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,refundType: null == refundType ? _self.refundType : refundType // ignore: cast_nullable_to_non_nullable
as RefundType,refundReason: null == refundReason ? _self.refundReason : refundReason // ignore: cast_nullable_to_non_nullable
as RefundReason,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as RefundStatus,reasonDescription: null == reasonDescription ? _self.reasonDescription : reasonDescription // ignore: cast_nullable_to_non_nullable
as String,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,supportingDocuments: null == supportingDocuments ? _self.supportingDocuments : supportingDocuments // ignore: cast_nullable_to_non_nullable
as List<String>,estimatedProcessingTime: freezed == estimatedProcessingTime ? _self.estimatedProcessingTime : estimatedProcessingTime // ignore: cast_nullable_to_non_nullable
as int?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,approvedAt: freezed == approvedAt ? _self.approvedAt : approvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,approvedBy: freezed == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String?,processedBy: freezed == processedBy ? _self.processedBy : processedBy // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [RefundModel].
extension RefundModelPatterns on RefundModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RefundModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RefundModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RefundModel value)  $default,){
final _that = this;
switch (_that) {
case _RefundModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RefundModel value)?  $default,){
final _that = this;
switch (_that) {
case _RefundModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId, @JsonKey(name: 'requester_id')  String requesterId, @JsonKey(name: 'seller_id')  String sellerId, @JsonKey(name: 'original_amount')  double originalAmount, @JsonKey(name: 'refund_amount')  double refundAmount, @JsonKey(name: 'refund_fee')  double refundFee, @JsonKey(name: 'net_refund_amount')  double netRefundAmount,  String currency, @JsonKey(name: 'refund_type')  RefundType refundType, @JsonKey(name: 'refund_reason')  RefundReason refundReason,  RefundStatus status, @JsonKey(name: 'reason_description')  String reasonDescription, @JsonKey(name: 'admin_notes')  String? adminNotes, @JsonKey(name: 'rejection_reason')  String? rejectionReason, @JsonKey(name: 'supporting_documents')  List<String> supportingDocuments, @JsonKey(name: 'estimated_processing_time')  int? estimatedProcessingTime, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'approved_at')  DateTime? approvedAt, @JsonKey(name: 'processed_at')  DateTime? processedAt, @JsonKey(name: 'completed_at')  DateTime? completedAt, @JsonKey(name: 'approved_by')  String? approvedBy, @JsonKey(name: 'processed_by')  String? processedBy)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RefundModel() when $default != null:
return $default(_that.id,_that.transactionId,_that.orderId,_that.productId,_that.requesterId,_that.sellerId,_that.originalAmount,_that.refundAmount,_that.refundFee,_that.netRefundAmount,_that.currency,_that.refundType,_that.refundReason,_that.status,_that.reasonDescription,_that.adminNotes,_that.rejectionReason,_that.supportingDocuments,_that.estimatedProcessingTime,_that.createdAt,_that.updatedAt,_that.approvedAt,_that.processedAt,_that.completedAt,_that.approvedBy,_that.processedBy);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId, @JsonKey(name: 'requester_id')  String requesterId, @JsonKey(name: 'seller_id')  String sellerId, @JsonKey(name: 'original_amount')  double originalAmount, @JsonKey(name: 'refund_amount')  double refundAmount, @JsonKey(name: 'refund_fee')  double refundFee, @JsonKey(name: 'net_refund_amount')  double netRefundAmount,  String currency, @JsonKey(name: 'refund_type')  RefundType refundType, @JsonKey(name: 'refund_reason')  RefundReason refundReason,  RefundStatus status, @JsonKey(name: 'reason_description')  String reasonDescription, @JsonKey(name: 'admin_notes')  String? adminNotes, @JsonKey(name: 'rejection_reason')  String? rejectionReason, @JsonKey(name: 'supporting_documents')  List<String> supportingDocuments, @JsonKey(name: 'estimated_processing_time')  int? estimatedProcessingTime, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'approved_at')  DateTime? approvedAt, @JsonKey(name: 'processed_at')  DateTime? processedAt, @JsonKey(name: 'completed_at')  DateTime? completedAt, @JsonKey(name: 'approved_by')  String? approvedBy, @JsonKey(name: 'processed_by')  String? processedBy)  $default,) {final _that = this;
switch (_that) {
case _RefundModel():
return $default(_that.id,_that.transactionId,_that.orderId,_that.productId,_that.requesterId,_that.sellerId,_that.originalAmount,_that.refundAmount,_that.refundFee,_that.netRefundAmount,_that.currency,_that.refundType,_that.refundReason,_that.status,_that.reasonDescription,_that.adminNotes,_that.rejectionReason,_that.supportingDocuments,_that.estimatedProcessingTime,_that.createdAt,_that.updatedAt,_that.approvedAt,_that.processedAt,_that.completedAt,_that.approvedBy,_that.processedBy);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'order_id')  String? orderId, @JsonKey(name: 'product_id')  String? productId, @JsonKey(name: 'requester_id')  String requesterId, @JsonKey(name: 'seller_id')  String sellerId, @JsonKey(name: 'original_amount')  double originalAmount, @JsonKey(name: 'refund_amount')  double refundAmount, @JsonKey(name: 'refund_fee')  double refundFee, @JsonKey(name: 'net_refund_amount')  double netRefundAmount,  String currency, @JsonKey(name: 'refund_type')  RefundType refundType, @JsonKey(name: 'refund_reason')  RefundReason refundReason,  RefundStatus status, @JsonKey(name: 'reason_description')  String reasonDescription, @JsonKey(name: 'admin_notes')  String? adminNotes, @JsonKey(name: 'rejection_reason')  String? rejectionReason, @JsonKey(name: 'supporting_documents')  List<String> supportingDocuments, @JsonKey(name: 'estimated_processing_time')  int? estimatedProcessingTime, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'approved_at')  DateTime? approvedAt, @JsonKey(name: 'processed_at')  DateTime? processedAt, @JsonKey(name: 'completed_at')  DateTime? completedAt, @JsonKey(name: 'approved_by')  String? approvedBy, @JsonKey(name: 'processed_by')  String? processedBy)?  $default,) {final _that = this;
switch (_that) {
case _RefundModel() when $default != null:
return $default(_that.id,_that.transactionId,_that.orderId,_that.productId,_that.requesterId,_that.sellerId,_that.originalAmount,_that.refundAmount,_that.refundFee,_that.netRefundAmount,_that.currency,_that.refundType,_that.refundReason,_that.status,_that.reasonDescription,_that.adminNotes,_that.rejectionReason,_that.supportingDocuments,_that.estimatedProcessingTime,_that.createdAt,_that.updatedAt,_that.approvedAt,_that.processedAt,_that.completedAt,_that.approvedBy,_that.processedBy);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RefundModel extends RefundModel {
  const _RefundModel({required this.id, @JsonKey(name: 'transaction_id') required this.transactionId, @JsonKey(name: 'order_id') this.orderId, @JsonKey(name: 'product_id') this.productId, @JsonKey(name: 'requester_id') required this.requesterId, @JsonKey(name: 'seller_id') required this.sellerId, @JsonKey(name: 'original_amount') this.originalAmount = 0.0, @JsonKey(name: 'refund_amount') this.refundAmount = 0.0, @JsonKey(name: 'refund_fee') this.refundFee = 0.0, @JsonKey(name: 'net_refund_amount') this.netRefundAmount = 0.0, this.currency = 'LYD', @JsonKey(name: 'refund_type') this.refundType = RefundType.manual, @JsonKey(name: 'refund_reason') this.refundReason = RefundReason.other, this.status = RefundStatus.requested, @JsonKey(name: 'reason_description') this.reasonDescription = '', @JsonKey(name: 'admin_notes') this.adminNotes, @JsonKey(name: 'rejection_reason') this.rejectionReason, @JsonKey(name: 'supporting_documents') final  List<String> supportingDocuments = const [], @JsonKey(name: 'estimated_processing_time') this.estimatedProcessingTime, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt, @JsonKey(name: 'approved_at') this.approvedAt, @JsonKey(name: 'processed_at') this.processedAt, @JsonKey(name: 'completed_at') this.completedAt, @JsonKey(name: 'approved_by') this.approvedBy, @JsonKey(name: 'processed_by') this.processedBy}): _supportingDocuments = supportingDocuments,super._();
  factory _RefundModel.fromJson(Map<String, dynamic> json) => _$RefundModelFromJson(json);

@override final  String id;
@override@JsonKey(name: 'transaction_id') final  String transactionId;
@override@JsonKey(name: 'order_id') final  String? orderId;
@override@JsonKey(name: 'product_id') final  String? productId;
@override@JsonKey(name: 'requester_id') final  String requesterId;
@override@JsonKey(name: 'seller_id') final  String sellerId;
@override@JsonKey(name: 'original_amount') final  double originalAmount;
@override@JsonKey(name: 'refund_amount') final  double refundAmount;
@override@JsonKey(name: 'refund_fee') final  double refundFee;
@override@JsonKey(name: 'net_refund_amount') final  double netRefundAmount;
@override@JsonKey() final  String currency;
@override@JsonKey(name: 'refund_type') final  RefundType refundType;
@override@JsonKey(name: 'refund_reason') final  RefundReason refundReason;
@override@JsonKey() final  RefundStatus status;
@override@JsonKey(name: 'reason_description') final  String reasonDescription;
@override@JsonKey(name: 'admin_notes') final  String? adminNotes;
@override@JsonKey(name: 'rejection_reason') final  String? rejectionReason;
 final  List<String> _supportingDocuments;
@override@JsonKey(name: 'supporting_documents') List<String> get supportingDocuments {
  if (_supportingDocuments is EqualUnmodifiableListView) return _supportingDocuments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_supportingDocuments);
}

@override@JsonKey(name: 'estimated_processing_time') final  int? estimatedProcessingTime;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;
@override@JsonKey(name: 'approved_at') final  DateTime? approvedAt;
@override@JsonKey(name: 'processed_at') final  DateTime? processedAt;
@override@JsonKey(name: 'completed_at') final  DateTime? completedAt;
@override@JsonKey(name: 'approved_by') final  String? approvedBy;
@override@JsonKey(name: 'processed_by') final  String? processedBy;

/// Create a copy of RefundModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RefundModelCopyWith<_RefundModel> get copyWith => __$RefundModelCopyWithImpl<_RefundModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RefundModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefundModel&&(identical(other.id, id) || other.id == id)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.requesterId, requesterId) || other.requesterId == requesterId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.originalAmount, originalAmount) || other.originalAmount == originalAmount)&&(identical(other.refundAmount, refundAmount) || other.refundAmount == refundAmount)&&(identical(other.refundFee, refundFee) || other.refundFee == refundFee)&&(identical(other.netRefundAmount, netRefundAmount) || other.netRefundAmount == netRefundAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.refundType, refundType) || other.refundType == refundType)&&(identical(other.refundReason, refundReason) || other.refundReason == refundReason)&&(identical(other.status, status) || other.status == status)&&(identical(other.reasonDescription, reasonDescription) || other.reasonDescription == reasonDescription)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&const DeepCollectionEquality().equals(other._supportingDocuments, _supportingDocuments)&&(identical(other.estimatedProcessingTime, estimatedProcessingTime) || other.estimatedProcessingTime == estimatedProcessingTime)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.approvedAt, approvedAt) || other.approvedAt == approvedAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy)&&(identical(other.processedBy, processedBy) || other.processedBy == processedBy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,transactionId,orderId,productId,requesterId,sellerId,originalAmount,refundAmount,refundFee,netRefundAmount,currency,refundType,refundReason,status,reasonDescription,adminNotes,rejectionReason,const DeepCollectionEquality().hash(_supportingDocuments),estimatedProcessingTime,createdAt,updatedAt,approvedAt,processedAt,completedAt,approvedBy,processedBy]);

@override
String toString() {
  return 'RefundModel(id: $id, transactionId: $transactionId, orderId: $orderId, productId: $productId, requesterId: $requesterId, sellerId: $sellerId, originalAmount: $originalAmount, refundAmount: $refundAmount, refundFee: $refundFee, netRefundAmount: $netRefundAmount, currency: $currency, refundType: $refundType, refundReason: $refundReason, status: $status, reasonDescription: $reasonDescription, adminNotes: $adminNotes, rejectionReason: $rejectionReason, supportingDocuments: $supportingDocuments, estimatedProcessingTime: $estimatedProcessingTime, createdAt: $createdAt, updatedAt: $updatedAt, approvedAt: $approvedAt, processedAt: $processedAt, completedAt: $completedAt, approvedBy: $approvedBy, processedBy: $processedBy)';
}


}

/// @nodoc
abstract mixin class _$RefundModelCopyWith<$Res> implements $RefundModelCopyWith<$Res> {
  factory _$RefundModelCopyWith(_RefundModel value, $Res Function(_RefundModel) _then) = __$RefundModelCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'transaction_id') String transactionId,@JsonKey(name: 'order_id') String? orderId,@JsonKey(name: 'product_id') String? productId,@JsonKey(name: 'requester_id') String requesterId,@JsonKey(name: 'seller_id') String sellerId,@JsonKey(name: 'original_amount') double originalAmount,@JsonKey(name: 'refund_amount') double refundAmount,@JsonKey(name: 'refund_fee') double refundFee,@JsonKey(name: 'net_refund_amount') double netRefundAmount, String currency,@JsonKey(name: 'refund_type') RefundType refundType,@JsonKey(name: 'refund_reason') RefundReason refundReason, RefundStatus status,@JsonKey(name: 'reason_description') String reasonDescription,@JsonKey(name: 'admin_notes') String? adminNotes,@JsonKey(name: 'rejection_reason') String? rejectionReason,@JsonKey(name: 'supporting_documents') List<String> supportingDocuments,@JsonKey(name: 'estimated_processing_time') int? estimatedProcessingTime,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'approved_at') DateTime? approvedAt,@JsonKey(name: 'processed_at') DateTime? processedAt,@JsonKey(name: 'completed_at') DateTime? completedAt,@JsonKey(name: 'approved_by') String? approvedBy,@JsonKey(name: 'processed_by') String? processedBy
});




}
/// @nodoc
class __$RefundModelCopyWithImpl<$Res>
    implements _$RefundModelCopyWith<$Res> {
  __$RefundModelCopyWithImpl(this._self, this._then);

  final _RefundModel _self;
  final $Res Function(_RefundModel) _then;

/// Create a copy of RefundModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? transactionId = null,Object? orderId = freezed,Object? productId = freezed,Object? requesterId = null,Object? sellerId = null,Object? originalAmount = null,Object? refundAmount = null,Object? refundFee = null,Object? netRefundAmount = null,Object? currency = null,Object? refundType = null,Object? refundReason = null,Object? status = null,Object? reasonDescription = null,Object? adminNotes = freezed,Object? rejectionReason = freezed,Object? supportingDocuments = null,Object? estimatedProcessingTime = freezed,Object? createdAt = null,Object? updatedAt = null,Object? approvedAt = freezed,Object? processedAt = freezed,Object? completedAt = freezed,Object? approvedBy = freezed,Object? processedBy = freezed,}) {
  return _then(_RefundModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,requesterId: null == requesterId ? _self.requesterId : requesterId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,originalAmount: null == originalAmount ? _self.originalAmount : originalAmount // ignore: cast_nullable_to_non_nullable
as double,refundAmount: null == refundAmount ? _self.refundAmount : refundAmount // ignore: cast_nullable_to_non_nullable
as double,refundFee: null == refundFee ? _self.refundFee : refundFee // ignore: cast_nullable_to_non_nullable
as double,netRefundAmount: null == netRefundAmount ? _self.netRefundAmount : netRefundAmount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,refundType: null == refundType ? _self.refundType : refundType // ignore: cast_nullable_to_non_nullable
as RefundType,refundReason: null == refundReason ? _self.refundReason : refundReason // ignore: cast_nullable_to_non_nullable
as RefundReason,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as RefundStatus,reasonDescription: null == reasonDescription ? _self.reasonDescription : reasonDescription // ignore: cast_nullable_to_non_nullable
as String,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,supportingDocuments: null == supportingDocuments ? _self._supportingDocuments : supportingDocuments // ignore: cast_nullable_to_non_nullable
as List<String>,estimatedProcessingTime: freezed == estimatedProcessingTime ? _self.estimatedProcessingTime : estimatedProcessingTime // ignore: cast_nullable_to_non_nullable
as int?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,approvedAt: freezed == approvedAt ? _self.approvedAt : approvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,approvedBy: freezed == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String?,processedBy: freezed == processedBy ? _self.processedBy : processedBy // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
