// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refund_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RefundRequest {

/// معرف المعاملة الأصلية
@JsonKey(name: 'transaction_id') String get transactionId;/// المبلغ المطلوب استرداده
@JsonKey(name: 'refund_amount') double get refundAmount;/// السبب المختصر للاسترداد (enum كقيمة نصية فى الوقت الحالى)
@JsonKey(name: 'reason') String get reason;/// وصف تفصيلى للسبب
@JsonKey(name: 'description') String? get description;/// مرفقات أو مستندات داعمة (روابط أو مسارات)
@JsonKey(name: 'supporting_documents') List<String>? get supportingDocuments;
/// Create a copy of RefundRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RefundRequestCopyWith<RefundRequest> get copyWith => _$RefundRequestCopyWithImpl<RefundRequest>(this as RefundRequest, _$identity);

  /// Serializes this RefundRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RefundRequest&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.refundAmount, refundAmount) || other.refundAmount == refundAmount)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other.supportingDocuments, supportingDocuments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,transactionId,refundAmount,reason,description,const DeepCollectionEquality().hash(supportingDocuments));

@override
String toString() {
  return 'RefundRequest(transactionId: $transactionId, refundAmount: $refundAmount, reason: $reason, description: $description, supportingDocuments: $supportingDocuments)';
}


}

/// @nodoc
abstract mixin class $RefundRequestCopyWith<$Res>  {
  factory $RefundRequestCopyWith(RefundRequest value, $Res Function(RefundRequest) _then) = _$RefundRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'transaction_id') String transactionId,@JsonKey(name: 'refund_amount') double refundAmount,@JsonKey(name: 'reason') String reason,@JsonKey(name: 'description') String? description,@JsonKey(name: 'supporting_documents') List<String>? supportingDocuments
});




}
/// @nodoc
class _$RefundRequestCopyWithImpl<$Res>
    implements $RefundRequestCopyWith<$Res> {
  _$RefundRequestCopyWithImpl(this._self, this._then);

  final RefundRequest _self;
  final $Res Function(RefundRequest) _then;

/// Create a copy of RefundRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? transactionId = null,Object? refundAmount = null,Object? reason = null,Object? description = freezed,Object? supportingDocuments = freezed,}) {
  return _then(_self.copyWith(
transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,refundAmount: null == refundAmount ? _self.refundAmount : refundAmount // ignore: cast_nullable_to_non_nullable
as double,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,supportingDocuments: freezed == supportingDocuments ? _self.supportingDocuments : supportingDocuments // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}

}


/// Adds pattern-matching-related methods to [RefundRequest].
extension RefundRequestPatterns on RefundRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RefundRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RefundRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RefundRequest value)  $default,){
final _that = this;
switch (_that) {
case _RefundRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RefundRequest value)?  $default,){
final _that = this;
switch (_that) {
case _RefundRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'refund_amount')  double refundAmount, @JsonKey(name: 'reason')  String reason, @JsonKey(name: 'description')  String? description, @JsonKey(name: 'supporting_documents')  List<String>? supportingDocuments)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RefundRequest() when $default != null:
return $default(_that.transactionId,_that.refundAmount,_that.reason,_that.description,_that.supportingDocuments);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'refund_amount')  double refundAmount, @JsonKey(name: 'reason')  String reason, @JsonKey(name: 'description')  String? description, @JsonKey(name: 'supporting_documents')  List<String>? supportingDocuments)  $default,) {final _that = this;
switch (_that) {
case _RefundRequest():
return $default(_that.transactionId,_that.refundAmount,_that.reason,_that.description,_that.supportingDocuments);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'transaction_id')  String transactionId, @JsonKey(name: 'refund_amount')  double refundAmount, @JsonKey(name: 'reason')  String reason, @JsonKey(name: 'description')  String? description, @JsonKey(name: 'supporting_documents')  List<String>? supportingDocuments)?  $default,) {final _that = this;
switch (_that) {
case _RefundRequest() when $default != null:
return $default(_that.transactionId,_that.refundAmount,_that.reason,_that.description,_that.supportingDocuments);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RefundRequest implements RefundRequest {
  const _RefundRequest({@JsonKey(name: 'transaction_id') required this.transactionId, @JsonKey(name: 'refund_amount') required this.refundAmount, @JsonKey(name: 'reason') required this.reason, @JsonKey(name: 'description') this.description, @JsonKey(name: 'supporting_documents') final  List<String>? supportingDocuments}): _supportingDocuments = supportingDocuments;
  factory _RefundRequest.fromJson(Map<String, dynamic> json) => _$RefundRequestFromJson(json);

/// معرف المعاملة الأصلية
@override@JsonKey(name: 'transaction_id') final  String transactionId;
/// المبلغ المطلوب استرداده
@override@JsonKey(name: 'refund_amount') final  double refundAmount;
/// السبب المختصر للاسترداد (enum كقيمة نصية فى الوقت الحالى)
@override@JsonKey(name: 'reason') final  String reason;
/// وصف تفصيلى للسبب
@override@JsonKey(name: 'description') final  String? description;
/// مرفقات أو مستندات داعمة (روابط أو مسارات)
 final  List<String>? _supportingDocuments;
/// مرفقات أو مستندات داعمة (روابط أو مسارات)
@override@JsonKey(name: 'supporting_documents') List<String>? get supportingDocuments {
  final value = _supportingDocuments;
  if (value == null) return null;
  if (_supportingDocuments is EqualUnmodifiableListView) return _supportingDocuments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of RefundRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RefundRequestCopyWith<_RefundRequest> get copyWith => __$RefundRequestCopyWithImpl<_RefundRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RefundRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefundRequest&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.refundAmount, refundAmount) || other.refundAmount == refundAmount)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other._supportingDocuments, _supportingDocuments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,transactionId,refundAmount,reason,description,const DeepCollectionEquality().hash(_supportingDocuments));

@override
String toString() {
  return 'RefundRequest(transactionId: $transactionId, refundAmount: $refundAmount, reason: $reason, description: $description, supportingDocuments: $supportingDocuments)';
}


}

/// @nodoc
abstract mixin class _$RefundRequestCopyWith<$Res> implements $RefundRequestCopyWith<$Res> {
  factory _$RefundRequestCopyWith(_RefundRequest value, $Res Function(_RefundRequest) _then) = __$RefundRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'transaction_id') String transactionId,@JsonKey(name: 'refund_amount') double refundAmount,@JsonKey(name: 'reason') String reason,@JsonKey(name: 'description') String? description,@JsonKey(name: 'supporting_documents') List<String>? supportingDocuments
});




}
/// @nodoc
class __$RefundRequestCopyWithImpl<$Res>
    implements _$RefundRequestCopyWith<$Res> {
  __$RefundRequestCopyWithImpl(this._self, this._then);

  final _RefundRequest _self;
  final $Res Function(_RefundRequest) _then;

/// Create a copy of RefundRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? transactionId = null,Object? refundAmount = null,Object? reason = null,Object? description = freezed,Object? supportingDocuments = freezed,}) {
  return _then(_RefundRequest(
transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,refundAmount: null == refundAmount ? _self.refundAmount : refundAmount // ignore: cast_nullable_to_non_nullable
as double,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,supportingDocuments: freezed == supportingDocuments ? _self._supportingDocuments : supportingDocuments // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}


}

// dart format on
