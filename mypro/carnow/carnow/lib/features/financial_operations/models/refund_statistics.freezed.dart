// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refund_statistics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RefundStatistics {

@JsonKey(name: 'total_refunds') int get totalRefunds;@JsonKey(name: 'pending_refunds') int get pendingRefunds;@JsonKey(name: 'approved_refunds') int get approvedRefunds;@JsonKey(name: 'rejected_refunds') int get rejectedRefunds;@JsonKey(name: 'processing_refunds') int get processingRefunds;@JsonKey(name: 'completed_refunds') int get completedRefunds;@JsonKey(name: 'failed_refunds') int get failedRefunds;@JsonKey(name: 'cancelled_refunds') int get cancelledRefunds;@JsonKey(name: 'total_refund_amount') double get totalRefundAmount;@JsonKey(name: 'average_refund_amount') double get averageRefundAmount;
/// Create a copy of RefundStatistics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RefundStatisticsCopyWith<RefundStatistics> get copyWith => _$RefundStatisticsCopyWithImpl<RefundStatistics>(this as RefundStatistics, _$identity);

  /// Serializes this RefundStatistics to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RefundStatistics&&(identical(other.totalRefunds, totalRefunds) || other.totalRefunds == totalRefunds)&&(identical(other.pendingRefunds, pendingRefunds) || other.pendingRefunds == pendingRefunds)&&(identical(other.approvedRefunds, approvedRefunds) || other.approvedRefunds == approvedRefunds)&&(identical(other.rejectedRefunds, rejectedRefunds) || other.rejectedRefunds == rejectedRefunds)&&(identical(other.processingRefunds, processingRefunds) || other.processingRefunds == processingRefunds)&&(identical(other.completedRefunds, completedRefunds) || other.completedRefunds == completedRefunds)&&(identical(other.failedRefunds, failedRefunds) || other.failedRefunds == failedRefunds)&&(identical(other.cancelledRefunds, cancelledRefunds) || other.cancelledRefunds == cancelledRefunds)&&(identical(other.totalRefundAmount, totalRefundAmount) || other.totalRefundAmount == totalRefundAmount)&&(identical(other.averageRefundAmount, averageRefundAmount) || other.averageRefundAmount == averageRefundAmount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalRefunds,pendingRefunds,approvedRefunds,rejectedRefunds,processingRefunds,completedRefunds,failedRefunds,cancelledRefunds,totalRefundAmount,averageRefundAmount);

@override
String toString() {
  return 'RefundStatistics(totalRefunds: $totalRefunds, pendingRefunds: $pendingRefunds, approvedRefunds: $approvedRefunds, rejectedRefunds: $rejectedRefunds, processingRefunds: $processingRefunds, completedRefunds: $completedRefunds, failedRefunds: $failedRefunds, cancelledRefunds: $cancelledRefunds, totalRefundAmount: $totalRefundAmount, averageRefundAmount: $averageRefundAmount)';
}


}

/// @nodoc
abstract mixin class $RefundStatisticsCopyWith<$Res>  {
  factory $RefundStatisticsCopyWith(RefundStatistics value, $Res Function(RefundStatistics) _then) = _$RefundStatisticsCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'total_refunds') int totalRefunds,@JsonKey(name: 'pending_refunds') int pendingRefunds,@JsonKey(name: 'approved_refunds') int approvedRefunds,@JsonKey(name: 'rejected_refunds') int rejectedRefunds,@JsonKey(name: 'processing_refunds') int processingRefunds,@JsonKey(name: 'completed_refunds') int completedRefunds,@JsonKey(name: 'failed_refunds') int failedRefunds,@JsonKey(name: 'cancelled_refunds') int cancelledRefunds,@JsonKey(name: 'total_refund_amount') double totalRefundAmount,@JsonKey(name: 'average_refund_amount') double averageRefundAmount
});




}
/// @nodoc
class _$RefundStatisticsCopyWithImpl<$Res>
    implements $RefundStatisticsCopyWith<$Res> {
  _$RefundStatisticsCopyWithImpl(this._self, this._then);

  final RefundStatistics _self;
  final $Res Function(RefundStatistics) _then;

/// Create a copy of RefundStatistics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalRefunds = null,Object? pendingRefunds = null,Object? approvedRefunds = null,Object? rejectedRefunds = null,Object? processingRefunds = null,Object? completedRefunds = null,Object? failedRefunds = null,Object? cancelledRefunds = null,Object? totalRefundAmount = null,Object? averageRefundAmount = null,}) {
  return _then(_self.copyWith(
totalRefunds: null == totalRefunds ? _self.totalRefunds : totalRefunds // ignore: cast_nullable_to_non_nullable
as int,pendingRefunds: null == pendingRefunds ? _self.pendingRefunds : pendingRefunds // ignore: cast_nullable_to_non_nullable
as int,approvedRefunds: null == approvedRefunds ? _self.approvedRefunds : approvedRefunds // ignore: cast_nullable_to_non_nullable
as int,rejectedRefunds: null == rejectedRefunds ? _self.rejectedRefunds : rejectedRefunds // ignore: cast_nullable_to_non_nullable
as int,processingRefunds: null == processingRefunds ? _self.processingRefunds : processingRefunds // ignore: cast_nullable_to_non_nullable
as int,completedRefunds: null == completedRefunds ? _self.completedRefunds : completedRefunds // ignore: cast_nullable_to_non_nullable
as int,failedRefunds: null == failedRefunds ? _self.failedRefunds : failedRefunds // ignore: cast_nullable_to_non_nullable
as int,cancelledRefunds: null == cancelledRefunds ? _self.cancelledRefunds : cancelledRefunds // ignore: cast_nullable_to_non_nullable
as int,totalRefundAmount: null == totalRefundAmount ? _self.totalRefundAmount : totalRefundAmount // ignore: cast_nullable_to_non_nullable
as double,averageRefundAmount: null == averageRefundAmount ? _self.averageRefundAmount : averageRefundAmount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [RefundStatistics].
extension RefundStatisticsPatterns on RefundStatistics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RefundStatistics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RefundStatistics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RefundStatistics value)  $default,){
final _that = this;
switch (_that) {
case _RefundStatistics():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RefundStatistics value)?  $default,){
final _that = this;
switch (_that) {
case _RefundStatistics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'total_refunds')  int totalRefunds, @JsonKey(name: 'pending_refunds')  int pendingRefunds, @JsonKey(name: 'approved_refunds')  int approvedRefunds, @JsonKey(name: 'rejected_refunds')  int rejectedRefunds, @JsonKey(name: 'processing_refunds')  int processingRefunds, @JsonKey(name: 'completed_refunds')  int completedRefunds, @JsonKey(name: 'failed_refunds')  int failedRefunds, @JsonKey(name: 'cancelled_refunds')  int cancelledRefunds, @JsonKey(name: 'total_refund_amount')  double totalRefundAmount, @JsonKey(name: 'average_refund_amount')  double averageRefundAmount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RefundStatistics() when $default != null:
return $default(_that.totalRefunds,_that.pendingRefunds,_that.approvedRefunds,_that.rejectedRefunds,_that.processingRefunds,_that.completedRefunds,_that.failedRefunds,_that.cancelledRefunds,_that.totalRefundAmount,_that.averageRefundAmount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'total_refunds')  int totalRefunds, @JsonKey(name: 'pending_refunds')  int pendingRefunds, @JsonKey(name: 'approved_refunds')  int approvedRefunds, @JsonKey(name: 'rejected_refunds')  int rejectedRefunds, @JsonKey(name: 'processing_refunds')  int processingRefunds, @JsonKey(name: 'completed_refunds')  int completedRefunds, @JsonKey(name: 'failed_refunds')  int failedRefunds, @JsonKey(name: 'cancelled_refunds')  int cancelledRefunds, @JsonKey(name: 'total_refund_amount')  double totalRefundAmount, @JsonKey(name: 'average_refund_amount')  double averageRefundAmount)  $default,) {final _that = this;
switch (_that) {
case _RefundStatistics():
return $default(_that.totalRefunds,_that.pendingRefunds,_that.approvedRefunds,_that.rejectedRefunds,_that.processingRefunds,_that.completedRefunds,_that.failedRefunds,_that.cancelledRefunds,_that.totalRefundAmount,_that.averageRefundAmount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'total_refunds')  int totalRefunds, @JsonKey(name: 'pending_refunds')  int pendingRefunds, @JsonKey(name: 'approved_refunds')  int approvedRefunds, @JsonKey(name: 'rejected_refunds')  int rejectedRefunds, @JsonKey(name: 'processing_refunds')  int processingRefunds, @JsonKey(name: 'completed_refunds')  int completedRefunds, @JsonKey(name: 'failed_refunds')  int failedRefunds, @JsonKey(name: 'cancelled_refunds')  int cancelledRefunds, @JsonKey(name: 'total_refund_amount')  double totalRefundAmount, @JsonKey(name: 'average_refund_amount')  double averageRefundAmount)?  $default,) {final _that = this;
switch (_that) {
case _RefundStatistics() when $default != null:
return $default(_that.totalRefunds,_that.pendingRefunds,_that.approvedRefunds,_that.rejectedRefunds,_that.processingRefunds,_that.completedRefunds,_that.failedRefunds,_that.cancelledRefunds,_that.totalRefundAmount,_that.averageRefundAmount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RefundStatistics implements RefundStatistics {
  const _RefundStatistics({@JsonKey(name: 'total_refunds') required this.totalRefunds, @JsonKey(name: 'pending_refunds') required this.pendingRefunds, @JsonKey(name: 'approved_refunds') required this.approvedRefunds, @JsonKey(name: 'rejected_refunds') required this.rejectedRefunds, @JsonKey(name: 'processing_refunds') required this.processingRefunds, @JsonKey(name: 'completed_refunds') required this.completedRefunds, @JsonKey(name: 'failed_refunds') required this.failedRefunds, @JsonKey(name: 'cancelled_refunds') required this.cancelledRefunds, @JsonKey(name: 'total_refund_amount') required this.totalRefundAmount, @JsonKey(name: 'average_refund_amount') required this.averageRefundAmount});
  factory _RefundStatistics.fromJson(Map<String, dynamic> json) => _$RefundStatisticsFromJson(json);

@override@JsonKey(name: 'total_refunds') final  int totalRefunds;
@override@JsonKey(name: 'pending_refunds') final  int pendingRefunds;
@override@JsonKey(name: 'approved_refunds') final  int approvedRefunds;
@override@JsonKey(name: 'rejected_refunds') final  int rejectedRefunds;
@override@JsonKey(name: 'processing_refunds') final  int processingRefunds;
@override@JsonKey(name: 'completed_refunds') final  int completedRefunds;
@override@JsonKey(name: 'failed_refunds') final  int failedRefunds;
@override@JsonKey(name: 'cancelled_refunds') final  int cancelledRefunds;
@override@JsonKey(name: 'total_refund_amount') final  double totalRefundAmount;
@override@JsonKey(name: 'average_refund_amount') final  double averageRefundAmount;

/// Create a copy of RefundStatistics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RefundStatisticsCopyWith<_RefundStatistics> get copyWith => __$RefundStatisticsCopyWithImpl<_RefundStatistics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RefundStatisticsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefundStatistics&&(identical(other.totalRefunds, totalRefunds) || other.totalRefunds == totalRefunds)&&(identical(other.pendingRefunds, pendingRefunds) || other.pendingRefunds == pendingRefunds)&&(identical(other.approvedRefunds, approvedRefunds) || other.approvedRefunds == approvedRefunds)&&(identical(other.rejectedRefunds, rejectedRefunds) || other.rejectedRefunds == rejectedRefunds)&&(identical(other.processingRefunds, processingRefunds) || other.processingRefunds == processingRefunds)&&(identical(other.completedRefunds, completedRefunds) || other.completedRefunds == completedRefunds)&&(identical(other.failedRefunds, failedRefunds) || other.failedRefunds == failedRefunds)&&(identical(other.cancelledRefunds, cancelledRefunds) || other.cancelledRefunds == cancelledRefunds)&&(identical(other.totalRefundAmount, totalRefundAmount) || other.totalRefundAmount == totalRefundAmount)&&(identical(other.averageRefundAmount, averageRefundAmount) || other.averageRefundAmount == averageRefundAmount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalRefunds,pendingRefunds,approvedRefunds,rejectedRefunds,processingRefunds,completedRefunds,failedRefunds,cancelledRefunds,totalRefundAmount,averageRefundAmount);

@override
String toString() {
  return 'RefundStatistics(totalRefunds: $totalRefunds, pendingRefunds: $pendingRefunds, approvedRefunds: $approvedRefunds, rejectedRefunds: $rejectedRefunds, processingRefunds: $processingRefunds, completedRefunds: $completedRefunds, failedRefunds: $failedRefunds, cancelledRefunds: $cancelledRefunds, totalRefundAmount: $totalRefundAmount, averageRefundAmount: $averageRefundAmount)';
}


}

/// @nodoc
abstract mixin class _$RefundStatisticsCopyWith<$Res> implements $RefundStatisticsCopyWith<$Res> {
  factory _$RefundStatisticsCopyWith(_RefundStatistics value, $Res Function(_RefundStatistics) _then) = __$RefundStatisticsCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'total_refunds') int totalRefunds,@JsonKey(name: 'pending_refunds') int pendingRefunds,@JsonKey(name: 'approved_refunds') int approvedRefunds,@JsonKey(name: 'rejected_refunds') int rejectedRefunds,@JsonKey(name: 'processing_refunds') int processingRefunds,@JsonKey(name: 'completed_refunds') int completedRefunds,@JsonKey(name: 'failed_refunds') int failedRefunds,@JsonKey(name: 'cancelled_refunds') int cancelledRefunds,@JsonKey(name: 'total_refund_amount') double totalRefundAmount,@JsonKey(name: 'average_refund_amount') double averageRefundAmount
});




}
/// @nodoc
class __$RefundStatisticsCopyWithImpl<$Res>
    implements _$RefundStatisticsCopyWith<$Res> {
  __$RefundStatisticsCopyWithImpl(this._self, this._then);

  final _RefundStatistics _self;
  final $Res Function(_RefundStatistics) _then;

/// Create a copy of RefundStatistics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalRefunds = null,Object? pendingRefunds = null,Object? approvedRefunds = null,Object? rejectedRefunds = null,Object? processingRefunds = null,Object? completedRefunds = null,Object? failedRefunds = null,Object? cancelledRefunds = null,Object? totalRefundAmount = null,Object? averageRefundAmount = null,}) {
  return _then(_RefundStatistics(
totalRefunds: null == totalRefunds ? _self.totalRefunds : totalRefunds // ignore: cast_nullable_to_non_nullable
as int,pendingRefunds: null == pendingRefunds ? _self.pendingRefunds : pendingRefunds // ignore: cast_nullable_to_non_nullable
as int,approvedRefunds: null == approvedRefunds ? _self.approvedRefunds : approvedRefunds // ignore: cast_nullable_to_non_nullable
as int,rejectedRefunds: null == rejectedRefunds ? _self.rejectedRefunds : rejectedRefunds // ignore: cast_nullable_to_non_nullable
as int,processingRefunds: null == processingRefunds ? _self.processingRefunds : processingRefunds // ignore: cast_nullable_to_non_nullable
as int,completedRefunds: null == completedRefunds ? _self.completedRefunds : completedRefunds // ignore: cast_nullable_to_non_nullable
as int,failedRefunds: null == failedRefunds ? _self.failedRefunds : failedRefunds // ignore: cast_nullable_to_non_nullable
as int,cancelledRefunds: null == cancelledRefunds ? _self.cancelledRefunds : cancelledRefunds // ignore: cast_nullable_to_non_nullable
as int,totalRefundAmount: null == totalRefundAmount ? _self.totalRefundAmount : totalRefundAmount // ignore: cast_nullable_to_non_nullable
as double,averageRefundAmount: null == averageRefundAmount ? _self.averageRefundAmount : averageRefundAmount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
