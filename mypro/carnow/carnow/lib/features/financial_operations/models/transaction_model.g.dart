// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TransactionModel _$TransactionModelFromJson(Map<String, dynamic> json) =>
    _TransactionModel(
      id: json['id'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'LYD',
      type: $enumDecode(_$TransactionTypeEnumMap, json['type']),
      status: $enumDecode(_$TransactionStatusEnumMap, json['status']),
      paymentMethod: $enumDecode(
        _$PaymentMethodEnumMap,
        json['payment_method'],
      ),
      payerId: json['payer_id'] as String,
      payeeId: json['payee_id'] as String,
      orderId: json['order_id'] as String?,
      productId: json['product_id'] as String?,
      description: json['description'] as String?,
      metadata: json['transaction_metadata'] as Map<String, dynamic>?,
      platformCommissionRate:
          (json['platform_commission_rate'] as num?)?.toDouble() ?? 0.05,
      platformCommissionAmount:
          (json['platform_commission_amount'] as num?)?.toDouble() ?? 0.0,
      codFeeRate: (json['cod_fee_rate'] as num?)?.toDouble() ?? 0.02,
      codFeeAmount: (json['cod_fee_amount'] as num?)?.toDouble() ?? 0.0,
      refundableAmount: (json['refundable_amount'] as num).toDouble(),
      refundedAmount: (json['refunded_amount'] as num?)?.toDouble() ?? 0.0,
      deliveryAddress: json['delivery_address'] as String?,
      deliveryPhone: json['delivery_phone'] as String?,
      deliveryNotes: json['delivery_notes'] as String?,
      estimatedDeliveryDate: json['estimated_delivery_date'] == null
          ? null
          : DateTime.parse(json['estimated_delivery_date'] as String),
      actualDeliveryDate: json['actual_delivery_date'] == null
          ? null
          : DateTime.parse(json['actual_delivery_date'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      completedAt: json['completed_at'] == null
          ? null
          : DateTime.parse(json['completed_at'] as String),
    );

Map<String, dynamic> _$TransactionModelToJson(
  _TransactionModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'amount': instance.amount,
  'currency': instance.currency,
  'type': _$TransactionTypeEnumMap[instance.type]!,
  'status': _$TransactionStatusEnumMap[instance.status]!,
  'payment_method': _$PaymentMethodEnumMap[instance.paymentMethod]!,
  'payer_id': instance.payerId,
  'payee_id': instance.payeeId,
  'order_id': instance.orderId,
  'product_id': instance.productId,
  'description': instance.description,
  'transaction_metadata': instance.metadata,
  'platform_commission_rate': instance.platformCommissionRate,
  'platform_commission_amount': instance.platformCommissionAmount,
  'cod_fee_rate': instance.codFeeRate,
  'cod_fee_amount': instance.codFeeAmount,
  'refundable_amount': instance.refundableAmount,
  'refunded_amount': instance.refundedAmount,
  'delivery_address': instance.deliveryAddress,
  'delivery_phone': instance.deliveryPhone,
  'delivery_notes': instance.deliveryNotes,
  'estimated_delivery_date': instance.estimatedDeliveryDate?.toIso8601String(),
  'actual_delivery_date': instance.actualDeliveryDate?.toIso8601String(),
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt.toIso8601String(),
  'completed_at': instance.completedAt?.toIso8601String(),
};

const _$TransactionTypeEnumMap = {
  TransactionType.purchase: 'purchase',
  TransactionType.sale: 'sale',
  TransactionType.refund: 'refund',
  TransactionType.commission: 'commission',
  TransactionType.platformFee: 'platform_fee',
  TransactionType.codFee: 'cod_fee',
  TransactionType.deposit: 'deposit',
  TransactionType.withdrawal: 'withdrawal',
  TransactionType.transfer: 'transfer',
};

const _$TransactionStatusEnumMap = {
  TransactionStatus.pending: 'pending',
  TransactionStatus.processing: 'processing',
  TransactionStatus.completed: 'completed',
  TransactionStatus.failed: 'failed',
  TransactionStatus.cancelled: 'cancelled',
  TransactionStatus.refundRequested: 'refund_requested',
  TransactionStatus.refundProcessing: 'refund_processing',
  TransactionStatus.refunded: 'refunded',
  TransactionStatus.partiallyRefunded: 'partially_refunded',
  TransactionStatus.awaitingDelivery: 'awaiting_delivery',
  TransactionStatus.delivered: 'delivered',
  TransactionStatus.deliveryFailed: 'delivery_failed',
};

const _$PaymentMethodEnumMap = {
  PaymentMethod.wallet: 'wallet',
  PaymentMethod.cashOnDelivery: 'cash_on_delivery',
};

_TransactionRequest _$TransactionRequestFromJson(Map<String, dynamic> json) =>
    _TransactionRequest(
      payeeId: json['payee_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      transactionType: $enumDecode(
        _$TransactionTypeEnumMap,
        json['transaction_type'],
      ),
      paymentMethod: $enumDecode(
        _$PaymentMethodEnumMap,
        json['payment_method'],
      ),
      description: json['description'] as String,
      orderId: json['order_id'] as String?,
      productId: json['product_id'] as String?,
      deliveryData: json['delivery_data'] as Map<String, dynamic>?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$TransactionRequestToJson(_TransactionRequest instance) =>
    <String, dynamic>{
      'payee_id': instance.payeeId,
      'amount': instance.amount,
      'transaction_type': _$TransactionTypeEnumMap[instance.transactionType]!,
      'payment_method': _$PaymentMethodEnumMap[instance.paymentMethod]!,
      'description': instance.description,
      'order_id': instance.orderId,
      'product_id': instance.productId,
      'delivery_data': instance.deliveryData,
      'metadata': instance.metadata,
    };

_TransactionResult _$TransactionResultFromJson(Map<String, dynamic> json) =>
    _TransactionResult(
      success: json['success'] as bool,
      transactionId: json['transaction_id'] as String?,
      amount: (json['amount'] as num).toDouble(),
      commission: (json['commission'] as num?)?.toDouble(),
      codFee: (json['cod_fee'] as num?)?.toDouble(),
      totalAmount: (json['total_amount'] as num?)?.toDouble(),
      netAmount: (json['net_amount'] as num?)?.toDouble(),
      deliveryStatus: json['delivery_status'] as String?,
      estimatedDelivery: json['estimated_delivery'] == null
          ? null
          : DateTime.parse(json['estimated_delivery'] as String),
      trackingInfo: json['tracking_info'] as Map<String, dynamic>?,
      completedAt: json['completed_at'] == null
          ? null
          : DateTime.parse(json['completed_at'] as String),
      error: json['error'] as String?,
      errorCode: json['error_code'] as String?,
    );

Map<String, dynamic> _$TransactionResultToJson(_TransactionResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'transaction_id': instance.transactionId,
      'amount': instance.amount,
      'commission': instance.commission,
      'cod_fee': instance.codFee,
      'total_amount': instance.totalAmount,
      'net_amount': instance.netAmount,
      'delivery_status': instance.deliveryStatus,
      'estimated_delivery': instance.estimatedDelivery?.toIso8601String(),
      'tracking_info': instance.trackingInfo,
      'completed_at': instance.completedAt?.toIso8601String(),
      'error': instance.error,
      'error_code': instance.errorCode,
    };

_CODDeliveryInfo _$CODDeliveryInfoFromJson(Map<String, dynamic> json) =>
    _CODDeliveryInfo(
      id: json['id'] as String,
      transactionId: json['transaction_id'] as String,
      customerName: json['customer_name'] as String,
      deliveryAddress: json['delivery_address'] as String,
      deliveryPhone: json['delivery_phone'] as String,
      alternativePhone: json['alternative_phone'] as String?,
      preferredDeliveryTime: json['preferred_delivery_time'] as String?,
      deliveryNotes: json['delivery_notes'] as String?,
      deliveryStatus: json['delivery_status'] as String? ?? 'pending',
      courierId: json['courier_id'] as String?,
      trackingNumber: json['tracking_number'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      deliveredAt: json['delivered_at'] == null
          ? null
          : DateTime.parse(json['delivered_at'] as String),
    );

Map<String, dynamic> _$CODDeliveryInfoToJson(_CODDeliveryInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'transaction_id': instance.transactionId,
      'customer_name': instance.customerName,
      'delivery_address': instance.deliveryAddress,
      'delivery_phone': instance.deliveryPhone,
      'alternative_phone': instance.alternativePhone,
      'preferred_delivery_time': instance.preferredDeliveryTime,
      'delivery_notes': instance.deliveryNotes,
      'delivery_status': instance.deliveryStatus,
      'courier_id': instance.courierId,
      'tracking_number': instance.trackingNumber,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'delivered_at': instance.deliveredAt?.toIso8601String(),
    };
