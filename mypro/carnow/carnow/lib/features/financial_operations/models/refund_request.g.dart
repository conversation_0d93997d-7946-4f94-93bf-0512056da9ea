// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'refund_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_RefundRequest _$RefundRequestFromJson(Map<String, dynamic> json) =>
    _RefundRequest(
      transactionId: json['transaction_id'] as String,
      refundAmount: (json['refund_amount'] as num).toDouble(),
      reason: json['reason'] as String,
      description: json['description'] as String?,
      supportingDocuments: (json['supporting_documents'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$RefundRequestToJson(_RefundRequest instance) =>
    <String, dynamic>{
      'transaction_id': instance.transactionId,
      'refund_amount': instance.refundAmount,
      'reason': instance.reason,
      'description': instance.description,
      'supporting_documents': instance.supportingDocuments,
    };
