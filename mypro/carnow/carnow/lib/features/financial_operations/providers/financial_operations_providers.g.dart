// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'financial_operations_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$financialOperationsServiceHash() =>
    r'a921352b5bb51b8817699d343b1a7602ba5cd2ae';

/// مزود خدمة العمليات المالية
///
/// Copied from [financialOperationsService].
@ProviderFor(financialOperationsService)
final financialOperationsServiceProvider =
    AutoDisposeProvider<FinancialOperationsService>.internal(
      financialOperationsService,
      name: r'financialOperationsServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$financialOperationsServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FinancialOperationsServiceRef =
    AutoDisposeProviderRef<FinancialOperationsService>;
String _$walletDataHash() => r'8acb0e79b1ccfa9d86454f28ebe8599a427e361b';

/// مزود بيانات المحفظة
///
/// Copied from [walletData].
@ProviderFor(walletData)
final walletDataProvider = AutoDisposeFutureProvider<WalletModel>.internal(
  walletData,
  name: r'walletDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$walletDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WalletDataRef = AutoDisposeFutureProviderRef<WalletModel>;
String _$walletSummaryHash() => r'be3d80e0cf81d07319721234a2ad3c1899bf4ed1';

/// مزود ملخص المحفظة
///
/// Copied from [walletSummary].
@ProviderFor(walletSummary)
final walletSummaryProvider = AutoDisposeFutureProvider<WalletSummary>.internal(
  walletSummary,
  name: r'walletSummaryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$walletSummaryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WalletSummaryRef = AutoDisposeFutureProviderRef<WalletSummary>;
String _$walletTransactionsHash() =>
    r'f547f3351f42ca7272e4315f32d3aa460fbac73b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود معاملات المحفظة
///
/// Copied from [walletTransactions].
@ProviderFor(walletTransactions)
const walletTransactionsProvider = WalletTransactionsFamily();

/// مزود معاملات المحفظة
///
/// Copied from [walletTransactions].
class WalletTransactionsFamily
    extends Family<AsyncValue<List<WalletTransaction>>> {
  /// مزود معاملات المحفظة
  ///
  /// Copied from [walletTransactions].
  const WalletTransactionsFamily();

  /// مزود معاملات المحفظة
  ///
  /// Copied from [walletTransactions].
  WalletTransactionsProvider call({
    int page = 1,
    int limit = 20,
    WalletTransactionType? type,
  }) {
    return WalletTransactionsProvider(page: page, limit: limit, type: type);
  }

  @override
  WalletTransactionsProvider getProviderOverride(
    covariant WalletTransactionsProvider provider,
  ) {
    return call(
      page: provider.page,
      limit: provider.limit,
      type: provider.type,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'walletTransactionsProvider';
}

/// مزود معاملات المحفظة
///
/// Copied from [walletTransactions].
class WalletTransactionsProvider
    extends AutoDisposeFutureProvider<List<WalletTransaction>> {
  /// مزود معاملات المحفظة
  ///
  /// Copied from [walletTransactions].
  WalletTransactionsProvider({
    int page = 1,
    int limit = 20,
    WalletTransactionType? type,
  }) : this._internal(
         (ref) => walletTransactions(
           ref as WalletTransactionsRef,
           page: page,
           limit: limit,
           type: type,
         ),
         from: walletTransactionsProvider,
         name: r'walletTransactionsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$walletTransactionsHash,
         dependencies: WalletTransactionsFamily._dependencies,
         allTransitiveDependencies:
             WalletTransactionsFamily._allTransitiveDependencies,
         page: page,
         limit: limit,
         type: type,
       );

  WalletTransactionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.page,
    required this.limit,
    required this.type,
  }) : super.internal();

  final int page;
  final int limit;
  final WalletTransactionType? type;

  @override
  Override overrideWith(
    FutureOr<List<WalletTransaction>> Function(WalletTransactionsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WalletTransactionsProvider._internal(
        (ref) => create(ref as WalletTransactionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        page: page,
        limit: limit,
        type: type,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<WalletTransaction>> createElement() {
    return _WalletTransactionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WalletTransactionsProvider &&
        other.page == page &&
        other.limit == limit &&
        other.type == type;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WalletTransactionsRef
    on AutoDisposeFutureProviderRef<List<WalletTransaction>> {
  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;

  /// The parameter `type` of this provider.
  WalletTransactionType? get type;
}

class _WalletTransactionsProviderElement
    extends AutoDisposeFutureProviderElement<List<WalletTransaction>>
    with WalletTransactionsRef {
  _WalletTransactionsProviderElement(super.provider);

  @override
  int get page => (origin as WalletTransactionsProvider).page;
  @override
  int get limit => (origin as WalletTransactionsProvider).limit;
  @override
  WalletTransactionType? get type =>
      (origin as WalletTransactionsProvider).type;
}

String _$transactionHash() => r'd33462c506e7efffc9c8465d219bce130a808983';

/// مزود معاملة واحدة
///
/// Copied from [transaction].
@ProviderFor(transaction)
const transactionProvider = TransactionFamily();

/// مزود معاملة واحدة
///
/// Copied from [transaction].
class TransactionFamily extends Family<AsyncValue<TransactionModel>> {
  /// مزود معاملة واحدة
  ///
  /// Copied from [transaction].
  const TransactionFamily();

  /// مزود معاملة واحدة
  ///
  /// Copied from [transaction].
  TransactionProvider call(String transactionId) {
    return TransactionProvider(transactionId);
  }

  @override
  TransactionProvider getProviderOverride(
    covariant TransactionProvider provider,
  ) {
    return call(provider.transactionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'transactionProvider';
}

/// مزود معاملة واحدة
///
/// Copied from [transaction].
class TransactionProvider extends AutoDisposeFutureProvider<TransactionModel> {
  /// مزود معاملة واحدة
  ///
  /// Copied from [transaction].
  TransactionProvider(String transactionId)
    : this._internal(
        (ref) => transaction(ref as TransactionRef, transactionId),
        from: transactionProvider,
        name: r'transactionProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$transactionHash,
        dependencies: TransactionFamily._dependencies,
        allTransitiveDependencies: TransactionFamily._allTransitiveDependencies,
        transactionId: transactionId,
      );

  TransactionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.transactionId,
  }) : super.internal();

  final String transactionId;

  @override
  Override overrideWith(
    FutureOr<TransactionModel> Function(TransactionRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TransactionProvider._internal(
        (ref) => create(ref as TransactionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        transactionId: transactionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<TransactionModel> createElement() {
    return _TransactionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TransactionProvider && other.transactionId == transactionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, transactionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TransactionRef on AutoDisposeFutureProviderRef<TransactionModel> {
  /// The parameter `transactionId` of this provider.
  String get transactionId;
}

class _TransactionProviderElement
    extends AutoDisposeFutureProviderElement<TransactionModel>
    with TransactionRef {
  _TransactionProviderElement(super.provider);

  @override
  String get transactionId => (origin as TransactionProvider).transactionId;
}

String _$walletStatisticsHash() => r'113913fc6dabd702c0066812966f26e086f71898';

/// مزود إحصائيات المحفظة
///
/// Copied from [walletStatistics].
@ProviderFor(walletStatistics)
const walletStatisticsProvider = WalletStatisticsFamily();

/// مزود إحصائيات المحفظة
///
/// Copied from [walletStatistics].
class WalletStatisticsFamily extends Family<AsyncValue<WalletStatistics>> {
  /// مزود إحصائيات المحفظة
  ///
  /// Copied from [walletStatistics].
  const WalletStatisticsFamily();

  /// مزود إحصائيات المحفظة
  ///
  /// Copied from [walletStatistics].
  WalletStatisticsProvider call({DateTime? startDate, DateTime? endDate}) {
    return WalletStatisticsProvider(startDate: startDate, endDate: endDate);
  }

  @override
  WalletStatisticsProvider getProviderOverride(
    covariant WalletStatisticsProvider provider,
  ) {
    return call(startDate: provider.startDate, endDate: provider.endDate);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'walletStatisticsProvider';
}

/// مزود إحصائيات المحفظة
///
/// Copied from [walletStatistics].
class WalletStatisticsProvider
    extends AutoDisposeFutureProvider<WalletStatistics> {
  /// مزود إحصائيات المحفظة
  ///
  /// Copied from [walletStatistics].
  WalletStatisticsProvider({DateTime? startDate, DateTime? endDate})
    : this._internal(
        (ref) => walletStatistics(
          ref as WalletStatisticsRef,
          startDate: startDate,
          endDate: endDate,
        ),
        from: walletStatisticsProvider,
        name: r'walletStatisticsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$walletStatisticsHash,
        dependencies: WalletStatisticsFamily._dependencies,
        allTransitiveDependencies:
            WalletStatisticsFamily._allTransitiveDependencies,
        startDate: startDate,
        endDate: endDate,
      );

  WalletStatisticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
    required this.endDate,
  }) : super.internal();

  final DateTime? startDate;
  final DateTime? endDate;

  @override
  Override overrideWith(
    FutureOr<WalletStatistics> Function(WalletStatisticsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WalletStatisticsProvider._internal(
        (ref) => create(ref as WalletStatisticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<WalletStatistics> createElement() {
    return _WalletStatisticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WalletStatisticsProvider &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WalletStatisticsRef on AutoDisposeFutureProviderRef<WalletStatistics> {
  /// The parameter `startDate` of this provider.
  DateTime? get startDate;

  /// The parameter `endDate` of this provider.
  DateTime? get endDate;
}

class _WalletStatisticsProviderElement
    extends AutoDisposeFutureProviderElement<WalletStatistics>
    with WalletStatisticsRef {
  _WalletStatisticsProviderElement(super.provider);

  @override
  DateTime? get startDate => (origin as WalletStatisticsProvider).startDate;
  @override
  DateTime? get endDate => (origin as WalletStatisticsProvider).endDate;
}

String _$walletBalanceHash() => r'7dad5906509c5251962402cf62af520afa8c0033';

/// مزود رصيد المحفظة (للوصول السريع)
///
/// Copied from [walletBalance].
@ProviderFor(walletBalance)
final walletBalanceProvider = AutoDisposeFutureProvider<double>.internal(
  walletBalance,
  name: r'walletBalanceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$walletBalanceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WalletBalanceRef = AutoDisposeFutureProviderRef<double>;
String _$walletVerificationLevelHash() =>
    r'323c97c607450ee21579251902c319dbe54ee7f3';

/// مزود حالة التحقق من المحفظة
///
/// Copied from [walletVerificationLevel].
@ProviderFor(walletVerificationLevel)
final walletVerificationLevelProvider =
    AutoDisposeFutureProvider<VerificationLevel>.internal(
      walletVerificationLevel,
      name: r'walletVerificationLevelProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$walletVerificationLevelHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WalletVerificationLevelRef =
    AutoDisposeFutureProviderRef<VerificationLevel>;
String _$transactionExecutorHash() =>
    r'7ada18edfd52b5dd0e9f3ab645386393b2830860';

/// مزود تنفيذ المعاملات
///
/// Copied from [TransactionExecutor].
@ProviderFor(TransactionExecutor)
final transactionExecutorProvider =
    AutoDisposeAsyncNotifierProvider<
      TransactionExecutor,
      TransactionResult?
    >.internal(
      TransactionExecutor.new,
      name: r'transactionExecutorProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$transactionExecutorHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransactionExecutor = AutoDisposeAsyncNotifier<TransactionResult?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
