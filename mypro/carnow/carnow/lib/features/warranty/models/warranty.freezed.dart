// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'warranty.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Warranty {

 String get id; String get productId; String get sellerId; String get buyerId; WarrantyType get type; Duration get duration; DateTime get startDate; DateTime get endDate; WarrantyStatus get status; Map<String, dynamic> get terms; List<String> get coveredIssues; List<String> get excludedIssues; String? get description; String? get certificateNumber; List<WarrantyClaim>? get claims; Map<String, dynamic>? get metadata;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;
/// Create a copy of Warranty
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WarrantyCopyWith<Warranty> get copyWith => _$WarrantyCopyWithImpl<Warranty>(this as Warranty, _$identity);

  /// Serializes this Warranty to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Warranty&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.type, type) || other.type == type)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.terms, terms)&&const DeepCollectionEquality().equals(other.coveredIssues, coveredIssues)&&const DeepCollectionEquality().equals(other.excludedIssues, excludedIssues)&&(identical(other.description, description) || other.description == description)&&(identical(other.certificateNumber, certificateNumber) || other.certificateNumber == certificateNumber)&&const DeepCollectionEquality().equals(other.claims, claims)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,sellerId,buyerId,type,duration,startDate,endDate,status,const DeepCollectionEquality().hash(terms),const DeepCollectionEquality().hash(coveredIssues),const DeepCollectionEquality().hash(excludedIssues),description,certificateNumber,const DeepCollectionEquality().hash(claims),const DeepCollectionEquality().hash(metadata),createdAt,updatedAt);

@override
String toString() {
  return 'Warranty(id: $id, productId: $productId, sellerId: $sellerId, buyerId: $buyerId, type: $type, duration: $duration, startDate: $startDate, endDate: $endDate, status: $status, terms: $terms, coveredIssues: $coveredIssues, excludedIssues: $excludedIssues, description: $description, certificateNumber: $certificateNumber, claims: $claims, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $WarrantyCopyWith<$Res>  {
  factory $WarrantyCopyWith(Warranty value, $Res Function(Warranty) _then) = _$WarrantyCopyWithImpl;
@useResult
$Res call({
 String id, String productId, String sellerId, String buyerId, WarrantyType type, Duration duration, DateTime startDate, DateTime endDate, WarrantyStatus status, Map<String, dynamic> terms, List<String> coveredIssues, List<String> excludedIssues, String? description, String? certificateNumber, List<WarrantyClaim>? claims, Map<String, dynamic>? metadata,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class _$WarrantyCopyWithImpl<$Res>
    implements $WarrantyCopyWith<$Res> {
  _$WarrantyCopyWithImpl(this._self, this._then);

  final Warranty _self;
  final $Res Function(Warranty) _then;

/// Create a copy of Warranty
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? productId = null,Object? sellerId = null,Object? buyerId = null,Object? type = null,Object? duration = null,Object? startDate = null,Object? endDate = null,Object? status = null,Object? terms = null,Object? coveredIssues = null,Object? excludedIssues = null,Object? description = freezed,Object? certificateNumber = freezed,Object? claims = freezed,Object? metadata = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as WarrantyType,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as Duration,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WarrantyStatus,terms: null == terms ? _self.terms : terms // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,coveredIssues: null == coveredIssues ? _self.coveredIssues : coveredIssues // ignore: cast_nullable_to_non_nullable
as List<String>,excludedIssues: null == excludedIssues ? _self.excludedIssues : excludedIssues // ignore: cast_nullable_to_non_nullable
as List<String>,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,certificateNumber: freezed == certificateNumber ? _self.certificateNumber : certificateNumber // ignore: cast_nullable_to_non_nullable
as String?,claims: freezed == claims ? _self.claims : claims // ignore: cast_nullable_to_non_nullable
as List<WarrantyClaim>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [Warranty].
extension WarrantyPatterns on Warranty {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Warranty value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Warranty() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Warranty value)  $default,){
final _that = this;
switch (_that) {
case _Warranty():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Warranty value)?  $default,){
final _that = this;
switch (_that) {
case _Warranty() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String productId,  String sellerId,  String buyerId,  WarrantyType type,  Duration duration,  DateTime startDate,  DateTime endDate,  WarrantyStatus status,  Map<String, dynamic> terms,  List<String> coveredIssues,  List<String> excludedIssues,  String? description,  String? certificateNumber,  List<WarrantyClaim>? claims,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Warranty() when $default != null:
return $default(_that.id,_that.productId,_that.sellerId,_that.buyerId,_that.type,_that.duration,_that.startDate,_that.endDate,_that.status,_that.terms,_that.coveredIssues,_that.excludedIssues,_that.description,_that.certificateNumber,_that.claims,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String productId,  String sellerId,  String buyerId,  WarrantyType type,  Duration duration,  DateTime startDate,  DateTime endDate,  WarrantyStatus status,  Map<String, dynamic> terms,  List<String> coveredIssues,  List<String> excludedIssues,  String? description,  String? certificateNumber,  List<WarrantyClaim>? claims,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _Warranty():
return $default(_that.id,_that.productId,_that.sellerId,_that.buyerId,_that.type,_that.duration,_that.startDate,_that.endDate,_that.status,_that.terms,_that.coveredIssues,_that.excludedIssues,_that.description,_that.certificateNumber,_that.claims,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String productId,  String sellerId,  String buyerId,  WarrantyType type,  Duration duration,  DateTime startDate,  DateTime endDate,  WarrantyStatus status,  Map<String, dynamic> terms,  List<String> coveredIssues,  List<String> excludedIssues,  String? description,  String? certificateNumber,  List<WarrantyClaim>? claims,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _Warranty() when $default != null:
return $default(_that.id,_that.productId,_that.sellerId,_that.buyerId,_that.type,_that.duration,_that.startDate,_that.endDate,_that.status,_that.terms,_that.coveredIssues,_that.excludedIssues,_that.description,_that.certificateNumber,_that.claims,_that.metadata,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Warranty implements Warranty {
  const _Warranty({required this.id, required this.productId, required this.sellerId, required this.buyerId, required this.type, required this.duration, required this.startDate, required this.endDate, required this.status, required final  Map<String, dynamic> terms, required final  List<String> coveredIssues, required final  List<String> excludedIssues, this.description, this.certificateNumber, final  List<WarrantyClaim>? claims, final  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt}): _terms = terms,_coveredIssues = coveredIssues,_excludedIssues = excludedIssues,_claims = claims,_metadata = metadata;
  factory _Warranty.fromJson(Map<String, dynamic> json) => _$WarrantyFromJson(json);

@override final  String id;
@override final  String productId;
@override final  String sellerId;
@override final  String buyerId;
@override final  WarrantyType type;
@override final  Duration duration;
@override final  DateTime startDate;
@override final  DateTime endDate;
@override final  WarrantyStatus status;
 final  Map<String, dynamic> _terms;
@override Map<String, dynamic> get terms {
  if (_terms is EqualUnmodifiableMapView) return _terms;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_terms);
}

 final  List<String> _coveredIssues;
@override List<String> get coveredIssues {
  if (_coveredIssues is EqualUnmodifiableListView) return _coveredIssues;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_coveredIssues);
}

 final  List<String> _excludedIssues;
@override List<String> get excludedIssues {
  if (_excludedIssues is EqualUnmodifiableListView) return _excludedIssues;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_excludedIssues);
}

@override final  String? description;
@override final  String? certificateNumber;
 final  List<WarrantyClaim>? _claims;
@override List<WarrantyClaim>? get claims {
  final value = _claims;
  if (value == null) return null;
  if (_claims is EqualUnmodifiableListView) return _claims;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;

/// Create a copy of Warranty
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WarrantyCopyWith<_Warranty> get copyWith => __$WarrantyCopyWithImpl<_Warranty>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WarrantyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Warranty&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.type, type) || other.type == type)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._terms, _terms)&&const DeepCollectionEquality().equals(other._coveredIssues, _coveredIssues)&&const DeepCollectionEquality().equals(other._excludedIssues, _excludedIssues)&&(identical(other.description, description) || other.description == description)&&(identical(other.certificateNumber, certificateNumber) || other.certificateNumber == certificateNumber)&&const DeepCollectionEquality().equals(other._claims, _claims)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,sellerId,buyerId,type,duration,startDate,endDate,status,const DeepCollectionEquality().hash(_terms),const DeepCollectionEquality().hash(_coveredIssues),const DeepCollectionEquality().hash(_excludedIssues),description,certificateNumber,const DeepCollectionEquality().hash(_claims),const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt);

@override
String toString() {
  return 'Warranty(id: $id, productId: $productId, sellerId: $sellerId, buyerId: $buyerId, type: $type, duration: $duration, startDate: $startDate, endDate: $endDate, status: $status, terms: $terms, coveredIssues: $coveredIssues, excludedIssues: $excludedIssues, description: $description, certificateNumber: $certificateNumber, claims: $claims, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$WarrantyCopyWith<$Res> implements $WarrantyCopyWith<$Res> {
  factory _$WarrantyCopyWith(_Warranty value, $Res Function(_Warranty) _then) = __$WarrantyCopyWithImpl;
@override @useResult
$Res call({
 String id, String productId, String sellerId, String buyerId, WarrantyType type, Duration duration, DateTime startDate, DateTime endDate, WarrantyStatus status, Map<String, dynamic> terms, List<String> coveredIssues, List<String> excludedIssues, String? description, String? certificateNumber, List<WarrantyClaim>? claims, Map<String, dynamic>? metadata,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class __$WarrantyCopyWithImpl<$Res>
    implements _$WarrantyCopyWith<$Res> {
  __$WarrantyCopyWithImpl(this._self, this._then);

  final _Warranty _self;
  final $Res Function(_Warranty) _then;

/// Create a copy of Warranty
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? productId = null,Object? sellerId = null,Object? buyerId = null,Object? type = null,Object? duration = null,Object? startDate = null,Object? endDate = null,Object? status = null,Object? terms = null,Object? coveredIssues = null,Object? excludedIssues = null,Object? description = freezed,Object? certificateNumber = freezed,Object? claims = freezed,Object? metadata = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_Warranty(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as WarrantyType,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as Duration,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WarrantyStatus,terms: null == terms ? _self._terms : terms // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,coveredIssues: null == coveredIssues ? _self._coveredIssues : coveredIssues // ignore: cast_nullable_to_non_nullable
as List<String>,excludedIssues: null == excludedIssues ? _self._excludedIssues : excludedIssues // ignore: cast_nullable_to_non_nullable
as List<String>,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,certificateNumber: freezed == certificateNumber ? _self.certificateNumber : certificateNumber // ignore: cast_nullable_to_non_nullable
as String?,claims: freezed == claims ? _self._claims : claims // ignore: cast_nullable_to_non_nullable
as List<WarrantyClaim>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$WarrantyClaim {

 String get id; String get warrantyId; String get claimantId; String get issueDescription; WarrantyClaimStatus get status; DateTime get submittedAt; DateTime? get resolvedAt; String? get resolution; List<String>? get supportingDocuments; String? get technicianNotes; double? get estimatedCost; double? get actualCost;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;
/// Create a copy of WarrantyClaim
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WarrantyClaimCopyWith<WarrantyClaim> get copyWith => _$WarrantyClaimCopyWithImpl<WarrantyClaim>(this as WarrantyClaim, _$identity);

  /// Serializes this WarrantyClaim to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WarrantyClaim&&(identical(other.id, id) || other.id == id)&&(identical(other.warrantyId, warrantyId) || other.warrantyId == warrantyId)&&(identical(other.claimantId, claimantId) || other.claimantId == claimantId)&&(identical(other.issueDescription, issueDescription) || other.issueDescription == issueDescription)&&(identical(other.status, status) || other.status == status)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&(identical(other.resolution, resolution) || other.resolution == resolution)&&const DeepCollectionEquality().equals(other.supportingDocuments, supportingDocuments)&&(identical(other.technicianNotes, technicianNotes) || other.technicianNotes == technicianNotes)&&(identical(other.estimatedCost, estimatedCost) || other.estimatedCost == estimatedCost)&&(identical(other.actualCost, actualCost) || other.actualCost == actualCost)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,warrantyId,claimantId,issueDescription,status,submittedAt,resolvedAt,resolution,const DeepCollectionEquality().hash(supportingDocuments),technicianNotes,estimatedCost,actualCost,createdAt,updatedAt);

@override
String toString() {
  return 'WarrantyClaim(id: $id, warrantyId: $warrantyId, claimantId: $claimantId, issueDescription: $issueDescription, status: $status, submittedAt: $submittedAt, resolvedAt: $resolvedAt, resolution: $resolution, supportingDocuments: $supportingDocuments, technicianNotes: $technicianNotes, estimatedCost: $estimatedCost, actualCost: $actualCost, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $WarrantyClaimCopyWith<$Res>  {
  factory $WarrantyClaimCopyWith(WarrantyClaim value, $Res Function(WarrantyClaim) _then) = _$WarrantyClaimCopyWithImpl;
@useResult
$Res call({
 String id, String warrantyId, String claimantId, String issueDescription, WarrantyClaimStatus status, DateTime submittedAt, DateTime? resolvedAt, String? resolution, List<String>? supportingDocuments, String? technicianNotes, double? estimatedCost, double? actualCost,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class _$WarrantyClaimCopyWithImpl<$Res>
    implements $WarrantyClaimCopyWith<$Res> {
  _$WarrantyClaimCopyWithImpl(this._self, this._then);

  final WarrantyClaim _self;
  final $Res Function(WarrantyClaim) _then;

/// Create a copy of WarrantyClaim
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? warrantyId = null,Object? claimantId = null,Object? issueDescription = null,Object? status = null,Object? submittedAt = null,Object? resolvedAt = freezed,Object? resolution = freezed,Object? supportingDocuments = freezed,Object? technicianNotes = freezed,Object? estimatedCost = freezed,Object? actualCost = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,warrantyId: null == warrantyId ? _self.warrantyId : warrantyId // ignore: cast_nullable_to_non_nullable
as String,claimantId: null == claimantId ? _self.claimantId : claimantId // ignore: cast_nullable_to_non_nullable
as String,issueDescription: null == issueDescription ? _self.issueDescription : issueDescription // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WarrantyClaimStatus,submittedAt: null == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime,resolvedAt: freezed == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,resolution: freezed == resolution ? _self.resolution : resolution // ignore: cast_nullable_to_non_nullable
as String?,supportingDocuments: freezed == supportingDocuments ? _self.supportingDocuments : supportingDocuments // ignore: cast_nullable_to_non_nullable
as List<String>?,technicianNotes: freezed == technicianNotes ? _self.technicianNotes : technicianNotes // ignore: cast_nullable_to_non_nullable
as String?,estimatedCost: freezed == estimatedCost ? _self.estimatedCost : estimatedCost // ignore: cast_nullable_to_non_nullable
as double?,actualCost: freezed == actualCost ? _self.actualCost : actualCost // ignore: cast_nullable_to_non_nullable
as double?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [WarrantyClaim].
extension WarrantyClaimPatterns on WarrantyClaim {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WarrantyClaim value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WarrantyClaim() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WarrantyClaim value)  $default,){
final _that = this;
switch (_that) {
case _WarrantyClaim():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WarrantyClaim value)?  $default,){
final _that = this;
switch (_that) {
case _WarrantyClaim() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String warrantyId,  String claimantId,  String issueDescription,  WarrantyClaimStatus status,  DateTime submittedAt,  DateTime? resolvedAt,  String? resolution,  List<String>? supportingDocuments,  String? technicianNotes,  double? estimatedCost,  double? actualCost, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WarrantyClaim() when $default != null:
return $default(_that.id,_that.warrantyId,_that.claimantId,_that.issueDescription,_that.status,_that.submittedAt,_that.resolvedAt,_that.resolution,_that.supportingDocuments,_that.technicianNotes,_that.estimatedCost,_that.actualCost,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String warrantyId,  String claimantId,  String issueDescription,  WarrantyClaimStatus status,  DateTime submittedAt,  DateTime? resolvedAt,  String? resolution,  List<String>? supportingDocuments,  String? technicianNotes,  double? estimatedCost,  double? actualCost, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _WarrantyClaim():
return $default(_that.id,_that.warrantyId,_that.claimantId,_that.issueDescription,_that.status,_that.submittedAt,_that.resolvedAt,_that.resolution,_that.supportingDocuments,_that.technicianNotes,_that.estimatedCost,_that.actualCost,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String warrantyId,  String claimantId,  String issueDescription,  WarrantyClaimStatus status,  DateTime submittedAt,  DateTime? resolvedAt,  String? resolution,  List<String>? supportingDocuments,  String? technicianNotes,  double? estimatedCost,  double? actualCost, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _WarrantyClaim() when $default != null:
return $default(_that.id,_that.warrantyId,_that.claimantId,_that.issueDescription,_that.status,_that.submittedAt,_that.resolvedAt,_that.resolution,_that.supportingDocuments,_that.technicianNotes,_that.estimatedCost,_that.actualCost,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WarrantyClaim implements WarrantyClaim {
  const _WarrantyClaim({required this.id, required this.warrantyId, required this.claimantId, required this.issueDescription, required this.status, required this.submittedAt, this.resolvedAt, this.resolution, final  List<String>? supportingDocuments, this.technicianNotes, this.estimatedCost, this.actualCost, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt}): _supportingDocuments = supportingDocuments;
  factory _WarrantyClaim.fromJson(Map<String, dynamic> json) => _$WarrantyClaimFromJson(json);

@override final  String id;
@override final  String warrantyId;
@override final  String claimantId;
@override final  String issueDescription;
@override final  WarrantyClaimStatus status;
@override final  DateTime submittedAt;
@override final  DateTime? resolvedAt;
@override final  String? resolution;
 final  List<String>? _supportingDocuments;
@override List<String>? get supportingDocuments {
  final value = _supportingDocuments;
  if (value == null) return null;
  if (_supportingDocuments is EqualUnmodifiableListView) return _supportingDocuments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? technicianNotes;
@override final  double? estimatedCost;
@override final  double? actualCost;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;

/// Create a copy of WarrantyClaim
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WarrantyClaimCopyWith<_WarrantyClaim> get copyWith => __$WarrantyClaimCopyWithImpl<_WarrantyClaim>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WarrantyClaimToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WarrantyClaim&&(identical(other.id, id) || other.id == id)&&(identical(other.warrantyId, warrantyId) || other.warrantyId == warrantyId)&&(identical(other.claimantId, claimantId) || other.claimantId == claimantId)&&(identical(other.issueDescription, issueDescription) || other.issueDescription == issueDescription)&&(identical(other.status, status) || other.status == status)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&(identical(other.resolution, resolution) || other.resolution == resolution)&&const DeepCollectionEquality().equals(other._supportingDocuments, _supportingDocuments)&&(identical(other.technicianNotes, technicianNotes) || other.technicianNotes == technicianNotes)&&(identical(other.estimatedCost, estimatedCost) || other.estimatedCost == estimatedCost)&&(identical(other.actualCost, actualCost) || other.actualCost == actualCost)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,warrantyId,claimantId,issueDescription,status,submittedAt,resolvedAt,resolution,const DeepCollectionEquality().hash(_supportingDocuments),technicianNotes,estimatedCost,actualCost,createdAt,updatedAt);

@override
String toString() {
  return 'WarrantyClaim(id: $id, warrantyId: $warrantyId, claimantId: $claimantId, issueDescription: $issueDescription, status: $status, submittedAt: $submittedAt, resolvedAt: $resolvedAt, resolution: $resolution, supportingDocuments: $supportingDocuments, technicianNotes: $technicianNotes, estimatedCost: $estimatedCost, actualCost: $actualCost, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$WarrantyClaimCopyWith<$Res> implements $WarrantyClaimCopyWith<$Res> {
  factory _$WarrantyClaimCopyWith(_WarrantyClaim value, $Res Function(_WarrantyClaim) _then) = __$WarrantyClaimCopyWithImpl;
@override @useResult
$Res call({
 String id, String warrantyId, String claimantId, String issueDescription, WarrantyClaimStatus status, DateTime submittedAt, DateTime? resolvedAt, String? resolution, List<String>? supportingDocuments, String? technicianNotes, double? estimatedCost, double? actualCost,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class __$WarrantyClaimCopyWithImpl<$Res>
    implements _$WarrantyClaimCopyWith<$Res> {
  __$WarrantyClaimCopyWithImpl(this._self, this._then);

  final _WarrantyClaim _self;
  final $Res Function(_WarrantyClaim) _then;

/// Create a copy of WarrantyClaim
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? warrantyId = null,Object? claimantId = null,Object? issueDescription = null,Object? status = null,Object? submittedAt = null,Object? resolvedAt = freezed,Object? resolution = freezed,Object? supportingDocuments = freezed,Object? technicianNotes = freezed,Object? estimatedCost = freezed,Object? actualCost = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_WarrantyClaim(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,warrantyId: null == warrantyId ? _self.warrantyId : warrantyId // ignore: cast_nullable_to_non_nullable
as String,claimantId: null == claimantId ? _self.claimantId : claimantId // ignore: cast_nullable_to_non_nullable
as String,issueDescription: null == issueDescription ? _self.issueDescription : issueDescription // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WarrantyClaimStatus,submittedAt: null == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime,resolvedAt: freezed == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,resolution: freezed == resolution ? _self.resolution : resolution // ignore: cast_nullable_to_non_nullable
as String?,supportingDocuments: freezed == supportingDocuments ? _self._supportingDocuments : supportingDocuments // ignore: cast_nullable_to_non_nullable
as List<String>?,technicianNotes: freezed == technicianNotes ? _self.technicianNotes : technicianNotes // ignore: cast_nullable_to_non_nullable
as String?,estimatedCost: freezed == estimatedCost ? _self.estimatedCost : estimatedCost // ignore: cast_nullable_to_non_nullable
as double?,actualCost: freezed == actualCost ? _self.actualCost : actualCost // ignore: cast_nullable_to_non_nullable
as double?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
