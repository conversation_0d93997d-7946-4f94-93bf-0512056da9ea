// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unified_order_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OrderItem implements DiagnosticableTreeMixin {

 String get id; String get productId; String get productName; String get productSku; String? get productImageUrl; int get quantity; double get unitPrice; double get totalPrice; String? get notes; Map<String, dynamic>? get specifications;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of OrderItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderItemCopyWith<OrderItem> get copyWith => _$OrderItemCopyWithImpl<OrderItem>(this as OrderItem, _$identity);

  /// Serializes this OrderItem to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderItem'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('productId', productId))..add(DiagnosticsProperty('productName', productName))..add(DiagnosticsProperty('productSku', productSku))..add(DiagnosticsProperty('productImageUrl', productImageUrl))..add(DiagnosticsProperty('quantity', quantity))..add(DiagnosticsProperty('unitPrice', unitPrice))..add(DiagnosticsProperty('totalPrice', totalPrice))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('specifications', specifications))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderItem&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productSku, productSku) || other.productSku == productSku)&&(identical(other.productImageUrl, productImageUrl) || other.productImageUrl == productImageUrl)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.unitPrice, unitPrice) || other.unitPrice == unitPrice)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.notes, notes) || other.notes == notes)&&const DeepCollectionEquality().equals(other.specifications, specifications)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,productName,productSku,productImageUrl,quantity,unitPrice,totalPrice,notes,const DeepCollectionEquality().hash(specifications),createdAt,updatedAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderItem(id: $id, productId: $productId, productName: $productName, productSku: $productSku, productImageUrl: $productImageUrl, quantity: $quantity, unitPrice: $unitPrice, totalPrice: $totalPrice, notes: $notes, specifications: $specifications, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $OrderItemCopyWith<$Res>  {
  factory $OrderItemCopyWith(OrderItem value, $Res Function(OrderItem) _then) = _$OrderItemCopyWithImpl;
@useResult
$Res call({
 String id, String productId, String productName, String productSku, String? productImageUrl, int quantity, double unitPrice, double totalPrice, String? notes, Map<String, dynamic>? specifications,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$OrderItemCopyWithImpl<$Res>
    implements $OrderItemCopyWith<$Res> {
  _$OrderItemCopyWithImpl(this._self, this._then);

  final OrderItem _self;
  final $Res Function(OrderItem) _then;

/// Create a copy of OrderItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? productId = null,Object? productName = null,Object? productSku = null,Object? productImageUrl = freezed,Object? quantity = null,Object? unitPrice = null,Object? totalPrice = null,Object? notes = freezed,Object? specifications = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productSku: null == productSku ? _self.productSku : productSku // ignore: cast_nullable_to_non_nullable
as String,productImageUrl: freezed == productImageUrl ? _self.productImageUrl : productImageUrl // ignore: cast_nullable_to_non_nullable
as String?,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,unitPrice: null == unitPrice ? _self.unitPrice : unitPrice // ignore: cast_nullable_to_non_nullable
as double,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,specifications: freezed == specifications ? _self.specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderItem].
extension OrderItemPatterns on OrderItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderItem value)  $default,){
final _that = this;
switch (_that) {
case _OrderItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderItem value)?  $default,){
final _that = this;
switch (_that) {
case _OrderItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String productId,  String productName,  String productSku,  String? productImageUrl,  int quantity,  double unitPrice,  double totalPrice,  String? notes,  Map<String, dynamic>? specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderItem() when $default != null:
return $default(_that.id,_that.productId,_that.productName,_that.productSku,_that.productImageUrl,_that.quantity,_that.unitPrice,_that.totalPrice,_that.notes,_that.specifications,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String productId,  String productName,  String productSku,  String? productImageUrl,  int quantity,  double unitPrice,  double totalPrice,  String? notes,  Map<String, dynamic>? specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _OrderItem():
return $default(_that.id,_that.productId,_that.productName,_that.productSku,_that.productImageUrl,_that.quantity,_that.unitPrice,_that.totalPrice,_that.notes,_that.specifications,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String productId,  String productName,  String productSku,  String? productImageUrl,  int quantity,  double unitPrice,  double totalPrice,  String? notes,  Map<String, dynamic>? specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _OrderItem() when $default != null:
return $default(_that.id,_that.productId,_that.productName,_that.productSku,_that.productImageUrl,_that.quantity,_that.unitPrice,_that.totalPrice,_that.notes,_that.specifications,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _OrderItem with DiagnosticableTreeMixin implements OrderItem {
  const _OrderItem({required this.id, required this.productId, required this.productName, required this.productSku, this.productImageUrl, required this.quantity, required this.unitPrice, required this.totalPrice, this.notes, final  Map<String, dynamic>? specifications, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _specifications = specifications;
  factory _OrderItem.fromJson(Map<String, dynamic> json) => _$OrderItemFromJson(json);

@override final  String id;
@override final  String productId;
@override final  String productName;
@override final  String productSku;
@override final  String? productImageUrl;
@override final  int quantity;
@override final  double unitPrice;
@override final  double totalPrice;
@override final  String? notes;
 final  Map<String, dynamic>? _specifications;
@override Map<String, dynamic>? get specifications {
  final value = _specifications;
  if (value == null) return null;
  if (_specifications is EqualUnmodifiableMapView) return _specifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of OrderItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderItemCopyWith<_OrderItem> get copyWith => __$OrderItemCopyWithImpl<_OrderItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderItemToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderItem'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('productId', productId))..add(DiagnosticsProperty('productName', productName))..add(DiagnosticsProperty('productSku', productSku))..add(DiagnosticsProperty('productImageUrl', productImageUrl))..add(DiagnosticsProperty('quantity', quantity))..add(DiagnosticsProperty('unitPrice', unitPrice))..add(DiagnosticsProperty('totalPrice', totalPrice))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('specifications', specifications))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderItem&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productSku, productSku) || other.productSku == productSku)&&(identical(other.productImageUrl, productImageUrl) || other.productImageUrl == productImageUrl)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.unitPrice, unitPrice) || other.unitPrice == unitPrice)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.notes, notes) || other.notes == notes)&&const DeepCollectionEquality().equals(other._specifications, _specifications)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,productName,productSku,productImageUrl,quantity,unitPrice,totalPrice,notes,const DeepCollectionEquality().hash(_specifications),createdAt,updatedAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderItem(id: $id, productId: $productId, productName: $productName, productSku: $productSku, productImageUrl: $productImageUrl, quantity: $quantity, unitPrice: $unitPrice, totalPrice: $totalPrice, notes: $notes, specifications: $specifications, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$OrderItemCopyWith<$Res> implements $OrderItemCopyWith<$Res> {
  factory _$OrderItemCopyWith(_OrderItem value, $Res Function(_OrderItem) _then) = __$OrderItemCopyWithImpl;
@override @useResult
$Res call({
 String id, String productId, String productName, String productSku, String? productImageUrl, int quantity, double unitPrice, double totalPrice, String? notes, Map<String, dynamic>? specifications,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$OrderItemCopyWithImpl<$Res>
    implements _$OrderItemCopyWith<$Res> {
  __$OrderItemCopyWithImpl(this._self, this._then);

  final _OrderItem _self;
  final $Res Function(_OrderItem) _then;

/// Create a copy of OrderItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? productId = null,Object? productName = null,Object? productSku = null,Object? productImageUrl = freezed,Object? quantity = null,Object? unitPrice = null,Object? totalPrice = null,Object? notes = freezed,Object? specifications = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_OrderItem(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productSku: null == productSku ? _self.productSku : productSku // ignore: cast_nullable_to_non_nullable
as String,productImageUrl: freezed == productImageUrl ? _self.productImageUrl : productImageUrl // ignore: cast_nullable_to_non_nullable
as String?,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,unitPrice: null == unitPrice ? _self.unitPrice : unitPrice // ignore: cast_nullable_to_non_nullable
as double,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,specifications: freezed == specifications ? _self._specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$ShippingAddress implements DiagnosticableTreeMixin {

 String get fullName; String get phoneNumber; String get addressLine1; String? get addressLine2; String get city; String get state; String get postalCode; String get country; String? get specialInstructions; bool? get isDefault;
/// Create a copy of ShippingAddress
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ShippingAddressCopyWith<ShippingAddress> get copyWith => _$ShippingAddressCopyWithImpl<ShippingAddress>(this as ShippingAddress, _$identity);

  /// Serializes this ShippingAddress to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ShippingAddress'))
    ..add(DiagnosticsProperty('fullName', fullName))..add(DiagnosticsProperty('phoneNumber', phoneNumber))..add(DiagnosticsProperty('addressLine1', addressLine1))..add(DiagnosticsProperty('addressLine2', addressLine2))..add(DiagnosticsProperty('city', city))..add(DiagnosticsProperty('state', state))..add(DiagnosticsProperty('postalCode', postalCode))..add(DiagnosticsProperty('country', country))..add(DiagnosticsProperty('specialInstructions', specialInstructions))..add(DiagnosticsProperty('isDefault', isDefault));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ShippingAddress&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.addressLine1, addressLine1) || other.addressLine1 == addressLine1)&&(identical(other.addressLine2, addressLine2) || other.addressLine2 == addressLine2)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.postalCode, postalCode) || other.postalCode == postalCode)&&(identical(other.country, country) || other.country == country)&&(identical(other.specialInstructions, specialInstructions) || other.specialInstructions == specialInstructions)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,fullName,phoneNumber,addressLine1,addressLine2,city,state,postalCode,country,specialInstructions,isDefault);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ShippingAddress(fullName: $fullName, phoneNumber: $phoneNumber, addressLine1: $addressLine1, addressLine2: $addressLine2, city: $city, state: $state, postalCode: $postalCode, country: $country, specialInstructions: $specialInstructions, isDefault: $isDefault)';
}


}

/// @nodoc
abstract mixin class $ShippingAddressCopyWith<$Res>  {
  factory $ShippingAddressCopyWith(ShippingAddress value, $Res Function(ShippingAddress) _then) = _$ShippingAddressCopyWithImpl;
@useResult
$Res call({
 String fullName, String phoneNumber, String addressLine1, String? addressLine2, String city, String state, String postalCode, String country, String? specialInstructions, bool? isDefault
});




}
/// @nodoc
class _$ShippingAddressCopyWithImpl<$Res>
    implements $ShippingAddressCopyWith<$Res> {
  _$ShippingAddressCopyWithImpl(this._self, this._then);

  final ShippingAddress _self;
  final $Res Function(ShippingAddress) _then;

/// Create a copy of ShippingAddress
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? fullName = null,Object? phoneNumber = null,Object? addressLine1 = null,Object? addressLine2 = freezed,Object? city = null,Object? state = null,Object? postalCode = null,Object? country = null,Object? specialInstructions = freezed,Object? isDefault = freezed,}) {
  return _then(_self.copyWith(
fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,addressLine1: null == addressLine1 ? _self.addressLine1 : addressLine1 // ignore: cast_nullable_to_non_nullable
as String,addressLine2: freezed == addressLine2 ? _self.addressLine2 : addressLine2 // ignore: cast_nullable_to_non_nullable
as String?,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,postalCode: null == postalCode ? _self.postalCode : postalCode // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,specialInstructions: freezed == specialInstructions ? _self.specialInstructions : specialInstructions // ignore: cast_nullable_to_non_nullable
as String?,isDefault: freezed == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

}


/// Adds pattern-matching-related methods to [ShippingAddress].
extension ShippingAddressPatterns on ShippingAddress {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ShippingAddress value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ShippingAddress() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ShippingAddress value)  $default,){
final _that = this;
switch (_that) {
case _ShippingAddress():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ShippingAddress value)?  $default,){
final _that = this;
switch (_that) {
case _ShippingAddress() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String fullName,  String phoneNumber,  String addressLine1,  String? addressLine2,  String city,  String state,  String postalCode,  String country,  String? specialInstructions,  bool? isDefault)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ShippingAddress() when $default != null:
return $default(_that.fullName,_that.phoneNumber,_that.addressLine1,_that.addressLine2,_that.city,_that.state,_that.postalCode,_that.country,_that.specialInstructions,_that.isDefault);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String fullName,  String phoneNumber,  String addressLine1,  String? addressLine2,  String city,  String state,  String postalCode,  String country,  String? specialInstructions,  bool? isDefault)  $default,) {final _that = this;
switch (_that) {
case _ShippingAddress():
return $default(_that.fullName,_that.phoneNumber,_that.addressLine1,_that.addressLine2,_that.city,_that.state,_that.postalCode,_that.country,_that.specialInstructions,_that.isDefault);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String fullName,  String phoneNumber,  String addressLine1,  String? addressLine2,  String city,  String state,  String postalCode,  String country,  String? specialInstructions,  bool? isDefault)?  $default,) {final _that = this;
switch (_that) {
case _ShippingAddress() when $default != null:
return $default(_that.fullName,_that.phoneNumber,_that.addressLine1,_that.addressLine2,_that.city,_that.state,_that.postalCode,_that.country,_that.specialInstructions,_that.isDefault);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _ShippingAddress with DiagnosticableTreeMixin implements ShippingAddress {
  const _ShippingAddress({required this.fullName, required this.phoneNumber, required this.addressLine1, this.addressLine2, required this.city, required this.state, required this.postalCode, required this.country, this.specialInstructions, this.isDefault});
  factory _ShippingAddress.fromJson(Map<String, dynamic> json) => _$ShippingAddressFromJson(json);

@override final  String fullName;
@override final  String phoneNumber;
@override final  String addressLine1;
@override final  String? addressLine2;
@override final  String city;
@override final  String state;
@override final  String postalCode;
@override final  String country;
@override final  String? specialInstructions;
@override final  bool? isDefault;

/// Create a copy of ShippingAddress
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShippingAddressCopyWith<_ShippingAddress> get copyWith => __$ShippingAddressCopyWithImpl<_ShippingAddress>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ShippingAddressToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ShippingAddress'))
    ..add(DiagnosticsProperty('fullName', fullName))..add(DiagnosticsProperty('phoneNumber', phoneNumber))..add(DiagnosticsProperty('addressLine1', addressLine1))..add(DiagnosticsProperty('addressLine2', addressLine2))..add(DiagnosticsProperty('city', city))..add(DiagnosticsProperty('state', state))..add(DiagnosticsProperty('postalCode', postalCode))..add(DiagnosticsProperty('country', country))..add(DiagnosticsProperty('specialInstructions', specialInstructions))..add(DiagnosticsProperty('isDefault', isDefault));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShippingAddress&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.addressLine1, addressLine1) || other.addressLine1 == addressLine1)&&(identical(other.addressLine2, addressLine2) || other.addressLine2 == addressLine2)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.postalCode, postalCode) || other.postalCode == postalCode)&&(identical(other.country, country) || other.country == country)&&(identical(other.specialInstructions, specialInstructions) || other.specialInstructions == specialInstructions)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,fullName,phoneNumber,addressLine1,addressLine2,city,state,postalCode,country,specialInstructions,isDefault);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ShippingAddress(fullName: $fullName, phoneNumber: $phoneNumber, addressLine1: $addressLine1, addressLine2: $addressLine2, city: $city, state: $state, postalCode: $postalCode, country: $country, specialInstructions: $specialInstructions, isDefault: $isDefault)';
}


}

/// @nodoc
abstract mixin class _$ShippingAddressCopyWith<$Res> implements $ShippingAddressCopyWith<$Res> {
  factory _$ShippingAddressCopyWith(_ShippingAddress value, $Res Function(_ShippingAddress) _then) = __$ShippingAddressCopyWithImpl;
@override @useResult
$Res call({
 String fullName, String phoneNumber, String addressLine1, String? addressLine2, String city, String state, String postalCode, String country, String? specialInstructions, bool? isDefault
});




}
/// @nodoc
class __$ShippingAddressCopyWithImpl<$Res>
    implements _$ShippingAddressCopyWith<$Res> {
  __$ShippingAddressCopyWithImpl(this._self, this._then);

  final _ShippingAddress _self;
  final $Res Function(_ShippingAddress) _then;

/// Create a copy of ShippingAddress
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? fullName = null,Object? phoneNumber = null,Object? addressLine1 = null,Object? addressLine2 = freezed,Object? city = null,Object? state = null,Object? postalCode = null,Object? country = null,Object? specialInstructions = freezed,Object? isDefault = freezed,}) {
  return _then(_ShippingAddress(
fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,addressLine1: null == addressLine1 ? _self.addressLine1 : addressLine1 // ignore: cast_nullable_to_non_nullable
as String,addressLine2: freezed == addressLine2 ? _self.addressLine2 : addressLine2 // ignore: cast_nullable_to_non_nullable
as String?,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,postalCode: null == postalCode ? _self.postalCode : postalCode // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,specialInstructions: freezed == specialInstructions ? _self.specialInstructions : specialInstructions // ignore: cast_nullable_to_non_nullable
as String?,isDefault: freezed == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}


/// @nodoc
mixin _$OrderStatusHistory implements DiagnosticableTreeMixin {

 OrderStatus get status; DateTime get timestamp; String? get notes; String? get updatedBy;
/// Create a copy of OrderStatusHistory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderStatusHistoryCopyWith<OrderStatusHistory> get copyWith => _$OrderStatusHistoryCopyWithImpl<OrderStatusHistory>(this as OrderStatusHistory, _$identity);

  /// Serializes this OrderStatusHistory to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderStatusHistory'))
    ..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('timestamp', timestamp))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('updatedBy', updatedBy));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderStatusHistory&&(identical(other.status, status) || other.status == status)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.updatedBy, updatedBy) || other.updatedBy == updatedBy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,timestamp,notes,updatedBy);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderStatusHistory(status: $status, timestamp: $timestamp, notes: $notes, updatedBy: $updatedBy)';
}


}

/// @nodoc
abstract mixin class $OrderStatusHistoryCopyWith<$Res>  {
  factory $OrderStatusHistoryCopyWith(OrderStatusHistory value, $Res Function(OrderStatusHistory) _then) = _$OrderStatusHistoryCopyWithImpl;
@useResult
$Res call({
 OrderStatus status, DateTime timestamp, String? notes, String? updatedBy
});




}
/// @nodoc
class _$OrderStatusHistoryCopyWithImpl<$Res>
    implements $OrderStatusHistoryCopyWith<$Res> {
  _$OrderStatusHistoryCopyWithImpl(this._self, this._then);

  final OrderStatusHistory _self;
  final $Res Function(OrderStatusHistory) _then;

/// Create a copy of OrderStatusHistory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? timestamp = null,Object? notes = freezed,Object? updatedBy = freezed,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,updatedBy: freezed == updatedBy ? _self.updatedBy : updatedBy // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderStatusHistory].
extension OrderStatusHistoryPatterns on OrderStatusHistory {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderStatusHistory value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderStatusHistory() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderStatusHistory value)  $default,){
final _that = this;
switch (_that) {
case _OrderStatusHistory():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderStatusHistory value)?  $default,){
final _that = this;
switch (_that) {
case _OrderStatusHistory() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( OrderStatus status,  DateTime timestamp,  String? notes,  String? updatedBy)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderStatusHistory() when $default != null:
return $default(_that.status,_that.timestamp,_that.notes,_that.updatedBy);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( OrderStatus status,  DateTime timestamp,  String? notes,  String? updatedBy)  $default,) {final _that = this;
switch (_that) {
case _OrderStatusHistory():
return $default(_that.status,_that.timestamp,_that.notes,_that.updatedBy);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( OrderStatus status,  DateTime timestamp,  String? notes,  String? updatedBy)?  $default,) {final _that = this;
switch (_that) {
case _OrderStatusHistory() when $default != null:
return $default(_that.status,_that.timestamp,_that.notes,_that.updatedBy);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _OrderStatusHistory with DiagnosticableTreeMixin implements OrderStatusHistory {
  const _OrderStatusHistory({required this.status, required this.timestamp, this.notes, this.updatedBy});
  factory _OrderStatusHistory.fromJson(Map<String, dynamic> json) => _$OrderStatusHistoryFromJson(json);

@override final  OrderStatus status;
@override final  DateTime timestamp;
@override final  String? notes;
@override final  String? updatedBy;

/// Create a copy of OrderStatusHistory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderStatusHistoryCopyWith<_OrderStatusHistory> get copyWith => __$OrderStatusHistoryCopyWithImpl<_OrderStatusHistory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderStatusHistoryToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderStatusHistory'))
    ..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('timestamp', timestamp))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('updatedBy', updatedBy));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderStatusHistory&&(identical(other.status, status) || other.status == status)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.updatedBy, updatedBy) || other.updatedBy == updatedBy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,timestamp,notes,updatedBy);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderStatusHistory(status: $status, timestamp: $timestamp, notes: $notes, updatedBy: $updatedBy)';
}


}

/// @nodoc
abstract mixin class _$OrderStatusHistoryCopyWith<$Res> implements $OrderStatusHistoryCopyWith<$Res> {
  factory _$OrderStatusHistoryCopyWith(_OrderStatusHistory value, $Res Function(_OrderStatusHistory) _then) = __$OrderStatusHistoryCopyWithImpl;
@override @useResult
$Res call({
 OrderStatus status, DateTime timestamp, String? notes, String? updatedBy
});




}
/// @nodoc
class __$OrderStatusHistoryCopyWithImpl<$Res>
    implements _$OrderStatusHistoryCopyWith<$Res> {
  __$OrderStatusHistoryCopyWithImpl(this._self, this._then);

  final _OrderStatusHistory _self;
  final $Res Function(_OrderStatusHistory) _then;

/// Create a copy of OrderStatusHistory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? timestamp = null,Object? notes = freezed,Object? updatedBy = freezed,}) {
  return _then(_OrderStatusHistory(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,updatedBy: freezed == updatedBy ? _self.updatedBy : updatedBy // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$PaymentInfo implements DiagnosticableTreeMixin {

 String get method; String? get transactionId; String? get paymentGateway; double? get paidAmount; String? get currency; DateTime? get paidAt; Map<String, dynamic>? get metadata;
/// Create a copy of PaymentInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentInfoCopyWith<PaymentInfo> get copyWith => _$PaymentInfoCopyWithImpl<PaymentInfo>(this as PaymentInfo, _$identity);

  /// Serializes this PaymentInfo to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'PaymentInfo'))
    ..add(DiagnosticsProperty('method', method))..add(DiagnosticsProperty('transactionId', transactionId))..add(DiagnosticsProperty('paymentGateway', paymentGateway))..add(DiagnosticsProperty('paidAmount', paidAmount))..add(DiagnosticsProperty('currency', currency))..add(DiagnosticsProperty('paidAt', paidAt))..add(DiagnosticsProperty('metadata', metadata));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentInfo&&(identical(other.method, method) || other.method == method)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.paymentGateway, paymentGateway) || other.paymentGateway == paymentGateway)&&(identical(other.paidAmount, paidAmount) || other.paidAmount == paidAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,method,transactionId,paymentGateway,paidAmount,currency,paidAt,const DeepCollectionEquality().hash(metadata));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'PaymentInfo(method: $method, transactionId: $transactionId, paymentGateway: $paymentGateway, paidAmount: $paidAmount, currency: $currency, paidAt: $paidAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $PaymentInfoCopyWith<$Res>  {
  factory $PaymentInfoCopyWith(PaymentInfo value, $Res Function(PaymentInfo) _then) = _$PaymentInfoCopyWithImpl;
@useResult
$Res call({
 String method, String? transactionId, String? paymentGateway, double? paidAmount, String? currency, DateTime? paidAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$PaymentInfoCopyWithImpl<$Res>
    implements $PaymentInfoCopyWith<$Res> {
  _$PaymentInfoCopyWithImpl(this._self, this._then);

  final PaymentInfo _self;
  final $Res Function(PaymentInfo) _then;

/// Create a copy of PaymentInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? method = null,Object? transactionId = freezed,Object? paymentGateway = freezed,Object? paidAmount = freezed,Object? currency = freezed,Object? paidAt = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as String,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,paymentGateway: freezed == paymentGateway ? _self.paymentGateway : paymentGateway // ignore: cast_nullable_to_non_nullable
as String?,paidAmount: freezed == paidAmount ? _self.paidAmount : paidAmount // ignore: cast_nullable_to_non_nullable
as double?,currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentInfo].
extension PaymentInfoPatterns on PaymentInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentInfo value)  $default,){
final _that = this;
switch (_that) {
case _PaymentInfo():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentInfo value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String method,  String? transactionId,  String? paymentGateway,  double? paidAmount,  String? currency,  DateTime? paidAt,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentInfo() when $default != null:
return $default(_that.method,_that.transactionId,_that.paymentGateway,_that.paidAmount,_that.currency,_that.paidAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String method,  String? transactionId,  String? paymentGateway,  double? paidAmount,  String? currency,  DateTime? paidAt,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _PaymentInfo():
return $default(_that.method,_that.transactionId,_that.paymentGateway,_that.paidAmount,_that.currency,_that.paidAt,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String method,  String? transactionId,  String? paymentGateway,  double? paidAmount,  String? currency,  DateTime? paidAt,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _PaymentInfo() when $default != null:
return $default(_that.method,_that.transactionId,_that.paymentGateway,_that.paidAmount,_that.currency,_that.paidAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _PaymentInfo with DiagnosticableTreeMixin implements PaymentInfo {
  const _PaymentInfo({required this.method, this.transactionId, this.paymentGateway, this.paidAmount, this.currency, this.paidAt, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _PaymentInfo.fromJson(Map<String, dynamic> json) => _$PaymentInfoFromJson(json);

@override final  String method;
@override final  String? transactionId;
@override final  String? paymentGateway;
@override final  double? paidAmount;
@override final  String? currency;
@override final  DateTime? paidAt;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of PaymentInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentInfoCopyWith<_PaymentInfo> get copyWith => __$PaymentInfoCopyWithImpl<_PaymentInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentInfoToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'PaymentInfo'))
    ..add(DiagnosticsProperty('method', method))..add(DiagnosticsProperty('transactionId', transactionId))..add(DiagnosticsProperty('paymentGateway', paymentGateway))..add(DiagnosticsProperty('paidAmount', paidAmount))..add(DiagnosticsProperty('currency', currency))..add(DiagnosticsProperty('paidAt', paidAt))..add(DiagnosticsProperty('metadata', metadata));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentInfo&&(identical(other.method, method) || other.method == method)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.paymentGateway, paymentGateway) || other.paymentGateway == paymentGateway)&&(identical(other.paidAmount, paidAmount) || other.paidAmount == paidAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,method,transactionId,paymentGateway,paidAmount,currency,paidAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'PaymentInfo(method: $method, transactionId: $transactionId, paymentGateway: $paymentGateway, paidAmount: $paidAmount, currency: $currency, paidAt: $paidAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$PaymentInfoCopyWith<$Res> implements $PaymentInfoCopyWith<$Res> {
  factory _$PaymentInfoCopyWith(_PaymentInfo value, $Res Function(_PaymentInfo) _then) = __$PaymentInfoCopyWithImpl;
@override @useResult
$Res call({
 String method, String? transactionId, String? paymentGateway, double? paidAmount, String? currency, DateTime? paidAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$PaymentInfoCopyWithImpl<$Res>
    implements _$PaymentInfoCopyWith<$Res> {
  __$PaymentInfoCopyWithImpl(this._self, this._then);

  final _PaymentInfo _self;
  final $Res Function(_PaymentInfo) _then;

/// Create a copy of PaymentInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? method = null,Object? transactionId = freezed,Object? paymentGateway = freezed,Object? paidAmount = freezed,Object? currency = freezed,Object? paidAt = freezed,Object? metadata = freezed,}) {
  return _then(_PaymentInfo(
method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as String,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,paymentGateway: freezed == paymentGateway ? _self.paymentGateway : paymentGateway // ignore: cast_nullable_to_non_nullable
as String?,paidAmount: freezed == paidAmount ? _self.paidAmount : paidAmount // ignore: cast_nullable_to_non_nullable
as double?,currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$UnifiedOrder implements DiagnosticableTreeMixin {

 String get id; String get orderNumber; String get displayName; OrderType get orderType; OrderStatus get status; OrderPriority get priority; String get buyerId; String? get sellerId; String? get buyerName; String? get buyerEmail; String? get sellerName; String? get sellerEmail; List<OrderItem> get items; ShippingAddress get shippingAddress; PaymentInfo? get paymentInfo; double get subtotal; double get taxAmount; double get shippingCost; double get totalAmount; String get currency; String? get trackingNumber; String? get notes; String? get cancellationReason; DateTime? get estimatedDeliveryDate; DateTime? get actualDeliveryDate; List<OrderStatusHistory>? get statusHistory; Map<String, dynamic>? get metadata;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;@JsonKey(includeFromJson: true, includeToJson: false) bool? get isDeleted;
/// Create a copy of UnifiedOrder
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UnifiedOrderCopyWith<UnifiedOrder> get copyWith => _$UnifiedOrderCopyWithImpl<UnifiedOrder>(this as UnifiedOrder, _$identity);

  /// Serializes this UnifiedOrder to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'UnifiedOrder'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('orderNumber', orderNumber))..add(DiagnosticsProperty('displayName', displayName))..add(DiagnosticsProperty('orderType', orderType))..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('priority', priority))..add(DiagnosticsProperty('buyerId', buyerId))..add(DiagnosticsProperty('sellerId', sellerId))..add(DiagnosticsProperty('buyerName', buyerName))..add(DiagnosticsProperty('buyerEmail', buyerEmail))..add(DiagnosticsProperty('sellerName', sellerName))..add(DiagnosticsProperty('sellerEmail', sellerEmail))..add(DiagnosticsProperty('items', items))..add(DiagnosticsProperty('shippingAddress', shippingAddress))..add(DiagnosticsProperty('paymentInfo', paymentInfo))..add(DiagnosticsProperty('subtotal', subtotal))..add(DiagnosticsProperty('taxAmount', taxAmount))..add(DiagnosticsProperty('shippingCost', shippingCost))..add(DiagnosticsProperty('totalAmount', totalAmount))..add(DiagnosticsProperty('currency', currency))..add(DiagnosticsProperty('trackingNumber', trackingNumber))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('cancellationReason', cancellationReason))..add(DiagnosticsProperty('estimatedDeliveryDate', estimatedDeliveryDate))..add(DiagnosticsProperty('actualDeliveryDate', actualDeliveryDate))..add(DiagnosticsProperty('statusHistory', statusHistory))..add(DiagnosticsProperty('metadata', metadata))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt))..add(DiagnosticsProperty('isDeleted', isDeleted));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UnifiedOrder&&(identical(other.id, id) || other.id == id)&&(identical(other.orderNumber, orderNumber) || other.orderNumber == orderNumber)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.orderType, orderType) || other.orderType == orderType)&&(identical(other.status, status) || other.status == status)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.buyerName, buyerName) || other.buyerName == buyerName)&&(identical(other.buyerEmail, buyerEmail) || other.buyerEmail == buyerEmail)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName)&&(identical(other.sellerEmail, sellerEmail) || other.sellerEmail == sellerEmail)&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.paymentInfo, paymentInfo) || other.paymentInfo == paymentInfo)&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.shippingCost, shippingCost) || other.shippingCost == shippingCost)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&(identical(other.estimatedDeliveryDate, estimatedDeliveryDate) || other.estimatedDeliveryDate == estimatedDeliveryDate)&&(identical(other.actualDeliveryDate, actualDeliveryDate) || other.actualDeliveryDate == actualDeliveryDate)&&const DeepCollectionEquality().equals(other.statusHistory, statusHistory)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,orderNumber,displayName,orderType,status,priority,buyerId,sellerId,buyerName,buyerEmail,sellerName,sellerEmail,const DeepCollectionEquality().hash(items),shippingAddress,paymentInfo,subtotal,taxAmount,shippingCost,totalAmount,currency,trackingNumber,notes,cancellationReason,estimatedDeliveryDate,actualDeliveryDate,const DeepCollectionEquality().hash(statusHistory),const DeepCollectionEquality().hash(metadata),createdAt,updatedAt,isDeleted]);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'UnifiedOrder(id: $id, orderNumber: $orderNumber, displayName: $displayName, orderType: $orderType, status: $status, priority: $priority, buyerId: $buyerId, sellerId: $sellerId, buyerName: $buyerName, buyerEmail: $buyerEmail, sellerName: $sellerName, sellerEmail: $sellerEmail, items: $items, shippingAddress: $shippingAddress, paymentInfo: $paymentInfo, subtotal: $subtotal, taxAmount: $taxAmount, shippingCost: $shippingCost, totalAmount: $totalAmount, currency: $currency, trackingNumber: $trackingNumber, notes: $notes, cancellationReason: $cancellationReason, estimatedDeliveryDate: $estimatedDeliveryDate, actualDeliveryDate: $actualDeliveryDate, statusHistory: $statusHistory, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $UnifiedOrderCopyWith<$Res>  {
  factory $UnifiedOrderCopyWith(UnifiedOrder value, $Res Function(UnifiedOrder) _then) = _$UnifiedOrderCopyWithImpl;
@useResult
$Res call({
 String id, String orderNumber, String displayName, OrderType orderType, OrderStatus status, OrderPriority priority, String buyerId, String? sellerId, String? buyerName, String? buyerEmail, String? sellerName, String? sellerEmail, List<OrderItem> items, ShippingAddress shippingAddress, PaymentInfo? paymentInfo, double subtotal, double taxAmount, double shippingCost, double totalAmount, String currency, String? trackingNumber, String? notes, String? cancellationReason, DateTime? estimatedDeliveryDate, DateTime? actualDeliveryDate, List<OrderStatusHistory>? statusHistory, Map<String, dynamic>? metadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,@JsonKey(includeFromJson: true, includeToJson: false) bool? isDeleted
});


$ShippingAddressCopyWith<$Res> get shippingAddress;$PaymentInfoCopyWith<$Res>? get paymentInfo;

}
/// @nodoc
class _$UnifiedOrderCopyWithImpl<$Res>
    implements $UnifiedOrderCopyWith<$Res> {
  _$UnifiedOrderCopyWithImpl(this._self, this._then);

  final UnifiedOrder _self;
  final $Res Function(UnifiedOrder) _then;

/// Create a copy of UnifiedOrder
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? orderNumber = null,Object? displayName = null,Object? orderType = null,Object? status = null,Object? priority = null,Object? buyerId = null,Object? sellerId = freezed,Object? buyerName = freezed,Object? buyerEmail = freezed,Object? sellerName = freezed,Object? sellerEmail = freezed,Object? items = null,Object? shippingAddress = null,Object? paymentInfo = freezed,Object? subtotal = null,Object? taxAmount = null,Object? shippingCost = null,Object? totalAmount = null,Object? currency = null,Object? trackingNumber = freezed,Object? notes = freezed,Object? cancellationReason = freezed,Object? estimatedDeliveryDate = freezed,Object? actualDeliveryDate = freezed,Object? statusHistory = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,orderNumber: null == orderNumber ? _self.orderNumber : orderNumber // ignore: cast_nullable_to_non_nullable
as String,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,orderType: null == orderType ? _self.orderType : orderType // ignore: cast_nullable_to_non_nullable
as OrderType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as OrderPriority,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,buyerName: freezed == buyerName ? _self.buyerName : buyerName // ignore: cast_nullable_to_non_nullable
as String?,buyerEmail: freezed == buyerEmail ? _self.buyerEmail : buyerEmail // ignore: cast_nullable_to_non_nullable
as String?,sellerName: freezed == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String?,sellerEmail: freezed == sellerEmail ? _self.sellerEmail : sellerEmail // ignore: cast_nullable_to_non_nullable
as String?,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<OrderItem>,shippingAddress: null == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as ShippingAddress,paymentInfo: freezed == paymentInfo ? _self.paymentInfo : paymentInfo // ignore: cast_nullable_to_non_nullable
as PaymentInfo?,subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,shippingCost: null == shippingCost ? _self.shippingCost : shippingCost // ignore: cast_nullable_to_non_nullable
as double,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,trackingNumber: freezed == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,estimatedDeliveryDate: freezed == estimatedDeliveryDate ? _self.estimatedDeliveryDate : estimatedDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,actualDeliveryDate: freezed == actualDeliveryDate ? _self.actualDeliveryDate : actualDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,statusHistory: freezed == statusHistory ? _self.statusHistory : statusHistory // ignore: cast_nullable_to_non_nullable
as List<OrderStatusHistory>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: freezed == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}
/// Create a copy of UnifiedOrder
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ShippingAddressCopyWith<$Res> get shippingAddress {
  
  return $ShippingAddressCopyWith<$Res>(_self.shippingAddress, (value) {
    return _then(_self.copyWith(shippingAddress: value));
  });
}/// Create a copy of UnifiedOrder
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentInfoCopyWith<$Res>? get paymentInfo {
    if (_self.paymentInfo == null) {
    return null;
  }

  return $PaymentInfoCopyWith<$Res>(_self.paymentInfo!, (value) {
    return _then(_self.copyWith(paymentInfo: value));
  });
}
}


/// Adds pattern-matching-related methods to [UnifiedOrder].
extension UnifiedOrderPatterns on UnifiedOrder {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UnifiedOrder value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UnifiedOrder() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UnifiedOrder value)  $default,){
final _that = this;
switch (_that) {
case _UnifiedOrder():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UnifiedOrder value)?  $default,){
final _that = this;
switch (_that) {
case _UnifiedOrder() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String orderNumber,  String displayName,  OrderType orderType,  OrderStatus status,  OrderPriority priority,  String buyerId,  String? sellerId,  String? buyerName,  String? buyerEmail,  String? sellerName,  String? sellerEmail,  List<OrderItem> items,  ShippingAddress shippingAddress,  PaymentInfo? paymentInfo,  double subtotal,  double taxAmount,  double shippingCost,  double totalAmount,  String currency,  String? trackingNumber,  String? notes,  String? cancellationReason,  DateTime? estimatedDeliveryDate,  DateTime? actualDeliveryDate,  List<OrderStatusHistory>? statusHistory,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(includeFromJson: true, includeToJson: false)  bool? isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UnifiedOrder() when $default != null:
return $default(_that.id,_that.orderNumber,_that.displayName,_that.orderType,_that.status,_that.priority,_that.buyerId,_that.sellerId,_that.buyerName,_that.buyerEmail,_that.sellerName,_that.sellerEmail,_that.items,_that.shippingAddress,_that.paymentInfo,_that.subtotal,_that.taxAmount,_that.shippingCost,_that.totalAmount,_that.currency,_that.trackingNumber,_that.notes,_that.cancellationReason,_that.estimatedDeliveryDate,_that.actualDeliveryDate,_that.statusHistory,_that.metadata,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String orderNumber,  String displayName,  OrderType orderType,  OrderStatus status,  OrderPriority priority,  String buyerId,  String? sellerId,  String? buyerName,  String? buyerEmail,  String? sellerName,  String? sellerEmail,  List<OrderItem> items,  ShippingAddress shippingAddress,  PaymentInfo? paymentInfo,  double subtotal,  double taxAmount,  double shippingCost,  double totalAmount,  String currency,  String? trackingNumber,  String? notes,  String? cancellationReason,  DateTime? estimatedDeliveryDate,  DateTime? actualDeliveryDate,  List<OrderStatusHistory>? statusHistory,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(includeFromJson: true, includeToJson: false)  bool? isDeleted)  $default,) {final _that = this;
switch (_that) {
case _UnifiedOrder():
return $default(_that.id,_that.orderNumber,_that.displayName,_that.orderType,_that.status,_that.priority,_that.buyerId,_that.sellerId,_that.buyerName,_that.buyerEmail,_that.sellerName,_that.sellerEmail,_that.items,_that.shippingAddress,_that.paymentInfo,_that.subtotal,_that.taxAmount,_that.shippingCost,_that.totalAmount,_that.currency,_that.trackingNumber,_that.notes,_that.cancellationReason,_that.estimatedDeliveryDate,_that.actualDeliveryDate,_that.statusHistory,_that.metadata,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String orderNumber,  String displayName,  OrderType orderType,  OrderStatus status,  OrderPriority priority,  String buyerId,  String? sellerId,  String? buyerName,  String? buyerEmail,  String? sellerName,  String? sellerEmail,  List<OrderItem> items,  ShippingAddress shippingAddress,  PaymentInfo? paymentInfo,  double subtotal,  double taxAmount,  double shippingCost,  double totalAmount,  String currency,  String? trackingNumber,  String? notes,  String? cancellationReason,  DateTime? estimatedDeliveryDate,  DateTime? actualDeliveryDate,  List<OrderStatusHistory>? statusHistory,  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(includeFromJson: true, includeToJson: false)  bool? isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _UnifiedOrder() when $default != null:
return $default(_that.id,_that.orderNumber,_that.displayName,_that.orderType,_that.status,_that.priority,_that.buyerId,_that.sellerId,_that.buyerName,_that.buyerEmail,_that.sellerName,_that.sellerEmail,_that.items,_that.shippingAddress,_that.paymentInfo,_that.subtotal,_that.taxAmount,_that.shippingCost,_that.totalAmount,_that.currency,_that.trackingNumber,_that.notes,_that.cancellationReason,_that.estimatedDeliveryDate,_that.actualDeliveryDate,_that.statusHistory,_that.metadata,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _UnifiedOrder with DiagnosticableTreeMixin implements UnifiedOrder {
  const _UnifiedOrder({required this.id, required this.orderNumber, required this.displayName, required this.orderType, required this.status, required this.priority, required this.buyerId, this.sellerId, this.buyerName, this.buyerEmail, this.sellerName, this.sellerEmail, required final  List<OrderItem> items, required this.shippingAddress, this.paymentInfo, required this.subtotal, required this.taxAmount, required this.shippingCost, required this.totalAmount, required this.currency, this.trackingNumber, this.notes, this.cancellationReason, this.estimatedDeliveryDate, this.actualDeliveryDate, final  List<OrderStatusHistory>? statusHistory, final  Map<String, dynamic>? metadata, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt, @JsonKey(includeFromJson: true, includeToJson: false) this.isDeleted}): _items = items,_statusHistory = statusHistory,_metadata = metadata;
  factory _UnifiedOrder.fromJson(Map<String, dynamic> json) => _$UnifiedOrderFromJson(json);

@override final  String id;
@override final  String orderNumber;
@override final  String displayName;
@override final  OrderType orderType;
@override final  OrderStatus status;
@override final  OrderPriority priority;
@override final  String buyerId;
@override final  String? sellerId;
@override final  String? buyerName;
@override final  String? buyerEmail;
@override final  String? sellerName;
@override final  String? sellerEmail;
 final  List<OrderItem> _items;
@override List<OrderItem> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}

@override final  ShippingAddress shippingAddress;
@override final  PaymentInfo? paymentInfo;
@override final  double subtotal;
@override final  double taxAmount;
@override final  double shippingCost;
@override final  double totalAmount;
@override final  String currency;
@override final  String? trackingNumber;
@override final  String? notes;
@override final  String? cancellationReason;
@override final  DateTime? estimatedDeliveryDate;
@override final  DateTime? actualDeliveryDate;
 final  List<OrderStatusHistory>? _statusHistory;
@override List<OrderStatusHistory>? get statusHistory {
  final value = _statusHistory;
  if (value == null) return null;
  if (_statusHistory is EqualUnmodifiableListView) return _statusHistory;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  bool? isDeleted;

/// Create a copy of UnifiedOrder
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UnifiedOrderCopyWith<_UnifiedOrder> get copyWith => __$UnifiedOrderCopyWithImpl<_UnifiedOrder>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UnifiedOrderToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'UnifiedOrder'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('orderNumber', orderNumber))..add(DiagnosticsProperty('displayName', displayName))..add(DiagnosticsProperty('orderType', orderType))..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('priority', priority))..add(DiagnosticsProperty('buyerId', buyerId))..add(DiagnosticsProperty('sellerId', sellerId))..add(DiagnosticsProperty('buyerName', buyerName))..add(DiagnosticsProperty('buyerEmail', buyerEmail))..add(DiagnosticsProperty('sellerName', sellerName))..add(DiagnosticsProperty('sellerEmail', sellerEmail))..add(DiagnosticsProperty('items', items))..add(DiagnosticsProperty('shippingAddress', shippingAddress))..add(DiagnosticsProperty('paymentInfo', paymentInfo))..add(DiagnosticsProperty('subtotal', subtotal))..add(DiagnosticsProperty('taxAmount', taxAmount))..add(DiagnosticsProperty('shippingCost', shippingCost))..add(DiagnosticsProperty('totalAmount', totalAmount))..add(DiagnosticsProperty('currency', currency))..add(DiagnosticsProperty('trackingNumber', trackingNumber))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('cancellationReason', cancellationReason))..add(DiagnosticsProperty('estimatedDeliveryDate', estimatedDeliveryDate))..add(DiagnosticsProperty('actualDeliveryDate', actualDeliveryDate))..add(DiagnosticsProperty('statusHistory', statusHistory))..add(DiagnosticsProperty('metadata', metadata))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt))..add(DiagnosticsProperty('isDeleted', isDeleted));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UnifiedOrder&&(identical(other.id, id) || other.id == id)&&(identical(other.orderNumber, orderNumber) || other.orderNumber == orderNumber)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.orderType, orderType) || other.orderType == orderType)&&(identical(other.status, status) || other.status == status)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.buyerName, buyerName) || other.buyerName == buyerName)&&(identical(other.buyerEmail, buyerEmail) || other.buyerEmail == buyerEmail)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName)&&(identical(other.sellerEmail, sellerEmail) || other.sellerEmail == sellerEmail)&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.paymentInfo, paymentInfo) || other.paymentInfo == paymentInfo)&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.shippingCost, shippingCost) || other.shippingCost == shippingCost)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&(identical(other.estimatedDeliveryDate, estimatedDeliveryDate) || other.estimatedDeliveryDate == estimatedDeliveryDate)&&(identical(other.actualDeliveryDate, actualDeliveryDate) || other.actualDeliveryDate == actualDeliveryDate)&&const DeepCollectionEquality().equals(other._statusHistory, _statusHistory)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,orderNumber,displayName,orderType,status,priority,buyerId,sellerId,buyerName,buyerEmail,sellerName,sellerEmail,const DeepCollectionEquality().hash(_items),shippingAddress,paymentInfo,subtotal,taxAmount,shippingCost,totalAmount,currency,trackingNumber,notes,cancellationReason,estimatedDeliveryDate,actualDeliveryDate,const DeepCollectionEquality().hash(_statusHistory),const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt,isDeleted]);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'UnifiedOrder(id: $id, orderNumber: $orderNumber, displayName: $displayName, orderType: $orderType, status: $status, priority: $priority, buyerId: $buyerId, sellerId: $sellerId, buyerName: $buyerName, buyerEmail: $buyerEmail, sellerName: $sellerName, sellerEmail: $sellerEmail, items: $items, shippingAddress: $shippingAddress, paymentInfo: $paymentInfo, subtotal: $subtotal, taxAmount: $taxAmount, shippingCost: $shippingCost, totalAmount: $totalAmount, currency: $currency, trackingNumber: $trackingNumber, notes: $notes, cancellationReason: $cancellationReason, estimatedDeliveryDate: $estimatedDeliveryDate, actualDeliveryDate: $actualDeliveryDate, statusHistory: $statusHistory, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$UnifiedOrderCopyWith<$Res> implements $UnifiedOrderCopyWith<$Res> {
  factory _$UnifiedOrderCopyWith(_UnifiedOrder value, $Res Function(_UnifiedOrder) _then) = __$UnifiedOrderCopyWithImpl;
@override @useResult
$Res call({
 String id, String orderNumber, String displayName, OrderType orderType, OrderStatus status, OrderPriority priority, String buyerId, String? sellerId, String? buyerName, String? buyerEmail, String? sellerName, String? sellerEmail, List<OrderItem> items, ShippingAddress shippingAddress, PaymentInfo? paymentInfo, double subtotal, double taxAmount, double shippingCost, double totalAmount, String currency, String? trackingNumber, String? notes, String? cancellationReason, DateTime? estimatedDeliveryDate, DateTime? actualDeliveryDate, List<OrderStatusHistory>? statusHistory, Map<String, dynamic>? metadata,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,@JsonKey(includeFromJson: true, includeToJson: false) bool? isDeleted
});


@override $ShippingAddressCopyWith<$Res> get shippingAddress;@override $PaymentInfoCopyWith<$Res>? get paymentInfo;

}
/// @nodoc
class __$UnifiedOrderCopyWithImpl<$Res>
    implements _$UnifiedOrderCopyWith<$Res> {
  __$UnifiedOrderCopyWithImpl(this._self, this._then);

  final _UnifiedOrder _self;
  final $Res Function(_UnifiedOrder) _then;

/// Create a copy of UnifiedOrder
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? orderNumber = null,Object? displayName = null,Object? orderType = null,Object? status = null,Object? priority = null,Object? buyerId = null,Object? sellerId = freezed,Object? buyerName = freezed,Object? buyerEmail = freezed,Object? sellerName = freezed,Object? sellerEmail = freezed,Object? items = null,Object? shippingAddress = null,Object? paymentInfo = freezed,Object? subtotal = null,Object? taxAmount = null,Object? shippingCost = null,Object? totalAmount = null,Object? currency = null,Object? trackingNumber = freezed,Object? notes = freezed,Object? cancellationReason = freezed,Object? estimatedDeliveryDate = freezed,Object? actualDeliveryDate = freezed,Object? statusHistory = freezed,Object? metadata = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = freezed,}) {
  return _then(_UnifiedOrder(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,orderNumber: null == orderNumber ? _self.orderNumber : orderNumber // ignore: cast_nullable_to_non_nullable
as String,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,orderType: null == orderType ? _self.orderType : orderType // ignore: cast_nullable_to_non_nullable
as OrderType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as OrderPriority,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,buyerName: freezed == buyerName ? _self.buyerName : buyerName // ignore: cast_nullable_to_non_nullable
as String?,buyerEmail: freezed == buyerEmail ? _self.buyerEmail : buyerEmail // ignore: cast_nullable_to_non_nullable
as String?,sellerName: freezed == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String?,sellerEmail: freezed == sellerEmail ? _self.sellerEmail : sellerEmail // ignore: cast_nullable_to_non_nullable
as String?,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<OrderItem>,shippingAddress: null == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as ShippingAddress,paymentInfo: freezed == paymentInfo ? _self.paymentInfo : paymentInfo // ignore: cast_nullable_to_non_nullable
as PaymentInfo?,subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,shippingCost: null == shippingCost ? _self.shippingCost : shippingCost // ignore: cast_nullable_to_non_nullable
as double,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,trackingNumber: freezed == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,estimatedDeliveryDate: freezed == estimatedDeliveryDate ? _self.estimatedDeliveryDate : estimatedDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,actualDeliveryDate: freezed == actualDeliveryDate ? _self.actualDeliveryDate : actualDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,statusHistory: freezed == statusHistory ? _self._statusHistory : statusHistory // ignore: cast_nullable_to_non_nullable
as List<OrderStatusHistory>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: freezed == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

/// Create a copy of UnifiedOrder
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ShippingAddressCopyWith<$Res> get shippingAddress {
  
  return $ShippingAddressCopyWith<$Res>(_self.shippingAddress, (value) {
    return _then(_self.copyWith(shippingAddress: value));
  });
}/// Create a copy of UnifiedOrder
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentInfoCopyWith<$Res>? get paymentInfo {
    if (_self.paymentInfo == null) {
    return null;
  }

  return $PaymentInfoCopyWith<$Res>(_self.paymentInfo!, (value) {
    return _then(_self.copyWith(paymentInfo: value));
  });
}
}

// dart format on
