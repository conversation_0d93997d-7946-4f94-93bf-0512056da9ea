// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_orders_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$unifiedOrdersRepositoryHash() =>
    r'db305149fa0d281d547572687b447e24d871c22b';

/// موفر مستودع الطلبات الموحد
///
/// Copied from [unifiedOrdersRepository].
@ProviderFor(unifiedOrdersRepository)
final unifiedOrdersRepositoryProvider =
    AutoDisposeProvider<UnifiedOrdersRepository>.internal(
      unifiedOrdersRepository,
      name: r'unifiedOrdersRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$unifiedOrdersRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UnifiedOrdersRepositoryRef =
    AutoDisposeProviderRef<UnifiedOrdersRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
