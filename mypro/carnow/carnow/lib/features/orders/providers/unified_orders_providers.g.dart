// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_orders_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$ordersByTypeHash() => r'99a562b869683ecf1524325ee289def8465c6928';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// موفر طلبات حسب النوع
///
/// Copied from [ordersByType].
@ProviderFor(ordersByType)
const ordersByTypeProvider = OrdersByTypeFamily();

/// موفر طلبات حسب النوع
///
/// Copied from [ordersByType].
class OrdersByTypeFamily extends Family<AsyncValue<List<UnifiedOrder>>> {
  /// موفر طلبات حسب النوع
  ///
  /// Copied from [ordersByType].
  const OrdersByTypeFamily();

  /// موفر طلبات حسب النوع
  ///
  /// Copied from [ordersByType].
  OrdersByTypeProvider call(OrderType orderType) {
    return OrdersByTypeProvider(orderType);
  }

  @override
  OrdersByTypeProvider getProviderOverride(
    covariant OrdersByTypeProvider provider,
  ) {
    return call(provider.orderType);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'ordersByTypeProvider';
}

/// موفر طلبات حسب النوع
///
/// Copied from [ordersByType].
class OrdersByTypeProvider
    extends AutoDisposeFutureProvider<List<UnifiedOrder>> {
  /// موفر طلبات حسب النوع
  ///
  /// Copied from [ordersByType].
  OrdersByTypeProvider(OrderType orderType)
    : this._internal(
        (ref) => ordersByType(ref as OrdersByTypeRef, orderType),
        from: ordersByTypeProvider,
        name: r'ordersByTypeProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$ordersByTypeHash,
        dependencies: OrdersByTypeFamily._dependencies,
        allTransitiveDependencies:
            OrdersByTypeFamily._allTransitiveDependencies,
        orderType: orderType,
      );

  OrdersByTypeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderType,
  }) : super.internal();

  final OrderType orderType;

  @override
  Override overrideWith(
    FutureOr<List<UnifiedOrder>> Function(OrdersByTypeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OrdersByTypeProvider._internal(
        (ref) => create(ref as OrdersByTypeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderType: orderType,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<UnifiedOrder>> createElement() {
    return _OrdersByTypeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrdersByTypeProvider && other.orderType == orderType;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderType.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OrdersByTypeRef on AutoDisposeFutureProviderRef<List<UnifiedOrder>> {
  /// The parameter `orderType` of this provider.
  OrderType get orderType;
}

class _OrdersByTypeProviderElement
    extends AutoDisposeFutureProviderElement<List<UnifiedOrder>>
    with OrdersByTypeRef {
  _OrdersByTypeProviderElement(super.provider);

  @override
  OrderType get orderType => (origin as OrdersByTypeProvider).orderType;
}

String _$ordersByStatusHash() => r'ee383088a7b826eb4394b850541e98ad4a3a3be4';

/// موفر طلبات حسب الحالة
///
/// Copied from [ordersByStatus].
@ProviderFor(ordersByStatus)
const ordersByStatusProvider = OrdersByStatusFamily();

/// موفر طلبات حسب الحالة
///
/// Copied from [ordersByStatus].
class OrdersByStatusFamily extends Family<AsyncValue<List<UnifiedOrder>>> {
  /// موفر طلبات حسب الحالة
  ///
  /// Copied from [ordersByStatus].
  const OrdersByStatusFamily();

  /// موفر طلبات حسب الحالة
  ///
  /// Copied from [ordersByStatus].
  OrdersByStatusProvider call(OrderStatus status) {
    return OrdersByStatusProvider(status);
  }

  @override
  OrdersByStatusProvider getProviderOverride(
    covariant OrdersByStatusProvider provider,
  ) {
    return call(provider.status);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'ordersByStatusProvider';
}

/// موفر طلبات حسب الحالة
///
/// Copied from [ordersByStatus].
class OrdersByStatusProvider
    extends AutoDisposeFutureProvider<List<UnifiedOrder>> {
  /// موفر طلبات حسب الحالة
  ///
  /// Copied from [ordersByStatus].
  OrdersByStatusProvider(OrderStatus status)
    : this._internal(
        (ref) => ordersByStatus(ref as OrdersByStatusRef, status),
        from: ordersByStatusProvider,
        name: r'ordersByStatusProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$ordersByStatusHash,
        dependencies: OrdersByStatusFamily._dependencies,
        allTransitiveDependencies:
            OrdersByStatusFamily._allTransitiveDependencies,
        status: status,
      );

  OrdersByStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.status,
  }) : super.internal();

  final OrderStatus status;

  @override
  Override overrideWith(
    FutureOr<List<UnifiedOrder>> Function(OrdersByStatusRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OrdersByStatusProvider._internal(
        (ref) => create(ref as OrdersByStatusRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        status: status,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<UnifiedOrder>> createElement() {
    return _OrdersByStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrdersByStatusProvider && other.status == status;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, status.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OrdersByStatusRef on AutoDisposeFutureProviderRef<List<UnifiedOrder>> {
  /// The parameter `status` of this provider.
  OrderStatus get status;
}

class _OrdersByStatusProviderElement
    extends AutoDisposeFutureProviderElement<List<UnifiedOrder>>
    with OrdersByStatusRef {
  _OrdersByStatusProviderElement(super.provider);

  @override
  OrderStatus get status => (origin as OrdersByStatusProvider).status;
}

String _$orderByIdHash() => r'77b26cc3c4b923c00ff2875928a97be0de4cfdd5';

/// موفر طلب محدد
///
/// Copied from [orderById].
@ProviderFor(orderById)
const orderByIdProvider = OrderByIdFamily();

/// موفر طلب محدد
///
/// Copied from [orderById].
class OrderByIdFamily extends Family<AsyncValue<UnifiedOrder?>> {
  /// موفر طلب محدد
  ///
  /// Copied from [orderById].
  const OrderByIdFamily();

  /// موفر طلب محدد
  ///
  /// Copied from [orderById].
  OrderByIdProvider call(String orderId) {
    return OrderByIdProvider(orderId);
  }

  @override
  OrderByIdProvider getProviderOverride(covariant OrderByIdProvider provider) {
    return call(provider.orderId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'orderByIdProvider';
}

/// موفر طلب محدد
///
/// Copied from [orderById].
class OrderByIdProvider extends AutoDisposeFutureProvider<UnifiedOrder?> {
  /// موفر طلب محدد
  ///
  /// Copied from [orderById].
  OrderByIdProvider(String orderId)
    : this._internal(
        (ref) => orderById(ref as OrderByIdRef, orderId),
        from: orderByIdProvider,
        name: r'orderByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$orderByIdHash,
        dependencies: OrderByIdFamily._dependencies,
        allTransitiveDependencies: OrderByIdFamily._allTransitiveDependencies,
        orderId: orderId,
      );

  OrderByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderId,
  }) : super.internal();

  final String orderId;

  @override
  Override overrideWith(
    FutureOr<UnifiedOrder?> Function(OrderByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OrderByIdProvider._internal(
        (ref) => create(ref as OrderByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderId: orderId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UnifiedOrder?> createElement() {
    return _OrderByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrderByIdProvider && other.orderId == orderId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OrderByIdRef on AutoDisposeFutureProviderRef<UnifiedOrder?> {
  /// The parameter `orderId` of this provider.
  String get orderId;
}

class _OrderByIdProviderElement
    extends AutoDisposeFutureProviderElement<UnifiedOrder?>
    with OrderByIdRef {
  _OrderByIdProviderElement(super.provider);

  @override
  String get orderId => (origin as OrderByIdProvider).orderId;
}

String _$activeOrdersHash() => r'77bdb59e2f390e2e2fdd520d809e50267fc6b216';

/// موفر الطلبات النشطة
///
/// Copied from [activeOrders].
@ProviderFor(activeOrders)
final activeOrdersProvider =
    AutoDisposeFutureProvider<List<UnifiedOrder>>.internal(
      activeOrders,
      name: r'activeOrdersProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$activeOrdersHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActiveOrdersRef = AutoDisposeFutureProviderRef<List<UnifiedOrder>>;
String _$orderStatisticsHash() => r'737435a8e4558a23e79d538067c415632f8c8ffc';

/// موفر إحصائيات الطلبات
///
/// Copied from [orderStatistics].
@ProviderFor(orderStatistics)
const orderStatisticsProvider = OrderStatisticsFamily();

/// موفر إحصائيات الطلبات
///
/// Copied from [orderStatistics].
class OrderStatisticsFamily extends Family<AsyncValue<Map<String, dynamic>>> {
  /// موفر إحصائيات الطلبات
  ///
  /// Copied from [orderStatistics].
  const OrderStatisticsFamily();

  /// موفر إحصائيات الطلبات
  ///
  /// Copied from [orderStatistics].
  OrderStatisticsProvider call({
    OrderType? orderType,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return OrderStatisticsProvider(
      orderType: orderType,
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  OrderStatisticsProvider getProviderOverride(
    covariant OrderStatisticsProvider provider,
  ) {
    return call(
      orderType: provider.orderType,
      startDate: provider.startDate,
      endDate: provider.endDate,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'orderStatisticsProvider';
}

/// موفر إحصائيات الطلبات
///
/// Copied from [orderStatistics].
class OrderStatisticsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// موفر إحصائيات الطلبات
  ///
  /// Copied from [orderStatistics].
  OrderStatisticsProvider({
    OrderType? orderType,
    DateTime? startDate,
    DateTime? endDate,
  }) : this._internal(
         (ref) => orderStatistics(
           ref as OrderStatisticsRef,
           orderType: orderType,
           startDate: startDate,
           endDate: endDate,
         ),
         from: orderStatisticsProvider,
         name: r'orderStatisticsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$orderStatisticsHash,
         dependencies: OrderStatisticsFamily._dependencies,
         allTransitiveDependencies:
             OrderStatisticsFamily._allTransitiveDependencies,
         orderType: orderType,
         startDate: startDate,
         endDate: endDate,
       );

  OrderStatisticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderType,
    required this.startDate,
    required this.endDate,
  }) : super.internal();

  final OrderType? orderType;
  final DateTime? startDate;
  final DateTime? endDate;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(OrderStatisticsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OrderStatisticsProvider._internal(
        (ref) => create(ref as OrderStatisticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderType: orderType,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _OrderStatisticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrderStatisticsProvider &&
        other.orderType == orderType &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OrderStatisticsRef on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `orderType` of this provider.
  OrderType? get orderType;

  /// The parameter `startDate` of this provider.
  DateTime? get startDate;

  /// The parameter `endDate` of this provider.
  DateTime? get endDate;
}

class _OrderStatisticsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with OrderStatisticsRef {
  _OrderStatisticsProviderElement(super.provider);

  @override
  OrderType? get orderType => (origin as OrderStatisticsProvider).orderType;
  @override
  DateTime? get startDate => (origin as OrderStatisticsProvider).startDate;
  @override
  DateTime? get endDate => (origin as OrderStatisticsProvider).endDate;
}

String _$orderDetailsHash() => r'd25c26312f5474a8b71e1259a90a46b3bdb83ea2';

/// موفر تفاصيل طلب محدد
///
/// Copied from [orderDetails].
@ProviderFor(orderDetails)
const orderDetailsProvider = OrderDetailsFamily();

/// موفر تفاصيل طلب محدد
///
/// Copied from [orderDetails].
class OrderDetailsFamily extends Family<AsyncValue<UnifiedOrder?>> {
  /// موفر تفاصيل طلب محدد
  ///
  /// Copied from [orderDetails].
  const OrderDetailsFamily();

  /// موفر تفاصيل طلب محدد
  ///
  /// Copied from [orderDetails].
  OrderDetailsProvider call(String orderId) {
    return OrderDetailsProvider(orderId);
  }

  @override
  OrderDetailsProvider getProviderOverride(
    covariant OrderDetailsProvider provider,
  ) {
    return call(provider.orderId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'orderDetailsProvider';
}

/// موفر تفاصيل طلب محدد
///
/// Copied from [orderDetails].
class OrderDetailsProvider extends AutoDisposeFutureProvider<UnifiedOrder?> {
  /// موفر تفاصيل طلب محدد
  ///
  /// Copied from [orderDetails].
  OrderDetailsProvider(String orderId)
    : this._internal(
        (ref) => orderDetails(ref as OrderDetailsRef, orderId),
        from: orderDetailsProvider,
        name: r'orderDetailsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$orderDetailsHash,
        dependencies: OrderDetailsFamily._dependencies,
        allTransitiveDependencies:
            OrderDetailsFamily._allTransitiveDependencies,
        orderId: orderId,
      );

  OrderDetailsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderId,
  }) : super.internal();

  final String orderId;

  @override
  Override overrideWith(
    FutureOr<UnifiedOrder?> Function(OrderDetailsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OrderDetailsProvider._internal(
        (ref) => create(ref as OrderDetailsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderId: orderId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UnifiedOrder?> createElement() {
    return _OrderDetailsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrderDetailsProvider && other.orderId == orderId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OrderDetailsRef on AutoDisposeFutureProviderRef<UnifiedOrder?> {
  /// The parameter `orderId` of this provider.
  String get orderId;
}

class _OrderDetailsProviderElement
    extends AutoDisposeFutureProviderElement<UnifiedOrder?>
    with OrderDetailsRef {
  _OrderDetailsProviderElement(super.provider);

  @override
  String get orderId => (origin as OrderDetailsProvider).orderId;
}

String _$orderStatusHash() => r'6154443216759cded331ff2ed5ec77c56d4f1541';

/// موفر حالة طلب محدد (للقراءة فقط)
///
/// Copied from [orderStatus].
@ProviderFor(orderStatus)
const orderStatusProvider = OrderStatusFamily();

/// موفر حالة طلب محدد (للقراءة فقط)
///
/// Copied from [orderStatus].
class OrderStatusFamily extends Family<AsyncValue<String>> {
  /// موفر حالة طلب محدد (للقراءة فقط)
  ///
  /// Copied from [orderStatus].
  const OrderStatusFamily();

  /// موفر حالة طلب محدد (للقراءة فقط)
  ///
  /// Copied from [orderStatus].
  OrderStatusProvider call(String orderId) {
    return OrderStatusProvider(orderId);
  }

  @override
  OrderStatusProvider getProviderOverride(
    covariant OrderStatusProvider provider,
  ) {
    return call(provider.orderId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'orderStatusProvider';
}

/// موفر حالة طلب محدد (للقراءة فقط)
///
/// Copied from [orderStatus].
class OrderStatusProvider extends AutoDisposeFutureProvider<String> {
  /// موفر حالة طلب محدد (للقراءة فقط)
  ///
  /// Copied from [orderStatus].
  OrderStatusProvider(String orderId)
    : this._internal(
        (ref) => orderStatus(ref as OrderStatusRef, orderId),
        from: orderStatusProvider,
        name: r'orderStatusProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$orderStatusHash,
        dependencies: OrderStatusFamily._dependencies,
        allTransitiveDependencies: OrderStatusFamily._allTransitiveDependencies,
        orderId: orderId,
      );

  OrderStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderId,
  }) : super.internal();

  final String orderId;

  @override
  Override overrideWith(
    FutureOr<String> Function(OrderStatusRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OrderStatusProvider._internal(
        (ref) => create(ref as OrderStatusRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderId: orderId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<String> createElement() {
    return _OrderStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrderStatusProvider && other.orderId == orderId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OrderStatusRef on AutoDisposeFutureProviderRef<String> {
  /// The parameter `orderId` of this provider.
  String get orderId;
}

class _OrderStatusProviderElement
    extends AutoDisposeFutureProviderElement<String>
    with OrderStatusRef {
  _OrderStatusProviderElement(super.provider);

  @override
  String get orderId => (origin as OrderStatusProvider).orderId;
}

String _$userOrdersHash() => r'9e5fcb3ba180de3c4fd2e99cfa4af189f77922ae';

abstract class _$UserOrders
    extends BuildlessAutoDisposeAsyncNotifier<List<UnifiedOrder>> {
  late final OrderType? orderType;
  late final OrderStatus? status;
  late final int? limit;

  FutureOr<List<UnifiedOrder>> build({
    OrderType? orderType,
    OrderStatus? status,
    int? limit,
  });
}

/// موفر جميع طلبات المستخدم
///
/// Copied from [UserOrders].
@ProviderFor(UserOrders)
const userOrdersProvider = UserOrdersFamily();

/// موفر جميع طلبات المستخدم
///
/// Copied from [UserOrders].
class UserOrdersFamily extends Family<AsyncValue<List<UnifiedOrder>>> {
  /// موفر جميع طلبات المستخدم
  ///
  /// Copied from [UserOrders].
  const UserOrdersFamily();

  /// موفر جميع طلبات المستخدم
  ///
  /// Copied from [UserOrders].
  UserOrdersProvider call({
    OrderType? orderType,
    OrderStatus? status,
    int? limit,
  }) {
    return UserOrdersProvider(
      orderType: orderType,
      status: status,
      limit: limit,
    );
  }

  @override
  UserOrdersProvider getProviderOverride(
    covariant UserOrdersProvider provider,
  ) {
    return call(
      orderType: provider.orderType,
      status: provider.status,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userOrdersProvider';
}

/// موفر جميع طلبات المستخدم
///
/// Copied from [UserOrders].
class UserOrdersProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<UserOrders, List<UnifiedOrder>> {
  /// موفر جميع طلبات المستخدم
  ///
  /// Copied from [UserOrders].
  UserOrdersProvider({OrderType? orderType, OrderStatus? status, int? limit})
    : this._internal(
        () => UserOrders()
          ..orderType = orderType
          ..status = status
          ..limit = limit,
        from: userOrdersProvider,
        name: r'userOrdersProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userOrdersHash,
        dependencies: UserOrdersFamily._dependencies,
        allTransitiveDependencies: UserOrdersFamily._allTransitiveDependencies,
        orderType: orderType,
        status: status,
        limit: limit,
      );

  UserOrdersProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderType,
    required this.status,
    required this.limit,
  }) : super.internal();

  final OrderType? orderType;
  final OrderStatus? status;
  final int? limit;

  @override
  FutureOr<List<UnifiedOrder>> runNotifierBuild(covariant UserOrders notifier) {
    return notifier.build(orderType: orderType, status: status, limit: limit);
  }

  @override
  Override overrideWith(UserOrders Function() create) {
    return ProviderOverride(
      origin: this,
      override: UserOrdersProvider._internal(
        () => create()
          ..orderType = orderType
          ..status = status
          ..limit = limit,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderType: orderType,
        status: status,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<UserOrders, List<UnifiedOrder>>
  createElement() {
    return _UserOrdersProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserOrdersProvider &&
        other.orderType == orderType &&
        other.status == status &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderType.hashCode);
    hash = _SystemHash.combine(hash, status.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserOrdersRef on AutoDisposeAsyncNotifierProviderRef<List<UnifiedOrder>> {
  /// The parameter `orderType` of this provider.
  OrderType? get orderType;

  /// The parameter `status` of this provider.
  OrderStatus? get status;

  /// The parameter `limit` of this provider.
  int? get limit;
}

class _UserOrdersProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<UserOrders, List<UnifiedOrder>>
    with UserOrdersRef {
  _UserOrdersProviderElement(super.provider);

  @override
  OrderType? get orderType => (origin as UserOrdersProvider).orderType;
  @override
  OrderStatus? get status => (origin as UserOrdersProvider).status;
  @override
  int? get limit => (origin as UserOrdersProvider).limit;
}

String _$orderSearchHash() => r'323cfbc5bec415a62883127af61121e23ae03be2';

/// موفر البحث في الطلبات
///
/// Copied from [OrderSearch].
@ProviderFor(OrderSearch)
final orderSearchProvider =
    AutoDisposeAsyncNotifierProvider<OrderSearch, List<UnifiedOrder>>.internal(
      OrderSearch.new,
      name: r'orderSearchProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$orderSearchHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OrderSearch = AutoDisposeAsyncNotifier<List<UnifiedOrder>>;
String _$createOrderHash() => r'74d6bf78072d9ce19d050802b19605de7c0bad8a';

/// موفر إنشاء طلب جديد
///
/// Copied from [CreateOrder].
@ProviderFor(CreateOrder)
final createOrderProvider =
    AutoDisposeAsyncNotifierProvider<CreateOrder, UnifiedOrder?>.internal(
      CreateOrder.new,
      name: r'createOrderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$createOrderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CreateOrder = AutoDisposeAsyncNotifier<UnifiedOrder?>;
String _$updateOrderHash() => r'0a63fa6966980edd6adba0b113ba914e49eb31a4';

/// موفر تحديث طلب
///
/// Copied from [UpdateOrder].
@ProviderFor(UpdateOrder)
final updateOrderProvider =
    AutoDisposeAsyncNotifierProvider<UpdateOrder, UnifiedOrder?>.internal(
      UpdateOrder.new,
      name: r'updateOrderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$updateOrderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UpdateOrder = AutoDisposeAsyncNotifier<UnifiedOrder?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
