// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checkout_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkoutNotifierHash() => r'88ceb43e967cdaa41af586a72bb14eb1ca0a1047';

/// Simple Checkout Provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [CheckoutNotifier].
@ProviderFor(CheckoutNotifier)
final checkoutNotifierProvider =
    AutoDisposeNotifierProvider<CheckoutNotifier, AsyncValue<void>>.internal(
      CheckoutNotifier.new,
      name: r'checkoutNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$checkoutNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CheckoutNotifier = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
