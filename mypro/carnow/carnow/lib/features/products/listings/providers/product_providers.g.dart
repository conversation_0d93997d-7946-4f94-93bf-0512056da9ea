// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$paginatedProductsHash() => r'7339a7d8b122f0b1649b253b98b13cacd543fca3';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Fetches products with pagination parameters - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [paginatedProducts].
@ProviderFor(paginatedProducts)
const paginatedProductsProvider = PaginatedProductsFamily();

/// Fetches products with pagination parameters - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [paginatedProducts].
class PaginatedProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Fetches products with pagination parameters - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [paginatedProducts].
  const PaginatedProductsFamily();

  /// Fetches products with pagination parameters - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [paginatedProducts].
  PaginatedProductsProvider call(Map<String, int> params) {
    return PaginatedProductsProvider(params);
  }

  @override
  PaginatedProductsProvider getProviderOverride(
    covariant PaginatedProductsProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'paginatedProductsProvider';
}

/// Fetches products with pagination parameters - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [paginatedProducts].
class PaginatedProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Fetches products with pagination parameters - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [paginatedProducts].
  PaginatedProductsProvider(Map<String, int> params)
    : this._internal(
        (ref) => paginatedProducts(ref as PaginatedProductsRef, params),
        from: paginatedProductsProvider,
        name: r'paginatedProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$paginatedProductsHash,
        dependencies: PaginatedProductsFamily._dependencies,
        allTransitiveDependencies:
            PaginatedProductsFamily._allTransitiveDependencies,
        params: params,
      );

  PaginatedProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final Map<String, int> params;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(PaginatedProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PaginatedProductsProvider._internal(
        (ref) => create(ref as PaginatedProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _PaginatedProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PaginatedProductsProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PaginatedProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `params` of this provider.
  Map<String, int> get params;
}

class _PaginatedProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with PaginatedProductsRef {
  _PaginatedProductsProviderElement(super.provider);

  @override
  Map<String, int> get params => (origin as PaginatedProductsProvider).params;
}

String _$productDetailsHash() => r'56754d79b3e9c19386968f85ab10a97973783313';

/// Fetches a specific product by ID - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productDetails].
@ProviderFor(productDetails)
const productDetailsProvider = ProductDetailsFamily();

/// Fetches a specific product by ID - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productDetails].
class ProductDetailsFamily extends Family<AsyncValue<ProductModel?>> {
  /// Fetches a specific product by ID - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productDetails].
  const ProductDetailsFamily();

  /// Fetches a specific product by ID - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productDetails].
  ProductDetailsProvider call(String id) {
    return ProductDetailsProvider(id);
  }

  @override
  ProductDetailsProvider getProviderOverride(
    covariant ProductDetailsProvider provider,
  ) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productDetailsProvider';
}

/// Fetches a specific product by ID - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productDetails].
class ProductDetailsProvider extends AutoDisposeFutureProvider<ProductModel?> {
  /// Fetches a specific product by ID - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productDetails].
  ProductDetailsProvider(String id)
    : this._internal(
        (ref) => productDetails(ref as ProductDetailsRef, id),
        from: productDetailsProvider,
        name: r'productDetailsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productDetailsHash,
        dependencies: ProductDetailsFamily._dependencies,
        allTransitiveDependencies:
            ProductDetailsFamily._allTransitiveDependencies,
        id: id,
      );

  ProductDetailsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<ProductModel?> Function(ProductDetailsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductDetailsProvider._internal(
        (ref) => create(ref as ProductDetailsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel?> createElement() {
    return _ProductDetailsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductDetailsProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductDetailsRef on AutoDisposeFutureProviderRef<ProductModel?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _ProductDetailsProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel?>
    with ProductDetailsRef {
  _ProductDetailsProviderElement(super.provider);

  @override
  String get id => (origin as ProductDetailsProvider).id;
}

String _$userProductsHash() => r'83f8b6752ec2613257a9e933d8d543db1be2a1c7';

/// Fetches all products for a specific user - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [userProducts].
@ProviderFor(userProducts)
const userProductsProvider = UserProductsFamily();

/// Fetches all products for a specific user - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [userProducts].
class UserProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Fetches all products for a specific user - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [userProducts].
  const UserProductsFamily();

  /// Fetches all products for a specific user - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [userProducts].
  UserProductsProvider call(String userId) {
    return UserProductsProvider(userId);
  }

  @override
  UserProductsProvider getProviderOverride(
    covariant UserProductsProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userProductsProvider';
}

/// Fetches all products for a specific user - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [userProducts].
class UserProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Fetches all products for a specific user - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [userProducts].
  UserProductsProvider(String userId)
    : this._internal(
        (ref) => userProducts(ref as UserProductsRef, userId),
        from: userProductsProvider,
        name: r'userProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userProductsHash,
        dependencies: UserProductsFamily._dependencies,
        allTransitiveDependencies:
            UserProductsFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(UserProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserProductsProvider._internal(
        (ref) => create(ref as UserProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _UserProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserProductsProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with UserProductsRef {
  _UserProductsProviderElement(super.provider);

  @override
  String get userId => (origin as UserProductsProvider).userId;
}

String _$paginatedUserProductsHash() =>
    r'2a8688b9a31a2f3e2d36327eeacb02eb2c03d30b';

/// Fetches user products with pagination - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [paginatedUserProducts].
@ProviderFor(paginatedUserProducts)
const paginatedUserProductsProvider = PaginatedUserProductsFamily();

/// Fetches user products with pagination - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [paginatedUserProducts].
class PaginatedUserProductsFamily
    extends Family<AsyncValue<List<ProductModel>>> {
  /// Fetches user products with pagination - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [paginatedUserProducts].
  const PaginatedUserProductsFamily();

  /// Fetches user products with pagination - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [paginatedUserProducts].
  PaginatedUserProductsProvider call(Map<String, dynamic> params) {
    return PaginatedUserProductsProvider(params);
  }

  @override
  PaginatedUserProductsProvider getProviderOverride(
    covariant PaginatedUserProductsProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'paginatedUserProductsProvider';
}

/// Fetches user products with pagination - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [paginatedUserProducts].
class PaginatedUserProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Fetches user products with pagination - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [paginatedUserProducts].
  PaginatedUserProductsProvider(Map<String, dynamic> params)
    : this._internal(
        (ref) => paginatedUserProducts(ref as PaginatedUserProductsRef, params),
        from: paginatedUserProductsProvider,
        name: r'paginatedUserProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$paginatedUserProductsHash,
        dependencies: PaginatedUserProductsFamily._dependencies,
        allTransitiveDependencies:
            PaginatedUserProductsFamily._allTransitiveDependencies,
        params: params,
      );

  PaginatedUserProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final Map<String, dynamic> params;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(PaginatedUserProductsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PaginatedUserProductsProvider._internal(
        (ref) => create(ref as PaginatedUserProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _PaginatedUserProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PaginatedUserProductsProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PaginatedUserProductsRef
    on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `params` of this provider.
  Map<String, dynamic> get params;
}

class _PaginatedUserProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with PaginatedUserProductsRef {
  _PaginatedUserProductsProviderElement(super.provider);

  @override
  Map<String, dynamic> get params =>
      (origin as PaginatedUserProductsProvider).params;
}

String _$carModelsHash() => r'285b483bbde2d1c3f04baa5159a9f88f1277213a';

/// Car models provider - FIXED: Converted to @riverpod pattern
/// Forever Plan: For now keeping minimal, should be moved to Go backend
///
/// Copied from [carModels].
@ProviderFor(carModels)
const carModelsProvider = CarModelsFamily();

/// Car models provider - FIXED: Converted to @riverpod pattern
/// Forever Plan: For now keeping minimal, should be moved to Go backend
///
/// Copied from [carModels].
class CarModelsFamily extends Family<AsyncValue<List<String>>> {
  /// Car models provider - FIXED: Converted to @riverpod pattern
  /// Forever Plan: For now keeping minimal, should be moved to Go backend
  ///
  /// Copied from [carModels].
  const CarModelsFamily();

  /// Car models provider - FIXED: Converted to @riverpod pattern
  /// Forever Plan: For now keeping minimal, should be moved to Go backend
  ///
  /// Copied from [carModels].
  CarModelsProvider call(int brandId) {
    return CarModelsProvider(brandId);
  }

  @override
  CarModelsProvider getProviderOverride(covariant CarModelsProvider provider) {
    return call(provider.brandId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'carModelsProvider';
}

/// Car models provider - FIXED: Converted to @riverpod pattern
/// Forever Plan: For now keeping minimal, should be moved to Go backend
///
/// Copied from [carModels].
class CarModelsProvider extends AutoDisposeFutureProvider<List<String>> {
  /// Car models provider - FIXED: Converted to @riverpod pattern
  /// Forever Plan: For now keeping minimal, should be moved to Go backend
  ///
  /// Copied from [carModels].
  CarModelsProvider(int brandId)
    : this._internal(
        (ref) => carModels(ref as CarModelsRef, brandId),
        from: carModelsProvider,
        name: r'carModelsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$carModelsHash,
        dependencies: CarModelsFamily._dependencies,
        allTransitiveDependencies: CarModelsFamily._allTransitiveDependencies,
        brandId: brandId,
      );

  CarModelsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.brandId,
  }) : super.internal();

  final int brandId;

  @override
  Override overrideWith(
    FutureOr<List<String>> Function(CarModelsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CarModelsProvider._internal(
        (ref) => create(ref as CarModelsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        brandId: brandId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<String>> createElement() {
    return _CarModelsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CarModelsProvider && other.brandId == brandId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, brandId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CarModelsRef on AutoDisposeFutureProviderRef<List<String>> {
  /// The parameter `brandId` of this provider.
  int get brandId;
}

class _CarModelsProviderElement
    extends AutoDisposeFutureProviderElement<List<String>>
    with CarModelsRef {
  _CarModelsProviderElement(super.provider);

  @override
  int get brandId => (origin as CarModelsProvider).brandId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
