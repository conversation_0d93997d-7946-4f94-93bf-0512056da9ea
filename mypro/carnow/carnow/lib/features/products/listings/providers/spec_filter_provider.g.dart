// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'spec_filter_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$specFilterHash() => r'2c5219c1d1354d3f9309217edada1442ad8b2d8f';

/// Holds dynamic specification filters as Map<String, dynamic>
///
/// Copied from [SpecFilter].
@ProviderFor(SpecFilter)
final specFilterProvider =
    AutoDisposeNotifierProvider<SpecFilter, Map<String, dynamic>>.internal(
      SpecFilter.new,
      name: r'specFilterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$specFilterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SpecFilter = AutoDisposeNotifier<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
