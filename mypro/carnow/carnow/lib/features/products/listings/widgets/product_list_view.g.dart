// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_list_view.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productListCurrentPageHash() =>
    r'f3c0b5145e73aa1b4936b286d24f1c90ad7a91da';

/// Provider for managing current page in product list view
///
/// Copied from [ProductListCurrentPage].
@ProviderFor(ProductListCurrentPage)
final productListCurrentPageProvider =
    AutoDisposeNotifierProvider<ProductListCurrentPage, int>.internal(
      ProductListCurrentPage.new,
      name: r'productListCurrentPageProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$productListCurrentPageHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ProductListCurrentPage = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
