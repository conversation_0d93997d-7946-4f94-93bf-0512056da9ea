// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductModel _$ProductModelFromJson(
  Map<String, dynamic> json,
) => _ProductModel(
  id: json['id'] as String,
  name: json['name'] as String,
  nameAr: json['nameAr'] as String?,
  nameEn: json['nameEn'] as String?,
  nameIt: json['nameIt'] as String?,
  description: json['description'] as String?,
  descriptionAr: json['descriptionAr'] as String?,
  descriptionEn: json['descriptionEn'] as String?,
  descriptionIt: json['descriptionIt'] as String?,
  price: (json['price'] as num).toDouble(),
  originalPrice: (json['originalPrice'] as num?)?.toDouble(),
  categoryId: json['categoryId'] as String,
  condition:
      $enumDecodeNullable(_$ProductConditionEnumMap, json['condition']) ??
      ProductCondition.used,
  subcategoryId: json['subcategoryId'] as String?,
  partCategoryId: json['partCategoryId'] as String?,
  isFeatured: json['isFeatured'] as bool? ?? false,
  isAvailable: json['isAvailable'] as bool? ?? true,
  isActive: json['isActive'] as bool? ?? true,
  stockQuantity: (json['stockQuantity'] as num?)?.toInt() ?? 0,
  images:
      (json['images'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  specifications: json['specifications'] as Map<String, dynamic>?,
  sellerId: json['sellerId'] as String,
  brand: json['brand'] as String?,
  model: json['model'] as String?,
  partNumber: json['partNumber'] as String?,
  manufacturer: json['manufacturer'] as String?,
  compatibleVehicles:
      (json['compatibleVehicles'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  location: json['location'] as String?,
  viewsCount: (json['viewsCount'] as num?)?.toInt() ?? 0,
  isPart: json['isPart'] as bool? ?? false,
  yearFrom: (json['yearFrom'] as num?)?.toInt(),
  yearTo: (json['yearTo'] as num?)?.toInt(),
  auctionStartDate: json['auctionStartDate'] == null
      ? null
      : DateTime.parse(json['auctionStartDate'] as String),
  auctionEndDate: json['auctionEndDate'] == null
      ? null
      : DateTime.parse(json['auctionEndDate'] as String),
  startingBid: (json['startingBid'] as num?)?.toDouble(),
  currentBid: (json['currentBid'] as num?)?.toDouble(),
  reservePrice: (json['reservePrice'] as num?)?.toDouble(),
  bidCount: (json['bidCount'] as num?)?.toInt() ?? 0,
  highestBidderId: json['highestBidderId'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
  productType: $enumDecodeNullable(_$ProductTypeEnumMap, json['productType']),
  automotiveType: $enumDecodeNullable(
    _$AutomotiveTypeEnumMap,
    json['automotiveType'],
  ),
);

Map<String, dynamic> _$ProductModelToJson(_ProductModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'nameAr': instance.nameAr,
      'nameEn': instance.nameEn,
      'nameIt': instance.nameIt,
      'description': instance.description,
      'descriptionAr': instance.descriptionAr,
      'descriptionEn': instance.descriptionEn,
      'descriptionIt': instance.descriptionIt,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'categoryId': instance.categoryId,
      'condition': _$ProductConditionEnumMap[instance.condition]!,
      'subcategoryId': instance.subcategoryId,
      'partCategoryId': instance.partCategoryId,
      'isFeatured': instance.isFeatured,
      'isAvailable': instance.isAvailable,
      'isActive': instance.isActive,
      'stockQuantity': instance.stockQuantity,
      'images': instance.images,
      'specifications': instance.specifications,
      'sellerId': instance.sellerId,
      'brand': instance.brand,
      'model': instance.model,
      'partNumber': instance.partNumber,
      'manufacturer': instance.manufacturer,
      'compatibleVehicles': instance.compatibleVehicles,
      'location': instance.location,
      'viewsCount': instance.viewsCount,
      'isPart': instance.isPart,
      'yearFrom': instance.yearFrom,
      'yearTo': instance.yearTo,
      'auctionStartDate': instance.auctionStartDate?.toIso8601String(),
      'auctionEndDate': instance.auctionEndDate?.toIso8601String(),
      'startingBid': instance.startingBid,
      'currentBid': instance.currentBid,
      'reservePrice': instance.reservePrice,
      'bidCount': instance.bidCount,
      'highestBidderId': instance.highestBidderId,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'productType': _$ProductTypeEnumMap[instance.productType],
      'automotiveType': _$AutomotiveTypeEnumMap[instance.automotiveType],
    };

const _$ProductConditionEnumMap = {
  ProductCondition.new_: 'new',
  ProductCondition.likeNew: 'like_new',
  ProductCondition.good: 'good',
  ProductCondition.used: 'used',
  ProductCondition.fair: 'fair',
  ProductCondition.poor: 'poor',
  ProductCondition.forParts: 'for_parts',
  ProductCondition.refurbished: 'refurbished',
};

const _$ProductTypeEnumMap = {
  ProductType.vehicles: 'vehicles',
  ProductType.autoParts: 'auto_parts',
  ProductType.electronics: 'electronics',
  ProductType.tools: 'tools',
  ProductType.accessories: 'accessories',
  ProductType.maintenance: 'maintenance',
  ProductType.other: 'other',
};

const _$AutomotiveTypeEnumMap = {
  AutomotiveType.engineParts: 'engine_parts',
  AutomotiveType.transmissionParts: 'transmission_parts',
  AutomotiveType.suspensionSteering: 'suspension_steering',
  AutomotiveType.brakeSystem: 'brake_system',
  AutomotiveType.electricalElectronic: 'electrical_electronic',
  AutomotiveType.bodyExterior: 'body_exterior',
  AutomotiveType.interiorParts: 'interior_parts',
  AutomotiveType.tiresWheels: 'tires_wheels',
  AutomotiveType.exhaustSystem: 'exhaust_system',
  AutomotiveType.coolingAC: 'cooling_ac',
  AutomotiveType.fuelSystem: 'fuel_system',
  AutomotiveType.autoParts: 'auto_parts',
  AutomotiveType.vehicles: 'vehicles',
  AutomotiveType.other: 'other',
};
