// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductFilter _$ProductFilterFromJson(Map<String, dynamic> json) =>
    _ProductFilter(
      searchQuery: json['searchQuery'] as String?,
      categoryId: json['categoryId'] as String?,
      subcategoryId: json['subcategoryId'] as String?,
      specFilters: json['specFilters'] as Map<String, dynamic>?,
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      minRating: (json['minRating'] as num?)?.toDouble(),
      isAvailable: json['isAvailable'] as bool?,
      isFeatured: json['isFeatured'] as bool?,
      hasDiscount: json['hasDiscount'] as bool?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      sellerId: json['sellerId'] as String?,
      sortBy: json['sortBy'] as String?,
    );

Map<String, dynamic> _$ProductFilterToJson(_ProductFilter instance) =>
    <String, dynamic>{
      'searchQuery': instance.searchQuery,
      'categoryId': instance.categoryId,
      'subcategoryId': instance.subcategoryId,
      'specFilters': instance.specFilters,
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
      'minRating': instance.minRating,
      'isAvailable': instance.isAvailable,
      'isFeatured': instance.isFeatured,
      'hasDiscount': instance.hasDiscount,
      'tags': instance.tags,
      'sellerId': instance.sellerId,
      'sortBy': instance.sortBy,
    };
