// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductModel {

// المعرف الأساسي للمنتج
 String get id;// أسماء المنتج بعدة لغات
 String get name;// الاسم الأساسي
 String? get nameAr;// الاسم بالعربية
 String? get nameEn;// الاسم بالإنجليزية
 String? get nameIt;// الاسم بالإيطالية
// وصف المنتج بعدة لغات
 String? get description;// الوصف الأساسي
 String? get descriptionAr;// الوصف بالعربية
 String? get descriptionEn;// الوصف بالإنجليزية
 String? get descriptionIt;// الوصف بالإيطالية
// معلومات السعر
 double get price;// السعر الحالي
 double? get originalPrice;// السعر الأصلي (قبل التخفيض)
// معلومات التصنيف
 String get categoryId;// معرف الفئة الرئيسية
 ProductCondition get condition;// حالة المنتج
 String? get subcategoryId;// معرف الفئة الفرعية
 String? get partCategoryId;// معرف فئة قطع الغيار
// حالة المنتج وإعداداته
 bool get isFeatured;// منتج مميز
 bool get isAvailable;// متوفر للبيع
 bool get isActive;// نشط في النظام
 int get stockQuantity;// كمية المخزون
// الصور والمواصفات
 List<String> get images;// قائمة روابط الصور
 Map<String, dynamic>? get specifications;// المواصفات التقنية
// معلومات البائع
 String get sellerId;// معرف البائع
// معلومات المنتج التقنية
 String? get brand;// العلامة التجارية
 String? get model;// الموديل
 String? get partNumber;// رقم القطعة
 String? get manufacturer;// الشركة المصنعة
 List<String> get compatibleVehicles;// المركبات المتوافقة
 String? get location;// موقع المنتج
// إحصائيات وحالة المنتج
 int get viewsCount;// عدد المشاهدات
 bool get isPart;// هل هو قطعة غيار
// سنوات التوافق
 int? get yearFrom;// من سنة
 int? get yearTo;// إلى سنة
// معلومات المزاد
 DateTime? get auctionStartDate;// تاريخ بداية المزاد
 DateTime? get auctionEndDate;// تاريخ نهاية المزاد
 double? get startingBid;// سعر البداية
 double? get currentBid;// العرض الحالي
 double? get reservePrice;// السعر الاحتياطي
 int get bidCount;// عدد العروض
 String? get highestBidderId;// معرف صاحب أعلى عرض
// طوابع زمنية
 DateTime? get createdAt;// تاريخ الإنشاء
 DateTime? get updatedAt;// تاريخ آخر تحديث
 bool get isDeleted;// محذوف (soft delete)
// أنواع المنتج
 ProductType? get productType;// نوع المنتج الأساسي
 AutomotiveType? get automotiveType;
/// Create a copy of ProductModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductModelCopyWith<ProductModel> get copyWith => _$ProductModelCopyWithImpl<ProductModel>(this as ProductModel, _$identity);

  /// Serializes this ProductModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&(identical(other.nameIt, nameIt) || other.nameIt == nameIt)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.descriptionEn, descriptionEn) || other.descriptionEn == descriptionEn)&&(identical(other.descriptionIt, descriptionIt) || other.descriptionIt == descriptionIt)&&(identical(other.price, price) || other.price == price)&&(identical(other.originalPrice, originalPrice) || other.originalPrice == originalPrice)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.subcategoryId, subcategoryId) || other.subcategoryId == subcategoryId)&&(identical(other.partCategoryId, partCategoryId) || other.partCategoryId == partCategoryId)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.stockQuantity, stockQuantity) || other.stockQuantity == stockQuantity)&&const DeepCollectionEquality().equals(other.images, images)&&const DeepCollectionEquality().equals(other.specifications, specifications)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.model, model) || other.model == model)&&(identical(other.partNumber, partNumber) || other.partNumber == partNumber)&&(identical(other.manufacturer, manufacturer) || other.manufacturer == manufacturer)&&const DeepCollectionEquality().equals(other.compatibleVehicles, compatibleVehicles)&&(identical(other.location, location) || other.location == location)&&(identical(other.viewsCount, viewsCount) || other.viewsCount == viewsCount)&&(identical(other.isPart, isPart) || other.isPart == isPart)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.auctionStartDate, auctionStartDate) || other.auctionStartDate == auctionStartDate)&&(identical(other.auctionEndDate, auctionEndDate) || other.auctionEndDate == auctionEndDate)&&(identical(other.startingBid, startingBid) || other.startingBid == startingBid)&&(identical(other.currentBid, currentBid) || other.currentBid == currentBid)&&(identical(other.reservePrice, reservePrice) || other.reservePrice == reservePrice)&&(identical(other.bidCount, bidCount) || other.bidCount == bidCount)&&(identical(other.highestBidderId, highestBidderId) || other.highestBidderId == highestBidderId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.productType, productType) || other.productType == productType)&&(identical(other.automotiveType, automotiveType) || other.automotiveType == automotiveType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,nameAr,nameEn,nameIt,description,descriptionAr,descriptionEn,descriptionIt,price,originalPrice,categoryId,condition,subcategoryId,partCategoryId,isFeatured,isAvailable,isActive,stockQuantity,const DeepCollectionEquality().hash(images),const DeepCollectionEquality().hash(specifications),sellerId,brand,model,partNumber,manufacturer,const DeepCollectionEquality().hash(compatibleVehicles),location,viewsCount,isPart,yearFrom,yearTo,auctionStartDate,auctionEndDate,startingBid,currentBid,reservePrice,bidCount,highestBidderId,createdAt,updatedAt,isDeleted,productType,automotiveType]);

@override
String toString() {
  return 'ProductModel(id: $id, name: $name, nameAr: $nameAr, nameEn: $nameEn, nameIt: $nameIt, description: $description, descriptionAr: $descriptionAr, descriptionEn: $descriptionEn, descriptionIt: $descriptionIt, price: $price, originalPrice: $originalPrice, categoryId: $categoryId, condition: $condition, subcategoryId: $subcategoryId, partCategoryId: $partCategoryId, isFeatured: $isFeatured, isAvailable: $isAvailable, isActive: $isActive, stockQuantity: $stockQuantity, images: $images, specifications: $specifications, sellerId: $sellerId, brand: $brand, model: $model, partNumber: $partNumber, manufacturer: $manufacturer, compatibleVehicles: $compatibleVehicles, location: $location, viewsCount: $viewsCount, isPart: $isPart, yearFrom: $yearFrom, yearTo: $yearTo, auctionStartDate: $auctionStartDate, auctionEndDate: $auctionEndDate, startingBid: $startingBid, currentBid: $currentBid, reservePrice: $reservePrice, bidCount: $bidCount, highestBidderId: $highestBidderId, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, productType: $productType, automotiveType: $automotiveType)';
}


}

/// @nodoc
abstract mixin class $ProductModelCopyWith<$Res>  {
  factory $ProductModelCopyWith(ProductModel value, $Res Function(ProductModel) _then) = _$ProductModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? nameAr, String? nameEn, String? nameIt, String? description, String? descriptionAr, String? descriptionEn, String? descriptionIt, double price, double? originalPrice, String categoryId, ProductCondition condition, String? subcategoryId, String? partCategoryId, bool isFeatured, bool isAvailable, bool isActive, int stockQuantity, List<String> images, Map<String, dynamic>? specifications, String sellerId, String? brand, String? model, String? partNumber, String? manufacturer, List<String> compatibleVehicles, String? location, int viewsCount, bool isPart, int? yearFrom, int? yearTo, DateTime? auctionStartDate, DateTime? auctionEndDate, double? startingBid, double? currentBid, double? reservePrice, int bidCount, String? highestBidderId, DateTime? createdAt, DateTime? updatedAt, bool isDeleted, ProductType? productType, AutomotiveType? automotiveType
});




}
/// @nodoc
class _$ProductModelCopyWithImpl<$Res>
    implements $ProductModelCopyWith<$Res> {
  _$ProductModelCopyWithImpl(this._self, this._then);

  final ProductModel _self;
  final $Res Function(ProductModel) _then;

/// Create a copy of ProductModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? nameEn = freezed,Object? nameIt = freezed,Object? description = freezed,Object? descriptionAr = freezed,Object? descriptionEn = freezed,Object? descriptionIt = freezed,Object? price = null,Object? originalPrice = freezed,Object? categoryId = null,Object? condition = null,Object? subcategoryId = freezed,Object? partCategoryId = freezed,Object? isFeatured = null,Object? isAvailable = null,Object? isActive = null,Object? stockQuantity = null,Object? images = null,Object? specifications = freezed,Object? sellerId = null,Object? brand = freezed,Object? model = freezed,Object? partNumber = freezed,Object? manufacturer = freezed,Object? compatibleVehicles = null,Object? location = freezed,Object? viewsCount = null,Object? isPart = null,Object? yearFrom = freezed,Object? yearTo = freezed,Object? auctionStartDate = freezed,Object? auctionEndDate = freezed,Object? startingBid = freezed,Object? currentBid = freezed,Object? reservePrice = freezed,Object? bidCount = null,Object? highestBidderId = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? productType = freezed,Object? automotiveType = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,nameEn: freezed == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String?,nameIt: freezed == nameIt ? _self.nameIt : nameIt // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,descriptionEn: freezed == descriptionEn ? _self.descriptionEn : descriptionEn // ignore: cast_nullable_to_non_nullable
as String?,descriptionIt: freezed == descriptionIt ? _self.descriptionIt : descriptionIt // ignore: cast_nullable_to_non_nullable
as String?,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,originalPrice: freezed == originalPrice ? _self.originalPrice : originalPrice // ignore: cast_nullable_to_non_nullable
as double?,categoryId: null == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as ProductCondition,subcategoryId: freezed == subcategoryId ? _self.subcategoryId : subcategoryId // ignore: cast_nullable_to_non_nullable
as String?,partCategoryId: freezed == partCategoryId ? _self.partCategoryId : partCategoryId // ignore: cast_nullable_to_non_nullable
as String?,isFeatured: null == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,stockQuantity: null == stockQuantity ? _self.stockQuantity : stockQuantity // ignore: cast_nullable_to_non_nullable
as int,images: null == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>,specifications: freezed == specifications ? _self.specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,partNumber: freezed == partNumber ? _self.partNumber : partNumber // ignore: cast_nullable_to_non_nullable
as String?,manufacturer: freezed == manufacturer ? _self.manufacturer : manufacturer // ignore: cast_nullable_to_non_nullable
as String?,compatibleVehicles: null == compatibleVehicles ? _self.compatibleVehicles : compatibleVehicles // ignore: cast_nullable_to_non_nullable
as List<String>,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,viewsCount: null == viewsCount ? _self.viewsCount : viewsCount // ignore: cast_nullable_to_non_nullable
as int,isPart: null == isPart ? _self.isPart : isPart // ignore: cast_nullable_to_non_nullable
as bool,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,auctionStartDate: freezed == auctionStartDate ? _self.auctionStartDate : auctionStartDate // ignore: cast_nullable_to_non_nullable
as DateTime?,auctionEndDate: freezed == auctionEndDate ? _self.auctionEndDate : auctionEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,startingBid: freezed == startingBid ? _self.startingBid : startingBid // ignore: cast_nullable_to_non_nullable
as double?,currentBid: freezed == currentBid ? _self.currentBid : currentBid // ignore: cast_nullable_to_non_nullable
as double?,reservePrice: freezed == reservePrice ? _self.reservePrice : reservePrice // ignore: cast_nullable_to_non_nullable
as double?,bidCount: null == bidCount ? _self.bidCount : bidCount // ignore: cast_nullable_to_non_nullable
as int,highestBidderId: freezed == highestBidderId ? _self.highestBidderId : highestBidderId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,productType: freezed == productType ? _self.productType : productType // ignore: cast_nullable_to_non_nullable
as ProductType?,automotiveType: freezed == automotiveType ? _self.automotiveType : automotiveType // ignore: cast_nullable_to_non_nullable
as AutomotiveType?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductModel].
extension ProductModelPatterns on ProductModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductModel value)  $default,){
final _that = this;
switch (_that) {
case _ProductModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductModel value)?  $default,){
final _that = this;
switch (_that) {
case _ProductModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? nameEn,  String? nameIt,  String? description,  String? descriptionAr,  String? descriptionEn,  String? descriptionIt,  double price,  double? originalPrice,  String categoryId,  ProductCondition condition,  String? subcategoryId,  String? partCategoryId,  bool isFeatured,  bool isAvailable,  bool isActive,  int stockQuantity,  List<String> images,  Map<String, dynamic>? specifications,  String sellerId,  String? brand,  String? model,  String? partNumber,  String? manufacturer,  List<String> compatibleVehicles,  String? location,  int viewsCount,  bool isPart,  int? yearFrom,  int? yearTo,  DateTime? auctionStartDate,  DateTime? auctionEndDate,  double? startingBid,  double? currentBid,  double? reservePrice,  int bidCount,  String? highestBidderId,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  ProductType? productType,  AutomotiveType? automotiveType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.nameIt,_that.description,_that.descriptionAr,_that.descriptionEn,_that.descriptionIt,_that.price,_that.originalPrice,_that.categoryId,_that.condition,_that.subcategoryId,_that.partCategoryId,_that.isFeatured,_that.isAvailable,_that.isActive,_that.stockQuantity,_that.images,_that.specifications,_that.sellerId,_that.brand,_that.model,_that.partNumber,_that.manufacturer,_that.compatibleVehicles,_that.location,_that.viewsCount,_that.isPart,_that.yearFrom,_that.yearTo,_that.auctionStartDate,_that.auctionEndDate,_that.startingBid,_that.currentBid,_that.reservePrice,_that.bidCount,_that.highestBidderId,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.productType,_that.automotiveType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? nameEn,  String? nameIt,  String? description,  String? descriptionAr,  String? descriptionEn,  String? descriptionIt,  double price,  double? originalPrice,  String categoryId,  ProductCondition condition,  String? subcategoryId,  String? partCategoryId,  bool isFeatured,  bool isAvailable,  bool isActive,  int stockQuantity,  List<String> images,  Map<String, dynamic>? specifications,  String sellerId,  String? brand,  String? model,  String? partNumber,  String? manufacturer,  List<String> compatibleVehicles,  String? location,  int viewsCount,  bool isPart,  int? yearFrom,  int? yearTo,  DateTime? auctionStartDate,  DateTime? auctionEndDate,  double? startingBid,  double? currentBid,  double? reservePrice,  int bidCount,  String? highestBidderId,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  ProductType? productType,  AutomotiveType? automotiveType)  $default,) {final _that = this;
switch (_that) {
case _ProductModel():
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.nameIt,_that.description,_that.descriptionAr,_that.descriptionEn,_that.descriptionIt,_that.price,_that.originalPrice,_that.categoryId,_that.condition,_that.subcategoryId,_that.partCategoryId,_that.isFeatured,_that.isAvailable,_that.isActive,_that.stockQuantity,_that.images,_that.specifications,_that.sellerId,_that.brand,_that.model,_that.partNumber,_that.manufacturer,_that.compatibleVehicles,_that.location,_that.viewsCount,_that.isPart,_that.yearFrom,_that.yearTo,_that.auctionStartDate,_that.auctionEndDate,_that.startingBid,_that.currentBid,_that.reservePrice,_that.bidCount,_that.highestBidderId,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.productType,_that.automotiveType);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? nameAr,  String? nameEn,  String? nameIt,  String? description,  String? descriptionAr,  String? descriptionEn,  String? descriptionIt,  double price,  double? originalPrice,  String categoryId,  ProductCondition condition,  String? subcategoryId,  String? partCategoryId,  bool isFeatured,  bool isAvailable,  bool isActive,  int stockQuantity,  List<String> images,  Map<String, dynamic>? specifications,  String sellerId,  String? brand,  String? model,  String? partNumber,  String? manufacturer,  List<String> compatibleVehicles,  String? location,  int viewsCount,  bool isPart,  int? yearFrom,  int? yearTo,  DateTime? auctionStartDate,  DateTime? auctionEndDate,  double? startingBid,  double? currentBid,  double? reservePrice,  int bidCount,  String? highestBidderId,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  ProductType? productType,  AutomotiveType? automotiveType)?  $default,) {final _that = this;
switch (_that) {
case _ProductModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.nameIt,_that.description,_that.descriptionAr,_that.descriptionEn,_that.descriptionIt,_that.price,_that.originalPrice,_that.categoryId,_that.condition,_that.subcategoryId,_that.partCategoryId,_that.isFeatured,_that.isAvailable,_that.isActive,_that.stockQuantity,_that.images,_that.specifications,_that.sellerId,_that.brand,_that.model,_that.partNumber,_that.manufacturer,_that.compatibleVehicles,_that.location,_that.viewsCount,_that.isPart,_that.yearFrom,_that.yearTo,_that.auctionStartDate,_that.auctionEndDate,_that.startingBid,_that.currentBid,_that.reservePrice,_that.bidCount,_that.highestBidderId,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.productType,_that.automotiveType);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductModel implements ProductModel {
  const _ProductModel({required this.id, required this.name, this.nameAr, this.nameEn, this.nameIt, this.description, this.descriptionAr, this.descriptionEn, this.descriptionIt, required this.price, this.originalPrice, required this.categoryId, this.condition = ProductCondition.used, this.subcategoryId, this.partCategoryId, this.isFeatured = false, this.isAvailable = true, this.isActive = true, this.stockQuantity = 0, final  List<String> images = const [], final  Map<String, dynamic>? specifications, required this.sellerId, this.brand, this.model, this.partNumber, this.manufacturer, final  List<String> compatibleVehicles = const [], this.location, this.viewsCount = 0, this.isPart = false, this.yearFrom, this.yearTo, this.auctionStartDate, this.auctionEndDate, this.startingBid, this.currentBid, this.reservePrice, this.bidCount = 0, this.highestBidderId, this.createdAt, this.updatedAt, this.isDeleted = false, this.productType, this.automotiveType}): _images = images,_specifications = specifications,_compatibleVehicles = compatibleVehicles;
  factory _ProductModel.fromJson(Map<String, dynamic> json) => _$ProductModelFromJson(json);

// المعرف الأساسي للمنتج
@override final  String id;
// أسماء المنتج بعدة لغات
@override final  String name;
// الاسم الأساسي
@override final  String? nameAr;
// الاسم بالعربية
@override final  String? nameEn;
// الاسم بالإنجليزية
@override final  String? nameIt;
// الاسم بالإيطالية
// وصف المنتج بعدة لغات
@override final  String? description;
// الوصف الأساسي
@override final  String? descriptionAr;
// الوصف بالعربية
@override final  String? descriptionEn;
// الوصف بالإنجليزية
@override final  String? descriptionIt;
// الوصف بالإيطالية
// معلومات السعر
@override final  double price;
// السعر الحالي
@override final  double? originalPrice;
// السعر الأصلي (قبل التخفيض)
// معلومات التصنيف
@override final  String categoryId;
// معرف الفئة الرئيسية
@override@JsonKey() final  ProductCondition condition;
// حالة المنتج
@override final  String? subcategoryId;
// معرف الفئة الفرعية
@override final  String? partCategoryId;
// معرف فئة قطع الغيار
// حالة المنتج وإعداداته
@override@JsonKey() final  bool isFeatured;
// منتج مميز
@override@JsonKey() final  bool isAvailable;
// متوفر للبيع
@override@JsonKey() final  bool isActive;
// نشط في النظام
@override@JsonKey() final  int stockQuantity;
// كمية المخزون
// الصور والمواصفات
 final  List<String> _images;
// كمية المخزون
// الصور والمواصفات
@override@JsonKey() List<String> get images {
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_images);
}

// قائمة روابط الصور
 final  Map<String, dynamic>? _specifications;
// قائمة روابط الصور
@override Map<String, dynamic>? get specifications {
  final value = _specifications;
  if (value == null) return null;
  if (_specifications is EqualUnmodifiableMapView) return _specifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

// المواصفات التقنية
// معلومات البائع
@override final  String sellerId;
// معرف البائع
// معلومات المنتج التقنية
@override final  String? brand;
// العلامة التجارية
@override final  String? model;
// الموديل
@override final  String? partNumber;
// رقم القطعة
@override final  String? manufacturer;
// الشركة المصنعة
 final  List<String> _compatibleVehicles;
// الشركة المصنعة
@override@JsonKey() List<String> get compatibleVehicles {
  if (_compatibleVehicles is EqualUnmodifiableListView) return _compatibleVehicles;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_compatibleVehicles);
}

// المركبات المتوافقة
@override final  String? location;
// موقع المنتج
// إحصائيات وحالة المنتج
@override@JsonKey() final  int viewsCount;
// عدد المشاهدات
@override@JsonKey() final  bool isPart;
// هل هو قطعة غيار
// سنوات التوافق
@override final  int? yearFrom;
// من سنة
@override final  int? yearTo;
// إلى سنة
// معلومات المزاد
@override final  DateTime? auctionStartDate;
// تاريخ بداية المزاد
@override final  DateTime? auctionEndDate;
// تاريخ نهاية المزاد
@override final  double? startingBid;
// سعر البداية
@override final  double? currentBid;
// العرض الحالي
@override final  double? reservePrice;
// السعر الاحتياطي
@override@JsonKey() final  int bidCount;
// عدد العروض
@override final  String? highestBidderId;
// معرف صاحب أعلى عرض
// طوابع زمنية
@override final  DateTime? createdAt;
// تاريخ الإنشاء
@override final  DateTime? updatedAt;
// تاريخ آخر تحديث
@override@JsonKey() final  bool isDeleted;
// محذوف (soft delete)
// أنواع المنتج
@override final  ProductType? productType;
// نوع المنتج الأساسي
@override final  AutomotiveType? automotiveType;

/// Create a copy of ProductModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductModelCopyWith<_ProductModel> get copyWith => __$ProductModelCopyWithImpl<_ProductModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&(identical(other.nameIt, nameIt) || other.nameIt == nameIt)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.descriptionEn, descriptionEn) || other.descriptionEn == descriptionEn)&&(identical(other.descriptionIt, descriptionIt) || other.descriptionIt == descriptionIt)&&(identical(other.price, price) || other.price == price)&&(identical(other.originalPrice, originalPrice) || other.originalPrice == originalPrice)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.subcategoryId, subcategoryId) || other.subcategoryId == subcategoryId)&&(identical(other.partCategoryId, partCategoryId) || other.partCategoryId == partCategoryId)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.stockQuantity, stockQuantity) || other.stockQuantity == stockQuantity)&&const DeepCollectionEquality().equals(other._images, _images)&&const DeepCollectionEquality().equals(other._specifications, _specifications)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.model, model) || other.model == model)&&(identical(other.partNumber, partNumber) || other.partNumber == partNumber)&&(identical(other.manufacturer, manufacturer) || other.manufacturer == manufacturer)&&const DeepCollectionEquality().equals(other._compatibleVehicles, _compatibleVehicles)&&(identical(other.location, location) || other.location == location)&&(identical(other.viewsCount, viewsCount) || other.viewsCount == viewsCount)&&(identical(other.isPart, isPart) || other.isPart == isPart)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.auctionStartDate, auctionStartDate) || other.auctionStartDate == auctionStartDate)&&(identical(other.auctionEndDate, auctionEndDate) || other.auctionEndDate == auctionEndDate)&&(identical(other.startingBid, startingBid) || other.startingBid == startingBid)&&(identical(other.currentBid, currentBid) || other.currentBid == currentBid)&&(identical(other.reservePrice, reservePrice) || other.reservePrice == reservePrice)&&(identical(other.bidCount, bidCount) || other.bidCount == bidCount)&&(identical(other.highestBidderId, highestBidderId) || other.highestBidderId == highestBidderId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.productType, productType) || other.productType == productType)&&(identical(other.automotiveType, automotiveType) || other.automotiveType == automotiveType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,nameAr,nameEn,nameIt,description,descriptionAr,descriptionEn,descriptionIt,price,originalPrice,categoryId,condition,subcategoryId,partCategoryId,isFeatured,isAvailable,isActive,stockQuantity,const DeepCollectionEquality().hash(_images),const DeepCollectionEquality().hash(_specifications),sellerId,brand,model,partNumber,manufacturer,const DeepCollectionEquality().hash(_compatibleVehicles),location,viewsCount,isPart,yearFrom,yearTo,auctionStartDate,auctionEndDate,startingBid,currentBid,reservePrice,bidCount,highestBidderId,createdAt,updatedAt,isDeleted,productType,automotiveType]);

@override
String toString() {
  return 'ProductModel(id: $id, name: $name, nameAr: $nameAr, nameEn: $nameEn, nameIt: $nameIt, description: $description, descriptionAr: $descriptionAr, descriptionEn: $descriptionEn, descriptionIt: $descriptionIt, price: $price, originalPrice: $originalPrice, categoryId: $categoryId, condition: $condition, subcategoryId: $subcategoryId, partCategoryId: $partCategoryId, isFeatured: $isFeatured, isAvailable: $isAvailable, isActive: $isActive, stockQuantity: $stockQuantity, images: $images, specifications: $specifications, sellerId: $sellerId, brand: $brand, model: $model, partNumber: $partNumber, manufacturer: $manufacturer, compatibleVehicles: $compatibleVehicles, location: $location, viewsCount: $viewsCount, isPart: $isPart, yearFrom: $yearFrom, yearTo: $yearTo, auctionStartDate: $auctionStartDate, auctionEndDate: $auctionEndDate, startingBid: $startingBid, currentBid: $currentBid, reservePrice: $reservePrice, bidCount: $bidCount, highestBidderId: $highestBidderId, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, productType: $productType, automotiveType: $automotiveType)';
}


}

/// @nodoc
abstract mixin class _$ProductModelCopyWith<$Res> implements $ProductModelCopyWith<$Res> {
  factory _$ProductModelCopyWith(_ProductModel value, $Res Function(_ProductModel) _then) = __$ProductModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? nameAr, String? nameEn, String? nameIt, String? description, String? descriptionAr, String? descriptionEn, String? descriptionIt, double price, double? originalPrice, String categoryId, ProductCondition condition, String? subcategoryId, String? partCategoryId, bool isFeatured, bool isAvailable, bool isActive, int stockQuantity, List<String> images, Map<String, dynamic>? specifications, String sellerId, String? brand, String? model, String? partNumber, String? manufacturer, List<String> compatibleVehicles, String? location, int viewsCount, bool isPart, int? yearFrom, int? yearTo, DateTime? auctionStartDate, DateTime? auctionEndDate, double? startingBid, double? currentBid, double? reservePrice, int bidCount, String? highestBidderId, DateTime? createdAt, DateTime? updatedAt, bool isDeleted, ProductType? productType, AutomotiveType? automotiveType
});




}
/// @nodoc
class __$ProductModelCopyWithImpl<$Res>
    implements _$ProductModelCopyWith<$Res> {
  __$ProductModelCopyWithImpl(this._self, this._then);

  final _ProductModel _self;
  final $Res Function(_ProductModel) _then;

/// Create a copy of ProductModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? nameEn = freezed,Object? nameIt = freezed,Object? description = freezed,Object? descriptionAr = freezed,Object? descriptionEn = freezed,Object? descriptionIt = freezed,Object? price = null,Object? originalPrice = freezed,Object? categoryId = null,Object? condition = null,Object? subcategoryId = freezed,Object? partCategoryId = freezed,Object? isFeatured = null,Object? isAvailable = null,Object? isActive = null,Object? stockQuantity = null,Object? images = null,Object? specifications = freezed,Object? sellerId = null,Object? brand = freezed,Object? model = freezed,Object? partNumber = freezed,Object? manufacturer = freezed,Object? compatibleVehicles = null,Object? location = freezed,Object? viewsCount = null,Object? isPart = null,Object? yearFrom = freezed,Object? yearTo = freezed,Object? auctionStartDate = freezed,Object? auctionEndDate = freezed,Object? startingBid = freezed,Object? currentBid = freezed,Object? reservePrice = freezed,Object? bidCount = null,Object? highestBidderId = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? productType = freezed,Object? automotiveType = freezed,}) {
  return _then(_ProductModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,nameEn: freezed == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String?,nameIt: freezed == nameIt ? _self.nameIt : nameIt // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,descriptionEn: freezed == descriptionEn ? _self.descriptionEn : descriptionEn // ignore: cast_nullable_to_non_nullable
as String?,descriptionIt: freezed == descriptionIt ? _self.descriptionIt : descriptionIt // ignore: cast_nullable_to_non_nullable
as String?,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,originalPrice: freezed == originalPrice ? _self.originalPrice : originalPrice // ignore: cast_nullable_to_non_nullable
as double?,categoryId: null == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as ProductCondition,subcategoryId: freezed == subcategoryId ? _self.subcategoryId : subcategoryId // ignore: cast_nullable_to_non_nullable
as String?,partCategoryId: freezed == partCategoryId ? _self.partCategoryId : partCategoryId // ignore: cast_nullable_to_non_nullable
as String?,isFeatured: null == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,stockQuantity: null == stockQuantity ? _self.stockQuantity : stockQuantity // ignore: cast_nullable_to_non_nullable
as int,images: null == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>,specifications: freezed == specifications ? _self._specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,partNumber: freezed == partNumber ? _self.partNumber : partNumber // ignore: cast_nullable_to_non_nullable
as String?,manufacturer: freezed == manufacturer ? _self.manufacturer : manufacturer // ignore: cast_nullable_to_non_nullable
as String?,compatibleVehicles: null == compatibleVehicles ? _self._compatibleVehicles : compatibleVehicles // ignore: cast_nullable_to_non_nullable
as List<String>,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,viewsCount: null == viewsCount ? _self.viewsCount : viewsCount // ignore: cast_nullable_to_non_nullable
as int,isPart: null == isPart ? _self.isPart : isPart // ignore: cast_nullable_to_non_nullable
as bool,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,auctionStartDate: freezed == auctionStartDate ? _self.auctionStartDate : auctionStartDate // ignore: cast_nullable_to_non_nullable
as DateTime?,auctionEndDate: freezed == auctionEndDate ? _self.auctionEndDate : auctionEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,startingBid: freezed == startingBid ? _self.startingBid : startingBid // ignore: cast_nullable_to_non_nullable
as double?,currentBid: freezed == currentBid ? _self.currentBid : currentBid // ignore: cast_nullable_to_non_nullable
as double?,reservePrice: freezed == reservePrice ? _self.reservePrice : reservePrice // ignore: cast_nullable_to_non_nullable
as double?,bidCount: null == bidCount ? _self.bidCount : bidCount // ignore: cast_nullable_to_non_nullable
as int,highestBidderId: freezed == highestBidderId ? _self.highestBidderId : highestBidderId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,productType: freezed == productType ? _self.productType : productType // ignore: cast_nullable_to_non_nullable
as ProductType?,automotiveType: freezed == automotiveType ? _self.automotiveType : automotiveType // ignore: cast_nullable_to_non_nullable
as AutomotiveType?,
  ));
}


}

// dart format on
