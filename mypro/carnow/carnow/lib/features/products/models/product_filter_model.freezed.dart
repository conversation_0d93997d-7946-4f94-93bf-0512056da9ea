// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductFilter {

 String? get searchQuery; String? get categoryId; String? get subcategoryId; Map<String, dynamic>? get specFilters; double? get minPrice; double? get maxPrice; double? get minRating; bool? get isAvailable; bool? get isFeatured; bool? get hasDiscount; List<String>? get tags; String? get sellerId; String? get sortBy;
/// Create a copy of ProductFilter
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductFilterCopyWith<ProductFilter> get copyWith => _$ProductFilterCopyWithImpl<ProductFilter>(this as ProductFilter, _$identity);

  /// Serializes this ProductFilter to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductFilter&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.subcategoryId, subcategoryId) || other.subcategoryId == subcategoryId)&&const DeepCollectionEquality().equals(other.specFilters, specFilters)&&(identical(other.minPrice, minPrice) || other.minPrice == minPrice)&&(identical(other.maxPrice, maxPrice) || other.maxPrice == maxPrice)&&(identical(other.minRating, minRating) || other.minRating == minRating)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.hasDiscount, hasDiscount) || other.hasDiscount == hasDiscount)&&const DeepCollectionEquality().equals(other.tags, tags)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.sortBy, sortBy) || other.sortBy == sortBy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,searchQuery,categoryId,subcategoryId,const DeepCollectionEquality().hash(specFilters),minPrice,maxPrice,minRating,isAvailable,isFeatured,hasDiscount,const DeepCollectionEquality().hash(tags),sellerId,sortBy);

@override
String toString() {
  return 'ProductFilter(searchQuery: $searchQuery, categoryId: $categoryId, subcategoryId: $subcategoryId, specFilters: $specFilters, minPrice: $minPrice, maxPrice: $maxPrice, minRating: $minRating, isAvailable: $isAvailable, isFeatured: $isFeatured, hasDiscount: $hasDiscount, tags: $tags, sellerId: $sellerId, sortBy: $sortBy)';
}


}

/// @nodoc
abstract mixin class $ProductFilterCopyWith<$Res>  {
  factory $ProductFilterCopyWith(ProductFilter value, $Res Function(ProductFilter) _then) = _$ProductFilterCopyWithImpl;
@useResult
$Res call({
 String? searchQuery, String? categoryId, String? subcategoryId, Map<String, dynamic>? specFilters, double? minPrice, double? maxPrice, double? minRating, bool? isAvailable, bool? isFeatured, bool? hasDiscount, List<String>? tags, String? sellerId, String? sortBy
});




}
/// @nodoc
class _$ProductFilterCopyWithImpl<$Res>
    implements $ProductFilterCopyWith<$Res> {
  _$ProductFilterCopyWithImpl(this._self, this._then);

  final ProductFilter _self;
  final $Res Function(ProductFilter) _then;

/// Create a copy of ProductFilter
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? searchQuery = freezed,Object? categoryId = freezed,Object? subcategoryId = freezed,Object? specFilters = freezed,Object? minPrice = freezed,Object? maxPrice = freezed,Object? minRating = freezed,Object? isAvailable = freezed,Object? isFeatured = freezed,Object? hasDiscount = freezed,Object? tags = freezed,Object? sellerId = freezed,Object? sortBy = freezed,}) {
  return _then(_self.copyWith(
searchQuery: freezed == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String?,categoryId: freezed == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String?,subcategoryId: freezed == subcategoryId ? _self.subcategoryId : subcategoryId // ignore: cast_nullable_to_non_nullable
as String?,specFilters: freezed == specFilters ? _self.specFilters : specFilters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,minPrice: freezed == minPrice ? _self.minPrice : minPrice // ignore: cast_nullable_to_non_nullable
as double?,maxPrice: freezed == maxPrice ? _self.maxPrice : maxPrice // ignore: cast_nullable_to_non_nullable
as double?,minRating: freezed == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double?,isAvailable: freezed == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool?,isFeatured: freezed == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool?,hasDiscount: freezed == hasDiscount ? _self.hasDiscount : hasDiscount // ignore: cast_nullable_to_non_nullable
as bool?,tags: freezed == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,sortBy: freezed == sortBy ? _self.sortBy : sortBy // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductFilter].
extension ProductFilterPatterns on ProductFilter {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductFilter value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductFilter() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductFilter value)  $default,){
final _that = this;
switch (_that) {
case _ProductFilter():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductFilter value)?  $default,){
final _that = this;
switch (_that) {
case _ProductFilter() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? searchQuery,  String? categoryId,  String? subcategoryId,  Map<String, dynamic>? specFilters,  double? minPrice,  double? maxPrice,  double? minRating,  bool? isAvailable,  bool? isFeatured,  bool? hasDiscount,  List<String>? tags,  String? sellerId,  String? sortBy)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductFilter() when $default != null:
return $default(_that.searchQuery,_that.categoryId,_that.subcategoryId,_that.specFilters,_that.minPrice,_that.maxPrice,_that.minRating,_that.isAvailable,_that.isFeatured,_that.hasDiscount,_that.tags,_that.sellerId,_that.sortBy);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? searchQuery,  String? categoryId,  String? subcategoryId,  Map<String, dynamic>? specFilters,  double? minPrice,  double? maxPrice,  double? minRating,  bool? isAvailable,  bool? isFeatured,  bool? hasDiscount,  List<String>? tags,  String? sellerId,  String? sortBy)  $default,) {final _that = this;
switch (_that) {
case _ProductFilter():
return $default(_that.searchQuery,_that.categoryId,_that.subcategoryId,_that.specFilters,_that.minPrice,_that.maxPrice,_that.minRating,_that.isAvailable,_that.isFeatured,_that.hasDiscount,_that.tags,_that.sellerId,_that.sortBy);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? searchQuery,  String? categoryId,  String? subcategoryId,  Map<String, dynamic>? specFilters,  double? minPrice,  double? maxPrice,  double? minRating,  bool? isAvailable,  bool? isFeatured,  bool? hasDiscount,  List<String>? tags,  String? sellerId,  String? sortBy)?  $default,) {final _that = this;
switch (_that) {
case _ProductFilter() when $default != null:
return $default(_that.searchQuery,_that.categoryId,_that.subcategoryId,_that.specFilters,_that.minPrice,_that.maxPrice,_that.minRating,_that.isAvailable,_that.isFeatured,_that.hasDiscount,_that.tags,_that.sellerId,_that.sortBy);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductFilter extends ProductFilter {
  const _ProductFilter({this.searchQuery, this.categoryId, this.subcategoryId, final  Map<String, dynamic>? specFilters, this.minPrice, this.maxPrice, this.minRating, this.isAvailable, this.isFeatured, this.hasDiscount, final  List<String>? tags, this.sellerId, this.sortBy}): _specFilters = specFilters,_tags = tags,super._();
  factory _ProductFilter.fromJson(Map<String, dynamic> json) => _$ProductFilterFromJson(json);

@override final  String? searchQuery;
@override final  String? categoryId;
@override final  String? subcategoryId;
 final  Map<String, dynamic>? _specFilters;
@override Map<String, dynamic>? get specFilters {
  final value = _specFilters;
  if (value == null) return null;
  if (_specFilters is EqualUnmodifiableMapView) return _specFilters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  double? minPrice;
@override final  double? maxPrice;
@override final  double? minRating;
@override final  bool? isAvailable;
@override final  bool? isFeatured;
@override final  bool? hasDiscount;
 final  List<String>? _tags;
@override List<String>? get tags {
  final value = _tags;
  if (value == null) return null;
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? sellerId;
@override final  String? sortBy;

/// Create a copy of ProductFilter
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductFilterCopyWith<_ProductFilter> get copyWith => __$ProductFilterCopyWithImpl<_ProductFilter>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductFilterToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductFilter&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.subcategoryId, subcategoryId) || other.subcategoryId == subcategoryId)&&const DeepCollectionEquality().equals(other._specFilters, _specFilters)&&(identical(other.minPrice, minPrice) || other.minPrice == minPrice)&&(identical(other.maxPrice, maxPrice) || other.maxPrice == maxPrice)&&(identical(other.minRating, minRating) || other.minRating == minRating)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.hasDiscount, hasDiscount) || other.hasDiscount == hasDiscount)&&const DeepCollectionEquality().equals(other._tags, _tags)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.sortBy, sortBy) || other.sortBy == sortBy));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,searchQuery,categoryId,subcategoryId,const DeepCollectionEquality().hash(_specFilters),minPrice,maxPrice,minRating,isAvailable,isFeatured,hasDiscount,const DeepCollectionEquality().hash(_tags),sellerId,sortBy);

@override
String toString() {
  return 'ProductFilter(searchQuery: $searchQuery, categoryId: $categoryId, subcategoryId: $subcategoryId, specFilters: $specFilters, minPrice: $minPrice, maxPrice: $maxPrice, minRating: $minRating, isAvailable: $isAvailable, isFeatured: $isFeatured, hasDiscount: $hasDiscount, tags: $tags, sellerId: $sellerId, sortBy: $sortBy)';
}


}

/// @nodoc
abstract mixin class _$ProductFilterCopyWith<$Res> implements $ProductFilterCopyWith<$Res> {
  factory _$ProductFilterCopyWith(_ProductFilter value, $Res Function(_ProductFilter) _then) = __$ProductFilterCopyWithImpl;
@override @useResult
$Res call({
 String? searchQuery, String? categoryId, String? subcategoryId, Map<String, dynamic>? specFilters, double? minPrice, double? maxPrice, double? minRating, bool? isAvailable, bool? isFeatured, bool? hasDiscount, List<String>? tags, String? sellerId, String? sortBy
});




}
/// @nodoc
class __$ProductFilterCopyWithImpl<$Res>
    implements _$ProductFilterCopyWith<$Res> {
  __$ProductFilterCopyWithImpl(this._self, this._then);

  final _ProductFilter _self;
  final $Res Function(_ProductFilter) _then;

/// Create a copy of ProductFilter
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? searchQuery = freezed,Object? categoryId = freezed,Object? subcategoryId = freezed,Object? specFilters = freezed,Object? minPrice = freezed,Object? maxPrice = freezed,Object? minRating = freezed,Object? isAvailable = freezed,Object? isFeatured = freezed,Object? hasDiscount = freezed,Object? tags = freezed,Object? sellerId = freezed,Object? sortBy = freezed,}) {
  return _then(_ProductFilter(
searchQuery: freezed == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String?,categoryId: freezed == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String?,subcategoryId: freezed == subcategoryId ? _self.subcategoryId : subcategoryId // ignore: cast_nullable_to_non_nullable
as String?,specFilters: freezed == specFilters ? _self._specFilters : specFilters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,minPrice: freezed == minPrice ? _self.minPrice : minPrice // ignore: cast_nullable_to_non_nullable
as double?,maxPrice: freezed == maxPrice ? _self.maxPrice : maxPrice // ignore: cast_nullable_to_non_nullable
as double?,minRating: freezed == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double?,isAvailable: freezed == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool?,isFeatured: freezed == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool?,hasDiscount: freezed == hasDiscount ? _self.hasDiscount : hasDiscount // ignore: cast_nullable_to_non_nullable
as bool?,tags: freezed == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,sortBy: freezed == sortBy ? _self.sortBy : sortBy // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
