// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerProfileHash() => r'c4983ccd44afae6e904292218a284038a95b17b4';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Get seller profile from Go backend
/// ENDPOINT: GET /api/v1/seller/profile/{sellerId}
///
/// Copied from [sellerProfile].
@ProviderFor(sellerProfile)
const sellerProfileProvider = SellerProfileFamily();

/// Get seller profile from Go backend
/// ENDPOINT: GET /api/v1/seller/profile/{sellerId}
///
/// Copied from [sellerProfile].
class SellerProfileFamily extends Family<AsyncValue<Map<String, dynamic>?>> {
  /// Get seller profile from Go backend
  /// ENDPOINT: GET /api/v1/seller/profile/{sellerId}
  ///
  /// Copied from [sellerProfile].
  const SellerProfileFamily();

  /// Get seller profile from Go backend
  /// ENDPOINT: GET /api/v1/seller/profile/{sellerId}
  ///
  /// Copied from [sellerProfile].
  SellerProfileProvider call(String sellerId) {
    return SellerProfileProvider(sellerId);
  }

  @override
  SellerProfileProvider getProviderOverride(
    covariant SellerProfileProvider provider,
  ) {
    return call(provider.sellerId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sellerProfileProvider';
}

/// Get seller profile from Go backend
/// ENDPOINT: GET /api/v1/seller/profile/{sellerId}
///
/// Copied from [sellerProfile].
class SellerProfileProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>?> {
  /// Get seller profile from Go backend
  /// ENDPOINT: GET /api/v1/seller/profile/{sellerId}
  ///
  /// Copied from [sellerProfile].
  SellerProfileProvider(String sellerId)
    : this._internal(
        (ref) => sellerProfile(ref as SellerProfileRef, sellerId),
        from: sellerProfileProvider,
        name: r'sellerProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sellerProfileHash,
        dependencies: SellerProfileFamily._dependencies,
        allTransitiveDependencies:
            SellerProfileFamily._allTransitiveDependencies,
        sellerId: sellerId,
      );

  SellerProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sellerId,
  }) : super.internal();

  final String sellerId;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>?> Function(SellerProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SellerProfileProvider._internal(
        (ref) => create(ref as SellerProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sellerId: sellerId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>?> createElement() {
    return _SellerProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SellerProfileProvider && other.sellerId == sellerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sellerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SellerProfileRef on AutoDisposeFutureProviderRef<Map<String, dynamic>?> {
  /// The parameter `sellerId` of this provider.
  String get sellerId;
}

class _SellerProfileProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>?>
    with SellerProfileRef {
  _SellerProfileProviderElement(super.provider);

  @override
  String get sellerId => (origin as SellerProfileProvider).sellerId;
}

String _$sellerProductsHash() => r'3ade4a1de430c4e69f75ae22369ed836039c91f0';

/// Get seller's products from Go backend
/// ENDPOINT: GET /api/v1/seller/products/{sellerId}?page={page}&limit={limit}
///
/// Copied from [sellerProducts].
@ProviderFor(sellerProducts)
const sellerProductsProvider = SellerProductsFamily();

/// Get seller's products from Go backend
/// ENDPOINT: GET /api/v1/seller/products/{sellerId}?page={page}&limit={limit}
///
/// Copied from [sellerProducts].
class SellerProductsFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// Get seller's products from Go backend
  /// ENDPOINT: GET /api/v1/seller/products/{sellerId}?page={page}&limit={limit}
  ///
  /// Copied from [sellerProducts].
  const SellerProductsFamily();

  /// Get seller's products from Go backend
  /// ENDPOINT: GET /api/v1/seller/products/{sellerId}?page={page}&limit={limit}
  ///
  /// Copied from [sellerProducts].
  SellerProductsProvider call(String sellerId) {
    return SellerProductsProvider(sellerId);
  }

  @override
  SellerProductsProvider getProviderOverride(
    covariant SellerProductsProvider provider,
  ) {
    return call(provider.sellerId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sellerProductsProvider';
}

/// Get seller's products from Go backend
/// ENDPOINT: GET /api/v1/seller/products/{sellerId}?page={page}&limit={limit}
///
/// Copied from [sellerProducts].
class SellerProductsProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// Get seller's products from Go backend
  /// ENDPOINT: GET /api/v1/seller/products/{sellerId}?page={page}&limit={limit}
  ///
  /// Copied from [sellerProducts].
  SellerProductsProvider(String sellerId)
    : this._internal(
        (ref) => sellerProducts(ref as SellerProductsRef, sellerId),
        from: sellerProductsProvider,
        name: r'sellerProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sellerProductsHash,
        dependencies: SellerProductsFamily._dependencies,
        allTransitiveDependencies:
            SellerProductsFamily._allTransitiveDependencies,
        sellerId: sellerId,
      );

  SellerProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sellerId,
  }) : super.internal();

  final String sellerId;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(SellerProductsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SellerProductsProvider._internal(
        (ref) => create(ref as SellerProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sellerId: sellerId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _SellerProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SellerProductsProvider && other.sellerId == sellerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sellerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SellerProductsRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `sellerId` of this provider.
  String get sellerId;
}

class _SellerProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with SellerProductsRef {
  _SellerProductsProviderElement(super.provider);

  @override
  String get sellerId => (origin as SellerProductsProvider).sellerId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
