// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productApiServiceHash() => r'8de036363a8bbdf9a4ac5d5af0517b044099cf37';

/// Provider for ProductApiService
///
/// Copied from [productApiService].
@ProviderFor(productApiService)
final productApiServiceProvider =
    AutoDisposeProvider<ProductApiService>.internal(
      productApiService,
      name: r'productApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$productApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProductApiServiceRef = AutoDisposeProviderRef<ProductApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
