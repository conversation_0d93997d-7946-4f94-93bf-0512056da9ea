// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recommendations_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$personalizedRecommendationsHash() =>
    r'13ac1af6b1936da19e444620c6ca4c67f625d276';

/// Personalized recommendations - Clean implementation using Go backend
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [personalizedRecommendations].
@ProviderFor(personalizedRecommendations)
final personalizedRecommendationsProvider =
    AutoDisposeFutureProvider<List<RecommendationGroup>>.internal(
      personalizedRecommendations,
      name: r'personalizedRecommendationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$personalizedRecommendationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PersonalizedRecommendationsRef =
    AutoDisposeFutureProviderRef<List<RecommendationGroup>>;
String _$productRecommendationsHash() =>
    r'ffd023e2c2990113f5fe524c1fd4a310d30e8d34';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for product-specific recommendations - Clean implementation
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productRecommendations].
@ProviderFor(productRecommendations)
const productRecommendationsProvider = ProductRecommendationsFamily();

/// Provider for product-specific recommendations - Clean implementation
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productRecommendations].
class ProductRecommendationsFamily
    extends Family<AsyncValue<List<RecommendationModel>>> {
  /// Provider for product-specific recommendations - Clean implementation
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productRecommendations].
  const ProductRecommendationsFamily();

  /// Provider for product-specific recommendations - Clean implementation
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productRecommendations].
  ProductRecommendationsProvider call(String productId) {
    return ProductRecommendationsProvider(productId);
  }

  @override
  ProductRecommendationsProvider getProviderOverride(
    covariant ProductRecommendationsProvider provider,
  ) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productRecommendationsProvider';
}

/// Provider for product-specific recommendations - Clean implementation
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productRecommendations].
class ProductRecommendationsProvider
    extends AutoDisposeFutureProvider<List<RecommendationModel>> {
  /// Provider for product-specific recommendations - Clean implementation
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productRecommendations].
  ProductRecommendationsProvider(String productId)
    : this._internal(
        (ref) =>
            productRecommendations(ref as ProductRecommendationsRef, productId),
        from: productRecommendationsProvider,
        name: r'productRecommendationsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productRecommendationsHash,
        dependencies: ProductRecommendationsFamily._dependencies,
        allTransitiveDependencies:
            ProductRecommendationsFamily._allTransitiveDependencies,
        productId: productId,
      );

  ProductRecommendationsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  Override overrideWith(
    FutureOr<List<RecommendationModel>> Function(
      ProductRecommendationsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductRecommendationsProvider._internal(
        (ref) => create(ref as ProductRecommendationsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<RecommendationModel>> createElement() {
    return _ProductRecommendationsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductRecommendationsProvider &&
        other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductRecommendationsRef
    on AutoDisposeFutureProviderRef<List<RecommendationModel>> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _ProductRecommendationsProviderElement
    extends AutoDisposeFutureProviderElement<List<RecommendationModel>>
    with ProductRecommendationsRef {
  _ProductRecommendationsProviderElement(super.provider);

  @override
  String get productId => (origin as ProductRecommendationsProvider).productId;
}

String _$trendingRecommendationsHash() =>
    r'4db573c6dfb0a28c50c67d209147af604053550f';

/// Provider for trending recommendations - Clean implementation
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [trendingRecommendations].
@ProviderFor(trendingRecommendations)
final trendingRecommendationsProvider =
    AutoDisposeFutureProvider<List<RecommendationModel>>.internal(
      trendingRecommendations,
      name: r'trendingRecommendationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$trendingRecommendationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TrendingRecommendationsRef =
    AutoDisposeFutureProviderRef<List<RecommendationModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
