// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_stream_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authStreamHash() => r'6ce1a5b37d302309a2579fbff02e7de2bdbe49e4';

/// Stream provider for authentication state changes
///
/// Copied from [authStream].
@ProviderFor(authStream)
final authStreamProvider = AutoDisposeStreamProvider<AuthState>.internal(
  authStream,
  name: r'authStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthStreamRef = AutoDisposeStreamProviderRef<AuthState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
