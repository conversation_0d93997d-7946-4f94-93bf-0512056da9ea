// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checkout_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CheckoutRequestModel _$CheckoutRequestModelFromJson(
  Map<String, dynamic> json,
) => _CheckoutRequestModel(
  cartId: json['cartId'] as String,
  shippingAddress: ShippingAddressModel.fromJson(
    json['shippingAddress'] as Map<String, dynamic>,
  ),
  paymentMethodId: json['paymentMethodId'] as String,
  currency: json['currency'] as String? ?? 'USD',
  metadata:
      (json['metadata'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ) ??
      const {},
);

Map<String, dynamic> _$CheckoutRequestModelToJson(
  _CheckoutRequestModel instance,
) => <String, dynamic>{
  'cartId': instance.cartId,
  'shippingAddress': instance.shippingAddress,
  'paymentMethodId': instance.paymentMethodId,
  'currency': instance.currency,
  'metadata': instance.metadata,
};

_ShippingAddressModel _$ShippingAddressModelFromJson(
  Map<String, dynamic> json,
) => _ShippingAddressModel(
  name: json['name'] as String,
  phone: json['phone'] as String,
  addressLine: json['addressLine'] as String,
  city: json['city'] as String,
  state: json['state'] as String,
  postalCode: json['postalCode'] as String,
  country: json['country'] as String? ?? 'SA',
);

Map<String, dynamic> _$ShippingAddressModelToJson(
  _ShippingAddressModel instance,
) => <String, dynamic>{
  'name': instance.name,
  'phone': instance.phone,
  'addressLine': instance.addressLine,
  'city': instance.city,
  'state': instance.state,
  'postalCode': instance.postalCode,
  'country': instance.country,
};

_CheckoutResultModel _$CheckoutResultModelFromJson(Map<String, dynamic> json) =>
    _CheckoutResultModel(
      success: json['success'] as bool,
      orderId: json['orderId'] as String?,
      paymentIntentId: json['paymentIntentId'] as String?,
      clientSecret: json['clientSecret'] as String?,
      status: json['status'] as String,
      message: json['message'] as String,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$CheckoutResultModelToJson(
  _CheckoutResultModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'orderId': instance.orderId,
  'paymentIntentId': instance.paymentIntentId,
  'clientSecret': instance.clientSecret,
  'status': instance.status,
  'message': instance.message,
  'error': instance.error,
};

_PaymentMethodModel _$PaymentMethodModelFromJson(Map<String, dynamic> json) =>
    _PaymentMethodModel(
      id: json['id'] as String,
      type: json['type'] as String,
      displayName: json['displayName'] as String,
      last4: json['last4'] as String?,
      brand: json['brand'] as String?,
      expiryMonth: json['expiryMonth'] as String?,
      expiryYear: json['expiryYear'] as String?,
      isEnabled: json['isEnabled'] as bool? ?? true,
      isDefault: json['isDefault'] as bool? ?? false,
    );

Map<String, dynamic> _$PaymentMethodModelToJson(_PaymentMethodModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'displayName': instance.displayName,
      'last4': instance.last4,
      'brand': instance.brand,
      'expiryMonth': instance.expiryMonth,
      'expiryYear': instance.expiryYear,
      'isEnabled': instance.isEnabled,
      'isDefault': instance.isDefault,
    };

_OrderSummaryModel _$OrderSummaryModelFromJson(Map<String, dynamic> json) =>
    _OrderSummaryModel(
      subtotal: (json['subtotal'] as num).toDouble(),
      tax: (json['tax'] as num).toDouble(),
      shipping: (json['shipping'] as num).toDouble(),
      total: (json['total'] as num).toDouble(),
      currency: json['currency'] as String,
      itemCount: (json['itemCount'] as num).toInt(),
      items:
          (json['items'] as List<dynamic>?)
              ?.map(
                (e) =>
                    OrderItemSummaryModel.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
    );

Map<String, dynamic> _$OrderSummaryModelToJson(_OrderSummaryModel instance) =>
    <String, dynamic>{
      'subtotal': instance.subtotal,
      'tax': instance.tax,
      'shipping': instance.shipping,
      'total': instance.total,
      'currency': instance.currency,
      'itemCount': instance.itemCount,
      'items': instance.items,
    };

_OrderItemSummaryModel _$OrderItemSummaryModelFromJson(
  Map<String, dynamic> json,
) => _OrderItemSummaryModel(
  productId: json['productId'] as String,
  productName: json['productName'] as String,
  productImage: json['productImage'] as String,
  quantity: (json['quantity'] as num).toInt(),
  price: (json['price'] as num).toDouble(),
  total: (json['total'] as num).toDouble(),
  category: json['category'] as String?,
  brand: json['brand'] as String?,
);

Map<String, dynamic> _$OrderItemSummaryModelToJson(
  _OrderItemSummaryModel instance,
) => <String, dynamic>{
  'productId': instance.productId,
  'productName': instance.productName,
  'productImage': instance.productImage,
  'quantity': instance.quantity,
  'price': instance.price,
  'total': instance.total,
  'category': instance.category,
  'brand': instance.brand,
};

_CheckoutStateModel _$CheckoutStateModelFromJson(Map<String, dynamic> json) =>
    _CheckoutStateModel(
      currentStep:
          $enumDecodeNullable(_$CheckoutStepEnumMap, json['currentStep']) ??
          CheckoutStep.shipping,
      shippingAddress: json['shippingAddress'] == null
          ? null
          : ShippingAddressModel.fromJson(
              json['shippingAddress'] as Map<String, dynamic>,
            ),
      paymentMethod: json['paymentMethod'] == null
          ? null
          : PaymentMethodModel.fromJson(
              json['paymentMethod'] as Map<String, dynamic>,
            ),
      orderSummary: json['orderSummary'] == null
          ? null
          : OrderSummaryModel.fromJson(
              json['orderSummary'] as Map<String, dynamic>,
            ),
      result: json['result'] == null
          ? null
          : CheckoutResultModel.fromJson(
              json['result'] as Map<String, dynamic>,
            ),
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$CheckoutStateModelToJson(_CheckoutStateModel instance) =>
    <String, dynamic>{
      'currentStep': _$CheckoutStepEnumMap[instance.currentStep]!,
      'shippingAddress': instance.shippingAddress,
      'paymentMethod': instance.paymentMethod,
      'orderSummary': instance.orderSummary,
      'result': instance.result,
      'isLoading': instance.isLoading,
      'error': instance.error,
    };

const _$CheckoutStepEnumMap = {
  CheckoutStep.shipping: 'shipping',
  CheckoutStep.payment: 'payment',
  CheckoutStep.review: 'review',
  CheckoutStep.processing: 'processing',
  CheckoutStep.completed: 'completed',
  CheckoutStep.failed: 'failed',
};
