// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'store_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$StoreModel {

 int get id; int get sellerId; String get storeName; String? get storeNameAr; String? get storeDescription; String? get storeDescriptionAr; String? get storeLogoUrl; String? get storeBannerUrl; String? get storeSlug;// Contact info
 String? get contactPhone; String? get contactEmail; String? get contactWhatsapp;// Location
 String? get storeAddress; String? get storeCity; String? get storeCountry; double? get latitude; double? get longitude;// Settings
 bool get isActive; bool get isFeatured; bool get allowNegotiation; double get minOrderAmount; String? get shippingPolicy; String? get returnPolicy;// Statistics
 int get totalProducts; int get totalOrders; double get totalSales; double get averageRating; int get totalReviews;// Timestamps
 DateTime? get createdAt; DateTime? get updatedAt;// Related data
 List<StoreCategoryModel>? get categories; List<StoreSectionModel>? get sections; List<StoreWorkingHourModel>? get workingHours; int get followersCount; bool get isFollowing;
/// Create a copy of StoreModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoreModelCopyWith<StoreModel> get copyWith => _$StoreModelCopyWithImpl<StoreModel>(this as StoreModel, _$identity);

  /// Serializes this StoreModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoreModel&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.storeNameAr, storeNameAr) || other.storeNameAr == storeNameAr)&&(identical(other.storeDescription, storeDescription) || other.storeDescription == storeDescription)&&(identical(other.storeDescriptionAr, storeDescriptionAr) || other.storeDescriptionAr == storeDescriptionAr)&&(identical(other.storeLogoUrl, storeLogoUrl) || other.storeLogoUrl == storeLogoUrl)&&(identical(other.storeBannerUrl, storeBannerUrl) || other.storeBannerUrl == storeBannerUrl)&&(identical(other.storeSlug, storeSlug) || other.storeSlug == storeSlug)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.contactWhatsapp, contactWhatsapp) || other.contactWhatsapp == contactWhatsapp)&&(identical(other.storeAddress, storeAddress) || other.storeAddress == storeAddress)&&(identical(other.storeCity, storeCity) || other.storeCity == storeCity)&&(identical(other.storeCountry, storeCountry) || other.storeCountry == storeCountry)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.allowNegotiation, allowNegotiation) || other.allowNegotiation == allowNegotiation)&&(identical(other.minOrderAmount, minOrderAmount) || other.minOrderAmount == minOrderAmount)&&(identical(other.shippingPolicy, shippingPolicy) || other.shippingPolicy == shippingPolicy)&&(identical(other.returnPolicy, returnPolicy) || other.returnPolicy == returnPolicy)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.averageRating, averageRating) || other.averageRating == averageRating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other.categories, categories)&&const DeepCollectionEquality().equals(other.sections, sections)&&const DeepCollectionEquality().equals(other.workingHours, workingHours)&&(identical(other.followersCount, followersCount) || other.followersCount == followersCount)&&(identical(other.isFollowing, isFollowing) || other.isFollowing == isFollowing));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,storeName,storeNameAr,storeDescription,storeDescriptionAr,storeLogoUrl,storeBannerUrl,storeSlug,contactPhone,contactEmail,contactWhatsapp,storeAddress,storeCity,storeCountry,latitude,longitude,isActive,isFeatured,allowNegotiation,minOrderAmount,shippingPolicy,returnPolicy,totalProducts,totalOrders,totalSales,averageRating,totalReviews,createdAt,updatedAt,const DeepCollectionEquality().hash(categories),const DeepCollectionEquality().hash(sections),const DeepCollectionEquality().hash(workingHours),followersCount,isFollowing]);

@override
String toString() {
  return 'StoreModel(id: $id, sellerId: $sellerId, storeName: $storeName, storeNameAr: $storeNameAr, storeDescription: $storeDescription, storeDescriptionAr: $storeDescriptionAr, storeLogoUrl: $storeLogoUrl, storeBannerUrl: $storeBannerUrl, storeSlug: $storeSlug, contactPhone: $contactPhone, contactEmail: $contactEmail, contactWhatsapp: $contactWhatsapp, storeAddress: $storeAddress, storeCity: $storeCity, storeCountry: $storeCountry, latitude: $latitude, longitude: $longitude, isActive: $isActive, isFeatured: $isFeatured, allowNegotiation: $allowNegotiation, minOrderAmount: $minOrderAmount, shippingPolicy: $shippingPolicy, returnPolicy: $returnPolicy, totalProducts: $totalProducts, totalOrders: $totalOrders, totalSales: $totalSales, averageRating: $averageRating, totalReviews: $totalReviews, createdAt: $createdAt, updatedAt: $updatedAt, categories: $categories, sections: $sections, workingHours: $workingHours, followersCount: $followersCount, isFollowing: $isFollowing)';
}


}

/// @nodoc
abstract mixin class $StoreModelCopyWith<$Res>  {
  factory $StoreModelCopyWith(StoreModel value, $Res Function(StoreModel) _then) = _$StoreModelCopyWithImpl;
@useResult
$Res call({
 int id, int sellerId, String storeName, String? storeNameAr, String? storeDescription, String? storeDescriptionAr, String? storeLogoUrl, String? storeBannerUrl, String? storeSlug, String? contactPhone, String? contactEmail, String? contactWhatsapp, String? storeAddress, String? storeCity, String? storeCountry, double? latitude, double? longitude, bool isActive, bool isFeatured, bool allowNegotiation, double minOrderAmount, String? shippingPolicy, String? returnPolicy, int totalProducts, int totalOrders, double totalSales, double averageRating, int totalReviews, DateTime? createdAt, DateTime? updatedAt, List<StoreCategoryModel>? categories, List<StoreSectionModel>? sections, List<StoreWorkingHourModel>? workingHours, int followersCount, bool isFollowing
});




}
/// @nodoc
class _$StoreModelCopyWithImpl<$Res>
    implements $StoreModelCopyWith<$Res> {
  _$StoreModelCopyWithImpl(this._self, this._then);

  final StoreModel _self;
  final $Res Function(StoreModel) _then;

/// Create a copy of StoreModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? storeName = null,Object? storeNameAr = freezed,Object? storeDescription = freezed,Object? storeDescriptionAr = freezed,Object? storeLogoUrl = freezed,Object? storeBannerUrl = freezed,Object? storeSlug = freezed,Object? contactPhone = freezed,Object? contactEmail = freezed,Object? contactWhatsapp = freezed,Object? storeAddress = freezed,Object? storeCity = freezed,Object? storeCountry = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? isActive = null,Object? isFeatured = null,Object? allowNegotiation = null,Object? minOrderAmount = null,Object? shippingPolicy = freezed,Object? returnPolicy = freezed,Object? totalProducts = null,Object? totalOrders = null,Object? totalSales = null,Object? averageRating = null,Object? totalReviews = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? categories = freezed,Object? sections = freezed,Object? workingHours = freezed,Object? followersCount = null,Object? isFollowing = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as int,storeName: null == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String,storeNameAr: freezed == storeNameAr ? _self.storeNameAr : storeNameAr // ignore: cast_nullable_to_non_nullable
as String?,storeDescription: freezed == storeDescription ? _self.storeDescription : storeDescription // ignore: cast_nullable_to_non_nullable
as String?,storeDescriptionAr: freezed == storeDescriptionAr ? _self.storeDescriptionAr : storeDescriptionAr // ignore: cast_nullable_to_non_nullable
as String?,storeLogoUrl: freezed == storeLogoUrl ? _self.storeLogoUrl : storeLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,storeBannerUrl: freezed == storeBannerUrl ? _self.storeBannerUrl : storeBannerUrl // ignore: cast_nullable_to_non_nullable
as String?,storeSlug: freezed == storeSlug ? _self.storeSlug : storeSlug // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,contactWhatsapp: freezed == contactWhatsapp ? _self.contactWhatsapp : contactWhatsapp // ignore: cast_nullable_to_non_nullable
as String?,storeAddress: freezed == storeAddress ? _self.storeAddress : storeAddress // ignore: cast_nullable_to_non_nullable
as String?,storeCity: freezed == storeCity ? _self.storeCity : storeCity // ignore: cast_nullable_to_non_nullable
as String?,storeCountry: freezed == storeCountry ? _self.storeCountry : storeCountry // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isFeatured: null == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool,allowNegotiation: null == allowNegotiation ? _self.allowNegotiation : allowNegotiation // ignore: cast_nullable_to_non_nullable
as bool,minOrderAmount: null == minOrderAmount ? _self.minOrderAmount : minOrderAmount // ignore: cast_nullable_to_non_nullable
as double,shippingPolicy: freezed == shippingPolicy ? _self.shippingPolicy : shippingPolicy // ignore: cast_nullable_to_non_nullable
as String?,returnPolicy: freezed == returnPolicy ? _self.returnPolicy : returnPolicy // ignore: cast_nullable_to_non_nullable
as String?,totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,averageRating: null == averageRating ? _self.averageRating : averageRating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,categories: freezed == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as List<StoreCategoryModel>?,sections: freezed == sections ? _self.sections : sections // ignore: cast_nullable_to_non_nullable
as List<StoreSectionModel>?,workingHours: freezed == workingHours ? _self.workingHours : workingHours // ignore: cast_nullable_to_non_nullable
as List<StoreWorkingHourModel>?,followersCount: null == followersCount ? _self.followersCount : followersCount // ignore: cast_nullable_to_non_nullable
as int,isFollowing: null == isFollowing ? _self.isFollowing : isFollowing // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [StoreModel].
extension StoreModelPatterns on StoreModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoreModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoreModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoreModel value)  $default,){
final _that = this;
switch (_that) {
case _StoreModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoreModel value)?  $default,){
final _that = this;
switch (_that) {
case _StoreModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int sellerId,  String storeName,  String? storeNameAr,  String? storeDescription,  String? storeDescriptionAr,  String? storeLogoUrl,  String? storeBannerUrl,  String? storeSlug,  String? contactPhone,  String? contactEmail,  String? contactWhatsapp,  String? storeAddress,  String? storeCity,  String? storeCountry,  double? latitude,  double? longitude,  bool isActive,  bool isFeatured,  bool allowNegotiation,  double minOrderAmount,  String? shippingPolicy,  String? returnPolicy,  int totalProducts,  int totalOrders,  double totalSales,  double averageRating,  int totalReviews,  DateTime? createdAt,  DateTime? updatedAt,  List<StoreCategoryModel>? categories,  List<StoreSectionModel>? sections,  List<StoreWorkingHourModel>? workingHours,  int followersCount,  bool isFollowing)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoreModel() when $default != null:
return $default(_that.id,_that.sellerId,_that.storeName,_that.storeNameAr,_that.storeDescription,_that.storeDescriptionAr,_that.storeLogoUrl,_that.storeBannerUrl,_that.storeSlug,_that.contactPhone,_that.contactEmail,_that.contactWhatsapp,_that.storeAddress,_that.storeCity,_that.storeCountry,_that.latitude,_that.longitude,_that.isActive,_that.isFeatured,_that.allowNegotiation,_that.minOrderAmount,_that.shippingPolicy,_that.returnPolicy,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews,_that.createdAt,_that.updatedAt,_that.categories,_that.sections,_that.workingHours,_that.followersCount,_that.isFollowing);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int sellerId,  String storeName,  String? storeNameAr,  String? storeDescription,  String? storeDescriptionAr,  String? storeLogoUrl,  String? storeBannerUrl,  String? storeSlug,  String? contactPhone,  String? contactEmail,  String? contactWhatsapp,  String? storeAddress,  String? storeCity,  String? storeCountry,  double? latitude,  double? longitude,  bool isActive,  bool isFeatured,  bool allowNegotiation,  double minOrderAmount,  String? shippingPolicy,  String? returnPolicy,  int totalProducts,  int totalOrders,  double totalSales,  double averageRating,  int totalReviews,  DateTime? createdAt,  DateTime? updatedAt,  List<StoreCategoryModel>? categories,  List<StoreSectionModel>? sections,  List<StoreWorkingHourModel>? workingHours,  int followersCount,  bool isFollowing)  $default,) {final _that = this;
switch (_that) {
case _StoreModel():
return $default(_that.id,_that.sellerId,_that.storeName,_that.storeNameAr,_that.storeDescription,_that.storeDescriptionAr,_that.storeLogoUrl,_that.storeBannerUrl,_that.storeSlug,_that.contactPhone,_that.contactEmail,_that.contactWhatsapp,_that.storeAddress,_that.storeCity,_that.storeCountry,_that.latitude,_that.longitude,_that.isActive,_that.isFeatured,_that.allowNegotiation,_that.minOrderAmount,_that.shippingPolicy,_that.returnPolicy,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews,_that.createdAt,_that.updatedAt,_that.categories,_that.sections,_that.workingHours,_that.followersCount,_that.isFollowing);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int sellerId,  String storeName,  String? storeNameAr,  String? storeDescription,  String? storeDescriptionAr,  String? storeLogoUrl,  String? storeBannerUrl,  String? storeSlug,  String? contactPhone,  String? contactEmail,  String? contactWhatsapp,  String? storeAddress,  String? storeCity,  String? storeCountry,  double? latitude,  double? longitude,  bool isActive,  bool isFeatured,  bool allowNegotiation,  double minOrderAmount,  String? shippingPolicy,  String? returnPolicy,  int totalProducts,  int totalOrders,  double totalSales,  double averageRating,  int totalReviews,  DateTime? createdAt,  DateTime? updatedAt,  List<StoreCategoryModel>? categories,  List<StoreSectionModel>? sections,  List<StoreWorkingHourModel>? workingHours,  int followersCount,  bool isFollowing)?  $default,) {final _that = this;
switch (_that) {
case _StoreModel() when $default != null:
return $default(_that.id,_that.sellerId,_that.storeName,_that.storeNameAr,_that.storeDescription,_that.storeDescriptionAr,_that.storeLogoUrl,_that.storeBannerUrl,_that.storeSlug,_that.contactPhone,_that.contactEmail,_that.contactWhatsapp,_that.storeAddress,_that.storeCity,_that.storeCountry,_that.latitude,_that.longitude,_that.isActive,_that.isFeatured,_that.allowNegotiation,_that.minOrderAmount,_that.shippingPolicy,_that.returnPolicy,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews,_that.createdAt,_that.updatedAt,_that.categories,_that.sections,_that.workingHours,_that.followersCount,_that.isFollowing);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoreModel implements StoreModel {
  const _StoreModel({required this.id, required this.sellerId, required this.storeName, this.storeNameAr, this.storeDescription, this.storeDescriptionAr, this.storeLogoUrl, this.storeBannerUrl, this.storeSlug, this.contactPhone, this.contactEmail, this.contactWhatsapp, this.storeAddress, this.storeCity, this.storeCountry, this.latitude, this.longitude, this.isActive = true, this.isFeatured = false, this.allowNegotiation = true, this.minOrderAmount = 0, this.shippingPolicy, this.returnPolicy, this.totalProducts = 0, this.totalOrders = 0, this.totalSales = 0, this.averageRating = 0, this.totalReviews = 0, this.createdAt, this.updatedAt, final  List<StoreCategoryModel>? categories, final  List<StoreSectionModel>? sections, final  List<StoreWorkingHourModel>? workingHours, this.followersCount = 0, this.isFollowing = false}): _categories = categories,_sections = sections,_workingHours = workingHours;
  factory _StoreModel.fromJson(Map<String, dynamic> json) => _$StoreModelFromJson(json);

@override final  int id;
@override final  int sellerId;
@override final  String storeName;
@override final  String? storeNameAr;
@override final  String? storeDescription;
@override final  String? storeDescriptionAr;
@override final  String? storeLogoUrl;
@override final  String? storeBannerUrl;
@override final  String? storeSlug;
// Contact info
@override final  String? contactPhone;
@override final  String? contactEmail;
@override final  String? contactWhatsapp;
// Location
@override final  String? storeAddress;
@override final  String? storeCity;
@override final  String? storeCountry;
@override final  double? latitude;
@override final  double? longitude;
// Settings
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  bool isFeatured;
@override@JsonKey() final  bool allowNegotiation;
@override@JsonKey() final  double minOrderAmount;
@override final  String? shippingPolicy;
@override final  String? returnPolicy;
// Statistics
@override@JsonKey() final  int totalProducts;
@override@JsonKey() final  int totalOrders;
@override@JsonKey() final  double totalSales;
@override@JsonKey() final  double averageRating;
@override@JsonKey() final  int totalReviews;
// Timestamps
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
// Related data
 final  List<StoreCategoryModel>? _categories;
// Related data
@override List<StoreCategoryModel>? get categories {
  final value = _categories;
  if (value == null) return null;
  if (_categories is EqualUnmodifiableListView) return _categories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<StoreSectionModel>? _sections;
@override List<StoreSectionModel>? get sections {
  final value = _sections;
  if (value == null) return null;
  if (_sections is EqualUnmodifiableListView) return _sections;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<StoreWorkingHourModel>? _workingHours;
@override List<StoreWorkingHourModel>? get workingHours {
  final value = _workingHours;
  if (value == null) return null;
  if (_workingHours is EqualUnmodifiableListView) return _workingHours;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey() final  int followersCount;
@override@JsonKey() final  bool isFollowing;

/// Create a copy of StoreModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoreModelCopyWith<_StoreModel> get copyWith => __$StoreModelCopyWithImpl<_StoreModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoreModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoreModel&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.storeNameAr, storeNameAr) || other.storeNameAr == storeNameAr)&&(identical(other.storeDescription, storeDescription) || other.storeDescription == storeDescription)&&(identical(other.storeDescriptionAr, storeDescriptionAr) || other.storeDescriptionAr == storeDescriptionAr)&&(identical(other.storeLogoUrl, storeLogoUrl) || other.storeLogoUrl == storeLogoUrl)&&(identical(other.storeBannerUrl, storeBannerUrl) || other.storeBannerUrl == storeBannerUrl)&&(identical(other.storeSlug, storeSlug) || other.storeSlug == storeSlug)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.contactWhatsapp, contactWhatsapp) || other.contactWhatsapp == contactWhatsapp)&&(identical(other.storeAddress, storeAddress) || other.storeAddress == storeAddress)&&(identical(other.storeCity, storeCity) || other.storeCity == storeCity)&&(identical(other.storeCountry, storeCountry) || other.storeCountry == storeCountry)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.allowNegotiation, allowNegotiation) || other.allowNegotiation == allowNegotiation)&&(identical(other.minOrderAmount, minOrderAmount) || other.minOrderAmount == minOrderAmount)&&(identical(other.shippingPolicy, shippingPolicy) || other.shippingPolicy == shippingPolicy)&&(identical(other.returnPolicy, returnPolicy) || other.returnPolicy == returnPolicy)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.averageRating, averageRating) || other.averageRating == averageRating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other._categories, _categories)&&const DeepCollectionEquality().equals(other._sections, _sections)&&const DeepCollectionEquality().equals(other._workingHours, _workingHours)&&(identical(other.followersCount, followersCount) || other.followersCount == followersCount)&&(identical(other.isFollowing, isFollowing) || other.isFollowing == isFollowing));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,storeName,storeNameAr,storeDescription,storeDescriptionAr,storeLogoUrl,storeBannerUrl,storeSlug,contactPhone,contactEmail,contactWhatsapp,storeAddress,storeCity,storeCountry,latitude,longitude,isActive,isFeatured,allowNegotiation,minOrderAmount,shippingPolicy,returnPolicy,totalProducts,totalOrders,totalSales,averageRating,totalReviews,createdAt,updatedAt,const DeepCollectionEquality().hash(_categories),const DeepCollectionEquality().hash(_sections),const DeepCollectionEquality().hash(_workingHours),followersCount,isFollowing]);

@override
String toString() {
  return 'StoreModel(id: $id, sellerId: $sellerId, storeName: $storeName, storeNameAr: $storeNameAr, storeDescription: $storeDescription, storeDescriptionAr: $storeDescriptionAr, storeLogoUrl: $storeLogoUrl, storeBannerUrl: $storeBannerUrl, storeSlug: $storeSlug, contactPhone: $contactPhone, contactEmail: $contactEmail, contactWhatsapp: $contactWhatsapp, storeAddress: $storeAddress, storeCity: $storeCity, storeCountry: $storeCountry, latitude: $latitude, longitude: $longitude, isActive: $isActive, isFeatured: $isFeatured, allowNegotiation: $allowNegotiation, minOrderAmount: $minOrderAmount, shippingPolicy: $shippingPolicy, returnPolicy: $returnPolicy, totalProducts: $totalProducts, totalOrders: $totalOrders, totalSales: $totalSales, averageRating: $averageRating, totalReviews: $totalReviews, createdAt: $createdAt, updatedAt: $updatedAt, categories: $categories, sections: $sections, workingHours: $workingHours, followersCount: $followersCount, isFollowing: $isFollowing)';
}


}

/// @nodoc
abstract mixin class _$StoreModelCopyWith<$Res> implements $StoreModelCopyWith<$Res> {
  factory _$StoreModelCopyWith(_StoreModel value, $Res Function(_StoreModel) _then) = __$StoreModelCopyWithImpl;
@override @useResult
$Res call({
 int id, int sellerId, String storeName, String? storeNameAr, String? storeDescription, String? storeDescriptionAr, String? storeLogoUrl, String? storeBannerUrl, String? storeSlug, String? contactPhone, String? contactEmail, String? contactWhatsapp, String? storeAddress, String? storeCity, String? storeCountry, double? latitude, double? longitude, bool isActive, bool isFeatured, bool allowNegotiation, double minOrderAmount, String? shippingPolicy, String? returnPolicy, int totalProducts, int totalOrders, double totalSales, double averageRating, int totalReviews, DateTime? createdAt, DateTime? updatedAt, List<StoreCategoryModel>? categories, List<StoreSectionModel>? sections, List<StoreWorkingHourModel>? workingHours, int followersCount, bool isFollowing
});




}
/// @nodoc
class __$StoreModelCopyWithImpl<$Res>
    implements _$StoreModelCopyWith<$Res> {
  __$StoreModelCopyWithImpl(this._self, this._then);

  final _StoreModel _self;
  final $Res Function(_StoreModel) _then;

/// Create a copy of StoreModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? storeName = null,Object? storeNameAr = freezed,Object? storeDescription = freezed,Object? storeDescriptionAr = freezed,Object? storeLogoUrl = freezed,Object? storeBannerUrl = freezed,Object? storeSlug = freezed,Object? contactPhone = freezed,Object? contactEmail = freezed,Object? contactWhatsapp = freezed,Object? storeAddress = freezed,Object? storeCity = freezed,Object? storeCountry = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? isActive = null,Object? isFeatured = null,Object? allowNegotiation = null,Object? minOrderAmount = null,Object? shippingPolicy = freezed,Object? returnPolicy = freezed,Object? totalProducts = null,Object? totalOrders = null,Object? totalSales = null,Object? averageRating = null,Object? totalReviews = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? categories = freezed,Object? sections = freezed,Object? workingHours = freezed,Object? followersCount = null,Object? isFollowing = null,}) {
  return _then(_StoreModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as int,storeName: null == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String,storeNameAr: freezed == storeNameAr ? _self.storeNameAr : storeNameAr // ignore: cast_nullable_to_non_nullable
as String?,storeDescription: freezed == storeDescription ? _self.storeDescription : storeDescription // ignore: cast_nullable_to_non_nullable
as String?,storeDescriptionAr: freezed == storeDescriptionAr ? _self.storeDescriptionAr : storeDescriptionAr // ignore: cast_nullable_to_non_nullable
as String?,storeLogoUrl: freezed == storeLogoUrl ? _self.storeLogoUrl : storeLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,storeBannerUrl: freezed == storeBannerUrl ? _self.storeBannerUrl : storeBannerUrl // ignore: cast_nullable_to_non_nullable
as String?,storeSlug: freezed == storeSlug ? _self.storeSlug : storeSlug // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,contactWhatsapp: freezed == contactWhatsapp ? _self.contactWhatsapp : contactWhatsapp // ignore: cast_nullable_to_non_nullable
as String?,storeAddress: freezed == storeAddress ? _self.storeAddress : storeAddress // ignore: cast_nullable_to_non_nullable
as String?,storeCity: freezed == storeCity ? _self.storeCity : storeCity // ignore: cast_nullable_to_non_nullable
as String?,storeCountry: freezed == storeCountry ? _self.storeCountry : storeCountry // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isFeatured: null == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool,allowNegotiation: null == allowNegotiation ? _self.allowNegotiation : allowNegotiation // ignore: cast_nullable_to_non_nullable
as bool,minOrderAmount: null == minOrderAmount ? _self.minOrderAmount : minOrderAmount // ignore: cast_nullable_to_non_nullable
as double,shippingPolicy: freezed == shippingPolicy ? _self.shippingPolicy : shippingPolicy // ignore: cast_nullable_to_non_nullable
as String?,returnPolicy: freezed == returnPolicy ? _self.returnPolicy : returnPolicy // ignore: cast_nullable_to_non_nullable
as String?,totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,averageRating: null == averageRating ? _self.averageRating : averageRating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,categories: freezed == categories ? _self._categories : categories // ignore: cast_nullable_to_non_nullable
as List<StoreCategoryModel>?,sections: freezed == sections ? _self._sections : sections // ignore: cast_nullable_to_non_nullable
as List<StoreSectionModel>?,workingHours: freezed == workingHours ? _self._workingHours : workingHours // ignore: cast_nullable_to_non_nullable
as List<StoreWorkingHourModel>?,followersCount: null == followersCount ? _self.followersCount : followersCount // ignore: cast_nullable_to_non_nullable
as int,isFollowing: null == isFollowing ? _self.isFollowing : isFollowing // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$StoreCategoryModel {

 int get id; String get name; String get nameAr; String? get description; String? get descriptionAr; String? get iconUrl; String? get colorCode; bool get isActive; int get sortOrder; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of StoreCategoryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoreCategoryModelCopyWith<StoreCategoryModel> get copyWith => _$StoreCategoryModelCopyWithImpl<StoreCategoryModel>(this as StoreCategoryModel, _$identity);

  /// Serializes this StoreCategoryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoreCategoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl)&&(identical(other.colorCode, colorCode) || other.colorCode == colorCode)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,descriptionAr,iconUrl,colorCode,isActive,sortOrder,createdAt,updatedAt);

@override
String toString() {
  return 'StoreCategoryModel(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, iconUrl: $iconUrl, colorCode: $colorCode, isActive: $isActive, sortOrder: $sortOrder, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $StoreCategoryModelCopyWith<$Res>  {
  factory $StoreCategoryModelCopyWith(StoreCategoryModel value, $Res Function(StoreCategoryModel) _then) = _$StoreCategoryModelCopyWithImpl;
@useResult
$Res call({
 int id, String name, String nameAr, String? description, String? descriptionAr, String? iconUrl, String? colorCode, bool isActive, int sortOrder, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$StoreCategoryModelCopyWithImpl<$Res>
    implements $StoreCategoryModelCopyWith<$Res> {
  _$StoreCategoryModelCopyWithImpl(this._self, this._then);

  final StoreCategoryModel _self;
  final $Res Function(StoreCategoryModel) _then;

/// Create a copy of StoreCategoryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = freezed,Object? descriptionAr = freezed,Object? iconUrl = freezed,Object? colorCode = freezed,Object? isActive = null,Object? sortOrder = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,iconUrl: freezed == iconUrl ? _self.iconUrl : iconUrl // ignore: cast_nullable_to_non_nullable
as String?,colorCode: freezed == colorCode ? _self.colorCode : colorCode // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [StoreCategoryModel].
extension StoreCategoryModelPatterns on StoreCategoryModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoreCategoryModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoreCategoryModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoreCategoryModel value)  $default,){
final _that = this;
switch (_that) {
case _StoreCategoryModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoreCategoryModel value)?  $default,){
final _that = this;
switch (_that) {
case _StoreCategoryModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String name,  String nameAr,  String? description,  String? descriptionAr,  String? iconUrl,  String? colorCode,  bool isActive,  int sortOrder,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoreCategoryModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.iconUrl,_that.colorCode,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String name,  String nameAr,  String? description,  String? descriptionAr,  String? iconUrl,  String? colorCode,  bool isActive,  int sortOrder,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _StoreCategoryModel():
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.iconUrl,_that.colorCode,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String name,  String nameAr,  String? description,  String? descriptionAr,  String? iconUrl,  String? colorCode,  bool isActive,  int sortOrder,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _StoreCategoryModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.iconUrl,_that.colorCode,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoreCategoryModel implements StoreCategoryModel {
  const _StoreCategoryModel({required this.id, required this.name, required this.nameAr, this.description, this.descriptionAr, this.iconUrl, this.colorCode, this.isActive = true, this.sortOrder = 0, this.createdAt, this.updatedAt});
  factory _StoreCategoryModel.fromJson(Map<String, dynamic> json) => _$StoreCategoryModelFromJson(json);

@override final  int id;
@override final  String name;
@override final  String nameAr;
@override final  String? description;
@override final  String? descriptionAr;
@override final  String? iconUrl;
@override final  String? colorCode;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int sortOrder;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of StoreCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoreCategoryModelCopyWith<_StoreCategoryModel> get copyWith => __$StoreCategoryModelCopyWithImpl<_StoreCategoryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoreCategoryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoreCategoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl)&&(identical(other.colorCode, colorCode) || other.colorCode == colorCode)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,descriptionAr,iconUrl,colorCode,isActive,sortOrder,createdAt,updatedAt);

@override
String toString() {
  return 'StoreCategoryModel(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, iconUrl: $iconUrl, colorCode: $colorCode, isActive: $isActive, sortOrder: $sortOrder, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$StoreCategoryModelCopyWith<$Res> implements $StoreCategoryModelCopyWith<$Res> {
  factory _$StoreCategoryModelCopyWith(_StoreCategoryModel value, $Res Function(_StoreCategoryModel) _then) = __$StoreCategoryModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, String nameAr, String? description, String? descriptionAr, String? iconUrl, String? colorCode, bool isActive, int sortOrder, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$StoreCategoryModelCopyWithImpl<$Res>
    implements _$StoreCategoryModelCopyWith<$Res> {
  __$StoreCategoryModelCopyWithImpl(this._self, this._then);

  final _StoreCategoryModel _self;
  final $Res Function(_StoreCategoryModel) _then;

/// Create a copy of StoreCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = freezed,Object? descriptionAr = freezed,Object? iconUrl = freezed,Object? colorCode = freezed,Object? isActive = null,Object? sortOrder = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_StoreCategoryModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,iconUrl: freezed == iconUrl ? _self.iconUrl : iconUrl // ignore: cast_nullable_to_non_nullable
as String?,colorCode: freezed == colorCode ? _self.colorCode : colorCode // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$StoreSectionModel {

 int get id; int get storeId; String get sectionName; String? get sectionNameAr; String? get sectionDescription; String? get sectionIconUrl; bool get isActive; int get sortOrder; int get productsCount; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of StoreSectionModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoreSectionModelCopyWith<StoreSectionModel> get copyWith => _$StoreSectionModelCopyWithImpl<StoreSectionModel>(this as StoreSectionModel, _$identity);

  /// Serializes this StoreSectionModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoreSectionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.sectionName, sectionName) || other.sectionName == sectionName)&&(identical(other.sectionNameAr, sectionNameAr) || other.sectionNameAr == sectionNameAr)&&(identical(other.sectionDescription, sectionDescription) || other.sectionDescription == sectionDescription)&&(identical(other.sectionIconUrl, sectionIconUrl) || other.sectionIconUrl == sectionIconUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.productsCount, productsCount) || other.productsCount == productsCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storeId,sectionName,sectionNameAr,sectionDescription,sectionIconUrl,isActive,sortOrder,productsCount,createdAt,updatedAt);

@override
String toString() {
  return 'StoreSectionModel(id: $id, storeId: $storeId, sectionName: $sectionName, sectionNameAr: $sectionNameAr, sectionDescription: $sectionDescription, sectionIconUrl: $sectionIconUrl, isActive: $isActive, sortOrder: $sortOrder, productsCount: $productsCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $StoreSectionModelCopyWith<$Res>  {
  factory $StoreSectionModelCopyWith(StoreSectionModel value, $Res Function(StoreSectionModel) _then) = _$StoreSectionModelCopyWithImpl;
@useResult
$Res call({
 int id, int storeId, String sectionName, String? sectionNameAr, String? sectionDescription, String? sectionIconUrl, bool isActive, int sortOrder, int productsCount, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$StoreSectionModelCopyWithImpl<$Res>
    implements $StoreSectionModelCopyWith<$Res> {
  _$StoreSectionModelCopyWithImpl(this._self, this._then);

  final StoreSectionModel _self;
  final $Res Function(StoreSectionModel) _then;

/// Create a copy of StoreSectionModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? storeId = null,Object? sectionName = null,Object? sectionNameAr = freezed,Object? sectionDescription = freezed,Object? sectionIconUrl = freezed,Object? isActive = null,Object? sortOrder = null,Object? productsCount = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as int,sectionName: null == sectionName ? _self.sectionName : sectionName // ignore: cast_nullable_to_non_nullable
as String,sectionNameAr: freezed == sectionNameAr ? _self.sectionNameAr : sectionNameAr // ignore: cast_nullable_to_non_nullable
as String?,sectionDescription: freezed == sectionDescription ? _self.sectionDescription : sectionDescription // ignore: cast_nullable_to_non_nullable
as String?,sectionIconUrl: freezed == sectionIconUrl ? _self.sectionIconUrl : sectionIconUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,productsCount: null == productsCount ? _self.productsCount : productsCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [StoreSectionModel].
extension StoreSectionModelPatterns on StoreSectionModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoreSectionModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoreSectionModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoreSectionModel value)  $default,){
final _that = this;
switch (_that) {
case _StoreSectionModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoreSectionModel value)?  $default,){
final _that = this;
switch (_that) {
case _StoreSectionModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int storeId,  String sectionName,  String? sectionNameAr,  String? sectionDescription,  String? sectionIconUrl,  bool isActive,  int sortOrder,  int productsCount,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoreSectionModel() when $default != null:
return $default(_that.id,_that.storeId,_that.sectionName,_that.sectionNameAr,_that.sectionDescription,_that.sectionIconUrl,_that.isActive,_that.sortOrder,_that.productsCount,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int storeId,  String sectionName,  String? sectionNameAr,  String? sectionDescription,  String? sectionIconUrl,  bool isActive,  int sortOrder,  int productsCount,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _StoreSectionModel():
return $default(_that.id,_that.storeId,_that.sectionName,_that.sectionNameAr,_that.sectionDescription,_that.sectionIconUrl,_that.isActive,_that.sortOrder,_that.productsCount,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int storeId,  String sectionName,  String? sectionNameAr,  String? sectionDescription,  String? sectionIconUrl,  bool isActive,  int sortOrder,  int productsCount,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _StoreSectionModel() when $default != null:
return $default(_that.id,_that.storeId,_that.sectionName,_that.sectionNameAr,_that.sectionDescription,_that.sectionIconUrl,_that.isActive,_that.sortOrder,_that.productsCount,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoreSectionModel implements StoreSectionModel {
  const _StoreSectionModel({required this.id, required this.storeId, required this.sectionName, this.sectionNameAr, this.sectionDescription, this.sectionIconUrl, this.isActive = true, this.sortOrder = 0, this.productsCount = 0, this.createdAt, this.updatedAt});
  factory _StoreSectionModel.fromJson(Map<String, dynamic> json) => _$StoreSectionModelFromJson(json);

@override final  int id;
@override final  int storeId;
@override final  String sectionName;
@override final  String? sectionNameAr;
@override final  String? sectionDescription;
@override final  String? sectionIconUrl;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int sortOrder;
@override@JsonKey() final  int productsCount;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of StoreSectionModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoreSectionModelCopyWith<_StoreSectionModel> get copyWith => __$StoreSectionModelCopyWithImpl<_StoreSectionModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoreSectionModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoreSectionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.sectionName, sectionName) || other.sectionName == sectionName)&&(identical(other.sectionNameAr, sectionNameAr) || other.sectionNameAr == sectionNameAr)&&(identical(other.sectionDescription, sectionDescription) || other.sectionDescription == sectionDescription)&&(identical(other.sectionIconUrl, sectionIconUrl) || other.sectionIconUrl == sectionIconUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.productsCount, productsCount) || other.productsCount == productsCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storeId,sectionName,sectionNameAr,sectionDescription,sectionIconUrl,isActive,sortOrder,productsCount,createdAt,updatedAt);

@override
String toString() {
  return 'StoreSectionModel(id: $id, storeId: $storeId, sectionName: $sectionName, sectionNameAr: $sectionNameAr, sectionDescription: $sectionDescription, sectionIconUrl: $sectionIconUrl, isActive: $isActive, sortOrder: $sortOrder, productsCount: $productsCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$StoreSectionModelCopyWith<$Res> implements $StoreSectionModelCopyWith<$Res> {
  factory _$StoreSectionModelCopyWith(_StoreSectionModel value, $Res Function(_StoreSectionModel) _then) = __$StoreSectionModelCopyWithImpl;
@override @useResult
$Res call({
 int id, int storeId, String sectionName, String? sectionNameAr, String? sectionDescription, String? sectionIconUrl, bool isActive, int sortOrder, int productsCount, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$StoreSectionModelCopyWithImpl<$Res>
    implements _$StoreSectionModelCopyWith<$Res> {
  __$StoreSectionModelCopyWithImpl(this._self, this._then);

  final _StoreSectionModel _self;
  final $Res Function(_StoreSectionModel) _then;

/// Create a copy of StoreSectionModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? storeId = null,Object? sectionName = null,Object? sectionNameAr = freezed,Object? sectionDescription = freezed,Object? sectionIconUrl = freezed,Object? isActive = null,Object? sortOrder = null,Object? productsCount = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_StoreSectionModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as int,sectionName: null == sectionName ? _self.sectionName : sectionName // ignore: cast_nullable_to_non_nullable
as String,sectionNameAr: freezed == sectionNameAr ? _self.sectionNameAr : sectionNameAr // ignore: cast_nullable_to_non_nullable
as String?,sectionDescription: freezed == sectionDescription ? _self.sectionDescription : sectionDescription // ignore: cast_nullable_to_non_nullable
as String?,sectionIconUrl: freezed == sectionIconUrl ? _self.sectionIconUrl : sectionIconUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,productsCount: null == productsCount ? _self.productsCount : productsCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$StoreWorkingHourModel {

 int get id; int get storeId; int get dayOfWeek;// 0=Sunday, 1=Monday, etc.
 String? get openingTime; String? get closingTime; bool get isClosed; DateTime? get createdAt;
/// Create a copy of StoreWorkingHourModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoreWorkingHourModelCopyWith<StoreWorkingHourModel> get copyWith => _$StoreWorkingHourModelCopyWithImpl<StoreWorkingHourModel>(this as StoreWorkingHourModel, _$identity);

  /// Serializes this StoreWorkingHourModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoreWorkingHourModel&&(identical(other.id, id) || other.id == id)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.dayOfWeek, dayOfWeek) || other.dayOfWeek == dayOfWeek)&&(identical(other.openingTime, openingTime) || other.openingTime == openingTime)&&(identical(other.closingTime, closingTime) || other.closingTime == closingTime)&&(identical(other.isClosed, isClosed) || other.isClosed == isClosed)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storeId,dayOfWeek,openingTime,closingTime,isClosed,createdAt);

@override
String toString() {
  return 'StoreWorkingHourModel(id: $id, storeId: $storeId, dayOfWeek: $dayOfWeek, openingTime: $openingTime, closingTime: $closingTime, isClosed: $isClosed, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $StoreWorkingHourModelCopyWith<$Res>  {
  factory $StoreWorkingHourModelCopyWith(StoreWorkingHourModel value, $Res Function(StoreWorkingHourModel) _then) = _$StoreWorkingHourModelCopyWithImpl;
@useResult
$Res call({
 int id, int storeId, int dayOfWeek, String? openingTime, String? closingTime, bool isClosed, DateTime? createdAt
});




}
/// @nodoc
class _$StoreWorkingHourModelCopyWithImpl<$Res>
    implements $StoreWorkingHourModelCopyWith<$Res> {
  _$StoreWorkingHourModelCopyWithImpl(this._self, this._then);

  final StoreWorkingHourModel _self;
  final $Res Function(StoreWorkingHourModel) _then;

/// Create a copy of StoreWorkingHourModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? storeId = null,Object? dayOfWeek = null,Object? openingTime = freezed,Object? closingTime = freezed,Object? isClosed = null,Object? createdAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as int,dayOfWeek: null == dayOfWeek ? _self.dayOfWeek : dayOfWeek // ignore: cast_nullable_to_non_nullable
as int,openingTime: freezed == openingTime ? _self.openingTime : openingTime // ignore: cast_nullable_to_non_nullable
as String?,closingTime: freezed == closingTime ? _self.closingTime : closingTime // ignore: cast_nullable_to_non_nullable
as String?,isClosed: null == isClosed ? _self.isClosed : isClosed // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [StoreWorkingHourModel].
extension StoreWorkingHourModelPatterns on StoreWorkingHourModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoreWorkingHourModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoreWorkingHourModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoreWorkingHourModel value)  $default,){
final _that = this;
switch (_that) {
case _StoreWorkingHourModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoreWorkingHourModel value)?  $default,){
final _that = this;
switch (_that) {
case _StoreWorkingHourModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int storeId,  int dayOfWeek,  String? openingTime,  String? closingTime,  bool isClosed,  DateTime? createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoreWorkingHourModel() when $default != null:
return $default(_that.id,_that.storeId,_that.dayOfWeek,_that.openingTime,_that.closingTime,_that.isClosed,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int storeId,  int dayOfWeek,  String? openingTime,  String? closingTime,  bool isClosed,  DateTime? createdAt)  $default,) {final _that = this;
switch (_that) {
case _StoreWorkingHourModel():
return $default(_that.id,_that.storeId,_that.dayOfWeek,_that.openingTime,_that.closingTime,_that.isClosed,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int storeId,  int dayOfWeek,  String? openingTime,  String? closingTime,  bool isClosed,  DateTime? createdAt)?  $default,) {final _that = this;
switch (_that) {
case _StoreWorkingHourModel() when $default != null:
return $default(_that.id,_that.storeId,_that.dayOfWeek,_that.openingTime,_that.closingTime,_that.isClosed,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoreWorkingHourModel implements StoreWorkingHourModel {
  const _StoreWorkingHourModel({required this.id, required this.storeId, required this.dayOfWeek, this.openingTime, this.closingTime, this.isClosed = false, this.createdAt});
  factory _StoreWorkingHourModel.fromJson(Map<String, dynamic> json) => _$StoreWorkingHourModelFromJson(json);

@override final  int id;
@override final  int storeId;
@override final  int dayOfWeek;
// 0=Sunday, 1=Monday, etc.
@override final  String? openingTime;
@override final  String? closingTime;
@override@JsonKey() final  bool isClosed;
@override final  DateTime? createdAt;

/// Create a copy of StoreWorkingHourModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoreWorkingHourModelCopyWith<_StoreWorkingHourModel> get copyWith => __$StoreWorkingHourModelCopyWithImpl<_StoreWorkingHourModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoreWorkingHourModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoreWorkingHourModel&&(identical(other.id, id) || other.id == id)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.dayOfWeek, dayOfWeek) || other.dayOfWeek == dayOfWeek)&&(identical(other.openingTime, openingTime) || other.openingTime == openingTime)&&(identical(other.closingTime, closingTime) || other.closingTime == closingTime)&&(identical(other.isClosed, isClosed) || other.isClosed == isClosed)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storeId,dayOfWeek,openingTime,closingTime,isClosed,createdAt);

@override
String toString() {
  return 'StoreWorkingHourModel(id: $id, storeId: $storeId, dayOfWeek: $dayOfWeek, openingTime: $openingTime, closingTime: $closingTime, isClosed: $isClosed, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$StoreWorkingHourModelCopyWith<$Res> implements $StoreWorkingHourModelCopyWith<$Res> {
  factory _$StoreWorkingHourModelCopyWith(_StoreWorkingHourModel value, $Res Function(_StoreWorkingHourModel) _then) = __$StoreWorkingHourModelCopyWithImpl;
@override @useResult
$Res call({
 int id, int storeId, int dayOfWeek, String? openingTime, String? closingTime, bool isClosed, DateTime? createdAt
});




}
/// @nodoc
class __$StoreWorkingHourModelCopyWithImpl<$Res>
    implements _$StoreWorkingHourModelCopyWith<$Res> {
  __$StoreWorkingHourModelCopyWithImpl(this._self, this._then);

  final _StoreWorkingHourModel _self;
  final $Res Function(_StoreWorkingHourModel) _then;

/// Create a copy of StoreWorkingHourModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? storeId = null,Object? dayOfWeek = null,Object? openingTime = freezed,Object? closingTime = freezed,Object? isClosed = null,Object? createdAt = freezed,}) {
  return _then(_StoreWorkingHourModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as int,dayOfWeek: null == dayOfWeek ? _self.dayOfWeek : dayOfWeek // ignore: cast_nullable_to_non_nullable
as int,openingTime: freezed == openingTime ? _self.openingTime : openingTime // ignore: cast_nullable_to_non_nullable
as String?,closingTime: freezed == closingTime ? _self.closingTime : closingTime // ignore: cast_nullable_to_non_nullable
as String?,isClosed: null == isClosed ? _self.isClosed : isClosed // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$StoreStatsModel {

 int get storeId; int get totalProducts; int get totalOrders; double get totalSales; double get averageRating; int get totalReviews; int get followersCount; int get totalViews; Map<String, int>? get productsByCategory; Map<String, double>? get salesByMonth;
/// Create a copy of StoreStatsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoreStatsModelCopyWith<StoreStatsModel> get copyWith => _$StoreStatsModelCopyWithImpl<StoreStatsModel>(this as StoreStatsModel, _$identity);

  /// Serializes this StoreStatsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoreStatsModel&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.averageRating, averageRating) || other.averageRating == averageRating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&(identical(other.followersCount, followersCount) || other.followersCount == followersCount)&&(identical(other.totalViews, totalViews) || other.totalViews == totalViews)&&const DeepCollectionEquality().equals(other.productsByCategory, productsByCategory)&&const DeepCollectionEquality().equals(other.salesByMonth, salesByMonth));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,storeId,totalProducts,totalOrders,totalSales,averageRating,totalReviews,followersCount,totalViews,const DeepCollectionEquality().hash(productsByCategory),const DeepCollectionEquality().hash(salesByMonth));

@override
String toString() {
  return 'StoreStatsModel(storeId: $storeId, totalProducts: $totalProducts, totalOrders: $totalOrders, totalSales: $totalSales, averageRating: $averageRating, totalReviews: $totalReviews, followersCount: $followersCount, totalViews: $totalViews, productsByCategory: $productsByCategory, salesByMonth: $salesByMonth)';
}


}

/// @nodoc
abstract mixin class $StoreStatsModelCopyWith<$Res>  {
  factory $StoreStatsModelCopyWith(StoreStatsModel value, $Res Function(StoreStatsModel) _then) = _$StoreStatsModelCopyWithImpl;
@useResult
$Res call({
 int storeId, int totalProducts, int totalOrders, double totalSales, double averageRating, int totalReviews, int followersCount, int totalViews, Map<String, int>? productsByCategory, Map<String, double>? salesByMonth
});




}
/// @nodoc
class _$StoreStatsModelCopyWithImpl<$Res>
    implements $StoreStatsModelCopyWith<$Res> {
  _$StoreStatsModelCopyWithImpl(this._self, this._then);

  final StoreStatsModel _self;
  final $Res Function(StoreStatsModel) _then;

/// Create a copy of StoreStatsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? storeId = null,Object? totalProducts = null,Object? totalOrders = null,Object? totalSales = null,Object? averageRating = null,Object? totalReviews = null,Object? followersCount = null,Object? totalViews = null,Object? productsByCategory = freezed,Object? salesByMonth = freezed,}) {
  return _then(_self.copyWith(
storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as int,totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,averageRating: null == averageRating ? _self.averageRating : averageRating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,followersCount: null == followersCount ? _self.followersCount : followersCount // ignore: cast_nullable_to_non_nullable
as int,totalViews: null == totalViews ? _self.totalViews : totalViews // ignore: cast_nullable_to_non_nullable
as int,productsByCategory: freezed == productsByCategory ? _self.productsByCategory : productsByCategory // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,salesByMonth: freezed == salesByMonth ? _self.salesByMonth : salesByMonth // ignore: cast_nullable_to_non_nullable
as Map<String, double>?,
  ));
}

}


/// Adds pattern-matching-related methods to [StoreStatsModel].
extension StoreStatsModelPatterns on StoreStatsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StoreStatsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StoreStatsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StoreStatsModel value)  $default,){
final _that = this;
switch (_that) {
case _StoreStatsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StoreStatsModel value)?  $default,){
final _that = this;
switch (_that) {
case _StoreStatsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int storeId,  int totalProducts,  int totalOrders,  double totalSales,  double averageRating,  int totalReviews,  int followersCount,  int totalViews,  Map<String, int>? productsByCategory,  Map<String, double>? salesByMonth)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StoreStatsModel() when $default != null:
return $default(_that.storeId,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews,_that.followersCount,_that.totalViews,_that.productsByCategory,_that.salesByMonth);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int storeId,  int totalProducts,  int totalOrders,  double totalSales,  double averageRating,  int totalReviews,  int followersCount,  int totalViews,  Map<String, int>? productsByCategory,  Map<String, double>? salesByMonth)  $default,) {final _that = this;
switch (_that) {
case _StoreStatsModel():
return $default(_that.storeId,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews,_that.followersCount,_that.totalViews,_that.productsByCategory,_that.salesByMonth);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int storeId,  int totalProducts,  int totalOrders,  double totalSales,  double averageRating,  int totalReviews,  int followersCount,  int totalViews,  Map<String, int>? productsByCategory,  Map<String, double>? salesByMonth)?  $default,) {final _that = this;
switch (_that) {
case _StoreStatsModel() when $default != null:
return $default(_that.storeId,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews,_that.followersCount,_that.totalViews,_that.productsByCategory,_that.salesByMonth);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StoreStatsModel implements StoreStatsModel {
  const _StoreStatsModel({required this.storeId, this.totalProducts = 0, this.totalOrders = 0, this.totalSales = 0, this.averageRating = 0, this.totalReviews = 0, this.followersCount = 0, this.totalViews = 0, final  Map<String, int>? productsByCategory, final  Map<String, double>? salesByMonth}): _productsByCategory = productsByCategory,_salesByMonth = salesByMonth;
  factory _StoreStatsModel.fromJson(Map<String, dynamic> json) => _$StoreStatsModelFromJson(json);

@override final  int storeId;
@override@JsonKey() final  int totalProducts;
@override@JsonKey() final  int totalOrders;
@override@JsonKey() final  double totalSales;
@override@JsonKey() final  double averageRating;
@override@JsonKey() final  int totalReviews;
@override@JsonKey() final  int followersCount;
@override@JsonKey() final  int totalViews;
 final  Map<String, int>? _productsByCategory;
@override Map<String, int>? get productsByCategory {
  final value = _productsByCategory;
  if (value == null) return null;
  if (_productsByCategory is EqualUnmodifiableMapView) return _productsByCategory;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, double>? _salesByMonth;
@override Map<String, double>? get salesByMonth {
  final value = _salesByMonth;
  if (value == null) return null;
  if (_salesByMonth is EqualUnmodifiableMapView) return _salesByMonth;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of StoreStatsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoreStatsModelCopyWith<_StoreStatsModel> get copyWith => __$StoreStatsModelCopyWithImpl<_StoreStatsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoreStatsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoreStatsModel&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.averageRating, averageRating) || other.averageRating == averageRating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&(identical(other.followersCount, followersCount) || other.followersCount == followersCount)&&(identical(other.totalViews, totalViews) || other.totalViews == totalViews)&&const DeepCollectionEquality().equals(other._productsByCategory, _productsByCategory)&&const DeepCollectionEquality().equals(other._salesByMonth, _salesByMonth));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,storeId,totalProducts,totalOrders,totalSales,averageRating,totalReviews,followersCount,totalViews,const DeepCollectionEquality().hash(_productsByCategory),const DeepCollectionEquality().hash(_salesByMonth));

@override
String toString() {
  return 'StoreStatsModel(storeId: $storeId, totalProducts: $totalProducts, totalOrders: $totalOrders, totalSales: $totalSales, averageRating: $averageRating, totalReviews: $totalReviews, followersCount: $followersCount, totalViews: $totalViews, productsByCategory: $productsByCategory, salesByMonth: $salesByMonth)';
}


}

/// @nodoc
abstract mixin class _$StoreStatsModelCopyWith<$Res> implements $StoreStatsModelCopyWith<$Res> {
  factory _$StoreStatsModelCopyWith(_StoreStatsModel value, $Res Function(_StoreStatsModel) _then) = __$StoreStatsModelCopyWithImpl;
@override @useResult
$Res call({
 int storeId, int totalProducts, int totalOrders, double totalSales, double averageRating, int totalReviews, int followersCount, int totalViews, Map<String, int>? productsByCategory, Map<String, double>? salesByMonth
});




}
/// @nodoc
class __$StoreStatsModelCopyWithImpl<$Res>
    implements _$StoreStatsModelCopyWith<$Res> {
  __$StoreStatsModelCopyWithImpl(this._self, this._then);

  final _StoreStatsModel _self;
  final $Res Function(_StoreStatsModel) _then;

/// Create a copy of StoreStatsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? storeId = null,Object? totalProducts = null,Object? totalOrders = null,Object? totalSales = null,Object? averageRating = null,Object? totalReviews = null,Object? followersCount = null,Object? totalViews = null,Object? productsByCategory = freezed,Object? salesByMonth = freezed,}) {
  return _then(_StoreStatsModel(
storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as int,totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,averageRating: null == averageRating ? _self.averageRating : averageRating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,followersCount: null == followersCount ? _self.followersCount : followersCount // ignore: cast_nullable_to_non_nullable
as int,totalViews: null == totalViews ? _self.totalViews : totalViews // ignore: cast_nullable_to_non_nullable
as int,productsByCategory: freezed == productsByCategory ? _self._productsByCategory : productsByCategory // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,salesByMonth: freezed == salesByMonth ? _self._salesByMonth : salesByMonth // ignore: cast_nullable_to_non_nullable
as Map<String, double>?,
  ));
}


}

// dart format on
