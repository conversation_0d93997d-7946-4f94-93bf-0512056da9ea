// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'store_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_StoreModel _$StoreModelFromJson(Map<String, dynamic> json) => _StoreModel(
  id: (json['id'] as num).toInt(),
  sellerId: (json['sellerId'] as num).toInt(),
  storeName: json['storeName'] as String,
  storeNameAr: json['storeNameAr'] as String?,
  storeDescription: json['storeDescription'] as String?,
  storeDescriptionAr: json['storeDescriptionAr'] as String?,
  storeLogoUrl: json['storeLogoUrl'] as String?,
  storeBannerUrl: json['storeBannerUrl'] as String?,
  storeSlug: json['storeSlug'] as String?,
  contactPhone: json['contactPhone'] as String?,
  contactEmail: json['contactEmail'] as String?,
  contactWhatsapp: json['contactWhatsapp'] as String?,
  storeAddress: json['storeAddress'] as String?,
  storeCity: json['storeCity'] as String?,
  storeCountry: json['storeCountry'] as String?,
  latitude: (json['latitude'] as num?)?.toDouble(),
  longitude: (json['longitude'] as num?)?.toDouble(),
  isActive: json['isActive'] as bool? ?? true,
  isFeatured: json['isFeatured'] as bool? ?? false,
  allowNegotiation: json['allowNegotiation'] as bool? ?? true,
  minOrderAmount: (json['minOrderAmount'] as num?)?.toDouble() ?? 0,
  shippingPolicy: json['shippingPolicy'] as String?,
  returnPolicy: json['returnPolicy'] as String?,
  totalProducts: (json['totalProducts'] as num?)?.toInt() ?? 0,
  totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
  totalSales: (json['totalSales'] as num?)?.toDouble() ?? 0,
  averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0,
  totalReviews: (json['totalReviews'] as num?)?.toInt() ?? 0,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  categories: (json['categories'] as List<dynamic>?)
      ?.map((e) => StoreCategoryModel.fromJson(e as Map<String, dynamic>))
      .toList(),
  sections: (json['sections'] as List<dynamic>?)
      ?.map((e) => StoreSectionModel.fromJson(e as Map<String, dynamic>))
      .toList(),
  workingHours: (json['workingHours'] as List<dynamic>?)
      ?.map((e) => StoreWorkingHourModel.fromJson(e as Map<String, dynamic>))
      .toList(),
  followersCount: (json['followersCount'] as num?)?.toInt() ?? 0,
  isFollowing: json['isFollowing'] as bool? ?? false,
);

Map<String, dynamic> _$StoreModelToJson(_StoreModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sellerId': instance.sellerId,
      'storeName': instance.storeName,
      'storeNameAr': instance.storeNameAr,
      'storeDescription': instance.storeDescription,
      'storeDescriptionAr': instance.storeDescriptionAr,
      'storeLogoUrl': instance.storeLogoUrl,
      'storeBannerUrl': instance.storeBannerUrl,
      'storeSlug': instance.storeSlug,
      'contactPhone': instance.contactPhone,
      'contactEmail': instance.contactEmail,
      'contactWhatsapp': instance.contactWhatsapp,
      'storeAddress': instance.storeAddress,
      'storeCity': instance.storeCity,
      'storeCountry': instance.storeCountry,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'isActive': instance.isActive,
      'isFeatured': instance.isFeatured,
      'allowNegotiation': instance.allowNegotiation,
      'minOrderAmount': instance.minOrderAmount,
      'shippingPolicy': instance.shippingPolicy,
      'returnPolicy': instance.returnPolicy,
      'totalProducts': instance.totalProducts,
      'totalOrders': instance.totalOrders,
      'totalSales': instance.totalSales,
      'averageRating': instance.averageRating,
      'totalReviews': instance.totalReviews,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'categories': instance.categories,
      'sections': instance.sections,
      'workingHours': instance.workingHours,
      'followersCount': instance.followersCount,
      'isFollowing': instance.isFollowing,
    };

_StoreCategoryModel _$StoreCategoryModelFromJson(Map<String, dynamic> json) =>
    _StoreCategoryModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      nameAr: json['nameAr'] as String,
      description: json['description'] as String?,
      descriptionAr: json['descriptionAr'] as String?,
      iconUrl: json['iconUrl'] as String?,
      colorCode: json['colorCode'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$StoreCategoryModelToJson(_StoreCategoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'nameAr': instance.nameAr,
      'description': instance.description,
      'descriptionAr': instance.descriptionAr,
      'iconUrl': instance.iconUrl,
      'colorCode': instance.colorCode,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_StoreSectionModel _$StoreSectionModelFromJson(Map<String, dynamic> json) =>
    _StoreSectionModel(
      id: (json['id'] as num).toInt(),
      storeId: (json['storeId'] as num).toInt(),
      sectionName: json['sectionName'] as String,
      sectionNameAr: json['sectionNameAr'] as String?,
      sectionDescription: json['sectionDescription'] as String?,
      sectionIconUrl: json['sectionIconUrl'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      productsCount: (json['productsCount'] as num?)?.toInt() ?? 0,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$StoreSectionModelToJson(_StoreSectionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'storeId': instance.storeId,
      'sectionName': instance.sectionName,
      'sectionNameAr': instance.sectionNameAr,
      'sectionDescription': instance.sectionDescription,
      'sectionIconUrl': instance.sectionIconUrl,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'productsCount': instance.productsCount,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_StoreWorkingHourModel _$StoreWorkingHourModelFromJson(
  Map<String, dynamic> json,
) => _StoreWorkingHourModel(
  id: (json['id'] as num).toInt(),
  storeId: (json['storeId'] as num).toInt(),
  dayOfWeek: (json['dayOfWeek'] as num).toInt(),
  openingTime: json['openingTime'] as String?,
  closingTime: json['closingTime'] as String?,
  isClosed: json['isClosed'] as bool? ?? false,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$StoreWorkingHourModelToJson(
  _StoreWorkingHourModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'storeId': instance.storeId,
  'dayOfWeek': instance.dayOfWeek,
  'openingTime': instance.openingTime,
  'closingTime': instance.closingTime,
  'isClosed': instance.isClosed,
  'createdAt': instance.createdAt?.toIso8601String(),
};

_StoreStatsModel _$StoreStatsModelFromJson(Map<String, dynamic> json) =>
    _StoreStatsModel(
      storeId: (json['storeId'] as num).toInt(),
      totalProducts: (json['totalProducts'] as num?)?.toInt() ?? 0,
      totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
      totalSales: (json['totalSales'] as num?)?.toDouble() ?? 0,
      averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0,
      totalReviews: (json['totalReviews'] as num?)?.toInt() ?? 0,
      followersCount: (json['followersCount'] as num?)?.toInt() ?? 0,
      totalViews: (json['totalViews'] as num?)?.toInt() ?? 0,
      productsByCategory: (json['productsByCategory'] as Map<String, dynamic>?)
          ?.map((k, e) => MapEntry(k, (e as num).toInt())),
      salesByMonth: (json['salesByMonth'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$StoreStatsModelToJson(_StoreStatsModel instance) =>
    <String, dynamic>{
      'storeId': instance.storeId,
      'totalProducts': instance.totalProducts,
      'totalOrders': instance.totalOrders,
      'totalSales': instance.totalSales,
      'averageRating': instance.averageRating,
      'totalReviews': instance.totalReviews,
      'followersCount': instance.followersCount,
      'totalViews': instance.totalViews,
      'productsByCategory': instance.productsByCategory,
      'salesByMonth': instance.salesByMonth,
    };
