// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'simple_category_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$categoryBySlugHash() => r'4ba4686d26a733a184a759b838db22fe961d3bdd';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Category by slug provider - FIXED: Converted to @riverpod pattern
///
/// Copied from [categoryBySlug].
@ProviderFor(categoryBySlug)
const categoryBySlugProvider = CategoryBySlugFamily();

/// Category by slug provider - FIXED: Converted to @riverpod pattern
///
/// Copied from [categoryBySlug].
class CategoryBySlugFamily extends Family<AsyncValue<CategoryModel>> {
  /// Category by slug provider - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [categoryBySlug].
  const CategoryBySlugFamily();

  /// Category by slug provider - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [categoryBySlug].
  CategoryBySlugProvider call(String slug) {
    return CategoryBySlugProvider(slug);
  }

  @override
  CategoryBySlugProvider getProviderOverride(
    covariant CategoryBySlugProvider provider,
  ) {
    return call(provider.slug);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryBySlugProvider';
}

/// Category by slug provider - FIXED: Converted to @riverpod pattern
///
/// Copied from [categoryBySlug].
class CategoryBySlugProvider extends AutoDisposeFutureProvider<CategoryModel> {
  /// Category by slug provider - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [categoryBySlug].
  CategoryBySlugProvider(String slug)
    : this._internal(
        (ref) => categoryBySlug(ref as CategoryBySlugRef, slug),
        from: categoryBySlugProvider,
        name: r'categoryBySlugProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$categoryBySlugHash,
        dependencies: CategoryBySlugFamily._dependencies,
        allTransitiveDependencies:
            CategoryBySlugFamily._allTransitiveDependencies,
        slug: slug,
      );

  CategoryBySlugProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.slug,
  }) : super.internal();

  final String slug;

  @override
  Override overrideWith(
    FutureOr<CategoryModel> Function(CategoryBySlugRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryBySlugProvider._internal(
        (ref) => create(ref as CategoryBySlugRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        slug: slug,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CategoryModel> createElement() {
    return _CategoryBySlugProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryBySlugProvider && other.slug == slug;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, slug.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryBySlugRef on AutoDisposeFutureProviderRef<CategoryModel> {
  /// The parameter `slug` of this provider.
  String get slug;
}

class _CategoryBySlugProviderElement
    extends AutoDisposeFutureProviderElement<CategoryModel>
    with CategoryBySlugRef {
  _CategoryBySlugProviderElement(super.provider);

  @override
  String get slug => (origin as CategoryBySlugProvider).slug;
}

String _$childCategoriesHash() => r'ec2e96a71883ad23887d8c87ef631c243d4b567c';

/// Child categories provider - FIXED: Converted to @riverpod pattern
///
/// Copied from [childCategories].
@ProviderFor(childCategories)
const childCategoriesProvider = ChildCategoriesFamily();

/// Child categories provider - FIXED: Converted to @riverpod pattern
///
/// Copied from [childCategories].
class ChildCategoriesFamily extends Family<AsyncValue<List<CategoryModel>>> {
  /// Child categories provider - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [childCategories].
  const ChildCategoriesFamily();

  /// Child categories provider - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [childCategories].
  ChildCategoriesProvider call(String parentId) {
    return ChildCategoriesProvider(parentId);
  }

  @override
  ChildCategoriesProvider getProviderOverride(
    covariant ChildCategoriesProvider provider,
  ) {
    return call(provider.parentId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'childCategoriesProvider';
}

/// Child categories provider - FIXED: Converted to @riverpod pattern
///
/// Copied from [childCategories].
class ChildCategoriesProvider
    extends AutoDisposeFutureProvider<List<CategoryModel>> {
  /// Child categories provider - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [childCategories].
  ChildCategoriesProvider(String parentId)
    : this._internal(
        (ref) => childCategories(ref as ChildCategoriesRef, parentId),
        from: childCategoriesProvider,
        name: r'childCategoriesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$childCategoriesHash,
        dependencies: ChildCategoriesFamily._dependencies,
        allTransitiveDependencies:
            ChildCategoriesFamily._allTransitiveDependencies,
        parentId: parentId,
      );

  ChildCategoriesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.parentId,
  }) : super.internal();

  final String parentId;

  @override
  Override overrideWith(
    FutureOr<List<CategoryModel>> Function(ChildCategoriesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ChildCategoriesProvider._internal(
        (ref) => create(ref as ChildCategoriesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        parentId: parentId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CategoryModel>> createElement() {
    return _ChildCategoriesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ChildCategoriesProvider && other.parentId == parentId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, parentId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ChildCategoriesRef on AutoDisposeFutureProviderRef<List<CategoryModel>> {
  /// The parameter `parentId` of this provider.
  String get parentId;
}

class _ChildCategoriesProviderElement
    extends AutoDisposeFutureProviderElement<List<CategoryModel>>
    with ChildCategoriesRef {
  _ChildCategoriesProviderElement(super.provider);

  @override
  String get parentId => (origin as ChildCategoriesProvider).parentId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
