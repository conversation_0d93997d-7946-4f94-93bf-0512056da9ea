// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_management_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isCurrentUserAdminHash() =>
    r'7e279470485aa8c37b21d52163c83921b0496dc1';

/// مزود فحص صلاحيات المشرف للمستخدم الحالي
///
/// Copied from [isCurrentUserAdmin].
@ProviderFor(isCurrentUserAdmin)
final isCurrentUserAdminProvider = AutoDisposeFutureProvider<bool>.internal(
  isCurrentUserAdmin,
  name: r'isCurrentUserAdminProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCurrentUserAdminHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCurrentUserAdminRef = AutoDisposeFutureProviderRef<bool>;
String _$adminUsersHash() => r'922b2866cec26c0bcec0fd46b1677a398e28cd78';

/// مزود المشرفين فقط
///
/// Copied from [adminUsers].
@ProviderFor(adminUsers)
final adminUsersProvider = AutoDisposeFutureProvider<List<UserModel>>.internal(
  adminUsers,
  name: r'adminUsersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminUsersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminUsersRef = AutoDisposeFutureProviderRef<List<UserModel>>;
String _$regularUsersHash() => r'a91da9ea485bf2c66ca290303290792cfc3e7419';

/// مزود المستخدمين العاديين (غير المشرفين)
///
/// Copied from [regularUsers].
@ProviderFor(regularUsers)
final regularUsersProvider =
    AutoDisposeFutureProvider<List<UserModel>>.internal(
      regularUsers,
      name: r'regularUsersProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$regularUsersHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RegularUsersRef = AutoDisposeFutureProviderRef<List<UserModel>>;
String _$adminManagementHash() => r'982a043abac539b9c7a6ed38f6a0efeb8c261dd6';

/// مزود إدارة المشرفين - تعيين وإزالة المشرفين
///
/// Copied from [AdminManagement].
@ProviderFor(AdminManagement)
final adminManagementProvider =
    AutoDisposeAsyncNotifierProvider<AdminManagement, List<UserModel>>.internal(
      AdminManagement.new,
      name: r'adminManagementProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminManagementHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdminManagement = AutoDisposeAsyncNotifier<List<UserModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
