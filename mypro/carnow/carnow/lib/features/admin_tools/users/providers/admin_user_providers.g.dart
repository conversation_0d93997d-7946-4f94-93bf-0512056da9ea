// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_user_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adminUserDetailsHash() => r'3fc12cc1b6319901e4a998fc60f6eea66d6599ca';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود تفاصيل مستخدم محدد
///
/// Copied from [adminUserDetails].
@ProviderFor(adminUserDetails)
const adminUserDetailsProvider = AdminUserDetailsFamily();

/// مزود تفاصيل مستخدم محدد
///
/// Copied from [adminUserDetails].
class AdminUserDetailsFamily extends Family<AsyncValue<AdminUserModel?>> {
  /// مزود تفاصيل مستخدم محدد
  ///
  /// Copied from [adminUserDetails].
  const AdminUserDetailsFamily();

  /// مزود تفاصيل مستخدم محدد
  ///
  /// Copied from [adminUserDetails].
  AdminUserDetailsProvider call(String userId) {
    return AdminUserDetailsProvider(userId);
  }

  @override
  AdminUserDetailsProvider getProviderOverride(
    covariant AdminUserDetailsProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'adminUserDetailsProvider';
}

/// مزود تفاصيل مستخدم محدد
///
/// Copied from [adminUserDetails].
class AdminUserDetailsProvider
    extends AutoDisposeFutureProvider<AdminUserModel?> {
  /// مزود تفاصيل مستخدم محدد
  ///
  /// Copied from [adminUserDetails].
  AdminUserDetailsProvider(String userId)
    : this._internal(
        (ref) => adminUserDetails(ref as AdminUserDetailsRef, userId),
        from: adminUserDetailsProvider,
        name: r'adminUserDetailsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$adminUserDetailsHash,
        dependencies: AdminUserDetailsFamily._dependencies,
        allTransitiveDependencies:
            AdminUserDetailsFamily._allTransitiveDependencies,
        userId: userId,
      );

  AdminUserDetailsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<AdminUserModel?> Function(AdminUserDetailsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AdminUserDetailsProvider._internal(
        (ref) => create(ref as AdminUserDetailsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<AdminUserModel?> createElement() {
    return _AdminUserDetailsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AdminUserDetailsProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AdminUserDetailsRef on AutoDisposeFutureProviderRef<AdminUserModel?> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _AdminUserDetailsProviderElement
    extends AutoDisposeFutureProviderElement<AdminUserModel?>
    with AdminUserDetailsRef {
  _AdminUserDetailsProviderElement(super.provider);

  @override
  String get userId => (origin as AdminUserDetailsProvider).userId;
}

String _$userManagementActionsHash() =>
    r'531842de10dd348c75ba9f36e0eb21fe1968102d';

/// مزود سجل إجراءات المستخدم
///
/// Copied from [userManagementActions].
@ProviderFor(userManagementActions)
const userManagementActionsProvider = UserManagementActionsFamily();

/// مزود سجل إجراءات المستخدم
///
/// Copied from [userManagementActions].
class UserManagementActionsFamily
    extends Family<AsyncValue<List<UserManagementAction>>> {
  /// مزود سجل إجراءات المستخدم
  ///
  /// Copied from [userManagementActions].
  const UserManagementActionsFamily();

  /// مزود سجل إجراءات المستخدم
  ///
  /// Copied from [userManagementActions].
  UserManagementActionsProvider call(String userId) {
    return UserManagementActionsProvider(userId);
  }

  @override
  UserManagementActionsProvider getProviderOverride(
    covariant UserManagementActionsProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userManagementActionsProvider';
}

/// مزود سجل إجراءات المستخدم
///
/// Copied from [userManagementActions].
class UserManagementActionsProvider
    extends AutoDisposeFutureProvider<List<UserManagementAction>> {
  /// مزود سجل إجراءات المستخدم
  ///
  /// Copied from [userManagementActions].
  UserManagementActionsProvider(String userId)
    : this._internal(
        (ref) => userManagementActions(ref as UserManagementActionsRef, userId),
        from: userManagementActionsProvider,
        name: r'userManagementActionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userManagementActionsHash,
        dependencies: UserManagementActionsFamily._dependencies,
        allTransitiveDependencies:
            UserManagementActionsFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserManagementActionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<List<UserManagementAction>> Function(
      UserManagementActionsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserManagementActionsProvider._internal(
        (ref) => create(ref as UserManagementActionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<UserManagementAction>> createElement() {
    return _UserManagementActionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserManagementActionsProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserManagementActionsRef
    on AutoDisposeFutureProviderRef<List<UserManagementAction>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserManagementActionsProviderElement
    extends AutoDisposeFutureProviderElement<List<UserManagementAction>>
    with UserManagementActionsRef {
  _UserManagementActionsProviderElement(super.provider);

  @override
  String get userId => (origin as UserManagementActionsProvider).userId;
}

String _$userStatisticsHash() => r'838caf059cc1d2182b4d7485ad1a2113db709b44';

/// مزود إحصائيات المستخدمين
///
/// Copied from [userStatistics].
@ProviderFor(userStatistics)
final userStatisticsProvider =
    AutoDisposeFutureProvider<UserStatistics>.internal(
      userStatistics,
      name: r'userStatisticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userStatisticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserStatisticsRef = AutoDisposeFutureProviderRef<UserStatistics>;
String _$adminUsersListHash() => r'59bed27949e162ea627c2f9e4c3cb8683ee29e93';

abstract class _$AdminUsersList
    extends BuildlessAutoDisposeAsyncNotifier<List<AdminUserModel>> {
  late final UserStatus? statusFilter;
  late final String? searchQuery;

  FutureOr<List<AdminUserModel>> build({
    UserStatus? statusFilter,
    String? searchQuery,
  });
}

/// مزود قائمة المستخدمين مع إمكانية البحث والفلترة
///
/// Copied from [AdminUsersList].
@ProviderFor(AdminUsersList)
const adminUsersListProvider = AdminUsersListFamily();

/// مزود قائمة المستخدمين مع إمكانية البحث والفلترة
///
/// Copied from [AdminUsersList].
class AdminUsersListFamily extends Family<AsyncValue<List<AdminUserModel>>> {
  /// مزود قائمة المستخدمين مع إمكانية البحث والفلترة
  ///
  /// Copied from [AdminUsersList].
  const AdminUsersListFamily();

  /// مزود قائمة المستخدمين مع إمكانية البحث والفلترة
  ///
  /// Copied from [AdminUsersList].
  AdminUsersListProvider call({UserStatus? statusFilter, String? searchQuery}) {
    return AdminUsersListProvider(
      statusFilter: statusFilter,
      searchQuery: searchQuery,
    );
  }

  @override
  AdminUsersListProvider getProviderOverride(
    covariant AdminUsersListProvider provider,
  ) {
    return call(
      statusFilter: provider.statusFilter,
      searchQuery: provider.searchQuery,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'adminUsersListProvider';
}

/// مزود قائمة المستخدمين مع إمكانية البحث والفلترة
///
/// Copied from [AdminUsersList].
class AdminUsersListProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          AdminUsersList,
          List<AdminUserModel>
        > {
  /// مزود قائمة المستخدمين مع إمكانية البحث والفلترة
  ///
  /// Copied from [AdminUsersList].
  AdminUsersListProvider({UserStatus? statusFilter, String? searchQuery})
    : this._internal(
        () => AdminUsersList()
          ..statusFilter = statusFilter
          ..searchQuery = searchQuery,
        from: adminUsersListProvider,
        name: r'adminUsersListProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$adminUsersListHash,
        dependencies: AdminUsersListFamily._dependencies,
        allTransitiveDependencies:
            AdminUsersListFamily._allTransitiveDependencies,
        statusFilter: statusFilter,
        searchQuery: searchQuery,
      );

  AdminUsersListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.statusFilter,
    required this.searchQuery,
  }) : super.internal();

  final UserStatus? statusFilter;
  final String? searchQuery;

  @override
  FutureOr<List<AdminUserModel>> runNotifierBuild(
    covariant AdminUsersList notifier,
  ) {
    return notifier.build(statusFilter: statusFilter, searchQuery: searchQuery);
  }

  @override
  Override overrideWith(AdminUsersList Function() create) {
    return ProviderOverride(
      origin: this,
      override: AdminUsersListProvider._internal(
        () => create()
          ..statusFilter = statusFilter
          ..searchQuery = searchQuery,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        statusFilter: statusFilter,
        searchQuery: searchQuery,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<AdminUsersList, List<AdminUserModel>>
  createElement() {
    return _AdminUsersListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AdminUsersListProvider &&
        other.statusFilter == statusFilter &&
        other.searchQuery == searchQuery;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, statusFilter.hashCode);
    hash = _SystemHash.combine(hash, searchQuery.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AdminUsersListRef
    on AutoDisposeAsyncNotifierProviderRef<List<AdminUserModel>> {
  /// The parameter `statusFilter` of this provider.
  UserStatus? get statusFilter;

  /// The parameter `searchQuery` of this provider.
  String? get searchQuery;
}

class _AdminUsersListProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          AdminUsersList,
          List<AdminUserModel>
        >
    with AdminUsersListRef {
  _AdminUsersListProviderElement(super.provider);

  @override
  UserStatus? get statusFilter =>
      (origin as AdminUsersListProvider).statusFilter;
  @override
  String? get searchQuery => (origin as AdminUsersListProvider).searchQuery;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
