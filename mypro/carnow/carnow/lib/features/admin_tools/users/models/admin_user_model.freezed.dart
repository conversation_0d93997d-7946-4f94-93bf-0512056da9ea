// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AdminUserModel {

 String get id; String? get email; String? get fullName; String? get phoneNumber; UserStatus get status; String? get bannedReason; DateTime? get bannedAt; String? get bannedBy; String? get frozenReason; DateTime? get frozenAt; String? get frozenBy; DateTime? get frozenUntil; bool get isEmailVerified; bool get isPhoneVerified; bool get isSeller; bool get isAdmin; DateTime? get lastLoginAt; DateTime get createdAt; DateTime? get updatedAt; int get totalOrders; double get totalSpent; int get violationsCount; String? get notes;
/// Create a copy of AdminUserModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdminUserModelCopyWith<AdminUserModel> get copyWith => _$AdminUserModelCopyWithImpl<AdminUserModel>(this as AdminUserModel, _$identity);

  /// Serializes this AdminUserModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdminUserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.status, status) || other.status == status)&&(identical(other.bannedReason, bannedReason) || other.bannedReason == bannedReason)&&(identical(other.bannedAt, bannedAt) || other.bannedAt == bannedAt)&&(identical(other.bannedBy, bannedBy) || other.bannedBy == bannedBy)&&(identical(other.frozenReason, frozenReason) || other.frozenReason == frozenReason)&&(identical(other.frozenAt, frozenAt) || other.frozenAt == frozenAt)&&(identical(other.frozenBy, frozenBy) || other.frozenBy == frozenBy)&&(identical(other.frozenUntil, frozenUntil) || other.frozenUntil == frozenUntil)&&(identical(other.isEmailVerified, isEmailVerified) || other.isEmailVerified == isEmailVerified)&&(identical(other.isPhoneVerified, isPhoneVerified) || other.isPhoneVerified == isPhoneVerified)&&(identical(other.isSeller, isSeller) || other.isSeller == isSeller)&&(identical(other.isAdmin, isAdmin) || other.isAdmin == isAdmin)&&(identical(other.lastLoginAt, lastLoginAt) || other.lastLoginAt == lastLoginAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalSpent, totalSpent) || other.totalSpent == totalSpent)&&(identical(other.violationsCount, violationsCount) || other.violationsCount == violationsCount)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,email,fullName,phoneNumber,status,bannedReason,bannedAt,bannedBy,frozenReason,frozenAt,frozenBy,frozenUntil,isEmailVerified,isPhoneVerified,isSeller,isAdmin,lastLoginAt,createdAt,updatedAt,totalOrders,totalSpent,violationsCount,notes]);

@override
String toString() {
  return 'AdminUserModel(id: $id, email: $email, fullName: $fullName, phoneNumber: $phoneNumber, status: $status, bannedReason: $bannedReason, bannedAt: $bannedAt, bannedBy: $bannedBy, frozenReason: $frozenReason, frozenAt: $frozenAt, frozenBy: $frozenBy, frozenUntil: $frozenUntil, isEmailVerified: $isEmailVerified, isPhoneVerified: $isPhoneVerified, isSeller: $isSeller, isAdmin: $isAdmin, lastLoginAt: $lastLoginAt, createdAt: $createdAt, updatedAt: $updatedAt, totalOrders: $totalOrders, totalSpent: $totalSpent, violationsCount: $violationsCount, notes: $notes)';
}


}

/// @nodoc
abstract mixin class $AdminUserModelCopyWith<$Res>  {
  factory $AdminUserModelCopyWith(AdminUserModel value, $Res Function(AdminUserModel) _then) = _$AdminUserModelCopyWithImpl;
@useResult
$Res call({
 String id, String? email, String? fullName, String? phoneNumber, UserStatus status, String? bannedReason, DateTime? bannedAt, String? bannedBy, String? frozenReason, DateTime? frozenAt, String? frozenBy, DateTime? frozenUntil, bool isEmailVerified, bool isPhoneVerified, bool isSeller, bool isAdmin, DateTime? lastLoginAt, DateTime createdAt, DateTime? updatedAt, int totalOrders, double totalSpent, int violationsCount, String? notes
});




}
/// @nodoc
class _$AdminUserModelCopyWithImpl<$Res>
    implements $AdminUserModelCopyWith<$Res> {
  _$AdminUserModelCopyWithImpl(this._self, this._then);

  final AdminUserModel _self;
  final $Res Function(AdminUserModel) _then;

/// Create a copy of AdminUserModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? email = freezed,Object? fullName = freezed,Object? phoneNumber = freezed,Object? status = null,Object? bannedReason = freezed,Object? bannedAt = freezed,Object? bannedBy = freezed,Object? frozenReason = freezed,Object? frozenAt = freezed,Object? frozenBy = freezed,Object? frozenUntil = freezed,Object? isEmailVerified = null,Object? isPhoneVerified = null,Object? isSeller = null,Object? isAdmin = null,Object? lastLoginAt = freezed,Object? createdAt = null,Object? updatedAt = freezed,Object? totalOrders = null,Object? totalSpent = null,Object? violationsCount = null,Object? notes = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,fullName: freezed == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as UserStatus,bannedReason: freezed == bannedReason ? _self.bannedReason : bannedReason // ignore: cast_nullable_to_non_nullable
as String?,bannedAt: freezed == bannedAt ? _self.bannedAt : bannedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,bannedBy: freezed == bannedBy ? _self.bannedBy : bannedBy // ignore: cast_nullable_to_non_nullable
as String?,frozenReason: freezed == frozenReason ? _self.frozenReason : frozenReason // ignore: cast_nullable_to_non_nullable
as String?,frozenAt: freezed == frozenAt ? _self.frozenAt : frozenAt // ignore: cast_nullable_to_non_nullable
as DateTime?,frozenBy: freezed == frozenBy ? _self.frozenBy : frozenBy // ignore: cast_nullable_to_non_nullable
as String?,frozenUntil: freezed == frozenUntil ? _self.frozenUntil : frozenUntil // ignore: cast_nullable_to_non_nullable
as DateTime?,isEmailVerified: null == isEmailVerified ? _self.isEmailVerified : isEmailVerified // ignore: cast_nullable_to_non_nullable
as bool,isPhoneVerified: null == isPhoneVerified ? _self.isPhoneVerified : isPhoneVerified // ignore: cast_nullable_to_non_nullable
as bool,isSeller: null == isSeller ? _self.isSeller : isSeller // ignore: cast_nullable_to_non_nullable
as bool,isAdmin: null == isAdmin ? _self.isAdmin : isAdmin // ignore: cast_nullable_to_non_nullable
as bool,lastLoginAt: freezed == lastLoginAt ? _self.lastLoginAt : lastLoginAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,totalSpent: null == totalSpent ? _self.totalSpent : totalSpent // ignore: cast_nullable_to_non_nullable
as double,violationsCount: null == violationsCount ? _self.violationsCount : violationsCount // ignore: cast_nullable_to_non_nullable
as int,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [AdminUserModel].
extension AdminUserModelPatterns on AdminUserModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AdminUserModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AdminUserModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AdminUserModel value)  $default,){
final _that = this;
switch (_that) {
case _AdminUserModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AdminUserModel value)?  $default,){
final _that = this;
switch (_that) {
case _AdminUserModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String? email,  String? fullName,  String? phoneNumber,  UserStatus status,  String? bannedReason,  DateTime? bannedAt,  String? bannedBy,  String? frozenReason,  DateTime? frozenAt,  String? frozenBy,  DateTime? frozenUntil,  bool isEmailVerified,  bool isPhoneVerified,  bool isSeller,  bool isAdmin,  DateTime? lastLoginAt,  DateTime createdAt,  DateTime? updatedAt,  int totalOrders,  double totalSpent,  int violationsCount,  String? notes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AdminUserModel() when $default != null:
return $default(_that.id,_that.email,_that.fullName,_that.phoneNumber,_that.status,_that.bannedReason,_that.bannedAt,_that.bannedBy,_that.frozenReason,_that.frozenAt,_that.frozenBy,_that.frozenUntil,_that.isEmailVerified,_that.isPhoneVerified,_that.isSeller,_that.isAdmin,_that.lastLoginAt,_that.createdAt,_that.updatedAt,_that.totalOrders,_that.totalSpent,_that.violationsCount,_that.notes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String? email,  String? fullName,  String? phoneNumber,  UserStatus status,  String? bannedReason,  DateTime? bannedAt,  String? bannedBy,  String? frozenReason,  DateTime? frozenAt,  String? frozenBy,  DateTime? frozenUntil,  bool isEmailVerified,  bool isPhoneVerified,  bool isSeller,  bool isAdmin,  DateTime? lastLoginAt,  DateTime createdAt,  DateTime? updatedAt,  int totalOrders,  double totalSpent,  int violationsCount,  String? notes)  $default,) {final _that = this;
switch (_that) {
case _AdminUserModel():
return $default(_that.id,_that.email,_that.fullName,_that.phoneNumber,_that.status,_that.bannedReason,_that.bannedAt,_that.bannedBy,_that.frozenReason,_that.frozenAt,_that.frozenBy,_that.frozenUntil,_that.isEmailVerified,_that.isPhoneVerified,_that.isSeller,_that.isAdmin,_that.lastLoginAt,_that.createdAt,_that.updatedAt,_that.totalOrders,_that.totalSpent,_that.violationsCount,_that.notes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String? email,  String? fullName,  String? phoneNumber,  UserStatus status,  String? bannedReason,  DateTime? bannedAt,  String? bannedBy,  String? frozenReason,  DateTime? frozenAt,  String? frozenBy,  DateTime? frozenUntil,  bool isEmailVerified,  bool isPhoneVerified,  bool isSeller,  bool isAdmin,  DateTime? lastLoginAt,  DateTime createdAt,  DateTime? updatedAt,  int totalOrders,  double totalSpent,  int violationsCount,  String? notes)?  $default,) {final _that = this;
switch (_that) {
case _AdminUserModel() when $default != null:
return $default(_that.id,_that.email,_that.fullName,_that.phoneNumber,_that.status,_that.bannedReason,_that.bannedAt,_that.bannedBy,_that.frozenReason,_that.frozenAt,_that.frozenBy,_that.frozenUntil,_that.isEmailVerified,_that.isPhoneVerified,_that.isSeller,_that.isAdmin,_that.lastLoginAt,_that.createdAt,_that.updatedAt,_that.totalOrders,_that.totalSpent,_that.violationsCount,_that.notes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AdminUserModel implements AdminUserModel {
  const _AdminUserModel({required this.id, required this.email, this.fullName, this.phoneNumber, required this.status, this.bannedReason, this.bannedAt, this.bannedBy, this.frozenReason, this.frozenAt, this.frozenBy, this.frozenUntil, required this.isEmailVerified, required this.isPhoneVerified, this.isSeller = false, this.isAdmin = false, this.lastLoginAt, required this.createdAt, this.updatedAt, this.totalOrders = 0, this.totalSpent = 0.0, this.violationsCount = 0, this.notes});
  factory _AdminUserModel.fromJson(Map<String, dynamic> json) => _$AdminUserModelFromJson(json);

@override final  String id;
@override final  String? email;
@override final  String? fullName;
@override final  String? phoneNumber;
@override final  UserStatus status;
@override final  String? bannedReason;
@override final  DateTime? bannedAt;
@override final  String? bannedBy;
@override final  String? frozenReason;
@override final  DateTime? frozenAt;
@override final  String? frozenBy;
@override final  DateTime? frozenUntil;
@override final  bool isEmailVerified;
@override final  bool isPhoneVerified;
@override@JsonKey() final  bool isSeller;
@override@JsonKey() final  bool isAdmin;
@override final  DateTime? lastLoginAt;
@override final  DateTime createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey() final  int totalOrders;
@override@JsonKey() final  double totalSpent;
@override@JsonKey() final  int violationsCount;
@override final  String? notes;

/// Create a copy of AdminUserModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AdminUserModelCopyWith<_AdminUserModel> get copyWith => __$AdminUserModelCopyWithImpl<_AdminUserModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AdminUserModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AdminUserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.status, status) || other.status == status)&&(identical(other.bannedReason, bannedReason) || other.bannedReason == bannedReason)&&(identical(other.bannedAt, bannedAt) || other.bannedAt == bannedAt)&&(identical(other.bannedBy, bannedBy) || other.bannedBy == bannedBy)&&(identical(other.frozenReason, frozenReason) || other.frozenReason == frozenReason)&&(identical(other.frozenAt, frozenAt) || other.frozenAt == frozenAt)&&(identical(other.frozenBy, frozenBy) || other.frozenBy == frozenBy)&&(identical(other.frozenUntil, frozenUntil) || other.frozenUntil == frozenUntil)&&(identical(other.isEmailVerified, isEmailVerified) || other.isEmailVerified == isEmailVerified)&&(identical(other.isPhoneVerified, isPhoneVerified) || other.isPhoneVerified == isPhoneVerified)&&(identical(other.isSeller, isSeller) || other.isSeller == isSeller)&&(identical(other.isAdmin, isAdmin) || other.isAdmin == isAdmin)&&(identical(other.lastLoginAt, lastLoginAt) || other.lastLoginAt == lastLoginAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalSpent, totalSpent) || other.totalSpent == totalSpent)&&(identical(other.violationsCount, violationsCount) || other.violationsCount == violationsCount)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,email,fullName,phoneNumber,status,bannedReason,bannedAt,bannedBy,frozenReason,frozenAt,frozenBy,frozenUntil,isEmailVerified,isPhoneVerified,isSeller,isAdmin,lastLoginAt,createdAt,updatedAt,totalOrders,totalSpent,violationsCount,notes]);

@override
String toString() {
  return 'AdminUserModel(id: $id, email: $email, fullName: $fullName, phoneNumber: $phoneNumber, status: $status, bannedReason: $bannedReason, bannedAt: $bannedAt, bannedBy: $bannedBy, frozenReason: $frozenReason, frozenAt: $frozenAt, frozenBy: $frozenBy, frozenUntil: $frozenUntil, isEmailVerified: $isEmailVerified, isPhoneVerified: $isPhoneVerified, isSeller: $isSeller, isAdmin: $isAdmin, lastLoginAt: $lastLoginAt, createdAt: $createdAt, updatedAt: $updatedAt, totalOrders: $totalOrders, totalSpent: $totalSpent, violationsCount: $violationsCount, notes: $notes)';
}


}

/// @nodoc
abstract mixin class _$AdminUserModelCopyWith<$Res> implements $AdminUserModelCopyWith<$Res> {
  factory _$AdminUserModelCopyWith(_AdminUserModel value, $Res Function(_AdminUserModel) _then) = __$AdminUserModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String? email, String? fullName, String? phoneNumber, UserStatus status, String? bannedReason, DateTime? bannedAt, String? bannedBy, String? frozenReason, DateTime? frozenAt, String? frozenBy, DateTime? frozenUntil, bool isEmailVerified, bool isPhoneVerified, bool isSeller, bool isAdmin, DateTime? lastLoginAt, DateTime createdAt, DateTime? updatedAt, int totalOrders, double totalSpent, int violationsCount, String? notes
});




}
/// @nodoc
class __$AdminUserModelCopyWithImpl<$Res>
    implements _$AdminUserModelCopyWith<$Res> {
  __$AdminUserModelCopyWithImpl(this._self, this._then);

  final _AdminUserModel _self;
  final $Res Function(_AdminUserModel) _then;

/// Create a copy of AdminUserModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? email = freezed,Object? fullName = freezed,Object? phoneNumber = freezed,Object? status = null,Object? bannedReason = freezed,Object? bannedAt = freezed,Object? bannedBy = freezed,Object? frozenReason = freezed,Object? frozenAt = freezed,Object? frozenBy = freezed,Object? frozenUntil = freezed,Object? isEmailVerified = null,Object? isPhoneVerified = null,Object? isSeller = null,Object? isAdmin = null,Object? lastLoginAt = freezed,Object? createdAt = null,Object? updatedAt = freezed,Object? totalOrders = null,Object? totalSpent = null,Object? violationsCount = null,Object? notes = freezed,}) {
  return _then(_AdminUserModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,fullName: freezed == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as UserStatus,bannedReason: freezed == bannedReason ? _self.bannedReason : bannedReason // ignore: cast_nullable_to_non_nullable
as String?,bannedAt: freezed == bannedAt ? _self.bannedAt : bannedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,bannedBy: freezed == bannedBy ? _self.bannedBy : bannedBy // ignore: cast_nullable_to_non_nullable
as String?,frozenReason: freezed == frozenReason ? _self.frozenReason : frozenReason // ignore: cast_nullable_to_non_nullable
as String?,frozenAt: freezed == frozenAt ? _self.frozenAt : frozenAt // ignore: cast_nullable_to_non_nullable
as DateTime?,frozenBy: freezed == frozenBy ? _self.frozenBy : frozenBy // ignore: cast_nullable_to_non_nullable
as String?,frozenUntil: freezed == frozenUntil ? _self.frozenUntil : frozenUntil // ignore: cast_nullable_to_non_nullable
as DateTime?,isEmailVerified: null == isEmailVerified ? _self.isEmailVerified : isEmailVerified // ignore: cast_nullable_to_non_nullable
as bool,isPhoneVerified: null == isPhoneVerified ? _self.isPhoneVerified : isPhoneVerified // ignore: cast_nullable_to_non_nullable
as bool,isSeller: null == isSeller ? _self.isSeller : isSeller // ignore: cast_nullable_to_non_nullable
as bool,isAdmin: null == isAdmin ? _self.isAdmin : isAdmin // ignore: cast_nullable_to_non_nullable
as bool,lastLoginAt: freezed == lastLoginAt ? _self.lastLoginAt : lastLoginAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,totalSpent: null == totalSpent ? _self.totalSpent : totalSpent // ignore: cast_nullable_to_non_nullable
as double,violationsCount: null == violationsCount ? _self.violationsCount : violationsCount // ignore: cast_nullable_to_non_nullable
as int,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$UserManagementAction {

 String get id; String get userId; String get adminId; String get actionType;// ban, unban, freeze, unfreeze, delete
 DateTime get actionDate; String? get reason; DateTime? get expiresAt;// للتجميد المؤقت
 Map<String, dynamic>? get metadata;
/// Create a copy of UserManagementAction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserManagementActionCopyWith<UserManagementAction> get copyWith => _$UserManagementActionCopyWithImpl<UserManagementAction>(this as UserManagementAction, _$identity);

  /// Serializes this UserManagementAction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserManagementAction&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.adminId, adminId) || other.adminId == adminId)&&(identical(other.actionType, actionType) || other.actionType == actionType)&&(identical(other.actionDate, actionDate) || other.actionDate == actionDate)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,adminId,actionType,actionDate,reason,expiresAt,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'UserManagementAction(id: $id, userId: $userId, adminId: $adminId, actionType: $actionType, actionDate: $actionDate, reason: $reason, expiresAt: $expiresAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $UserManagementActionCopyWith<$Res>  {
  factory $UserManagementActionCopyWith(UserManagementAction value, $Res Function(UserManagementAction) _then) = _$UserManagementActionCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String adminId, String actionType, DateTime actionDate, String? reason, DateTime? expiresAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$UserManagementActionCopyWithImpl<$Res>
    implements $UserManagementActionCopyWith<$Res> {
  _$UserManagementActionCopyWithImpl(this._self, this._then);

  final UserManagementAction _self;
  final $Res Function(UserManagementAction) _then;

/// Create a copy of UserManagementAction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? adminId = null,Object? actionType = null,Object? actionDate = null,Object? reason = freezed,Object? expiresAt = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,adminId: null == adminId ? _self.adminId : adminId // ignore: cast_nullable_to_non_nullable
as String,actionType: null == actionType ? _self.actionType : actionType // ignore: cast_nullable_to_non_nullable
as String,actionDate: null == actionDate ? _self.actionDate : actionDate // ignore: cast_nullable_to_non_nullable
as DateTime,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [UserManagementAction].
extension UserManagementActionPatterns on UserManagementAction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserManagementAction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserManagementAction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserManagementAction value)  $default,){
final _that = this;
switch (_that) {
case _UserManagementAction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserManagementAction value)?  $default,){
final _that = this;
switch (_that) {
case _UserManagementAction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String adminId,  String actionType,  DateTime actionDate,  String? reason,  DateTime? expiresAt,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserManagementAction() when $default != null:
return $default(_that.id,_that.userId,_that.adminId,_that.actionType,_that.actionDate,_that.reason,_that.expiresAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String adminId,  String actionType,  DateTime actionDate,  String? reason,  DateTime? expiresAt,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _UserManagementAction():
return $default(_that.id,_that.userId,_that.adminId,_that.actionType,_that.actionDate,_that.reason,_that.expiresAt,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String adminId,  String actionType,  DateTime actionDate,  String? reason,  DateTime? expiresAt,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _UserManagementAction() when $default != null:
return $default(_that.id,_that.userId,_that.adminId,_that.actionType,_that.actionDate,_that.reason,_that.expiresAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UserManagementAction implements UserManagementAction {
  const _UserManagementAction({required this.id, required this.userId, required this.adminId, required this.actionType, required this.actionDate, this.reason, this.expiresAt, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _UserManagementAction.fromJson(Map<String, dynamic> json) => _$UserManagementActionFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String adminId;
@override final  String actionType;
// ban, unban, freeze, unfreeze, delete
@override final  DateTime actionDate;
@override final  String? reason;
@override final  DateTime? expiresAt;
// للتجميد المؤقت
 final  Map<String, dynamic>? _metadata;
// للتجميد المؤقت
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of UserManagementAction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserManagementActionCopyWith<_UserManagementAction> get copyWith => __$UserManagementActionCopyWithImpl<_UserManagementAction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserManagementActionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserManagementAction&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.adminId, adminId) || other.adminId == adminId)&&(identical(other.actionType, actionType) || other.actionType == actionType)&&(identical(other.actionDate, actionDate) || other.actionDate == actionDate)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,adminId,actionType,actionDate,reason,expiresAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'UserManagementAction(id: $id, userId: $userId, adminId: $adminId, actionType: $actionType, actionDate: $actionDate, reason: $reason, expiresAt: $expiresAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$UserManagementActionCopyWith<$Res> implements $UserManagementActionCopyWith<$Res> {
  factory _$UserManagementActionCopyWith(_UserManagementAction value, $Res Function(_UserManagementAction) _then) = __$UserManagementActionCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String adminId, String actionType, DateTime actionDate, String? reason, DateTime? expiresAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$UserManagementActionCopyWithImpl<$Res>
    implements _$UserManagementActionCopyWith<$Res> {
  __$UserManagementActionCopyWithImpl(this._self, this._then);

  final _UserManagementAction _self;
  final $Res Function(_UserManagementAction) _then;

/// Create a copy of UserManagementAction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? adminId = null,Object? actionType = null,Object? actionDate = null,Object? reason = freezed,Object? expiresAt = freezed,Object? metadata = freezed,}) {
  return _then(_UserManagementAction(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,adminId: null == adminId ? _self.adminId : adminId // ignore: cast_nullable_to_non_nullable
as String,actionType: null == actionType ? _self.actionType : actionType // ignore: cast_nullable_to_non_nullable
as String,actionDate: null == actionDate ? _self.actionDate : actionDate // ignore: cast_nullable_to_non_nullable
as DateTime,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on
