// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_list_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$vehicleMakesHash() => r'be409e8d172e9f7269edb5efb045ead6acfacfbb';

/// See also [vehicleMakes].
@ProviderFor(vehicleMakes)
final vehicleMakesProvider =
    AutoDisposeFutureProvider<List<VehicleMake>>.internal(
      vehicleMakes,
      name: r'vehicleMakesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$vehicleMakesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef VehicleMakesRef = AutoDisposeFutureProviderRef<List<VehicleMake>>;
String _$vehicleModelsHash() => r'8e3407fcdfdcbb0c2c009e9c11913c23234bfab6';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [vehicleModels].
@ProviderFor(vehicleModels)
const vehicleModelsProvider = VehicleModelsFamily();

/// See also [vehicleModels].
class VehicleModelsFamily extends Family<AsyncValue<List<VehicleModel>>> {
  /// See also [vehicleModels].
  const VehicleModelsFamily();

  /// See also [vehicleModels].
  VehicleModelsProvider call({int? makeId}) {
    return VehicleModelsProvider(makeId: makeId);
  }

  @override
  VehicleModelsProvider getProviderOverride(
    covariant VehicleModelsProvider provider,
  ) {
    return call(makeId: provider.makeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleModelsProvider';
}

/// See also [vehicleModels].
class VehicleModelsProvider
    extends AutoDisposeFutureProvider<List<VehicleModel>> {
  /// See also [vehicleModels].
  VehicleModelsProvider({int? makeId})
    : this._internal(
        (ref) => vehicleModels(ref as VehicleModelsRef, makeId: makeId),
        from: vehicleModelsProvider,
        name: r'vehicleModelsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleModelsHash,
        dependencies: VehicleModelsFamily._dependencies,
        allTransitiveDependencies:
            VehicleModelsFamily._allTransitiveDependencies,
        makeId: makeId,
      );

  VehicleModelsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.makeId,
  }) : super.internal();

  final int? makeId;

  @override
  Override overrideWith(
    FutureOr<List<VehicleModel>> Function(VehicleModelsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleModelsProvider._internal(
        (ref) => create(ref as VehicleModelsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        makeId: makeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleModel>> createElement() {
    return _VehicleModelsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleModelsProvider && other.makeId == makeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, makeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleModelsRef on AutoDisposeFutureProviderRef<List<VehicleModel>> {
  /// The parameter `makeId` of this provider.
  int? get makeId;
}

class _VehicleModelsProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleModel>>
    with VehicleModelsRef {
  _VehicleModelsProviderElement(super.provider);

  @override
  int? get makeId => (origin as VehicleModelsProvider).makeId;
}

String _$vehicleGenerationsHash() =>
    r'1f99bfb25c7808023601b8915ada3acc5cf9c682';

/// See also [vehicleGenerations].
@ProviderFor(vehicleGenerations)
const vehicleGenerationsProvider = VehicleGenerationsFamily();

/// See also [vehicleGenerations].
class VehicleGenerationsFamily
    extends Family<AsyncValue<List<VehicleGeneration>>> {
  /// See also [vehicleGenerations].
  const VehicleGenerationsFamily();

  /// See also [vehicleGenerations].
  VehicleGenerationsProvider call({required int modelId}) {
    return VehicleGenerationsProvider(modelId: modelId);
  }

  @override
  VehicleGenerationsProvider getProviderOverride(
    covariant VehicleGenerationsProvider provider,
  ) {
    return call(modelId: provider.modelId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleGenerationsProvider';
}

/// See also [vehicleGenerations].
class VehicleGenerationsProvider
    extends AutoDisposeFutureProvider<List<VehicleGeneration>> {
  /// See also [vehicleGenerations].
  VehicleGenerationsProvider({required int modelId})
    : this._internal(
        (ref) =>
            vehicleGenerations(ref as VehicleGenerationsRef, modelId: modelId),
        from: vehicleGenerationsProvider,
        name: r'vehicleGenerationsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleGenerationsHash,
        dependencies: VehicleGenerationsFamily._dependencies,
        allTransitiveDependencies:
            VehicleGenerationsFamily._allTransitiveDependencies,
        modelId: modelId,
      );

  VehicleGenerationsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.modelId,
  }) : super.internal();

  final int modelId;

  @override
  Override overrideWith(
    FutureOr<List<VehicleGeneration>> Function(VehicleGenerationsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleGenerationsProvider._internal(
        (ref) => create(ref as VehicleGenerationsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        modelId: modelId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleGeneration>> createElement() {
    return _VehicleGenerationsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleGenerationsProvider && other.modelId == modelId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, modelId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleGenerationsRef
    on AutoDisposeFutureProviderRef<List<VehicleGeneration>> {
  /// The parameter `modelId` of this provider.
  int get modelId;
}

class _VehicleGenerationsProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleGeneration>>
    with VehicleGenerationsRef {
  _VehicleGenerationsProviderElement(super.provider);

  @override
  int get modelId => (origin as VehicleGenerationsProvider).modelId;
}

String _$vehicleTrimsHash() => r'ae36adfccd74bdf9e74421c83d68585edd2595d1';

/// See also [vehicleTrims].
@ProviderFor(vehicleTrims)
const vehicleTrimsProvider = VehicleTrimsFamily();

/// See also [vehicleTrims].
class VehicleTrimsFamily extends Family<AsyncValue<List<VehicleTrim>>> {
  /// See also [vehicleTrims].
  const VehicleTrimsFamily();

  /// See also [vehicleTrims].
  VehicleTrimsProvider call({required int modelId}) {
    return VehicleTrimsProvider(modelId: modelId);
  }

  @override
  VehicleTrimsProvider getProviderOverride(
    covariant VehicleTrimsProvider provider,
  ) {
    return call(modelId: provider.modelId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleTrimsProvider';
}

/// See also [vehicleTrims].
class VehicleTrimsProvider
    extends AutoDisposeFutureProvider<List<VehicleTrim>> {
  /// See also [vehicleTrims].
  VehicleTrimsProvider({required int modelId})
    : this._internal(
        (ref) => vehicleTrims(ref as VehicleTrimsRef, modelId: modelId),
        from: vehicleTrimsProvider,
        name: r'vehicleTrimsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleTrimsHash,
        dependencies: VehicleTrimsFamily._dependencies,
        allTransitiveDependencies:
            VehicleTrimsFamily._allTransitiveDependencies,
        modelId: modelId,
      );

  VehicleTrimsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.modelId,
  }) : super.internal();

  final int modelId;

  @override
  Override overrideWith(
    FutureOr<List<VehicleTrim>> Function(VehicleTrimsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleTrimsProvider._internal(
        (ref) => create(ref as VehicleTrimsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        modelId: modelId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleTrim>> createElement() {
    return _VehicleTrimsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleTrimsProvider && other.modelId == modelId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, modelId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleTrimsRef on AutoDisposeFutureProviderRef<List<VehicleTrim>> {
  /// The parameter `modelId` of this provider.
  int get modelId;
}

class _VehicleTrimsProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleTrim>>
    with VehicleTrimsRef {
  _VehicleTrimsProviderElement(super.provider);

  @override
  int get modelId => (origin as VehicleTrimsProvider).modelId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
