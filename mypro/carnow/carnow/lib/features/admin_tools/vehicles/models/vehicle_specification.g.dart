// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_specification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleSpecification _$VehicleSpecificationFromJson(
  Map<String, dynamic> json,
) => _VehicleSpecification(
  id: (json['id'] as num).toInt(),
  makeId: (json['make_id'] as num).toInt(),
  modelId: (json['model_id'] as num).toInt(),
  vehicleTypeId: (json['vehicle_type_id'] as num).toInt(),
  yearFrom: (json['year_from'] as num).toInt(),
  yearTo: (json['year_to'] as num?)?.toInt(),
  engineConfigurationId: (json['engine_configuration_id'] as num?)?.toInt(),
  fuelTypeId: (json['fuel_type_id'] as num?)?.toInt(),
  transmissionId: (json['transmission_id'] as num?)?.toInt(),
  specs: json['specifications'] as Map<String, dynamic>? ?? const {},
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleSpecificationToJson(
  _VehicleSpecification instance,
) => <String, dynamic>{
  'id': instance.id,
  'make_id': instance.makeId,
  'model_id': instance.modelId,
  'vehicle_type_id': instance.vehicleTypeId,
  'year_from': instance.yearFrom,
  'year_to': instance.yearTo,
  'engine_configuration_id': instance.engineConfigurationId,
  'fuel_type_id': instance.fuelTypeId,
  'transmission_id': instance.transmissionId,
  'specifications': instance.specs,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
  'is_deleted': instance.isDeleted,
};
