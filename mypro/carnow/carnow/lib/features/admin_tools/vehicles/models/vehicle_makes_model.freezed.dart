// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_makes_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleMakeModel {

 String get id; String get name; String? get nameAr; String? get logoUrl; String? get countryOfOrigin; String? get website; bool get isActive; int get sortOrder;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of VehicleMakeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleMakeModelCopyWith<VehicleMakeModel> get copyWith => _$VehicleMakeModelCopyWithImpl<VehicleMakeModel>(this as VehicleMakeModel, _$identity);

  /// Serializes this VehicleMakeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleMakeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.logoUrl, logoUrl) || other.logoUrl == logoUrl)&&(identical(other.countryOfOrigin, countryOfOrigin) || other.countryOfOrigin == countryOfOrigin)&&(identical(other.website, website) || other.website == website)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,logoUrl,countryOfOrigin,website,isActive,sortOrder,createdAt,updatedAt);

@override
String toString() {
  return 'VehicleMakeModel(id: $id, name: $name, nameAr: $nameAr, logoUrl: $logoUrl, countryOfOrigin: $countryOfOrigin, website: $website, isActive: $isActive, sortOrder: $sortOrder, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $VehicleMakeModelCopyWith<$Res>  {
  factory $VehicleMakeModelCopyWith(VehicleMakeModel value, $Res Function(VehicleMakeModel) _then) = _$VehicleMakeModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? nameAr, String? logoUrl, String? countryOfOrigin, String? website, bool isActive, int sortOrder,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$VehicleMakeModelCopyWithImpl<$Res>
    implements $VehicleMakeModelCopyWith<$Res> {
  _$VehicleMakeModelCopyWithImpl(this._self, this._then);

  final VehicleMakeModel _self;
  final $Res Function(VehicleMakeModel) _then;

/// Create a copy of VehicleMakeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? logoUrl = freezed,Object? countryOfOrigin = freezed,Object? website = freezed,Object? isActive = null,Object? sortOrder = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,logoUrl: freezed == logoUrl ? _self.logoUrl : logoUrl // ignore: cast_nullable_to_non_nullable
as String?,countryOfOrigin: freezed == countryOfOrigin ? _self.countryOfOrigin : countryOfOrigin // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleMakeModel].
extension VehicleMakeModelPatterns on VehicleMakeModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleMakeModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleMakeModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleMakeModel value)  $default,){
final _that = this;
switch (_that) {
case _VehicleMakeModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleMakeModel value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleMakeModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? logoUrl,  String? countryOfOrigin,  String? website,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleMakeModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.logoUrl,_that.countryOfOrigin,_that.website,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? logoUrl,  String? countryOfOrigin,  String? website,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _VehicleMakeModel():
return $default(_that.id,_that.name,_that.nameAr,_that.logoUrl,_that.countryOfOrigin,_that.website,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? nameAr,  String? logoUrl,  String? countryOfOrigin,  String? website,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _VehicleMakeModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.logoUrl,_that.countryOfOrigin,_that.website,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _VehicleMakeModel implements VehicleMakeModel {
  const _VehicleMakeModel({required this.id, required this.name, this.nameAr, this.logoUrl, this.countryOfOrigin, this.website, this.isActive = true, this.sortOrder = 0, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _VehicleMakeModel.fromJson(Map<String, dynamic> json) => _$VehicleMakeModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String? nameAr;
@override final  String? logoUrl;
@override final  String? countryOfOrigin;
@override final  String? website;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int sortOrder;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of VehicleMakeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleMakeModelCopyWith<_VehicleMakeModel> get copyWith => __$VehicleMakeModelCopyWithImpl<_VehicleMakeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleMakeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleMakeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.logoUrl, logoUrl) || other.logoUrl == logoUrl)&&(identical(other.countryOfOrigin, countryOfOrigin) || other.countryOfOrigin == countryOfOrigin)&&(identical(other.website, website) || other.website == website)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,logoUrl,countryOfOrigin,website,isActive,sortOrder,createdAt,updatedAt);

@override
String toString() {
  return 'VehicleMakeModel(id: $id, name: $name, nameAr: $nameAr, logoUrl: $logoUrl, countryOfOrigin: $countryOfOrigin, website: $website, isActive: $isActive, sortOrder: $sortOrder, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$VehicleMakeModelCopyWith<$Res> implements $VehicleMakeModelCopyWith<$Res> {
  factory _$VehicleMakeModelCopyWith(_VehicleMakeModel value, $Res Function(_VehicleMakeModel) _then) = __$VehicleMakeModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? nameAr, String? logoUrl, String? countryOfOrigin, String? website, bool isActive, int sortOrder,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$VehicleMakeModelCopyWithImpl<$Res>
    implements _$VehicleMakeModelCopyWith<$Res> {
  __$VehicleMakeModelCopyWithImpl(this._self, this._then);

  final _VehicleMakeModel _self;
  final $Res Function(_VehicleMakeModel) _then;

/// Create a copy of VehicleMakeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? logoUrl = freezed,Object? countryOfOrigin = freezed,Object? website = freezed,Object? isActive = null,Object? sortOrder = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_VehicleMakeModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,logoUrl: freezed == logoUrl ? _self.logoUrl : logoUrl // ignore: cast_nullable_to_non_nullable
as String?,countryOfOrigin: freezed == countryOfOrigin ? _self.countryOfOrigin : countryOfOrigin // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$VehicleModelDetail {

 String get id; String get makeId; String get name; String? get nameAr; int? get yearFrom; int? get yearTo; String? get generation; String? get bodyStyle; bool get isActive; int get sortOrder;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of VehicleModelDetail
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleModelDetailCopyWith<VehicleModelDetail> get copyWith => _$VehicleModelDetailCopyWithImpl<VehicleModelDetail>(this as VehicleModelDetail, _$identity);

  /// Serializes this VehicleModelDetail to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleModelDetail&&(identical(other.id, id) || other.id == id)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.generation, generation) || other.generation == generation)&&(identical(other.bodyStyle, bodyStyle) || other.bodyStyle == bodyStyle)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeId,name,nameAr,yearFrom,yearTo,generation,bodyStyle,isActive,sortOrder,createdAt,updatedAt);

@override
String toString() {
  return 'VehicleModelDetail(id: $id, makeId: $makeId, name: $name, nameAr: $nameAr, yearFrom: $yearFrom, yearTo: $yearTo, generation: $generation, bodyStyle: $bodyStyle, isActive: $isActive, sortOrder: $sortOrder, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $VehicleModelDetailCopyWith<$Res>  {
  factory $VehicleModelDetailCopyWith(VehicleModelDetail value, $Res Function(VehicleModelDetail) _then) = _$VehicleModelDetailCopyWithImpl;
@useResult
$Res call({
 String id, String makeId, String name, String? nameAr, int? yearFrom, int? yearTo, String? generation, String? bodyStyle, bool isActive, int sortOrder,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$VehicleModelDetailCopyWithImpl<$Res>
    implements $VehicleModelDetailCopyWith<$Res> {
  _$VehicleModelDetailCopyWithImpl(this._self, this._then);

  final VehicleModelDetail _self;
  final $Res Function(VehicleModelDetail) _then;

/// Create a copy of VehicleModelDetail
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? makeId = null,Object? name = null,Object? nameAr = freezed,Object? yearFrom = freezed,Object? yearTo = freezed,Object? generation = freezed,Object? bodyStyle = freezed,Object? isActive = null,Object? sortOrder = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,generation: freezed == generation ? _self.generation : generation // ignore: cast_nullable_to_non_nullable
as String?,bodyStyle: freezed == bodyStyle ? _self.bodyStyle : bodyStyle // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleModelDetail].
extension VehicleModelDetailPatterns on VehicleModelDetail {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleModelDetail value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleModelDetail() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleModelDetail value)  $default,){
final _that = this;
switch (_that) {
case _VehicleModelDetail():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleModelDetail value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleModelDetail() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String makeId,  String name,  String? nameAr,  int? yearFrom,  int? yearTo,  String? generation,  String? bodyStyle,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleModelDetail() when $default != null:
return $default(_that.id,_that.makeId,_that.name,_that.nameAr,_that.yearFrom,_that.yearTo,_that.generation,_that.bodyStyle,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String makeId,  String name,  String? nameAr,  int? yearFrom,  int? yearTo,  String? generation,  String? bodyStyle,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _VehicleModelDetail():
return $default(_that.id,_that.makeId,_that.name,_that.nameAr,_that.yearFrom,_that.yearTo,_that.generation,_that.bodyStyle,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String makeId,  String name,  String? nameAr,  int? yearFrom,  int? yearTo,  String? generation,  String? bodyStyle,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _VehicleModelDetail() when $default != null:
return $default(_that.id,_that.makeId,_that.name,_that.nameAr,_that.yearFrom,_that.yearTo,_that.generation,_that.bodyStyle,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _VehicleModelDetail implements VehicleModelDetail {
  const _VehicleModelDetail({required this.id, required this.makeId, required this.name, this.nameAr, this.yearFrom, this.yearTo, this.generation, this.bodyStyle, this.isActive = true, this.sortOrder = 0, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _VehicleModelDetail.fromJson(Map<String, dynamic> json) => _$VehicleModelDetailFromJson(json);

@override final  String id;
@override final  String makeId;
@override final  String name;
@override final  String? nameAr;
@override final  int? yearFrom;
@override final  int? yearTo;
@override final  String? generation;
@override final  String? bodyStyle;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int sortOrder;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of VehicleModelDetail
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleModelDetailCopyWith<_VehicleModelDetail> get copyWith => __$VehicleModelDetailCopyWithImpl<_VehicleModelDetail>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleModelDetailToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleModelDetail&&(identical(other.id, id) || other.id == id)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.generation, generation) || other.generation == generation)&&(identical(other.bodyStyle, bodyStyle) || other.bodyStyle == bodyStyle)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeId,name,nameAr,yearFrom,yearTo,generation,bodyStyle,isActive,sortOrder,createdAt,updatedAt);

@override
String toString() {
  return 'VehicleModelDetail(id: $id, makeId: $makeId, name: $name, nameAr: $nameAr, yearFrom: $yearFrom, yearTo: $yearTo, generation: $generation, bodyStyle: $bodyStyle, isActive: $isActive, sortOrder: $sortOrder, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$VehicleModelDetailCopyWith<$Res> implements $VehicleModelDetailCopyWith<$Res> {
  factory _$VehicleModelDetailCopyWith(_VehicleModelDetail value, $Res Function(_VehicleModelDetail) _then) = __$VehicleModelDetailCopyWithImpl;
@override @useResult
$Res call({
 String id, String makeId, String name, String? nameAr, int? yearFrom, int? yearTo, String? generation, String? bodyStyle, bool isActive, int sortOrder,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$VehicleModelDetailCopyWithImpl<$Res>
    implements _$VehicleModelDetailCopyWith<$Res> {
  __$VehicleModelDetailCopyWithImpl(this._self, this._then);

  final _VehicleModelDetail _self;
  final $Res Function(_VehicleModelDetail) _then;

/// Create a copy of VehicleModelDetail
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? makeId = null,Object? name = null,Object? nameAr = freezed,Object? yearFrom = freezed,Object? yearTo = freezed,Object? generation = freezed,Object? bodyStyle = freezed,Object? isActive = null,Object? sortOrder = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_VehicleModelDetail(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,generation: freezed == generation ? _self.generation : generation // ignore: cast_nullable_to_non_nullable
as String?,bodyStyle: freezed == bodyStyle ? _self.bodyStyle : bodyStyle // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$VehicleTypeModel {

 String get id; String get name; String? get nameAr; String? get description; String? get descriptionAr; String? get iconUrl; bool get isActive; int get sortOrder;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of VehicleTypeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleTypeModelCopyWith<VehicleTypeModel> get copyWith => _$VehicleTypeModelCopyWithImpl<VehicleTypeModel>(this as VehicleTypeModel, _$identity);

  /// Serializes this VehicleTypeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleTypeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,descriptionAr,iconUrl,isActive,sortOrder,createdAt,updatedAt);

@override
String toString() {
  return 'VehicleTypeModel(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, iconUrl: $iconUrl, isActive: $isActive, sortOrder: $sortOrder, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $VehicleTypeModelCopyWith<$Res>  {
  factory $VehicleTypeModelCopyWith(VehicleTypeModel value, $Res Function(VehicleTypeModel) _then) = _$VehicleTypeModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? nameAr, String? description, String? descriptionAr, String? iconUrl, bool isActive, int sortOrder,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$VehicleTypeModelCopyWithImpl<$Res>
    implements $VehicleTypeModelCopyWith<$Res> {
  _$VehicleTypeModelCopyWithImpl(this._self, this._then);

  final VehicleTypeModel _self;
  final $Res Function(VehicleTypeModel) _then;

/// Create a copy of VehicleTypeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? description = freezed,Object? descriptionAr = freezed,Object? iconUrl = freezed,Object? isActive = null,Object? sortOrder = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,iconUrl: freezed == iconUrl ? _self.iconUrl : iconUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleTypeModel].
extension VehicleTypeModelPatterns on VehicleTypeModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleTypeModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleTypeModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleTypeModel value)  $default,){
final _that = this;
switch (_that) {
case _VehicleTypeModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleTypeModel value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleTypeModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? description,  String? descriptionAr,  String? iconUrl,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleTypeModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.iconUrl,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? description,  String? descriptionAr,  String? iconUrl,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _VehicleTypeModel():
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.iconUrl,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? nameAr,  String? description,  String? descriptionAr,  String? iconUrl,  bool isActive,  int sortOrder, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _VehicleTypeModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.iconUrl,_that.isActive,_that.sortOrder,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _VehicleTypeModel implements VehicleTypeModel {
  const _VehicleTypeModel({required this.id, required this.name, this.nameAr, this.description, this.descriptionAr, this.iconUrl, this.isActive = true, this.sortOrder = 0, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _VehicleTypeModel.fromJson(Map<String, dynamic> json) => _$VehicleTypeModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String? nameAr;
@override final  String? description;
@override final  String? descriptionAr;
@override final  String? iconUrl;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int sortOrder;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of VehicleTypeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleTypeModelCopyWith<_VehicleTypeModel> get copyWith => __$VehicleTypeModelCopyWithImpl<_VehicleTypeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleTypeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleTypeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,descriptionAr,iconUrl,isActive,sortOrder,createdAt,updatedAt);

@override
String toString() {
  return 'VehicleTypeModel(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, iconUrl: $iconUrl, isActive: $isActive, sortOrder: $sortOrder, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$VehicleTypeModelCopyWith<$Res> implements $VehicleTypeModelCopyWith<$Res> {
  factory _$VehicleTypeModelCopyWith(_VehicleTypeModel value, $Res Function(_VehicleTypeModel) _then) = __$VehicleTypeModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? nameAr, String? description, String? descriptionAr, String? iconUrl, bool isActive, int sortOrder,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$VehicleTypeModelCopyWithImpl<$Res>
    implements _$VehicleTypeModelCopyWith<$Res> {
  __$VehicleTypeModelCopyWithImpl(this._self, this._then);

  final _VehicleTypeModel _self;
  final $Res Function(_VehicleTypeModel) _then;

/// Create a copy of VehicleTypeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? description = freezed,Object? descriptionAr = freezed,Object? iconUrl = freezed,Object? isActive = null,Object? sortOrder = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_VehicleTypeModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,iconUrl: freezed == iconUrl ? _self.iconUrl : iconUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CompleteVehicleModel {

 VehicleModelDetail get model; VehicleMakeModel get make; VehicleTypeModel? get vehicleType;
/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CompleteVehicleModelCopyWith<CompleteVehicleModel> get copyWith => _$CompleteVehicleModelCopyWithImpl<CompleteVehicleModel>(this as CompleteVehicleModel, _$identity);

  /// Serializes this CompleteVehicleModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CompleteVehicleModel&&(identical(other.model, model) || other.model == model)&&(identical(other.make, make) || other.make == make)&&(identical(other.vehicleType, vehicleType) || other.vehicleType == vehicleType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,model,make,vehicleType);

@override
String toString() {
  return 'CompleteVehicleModel(model: $model, make: $make, vehicleType: $vehicleType)';
}


}

/// @nodoc
abstract mixin class $CompleteVehicleModelCopyWith<$Res>  {
  factory $CompleteVehicleModelCopyWith(CompleteVehicleModel value, $Res Function(CompleteVehicleModel) _then) = _$CompleteVehicleModelCopyWithImpl;
@useResult
$Res call({
 VehicleModelDetail model, VehicleMakeModel make, VehicleTypeModel? vehicleType
});


$VehicleModelDetailCopyWith<$Res> get model;$VehicleMakeModelCopyWith<$Res> get make;$VehicleTypeModelCopyWith<$Res>? get vehicleType;

}
/// @nodoc
class _$CompleteVehicleModelCopyWithImpl<$Res>
    implements $CompleteVehicleModelCopyWith<$Res> {
  _$CompleteVehicleModelCopyWithImpl(this._self, this._then);

  final CompleteVehicleModel _self;
  final $Res Function(CompleteVehicleModel) _then;

/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? model = null,Object? make = null,Object? vehicleType = freezed,}) {
  return _then(_self.copyWith(
model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as VehicleModelDetail,make: null == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as VehicleMakeModel,vehicleType: freezed == vehicleType ? _self.vehicleType : vehicleType // ignore: cast_nullable_to_non_nullable
as VehicleTypeModel?,
  ));
}
/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleModelDetailCopyWith<$Res> get model {
  
  return $VehicleModelDetailCopyWith<$Res>(_self.model, (value) {
    return _then(_self.copyWith(model: value));
  });
}/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleMakeModelCopyWith<$Res> get make {
  
  return $VehicleMakeModelCopyWith<$Res>(_self.make, (value) {
    return _then(_self.copyWith(make: value));
  });
}/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleTypeModelCopyWith<$Res>? get vehicleType {
    if (_self.vehicleType == null) {
    return null;
  }

  return $VehicleTypeModelCopyWith<$Res>(_self.vehicleType!, (value) {
    return _then(_self.copyWith(vehicleType: value));
  });
}
}


/// Adds pattern-matching-related methods to [CompleteVehicleModel].
extension CompleteVehicleModelPatterns on CompleteVehicleModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CompleteVehicleModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CompleteVehicleModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CompleteVehicleModel value)  $default,){
final _that = this;
switch (_that) {
case _CompleteVehicleModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CompleteVehicleModel value)?  $default,){
final _that = this;
switch (_that) {
case _CompleteVehicleModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( VehicleModelDetail model,  VehicleMakeModel make,  VehicleTypeModel? vehicleType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CompleteVehicleModel() when $default != null:
return $default(_that.model,_that.make,_that.vehicleType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( VehicleModelDetail model,  VehicleMakeModel make,  VehicleTypeModel? vehicleType)  $default,) {final _that = this;
switch (_that) {
case _CompleteVehicleModel():
return $default(_that.model,_that.make,_that.vehicleType);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( VehicleModelDetail model,  VehicleMakeModel make,  VehicleTypeModel? vehicleType)?  $default,) {final _that = this;
switch (_that) {
case _CompleteVehicleModel() when $default != null:
return $default(_that.model,_that.make,_that.vehicleType);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _CompleteVehicleModel implements CompleteVehicleModel {
  const _CompleteVehicleModel({required this.model, required this.make, this.vehicleType});
  factory _CompleteVehicleModel.fromJson(Map<String, dynamic> json) => _$CompleteVehicleModelFromJson(json);

@override final  VehicleModelDetail model;
@override final  VehicleMakeModel make;
@override final  VehicleTypeModel? vehicleType;

/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CompleteVehicleModelCopyWith<_CompleteVehicleModel> get copyWith => __$CompleteVehicleModelCopyWithImpl<_CompleteVehicleModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CompleteVehicleModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CompleteVehicleModel&&(identical(other.model, model) || other.model == model)&&(identical(other.make, make) || other.make == make)&&(identical(other.vehicleType, vehicleType) || other.vehicleType == vehicleType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,model,make,vehicleType);

@override
String toString() {
  return 'CompleteVehicleModel(model: $model, make: $make, vehicleType: $vehicleType)';
}


}

/// @nodoc
abstract mixin class _$CompleteVehicleModelCopyWith<$Res> implements $CompleteVehicleModelCopyWith<$Res> {
  factory _$CompleteVehicleModelCopyWith(_CompleteVehicleModel value, $Res Function(_CompleteVehicleModel) _then) = __$CompleteVehicleModelCopyWithImpl;
@override @useResult
$Res call({
 VehicleModelDetail model, VehicleMakeModel make, VehicleTypeModel? vehicleType
});


@override $VehicleModelDetailCopyWith<$Res> get model;@override $VehicleMakeModelCopyWith<$Res> get make;@override $VehicleTypeModelCopyWith<$Res>? get vehicleType;

}
/// @nodoc
class __$CompleteVehicleModelCopyWithImpl<$Res>
    implements _$CompleteVehicleModelCopyWith<$Res> {
  __$CompleteVehicleModelCopyWithImpl(this._self, this._then);

  final _CompleteVehicleModel _self;
  final $Res Function(_CompleteVehicleModel) _then;

/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? model = null,Object? make = null,Object? vehicleType = freezed,}) {
  return _then(_CompleteVehicleModel(
model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as VehicleModelDetail,make: null == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as VehicleMakeModel,vehicleType: freezed == vehicleType ? _self.vehicleType : vehicleType // ignore: cast_nullable_to_non_nullable
as VehicleTypeModel?,
  ));
}

/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleModelDetailCopyWith<$Res> get model {
  
  return $VehicleModelDetailCopyWith<$Res>(_self.model, (value) {
    return _then(_self.copyWith(model: value));
  });
}/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleMakeModelCopyWith<$Res> get make {
  
  return $VehicleMakeModelCopyWith<$Res>(_self.make, (value) {
    return _then(_self.copyWith(make: value));
  });
}/// Create a copy of CompleteVehicleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleTypeModelCopyWith<$Res>? get vehicleType {
    if (_self.vehicleType == null) {
    return null;
  }

  return $VehicleTypeModelCopyWith<$Res>(_self.vehicleType!, (value) {
    return _then(_self.copyWith(vehicleType: value));
  });
}
}

// dart format on
