// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_generation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleGeneration _$VehicleGenerationFromJson(Map<String, dynamic> json) =>
    _VehicleGeneration(
      id: (json['id'] as num).toInt(),
      modelId: (json['model_id'] as num).toInt(),
      generationName: json['generation_name'] as String,
      generationCode: json['generation_code'] as String?,
      yearStart: (json['year_start'] as num).toInt(),
      yearEnd: (json['year_end'] as num?)?.toInt(),
      engineId: (json['engine_id'] as num?)?.toInt(),
      platform: json['platform'] as String?,
      isCurrent: json['is_current'] as bool? ?? false,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      isDeleted: json['is_deleted'] as bool? ?? false,
    );

Map<String, dynamic> _$VehicleGenerationToJson(_VehicleGeneration instance) =>
    <String, dynamic>{
      'id': instance.id,
      'model_id': instance.modelId,
      'generation_name': instance.generationName,
      'generation_code': instance.generationCode,
      'year_start': instance.yearStart,
      'year_end': instance.yearEnd,
      'engine_id': instance.engineId,
      'platform': instance.platform,
      'is_current': instance.isCurrent,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
    };
