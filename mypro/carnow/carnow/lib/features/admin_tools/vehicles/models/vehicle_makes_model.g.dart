// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_makes_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleMakeModel _$VehicleMakeModelFromJson(Map<String, dynamic> json) =>
    _VehicleMakeModel(
      id: json['id'] as String,
      name: json['name'] as String,
      nameAr: json['name_ar'] as String?,
      logoUrl: json['logo_url'] as String?,
      countryOfOrigin: json['country_of_origin'] as String?,
      website: json['website'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      sortOrder: (json['sort_order'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$VehicleMakeModelToJson(_VehicleMakeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'name_ar': instance.nameAr,
      'logo_url': instance.logoUrl,
      'country_of_origin': instance.countryOfOrigin,
      'website': instance.website,
      'is_active': instance.isActive,
      'sort_order': instance.sortOrder,
    };

_VehicleModelDetail _$VehicleModelDetailFromJson(Map<String, dynamic> json) =>
    _VehicleModelDetail(
      id: json['id'] as String,
      makeId: json['make_id'] as String,
      name: json['name'] as String,
      nameAr: json['name_ar'] as String?,
      yearFrom: (json['year_from'] as num?)?.toInt(),
      yearTo: (json['year_to'] as num?)?.toInt(),
      generation: json['generation'] as String?,
      bodyStyle: json['body_style'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      sortOrder: (json['sort_order'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$VehicleModelDetailToJson(_VehicleModelDetail instance) =>
    <String, dynamic>{
      'id': instance.id,
      'make_id': instance.makeId,
      'name': instance.name,
      'name_ar': instance.nameAr,
      'year_from': instance.yearFrom,
      'year_to': instance.yearTo,
      'generation': instance.generation,
      'body_style': instance.bodyStyle,
      'is_active': instance.isActive,
      'sort_order': instance.sortOrder,
    };

_VehicleTypeModel _$VehicleTypeModelFromJson(Map<String, dynamic> json) =>
    _VehicleTypeModel(
      id: json['id'] as String,
      name: json['name'] as String,
      nameAr: json['name_ar'] as String?,
      description: json['description'] as String?,
      descriptionAr: json['description_ar'] as String?,
      iconUrl: json['icon_url'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      sortOrder: (json['sort_order'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$VehicleTypeModelToJson(_VehicleTypeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'name_ar': instance.nameAr,
      'description': instance.description,
      'description_ar': instance.descriptionAr,
      'icon_url': instance.iconUrl,
      'is_active': instance.isActive,
      'sort_order': instance.sortOrder,
    };

_CompleteVehicleModel _$CompleteVehicleModelFromJson(
  Map<String, dynamic> json,
) => _CompleteVehicleModel(
  model: VehicleModelDetail.fromJson(json['model'] as Map<String, dynamic>),
  make: VehicleMakeModel.fromJson(json['make'] as Map<String, dynamic>),
  vehicleType: json['vehicle_type'] == null
      ? null
      : VehicleTypeModel.fromJson(json['vehicle_type'] as Map<String, dynamic>),
);

Map<String, dynamic> _$CompleteVehicleModelToJson(
  _CompleteVehicleModel instance,
) => <String, dynamic>{
  'model': instance.model.toJson(),
  'make': instance.make.toJson(),
  'vehicle_type': instance.vehicleType?.toJson(),
};
