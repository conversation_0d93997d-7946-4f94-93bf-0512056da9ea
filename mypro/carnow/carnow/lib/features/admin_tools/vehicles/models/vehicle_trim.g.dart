// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_trim.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleTrim _$VehicleTrimFromJson(Map<String, dynamic> json) => _VehicleTrim(
  id: (json['id'] as num).toInt(),
  modelId: (json['model_id'] as num).toInt(),
  name: json['name'] as String,
  trimLevel: json['trim_level'] as String?,
  trimType: json['trim_type'] as String?,
  engine: json['engine'] as String?,
  basePriceUsd: (json['base_price_usd'] as num?)?.toDouble(),
  doors: (json['doors'] as num?)?.toInt(),
  seats: (json['seats'] as num?)?.toInt(),
  isCurrent: json['is_current'] as bool? ?? true,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleTrimToJson(_VehicleTrim instance) =>
    <String, dynamic>{
      'id': instance.id,
      'model_id': instance.modelId,
      'name': instance.name,
      'trim_level': instance.trimLevel,
      'trim_type': instance.trimType,
      'engine': instance.engine,
      'base_price_usd': instance.basePriceUsd,
      'doors': instance.doors,
      'seats': instance.seats,
      'is_current': instance.isCurrent,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
    };
