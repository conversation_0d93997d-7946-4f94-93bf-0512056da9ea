// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_generation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleGeneration {

 int get id;@JsonKey(name: 'model_id') int get modelId;@JsonKey(name: 'generation_name') String get generationName;@JsonKey(name: 'generation_code') String? get generationCode;@JsonKey(name: 'year_start') int get yearStart;@JsonKey(name: 'year_end') int? get yearEnd;@JsonKey(name: 'engine_id') int? get engineId; String? get platform;@JsonKey(name: 'is_current') bool get isCurrent;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'is_deleted') bool get isDeleted;
/// Create a copy of VehicleGeneration
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleGenerationCopyWith<VehicleGeneration> get copyWith => _$VehicleGenerationCopyWithImpl<VehicleGeneration>(this as VehicleGeneration, _$identity);

  /// Serializes this VehicleGeneration to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleGeneration&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.generationName, generationName) || other.generationName == generationName)&&(identical(other.generationCode, generationCode) || other.generationCode == generationCode)&&(identical(other.yearStart, yearStart) || other.yearStart == yearStart)&&(identical(other.yearEnd, yearEnd) || other.yearEnd == yearEnd)&&(identical(other.engineId, engineId) || other.engineId == engineId)&&(identical(other.platform, platform) || other.platform == platform)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,generationName,generationCode,yearStart,yearEnd,engineId,platform,isCurrent,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'VehicleGeneration(id: $id, modelId: $modelId, generationName: $generationName, generationCode: $generationCode, yearStart: $yearStart, yearEnd: $yearEnd, engineId: $engineId, platform: $platform, isCurrent: $isCurrent, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $VehicleGenerationCopyWith<$Res>  {
  factory $VehicleGenerationCopyWith(VehicleGeneration value, $Res Function(VehicleGeneration) _then) = _$VehicleGenerationCopyWithImpl;
@useResult
$Res call({
 int id,@JsonKey(name: 'model_id') int modelId,@JsonKey(name: 'generation_name') String generationName,@JsonKey(name: 'generation_code') String? generationCode,@JsonKey(name: 'year_start') int yearStart,@JsonKey(name: 'year_end') int? yearEnd,@JsonKey(name: 'engine_id') int? engineId, String? platform,@JsonKey(name: 'is_current') bool isCurrent,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class _$VehicleGenerationCopyWithImpl<$Res>
    implements $VehicleGenerationCopyWith<$Res> {
  _$VehicleGenerationCopyWithImpl(this._self, this._then);

  final VehicleGeneration _self;
  final $Res Function(VehicleGeneration) _then;

/// Create a copy of VehicleGeneration
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? modelId = null,Object? generationName = null,Object? generationCode = freezed,Object? yearStart = null,Object? yearEnd = freezed,Object? engineId = freezed,Object? platform = freezed,Object? isCurrent = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,generationName: null == generationName ? _self.generationName : generationName // ignore: cast_nullable_to_non_nullable
as String,generationCode: freezed == generationCode ? _self.generationCode : generationCode // ignore: cast_nullable_to_non_nullable
as String?,yearStart: null == yearStart ? _self.yearStart : yearStart // ignore: cast_nullable_to_non_nullable
as int,yearEnd: freezed == yearEnd ? _self.yearEnd : yearEnd // ignore: cast_nullable_to_non_nullable
as int?,engineId: freezed == engineId ? _self.engineId : engineId // ignore: cast_nullable_to_non_nullable
as int?,platform: freezed == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as String?,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleGeneration].
extension VehicleGenerationPatterns on VehicleGeneration {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleGeneration value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleGeneration() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleGeneration value)  $default,){
final _that = this;
switch (_that) {
case _VehicleGeneration():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleGeneration value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleGeneration() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id, @JsonKey(name: 'model_id')  int modelId, @JsonKey(name: 'generation_name')  String generationName, @JsonKey(name: 'generation_code')  String? generationCode, @JsonKey(name: 'year_start')  int yearStart, @JsonKey(name: 'year_end')  int? yearEnd, @JsonKey(name: 'engine_id')  int? engineId,  String? platform, @JsonKey(name: 'is_current')  bool isCurrent, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleGeneration() when $default != null:
return $default(_that.id,_that.modelId,_that.generationName,_that.generationCode,_that.yearStart,_that.yearEnd,_that.engineId,_that.platform,_that.isCurrent,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id, @JsonKey(name: 'model_id')  int modelId, @JsonKey(name: 'generation_name')  String generationName, @JsonKey(name: 'generation_code')  String? generationCode, @JsonKey(name: 'year_start')  int yearStart, @JsonKey(name: 'year_end')  int? yearEnd, @JsonKey(name: 'engine_id')  int? engineId,  String? platform, @JsonKey(name: 'is_current')  bool isCurrent, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _VehicleGeneration():
return $default(_that.id,_that.modelId,_that.generationName,_that.generationCode,_that.yearStart,_that.yearEnd,_that.engineId,_that.platform,_that.isCurrent,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id, @JsonKey(name: 'model_id')  int modelId, @JsonKey(name: 'generation_name')  String generationName, @JsonKey(name: 'generation_code')  String? generationCode, @JsonKey(name: 'year_start')  int yearStart, @JsonKey(name: 'year_end')  int? yearEnd, @JsonKey(name: 'engine_id')  int? engineId,  String? platform, @JsonKey(name: 'is_current')  bool isCurrent, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _VehicleGeneration() when $default != null:
return $default(_that.id,_that.modelId,_that.generationName,_that.generationCode,_that.yearStart,_that.yearEnd,_that.engineId,_that.platform,_that.isCurrent,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleGeneration implements VehicleGeneration {
  const _VehicleGeneration({required this.id, @JsonKey(name: 'model_id') required this.modelId, @JsonKey(name: 'generation_name') required this.generationName, @JsonKey(name: 'generation_code') this.generationCode, @JsonKey(name: 'year_start') required this.yearStart, @JsonKey(name: 'year_end') this.yearEnd, @JsonKey(name: 'engine_id') this.engineId, this.platform, @JsonKey(name: 'is_current') this.isCurrent = false, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'is_deleted') this.isDeleted = false});
  factory _VehicleGeneration.fromJson(Map<String, dynamic> json) => _$VehicleGenerationFromJson(json);

@override final  int id;
@override@JsonKey(name: 'model_id') final  int modelId;
@override@JsonKey(name: 'generation_name') final  String generationName;
@override@JsonKey(name: 'generation_code') final  String? generationCode;
@override@JsonKey(name: 'year_start') final  int yearStart;
@override@JsonKey(name: 'year_end') final  int? yearEnd;
@override@JsonKey(name: 'engine_id') final  int? engineId;
@override final  String? platform;
@override@JsonKey(name: 'is_current') final  bool isCurrent;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted') final  bool isDeleted;

/// Create a copy of VehicleGeneration
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleGenerationCopyWith<_VehicleGeneration> get copyWith => __$VehicleGenerationCopyWithImpl<_VehicleGeneration>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleGenerationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleGeneration&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.generationName, generationName) || other.generationName == generationName)&&(identical(other.generationCode, generationCode) || other.generationCode == generationCode)&&(identical(other.yearStart, yearStart) || other.yearStart == yearStart)&&(identical(other.yearEnd, yearEnd) || other.yearEnd == yearEnd)&&(identical(other.engineId, engineId) || other.engineId == engineId)&&(identical(other.platform, platform) || other.platform == platform)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,generationName,generationCode,yearStart,yearEnd,engineId,platform,isCurrent,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'VehicleGeneration(id: $id, modelId: $modelId, generationName: $generationName, generationCode: $generationCode, yearStart: $yearStart, yearEnd: $yearEnd, engineId: $engineId, platform: $platform, isCurrent: $isCurrent, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$VehicleGenerationCopyWith<$Res> implements $VehicleGenerationCopyWith<$Res> {
  factory _$VehicleGenerationCopyWith(_VehicleGeneration value, $Res Function(_VehicleGeneration) _then) = __$VehicleGenerationCopyWithImpl;
@override @useResult
$Res call({
 int id,@JsonKey(name: 'model_id') int modelId,@JsonKey(name: 'generation_name') String generationName,@JsonKey(name: 'generation_code') String? generationCode,@JsonKey(name: 'year_start') int yearStart,@JsonKey(name: 'year_end') int? yearEnd,@JsonKey(name: 'engine_id') int? engineId, String? platform,@JsonKey(name: 'is_current') bool isCurrent,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class __$VehicleGenerationCopyWithImpl<$Res>
    implements _$VehicleGenerationCopyWith<$Res> {
  __$VehicleGenerationCopyWithImpl(this._self, this._then);

  final _VehicleGeneration _self;
  final $Res Function(_VehicleGeneration) _then;

/// Create a copy of VehicleGeneration
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? modelId = null,Object? generationName = null,Object? generationCode = freezed,Object? yearStart = null,Object? yearEnd = freezed,Object? engineId = freezed,Object? platform = freezed,Object? isCurrent = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_VehicleGeneration(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,generationName: null == generationName ? _self.generationName : generationName // ignore: cast_nullable_to_non_nullable
as String,generationCode: freezed == generationCode ? _self.generationCode : generationCode // ignore: cast_nullable_to_non_nullable
as String?,yearStart: null == yearStart ? _self.yearStart : yearStart // ignore: cast_nullable_to_non_nullable
as int,yearEnd: freezed == yearEnd ? _self.yearEnd : yearEnd // ignore: cast_nullable_to_non_nullable
as int?,engineId: freezed == engineId ? _self.engineId : engineId // ignore: cast_nullable_to_non_nullable
as int?,platform: freezed == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as String?,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
