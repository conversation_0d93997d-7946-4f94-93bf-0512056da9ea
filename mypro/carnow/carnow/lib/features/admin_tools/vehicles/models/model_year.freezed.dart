// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'model_year.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ModelYear {

 int get id;@JsonKey(name: 'model_id') int get modelId; int get year;@JsonKey(name: 'is_facelift') bool get isFacelift;@JsonKey(name: 'facelift_description') String? get faceliftDescription;@JsonKey(name: 'production_start_month') int? get productionStartMonth;@JsonKey(name: 'production_end_month') int? get productionEndMonth;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'is_deleted') bool get isDeleted;
/// Create a copy of ModelYear
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ModelYearCopyWith<ModelYear> get copyWith => _$ModelYearCopyWithImpl<ModelYear>(this as ModelYear, _$identity);

  /// Serializes this ModelYear to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModelYear&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.year, year) || other.year == year)&&(identical(other.isFacelift, isFacelift) || other.isFacelift == isFacelift)&&(identical(other.faceliftDescription, faceliftDescription) || other.faceliftDescription == faceliftDescription)&&(identical(other.productionStartMonth, productionStartMonth) || other.productionStartMonth == productionStartMonth)&&(identical(other.productionEndMonth, productionEndMonth) || other.productionEndMonth == productionEndMonth)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,year,isFacelift,faceliftDescription,productionStartMonth,productionEndMonth,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'ModelYear(id: $id, modelId: $modelId, year: $year, isFacelift: $isFacelift, faceliftDescription: $faceliftDescription, productionStartMonth: $productionStartMonth, productionEndMonth: $productionEndMonth, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $ModelYearCopyWith<$Res>  {
  factory $ModelYearCopyWith(ModelYear value, $Res Function(ModelYear) _then) = _$ModelYearCopyWithImpl;
@useResult
$Res call({
 int id,@JsonKey(name: 'model_id') int modelId, int year,@JsonKey(name: 'is_facelift') bool isFacelift,@JsonKey(name: 'facelift_description') String? faceliftDescription,@JsonKey(name: 'production_start_month') int? productionStartMonth,@JsonKey(name: 'production_end_month') int? productionEndMonth,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class _$ModelYearCopyWithImpl<$Res>
    implements $ModelYearCopyWith<$Res> {
  _$ModelYearCopyWithImpl(this._self, this._then);

  final ModelYear _self;
  final $Res Function(ModelYear) _then;

/// Create a copy of ModelYear
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? modelId = null,Object? year = null,Object? isFacelift = null,Object? faceliftDescription = freezed,Object? productionStartMonth = freezed,Object? productionEndMonth = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,isFacelift: null == isFacelift ? _self.isFacelift : isFacelift // ignore: cast_nullable_to_non_nullable
as bool,faceliftDescription: freezed == faceliftDescription ? _self.faceliftDescription : faceliftDescription // ignore: cast_nullable_to_non_nullable
as String?,productionStartMonth: freezed == productionStartMonth ? _self.productionStartMonth : productionStartMonth // ignore: cast_nullable_to_non_nullable
as int?,productionEndMonth: freezed == productionEndMonth ? _self.productionEndMonth : productionEndMonth // ignore: cast_nullable_to_non_nullable
as int?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ModelYear].
extension ModelYearPatterns on ModelYear {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ModelYear value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ModelYear() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ModelYear value)  $default,){
final _that = this;
switch (_that) {
case _ModelYear():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ModelYear value)?  $default,){
final _that = this;
switch (_that) {
case _ModelYear() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id, @JsonKey(name: 'model_id')  int modelId,  int year, @JsonKey(name: 'is_facelift')  bool isFacelift, @JsonKey(name: 'facelift_description')  String? faceliftDescription, @JsonKey(name: 'production_start_month')  int? productionStartMonth, @JsonKey(name: 'production_end_month')  int? productionEndMonth, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ModelYear() when $default != null:
return $default(_that.id,_that.modelId,_that.year,_that.isFacelift,_that.faceliftDescription,_that.productionStartMonth,_that.productionEndMonth,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id, @JsonKey(name: 'model_id')  int modelId,  int year, @JsonKey(name: 'is_facelift')  bool isFacelift, @JsonKey(name: 'facelift_description')  String? faceliftDescription, @JsonKey(name: 'production_start_month')  int? productionStartMonth, @JsonKey(name: 'production_end_month')  int? productionEndMonth, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _ModelYear():
return $default(_that.id,_that.modelId,_that.year,_that.isFacelift,_that.faceliftDescription,_that.productionStartMonth,_that.productionEndMonth,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id, @JsonKey(name: 'model_id')  int modelId,  int year, @JsonKey(name: 'is_facelift')  bool isFacelift, @JsonKey(name: 'facelift_description')  String? faceliftDescription, @JsonKey(name: 'production_start_month')  int? productionStartMonth, @JsonKey(name: 'production_end_month')  int? productionEndMonth, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _ModelYear() when $default != null:
return $default(_that.id,_that.modelId,_that.year,_that.isFacelift,_that.faceliftDescription,_that.productionStartMonth,_that.productionEndMonth,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ModelYear implements ModelYear {
  const _ModelYear({required this.id, @JsonKey(name: 'model_id') required this.modelId, required this.year, @JsonKey(name: 'is_facelift') this.isFacelift = false, @JsonKey(name: 'facelift_description') this.faceliftDescription, @JsonKey(name: 'production_start_month') this.productionStartMonth, @JsonKey(name: 'production_end_month') this.productionEndMonth, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'is_deleted') this.isDeleted = false});
  factory _ModelYear.fromJson(Map<String, dynamic> json) => _$ModelYearFromJson(json);

@override final  int id;
@override@JsonKey(name: 'model_id') final  int modelId;
@override final  int year;
@override@JsonKey(name: 'is_facelift') final  bool isFacelift;
@override@JsonKey(name: 'facelift_description') final  String? faceliftDescription;
@override@JsonKey(name: 'production_start_month') final  int? productionStartMonth;
@override@JsonKey(name: 'production_end_month') final  int? productionEndMonth;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted') final  bool isDeleted;

/// Create a copy of ModelYear
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModelYearCopyWith<_ModelYear> get copyWith => __$ModelYearCopyWithImpl<_ModelYear>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ModelYearToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModelYear&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.year, year) || other.year == year)&&(identical(other.isFacelift, isFacelift) || other.isFacelift == isFacelift)&&(identical(other.faceliftDescription, faceliftDescription) || other.faceliftDescription == faceliftDescription)&&(identical(other.productionStartMonth, productionStartMonth) || other.productionStartMonth == productionStartMonth)&&(identical(other.productionEndMonth, productionEndMonth) || other.productionEndMonth == productionEndMonth)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,year,isFacelift,faceliftDescription,productionStartMonth,productionEndMonth,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'ModelYear(id: $id, modelId: $modelId, year: $year, isFacelift: $isFacelift, faceliftDescription: $faceliftDescription, productionStartMonth: $productionStartMonth, productionEndMonth: $productionEndMonth, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$ModelYearCopyWith<$Res> implements $ModelYearCopyWith<$Res> {
  factory _$ModelYearCopyWith(_ModelYear value, $Res Function(_ModelYear) _then) = __$ModelYearCopyWithImpl;
@override @useResult
$Res call({
 int id,@JsonKey(name: 'model_id') int modelId, int year,@JsonKey(name: 'is_facelift') bool isFacelift,@JsonKey(name: 'facelift_description') String? faceliftDescription,@JsonKey(name: 'production_start_month') int? productionStartMonth,@JsonKey(name: 'production_end_month') int? productionEndMonth,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class __$ModelYearCopyWithImpl<$Res>
    implements _$ModelYearCopyWith<$Res> {
  __$ModelYearCopyWithImpl(this._self, this._then);

  final _ModelYear _self;
  final $Res Function(_ModelYear) _then;

/// Create a copy of ModelYear
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? modelId = null,Object? year = null,Object? isFacelift = null,Object? faceliftDescription = freezed,Object? productionStartMonth = freezed,Object? productionEndMonth = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_ModelYear(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,isFacelift: null == isFacelift ? _self.isFacelift : isFacelift // ignore: cast_nullable_to_non_nullable
as bool,faceliftDescription: freezed == faceliftDescription ? _self.faceliftDescription : faceliftDescription // ignore: cast_nullable_to_non_nullable
as String?,productionStartMonth: freezed == productionStartMonth ? _self.productionStartMonth : productionStartMonth // ignore: cast_nullable_to_non_nullable
as int?,productionEndMonth: freezed == productionEndMonth ? _self.productionEndMonth : productionEndMonth // ignore: cast_nullable_to_non_nullable
as int?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
