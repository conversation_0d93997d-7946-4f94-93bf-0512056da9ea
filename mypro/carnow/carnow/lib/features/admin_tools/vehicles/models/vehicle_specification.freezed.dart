// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_specification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleSpecification {

 int get id;@JsonKey(name: 'make_id') int get makeId;@JsonKey(name: 'model_id') int get modelId;@JsonKey(name: 'vehicle_type_id') int get vehicleTypeId;@JsonKey(name: 'year_from') int get yearFrom;@JsonKey(name: 'year_to') int? get yearTo;@JsonKey(name: 'engine_configuration_id') int? get engineConfigurationId;@JsonKey(name: 'fuel_type_id') int? get fuelTypeId;@JsonKey(name: 'transmission_id') int? get transmissionId;@JsonKey(name: 'specifications') Map<String, dynamic> get specs;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'is_deleted') bool get isDeleted;
/// Create a copy of VehicleSpecification
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleSpecificationCopyWith<VehicleSpecification> get copyWith => _$VehicleSpecificationCopyWithImpl<VehicleSpecification>(this as VehicleSpecification, _$identity);

  /// Serializes this VehicleSpecification to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleSpecification&&(identical(other.id, id) || other.id == id)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.vehicleTypeId, vehicleTypeId) || other.vehicleTypeId == vehicleTypeId)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.engineConfigurationId, engineConfigurationId) || other.engineConfigurationId == engineConfigurationId)&&(identical(other.fuelTypeId, fuelTypeId) || other.fuelTypeId == fuelTypeId)&&(identical(other.transmissionId, transmissionId) || other.transmissionId == transmissionId)&&const DeepCollectionEquality().equals(other.specs, specs)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeId,modelId,vehicleTypeId,yearFrom,yearTo,engineConfigurationId,fuelTypeId,transmissionId,const DeepCollectionEquality().hash(specs),createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'VehicleSpecification(id: $id, makeId: $makeId, modelId: $modelId, vehicleTypeId: $vehicleTypeId, yearFrom: $yearFrom, yearTo: $yearTo, engineConfigurationId: $engineConfigurationId, fuelTypeId: $fuelTypeId, transmissionId: $transmissionId, specs: $specs, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $VehicleSpecificationCopyWith<$Res>  {
  factory $VehicleSpecificationCopyWith(VehicleSpecification value, $Res Function(VehicleSpecification) _then) = _$VehicleSpecificationCopyWithImpl;
@useResult
$Res call({
 int id,@JsonKey(name: 'make_id') int makeId,@JsonKey(name: 'model_id') int modelId,@JsonKey(name: 'vehicle_type_id') int vehicleTypeId,@JsonKey(name: 'year_from') int yearFrom,@JsonKey(name: 'year_to') int? yearTo,@JsonKey(name: 'engine_configuration_id') int? engineConfigurationId,@JsonKey(name: 'fuel_type_id') int? fuelTypeId,@JsonKey(name: 'transmission_id') int? transmissionId,@JsonKey(name: 'specifications') Map<String, dynamic> specs,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class _$VehicleSpecificationCopyWithImpl<$Res>
    implements $VehicleSpecificationCopyWith<$Res> {
  _$VehicleSpecificationCopyWithImpl(this._self, this._then);

  final VehicleSpecification _self;
  final $Res Function(VehicleSpecification) _then;

/// Create a copy of VehicleSpecification
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? makeId = null,Object? modelId = null,Object? vehicleTypeId = null,Object? yearFrom = null,Object? yearTo = freezed,Object? engineConfigurationId = freezed,Object? fuelTypeId = freezed,Object? transmissionId = freezed,Object? specs = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,vehicleTypeId: null == vehicleTypeId ? _self.vehicleTypeId : vehicleTypeId // ignore: cast_nullable_to_non_nullable
as int,yearFrom: null == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,engineConfigurationId: freezed == engineConfigurationId ? _self.engineConfigurationId : engineConfigurationId // ignore: cast_nullable_to_non_nullable
as int?,fuelTypeId: freezed == fuelTypeId ? _self.fuelTypeId : fuelTypeId // ignore: cast_nullable_to_non_nullable
as int?,transmissionId: freezed == transmissionId ? _self.transmissionId : transmissionId // ignore: cast_nullable_to_non_nullable
as int?,specs: null == specs ? _self.specs : specs // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleSpecification].
extension VehicleSpecificationPatterns on VehicleSpecification {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleSpecification value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleSpecification() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleSpecification value)  $default,){
final _that = this;
switch (_that) {
case _VehicleSpecification():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleSpecification value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleSpecification() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id, @JsonKey(name: 'make_id')  int makeId, @JsonKey(name: 'model_id')  int modelId, @JsonKey(name: 'vehicle_type_id')  int vehicleTypeId, @JsonKey(name: 'year_from')  int yearFrom, @JsonKey(name: 'year_to')  int? yearTo, @JsonKey(name: 'engine_configuration_id')  int? engineConfigurationId, @JsonKey(name: 'fuel_type_id')  int? fuelTypeId, @JsonKey(name: 'transmission_id')  int? transmissionId, @JsonKey(name: 'specifications')  Map<String, dynamic> specs, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleSpecification() when $default != null:
return $default(_that.id,_that.makeId,_that.modelId,_that.vehicleTypeId,_that.yearFrom,_that.yearTo,_that.engineConfigurationId,_that.fuelTypeId,_that.transmissionId,_that.specs,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id, @JsonKey(name: 'make_id')  int makeId, @JsonKey(name: 'model_id')  int modelId, @JsonKey(name: 'vehicle_type_id')  int vehicleTypeId, @JsonKey(name: 'year_from')  int yearFrom, @JsonKey(name: 'year_to')  int? yearTo, @JsonKey(name: 'engine_configuration_id')  int? engineConfigurationId, @JsonKey(name: 'fuel_type_id')  int? fuelTypeId, @JsonKey(name: 'transmission_id')  int? transmissionId, @JsonKey(name: 'specifications')  Map<String, dynamic> specs, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _VehicleSpecification():
return $default(_that.id,_that.makeId,_that.modelId,_that.vehicleTypeId,_that.yearFrom,_that.yearTo,_that.engineConfigurationId,_that.fuelTypeId,_that.transmissionId,_that.specs,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id, @JsonKey(name: 'make_id')  int makeId, @JsonKey(name: 'model_id')  int modelId, @JsonKey(name: 'vehicle_type_id')  int vehicleTypeId, @JsonKey(name: 'year_from')  int yearFrom, @JsonKey(name: 'year_to')  int? yearTo, @JsonKey(name: 'engine_configuration_id')  int? engineConfigurationId, @JsonKey(name: 'fuel_type_id')  int? fuelTypeId, @JsonKey(name: 'transmission_id')  int? transmissionId, @JsonKey(name: 'specifications')  Map<String, dynamic> specs, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _VehicleSpecification() when $default != null:
return $default(_that.id,_that.makeId,_that.modelId,_that.vehicleTypeId,_that.yearFrom,_that.yearTo,_that.engineConfigurationId,_that.fuelTypeId,_that.transmissionId,_that.specs,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleSpecification implements VehicleSpecification {
  const _VehicleSpecification({required this.id, @JsonKey(name: 'make_id') required this.makeId, @JsonKey(name: 'model_id') required this.modelId, @JsonKey(name: 'vehicle_type_id') required this.vehicleTypeId, @JsonKey(name: 'year_from') required this.yearFrom, @JsonKey(name: 'year_to') this.yearTo, @JsonKey(name: 'engine_configuration_id') this.engineConfigurationId, @JsonKey(name: 'fuel_type_id') this.fuelTypeId, @JsonKey(name: 'transmission_id') this.transmissionId, @JsonKey(name: 'specifications') final  Map<String, dynamic> specs = const {}, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'is_deleted') this.isDeleted = false}): _specs = specs;
  factory _VehicleSpecification.fromJson(Map<String, dynamic> json) => _$VehicleSpecificationFromJson(json);

@override final  int id;
@override@JsonKey(name: 'make_id') final  int makeId;
@override@JsonKey(name: 'model_id') final  int modelId;
@override@JsonKey(name: 'vehicle_type_id') final  int vehicleTypeId;
@override@JsonKey(name: 'year_from') final  int yearFrom;
@override@JsonKey(name: 'year_to') final  int? yearTo;
@override@JsonKey(name: 'engine_configuration_id') final  int? engineConfigurationId;
@override@JsonKey(name: 'fuel_type_id') final  int? fuelTypeId;
@override@JsonKey(name: 'transmission_id') final  int? transmissionId;
 final  Map<String, dynamic> _specs;
@override@JsonKey(name: 'specifications') Map<String, dynamic> get specs {
  if (_specs is EqualUnmodifiableMapView) return _specs;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_specs);
}

@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted') final  bool isDeleted;

/// Create a copy of VehicleSpecification
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleSpecificationCopyWith<_VehicleSpecification> get copyWith => __$VehicleSpecificationCopyWithImpl<_VehicleSpecification>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleSpecificationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleSpecification&&(identical(other.id, id) || other.id == id)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.vehicleTypeId, vehicleTypeId) || other.vehicleTypeId == vehicleTypeId)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.engineConfigurationId, engineConfigurationId) || other.engineConfigurationId == engineConfigurationId)&&(identical(other.fuelTypeId, fuelTypeId) || other.fuelTypeId == fuelTypeId)&&(identical(other.transmissionId, transmissionId) || other.transmissionId == transmissionId)&&const DeepCollectionEquality().equals(other._specs, _specs)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeId,modelId,vehicleTypeId,yearFrom,yearTo,engineConfigurationId,fuelTypeId,transmissionId,const DeepCollectionEquality().hash(_specs),createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'VehicleSpecification(id: $id, makeId: $makeId, modelId: $modelId, vehicleTypeId: $vehicleTypeId, yearFrom: $yearFrom, yearTo: $yearTo, engineConfigurationId: $engineConfigurationId, fuelTypeId: $fuelTypeId, transmissionId: $transmissionId, specs: $specs, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$VehicleSpecificationCopyWith<$Res> implements $VehicleSpecificationCopyWith<$Res> {
  factory _$VehicleSpecificationCopyWith(_VehicleSpecification value, $Res Function(_VehicleSpecification) _then) = __$VehicleSpecificationCopyWithImpl;
@override @useResult
$Res call({
 int id,@JsonKey(name: 'make_id') int makeId,@JsonKey(name: 'model_id') int modelId,@JsonKey(name: 'vehicle_type_id') int vehicleTypeId,@JsonKey(name: 'year_from') int yearFrom,@JsonKey(name: 'year_to') int? yearTo,@JsonKey(name: 'engine_configuration_id') int? engineConfigurationId,@JsonKey(name: 'fuel_type_id') int? fuelTypeId,@JsonKey(name: 'transmission_id') int? transmissionId,@JsonKey(name: 'specifications') Map<String, dynamic> specs,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class __$VehicleSpecificationCopyWithImpl<$Res>
    implements _$VehicleSpecificationCopyWith<$Res> {
  __$VehicleSpecificationCopyWithImpl(this._self, this._then);

  final _VehicleSpecification _self;
  final $Res Function(_VehicleSpecification) _then;

/// Create a copy of VehicleSpecification
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? makeId = null,Object? modelId = null,Object? vehicleTypeId = null,Object? yearFrom = null,Object? yearTo = freezed,Object? engineConfigurationId = freezed,Object? fuelTypeId = freezed,Object? transmissionId = freezed,Object? specs = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_VehicleSpecification(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,vehicleTypeId: null == vehicleTypeId ? _self.vehicleTypeId : vehicleTypeId // ignore: cast_nullable_to_non_nullable
as int,yearFrom: null == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,engineConfigurationId: freezed == engineConfigurationId ? _self.engineConfigurationId : engineConfigurationId // ignore: cast_nullable_to_non_nullable
as int?,fuelTypeId: freezed == fuelTypeId ? _self.fuelTypeId : fuelTypeId // ignore: cast_nullable_to_non_nullable
as int?,transmissionId: freezed == transmissionId ? _self.transmissionId : transmissionId // ignore: cast_nullable_to_non_nullable
as int?,specs: null == specs ? _self._specs : specs // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
