// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_trim.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleTrim {

 int get id;@JsonKey(name: 'model_id') int get modelId; String get name;@JsonKey(name: 'trim_level') String? get trimLevel;@JsonKey(name: 'trim_type') String? get trimType; String? get engine;@JsonKey(name: 'base_price_usd') double? get basePriceUsd; int? get doors; int? get seats;@JsonKey(name: 'is_current') bool get isCurrent;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'is_deleted') bool get isDeleted;
/// Create a copy of VehicleTrim
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleTrimCopyWith<VehicleTrim> get copyWith => _$VehicleTrimCopyWithImpl<VehicleTrim>(this as VehicleTrim, _$identity);

  /// Serializes this VehicleTrim to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleTrim&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.name, name) || other.name == name)&&(identical(other.trimLevel, trimLevel) || other.trimLevel == trimLevel)&&(identical(other.trimType, trimType) || other.trimType == trimType)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.basePriceUsd, basePriceUsd) || other.basePriceUsd == basePriceUsd)&&(identical(other.doors, doors) || other.doors == doors)&&(identical(other.seats, seats) || other.seats == seats)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,name,trimLevel,trimType,engine,basePriceUsd,doors,seats,isCurrent,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'VehicleTrim(id: $id, modelId: $modelId, name: $name, trimLevel: $trimLevel, trimType: $trimType, engine: $engine, basePriceUsd: $basePriceUsd, doors: $doors, seats: $seats, isCurrent: $isCurrent, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $VehicleTrimCopyWith<$Res>  {
  factory $VehicleTrimCopyWith(VehicleTrim value, $Res Function(VehicleTrim) _then) = _$VehicleTrimCopyWithImpl;
@useResult
$Res call({
 int id,@JsonKey(name: 'model_id') int modelId, String name,@JsonKey(name: 'trim_level') String? trimLevel,@JsonKey(name: 'trim_type') String? trimType, String? engine,@JsonKey(name: 'base_price_usd') double? basePriceUsd, int? doors, int? seats,@JsonKey(name: 'is_current') bool isCurrent,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class _$VehicleTrimCopyWithImpl<$Res>
    implements $VehicleTrimCopyWith<$Res> {
  _$VehicleTrimCopyWithImpl(this._self, this._then);

  final VehicleTrim _self;
  final $Res Function(VehicleTrim) _then;

/// Create a copy of VehicleTrim
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? modelId = null,Object? name = null,Object? trimLevel = freezed,Object? trimType = freezed,Object? engine = freezed,Object? basePriceUsd = freezed,Object? doors = freezed,Object? seats = freezed,Object? isCurrent = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,trimLevel: freezed == trimLevel ? _self.trimLevel : trimLevel // ignore: cast_nullable_to_non_nullable
as String?,trimType: freezed == trimType ? _self.trimType : trimType // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,basePriceUsd: freezed == basePriceUsd ? _self.basePriceUsd : basePriceUsd // ignore: cast_nullable_to_non_nullable
as double?,doors: freezed == doors ? _self.doors : doors // ignore: cast_nullable_to_non_nullable
as int?,seats: freezed == seats ? _self.seats : seats // ignore: cast_nullable_to_non_nullable
as int?,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleTrim].
extension VehicleTrimPatterns on VehicleTrim {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleTrim value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleTrim() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleTrim value)  $default,){
final _that = this;
switch (_that) {
case _VehicleTrim():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleTrim value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleTrim() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id, @JsonKey(name: 'model_id')  int modelId,  String name, @JsonKey(name: 'trim_level')  String? trimLevel, @JsonKey(name: 'trim_type')  String? trimType,  String? engine, @JsonKey(name: 'base_price_usd')  double? basePriceUsd,  int? doors,  int? seats, @JsonKey(name: 'is_current')  bool isCurrent, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleTrim() when $default != null:
return $default(_that.id,_that.modelId,_that.name,_that.trimLevel,_that.trimType,_that.engine,_that.basePriceUsd,_that.doors,_that.seats,_that.isCurrent,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id, @JsonKey(name: 'model_id')  int modelId,  String name, @JsonKey(name: 'trim_level')  String? trimLevel, @JsonKey(name: 'trim_type')  String? trimType,  String? engine, @JsonKey(name: 'base_price_usd')  double? basePriceUsd,  int? doors,  int? seats, @JsonKey(name: 'is_current')  bool isCurrent, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _VehicleTrim():
return $default(_that.id,_that.modelId,_that.name,_that.trimLevel,_that.trimType,_that.engine,_that.basePriceUsd,_that.doors,_that.seats,_that.isCurrent,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id, @JsonKey(name: 'model_id')  int modelId,  String name, @JsonKey(name: 'trim_level')  String? trimLevel, @JsonKey(name: 'trim_type')  String? trimType,  String? engine, @JsonKey(name: 'base_price_usd')  double? basePriceUsd,  int? doors,  int? seats, @JsonKey(name: 'is_current')  bool isCurrent, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted')  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _VehicleTrim() when $default != null:
return $default(_that.id,_that.modelId,_that.name,_that.trimLevel,_that.trimType,_that.engine,_that.basePriceUsd,_that.doors,_that.seats,_that.isCurrent,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleTrim implements VehicleTrim {
  const _VehicleTrim({required this.id, @JsonKey(name: 'model_id') required this.modelId, required this.name, @JsonKey(name: 'trim_level') this.trimLevel, @JsonKey(name: 'trim_type') this.trimType, this.engine, @JsonKey(name: 'base_price_usd') this.basePriceUsd, this.doors, this.seats, @JsonKey(name: 'is_current') this.isCurrent = true, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'is_deleted') this.isDeleted = false});
  factory _VehicleTrim.fromJson(Map<String, dynamic> json) => _$VehicleTrimFromJson(json);

@override final  int id;
@override@JsonKey(name: 'model_id') final  int modelId;
@override final  String name;
@override@JsonKey(name: 'trim_level') final  String? trimLevel;
@override@JsonKey(name: 'trim_type') final  String? trimType;
@override final  String? engine;
@override@JsonKey(name: 'base_price_usd') final  double? basePriceUsd;
@override final  int? doors;
@override final  int? seats;
@override@JsonKey(name: 'is_current') final  bool isCurrent;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted') final  bool isDeleted;

/// Create a copy of VehicleTrim
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleTrimCopyWith<_VehicleTrim> get copyWith => __$VehicleTrimCopyWithImpl<_VehicleTrim>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleTrimToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleTrim&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.name, name) || other.name == name)&&(identical(other.trimLevel, trimLevel) || other.trimLevel == trimLevel)&&(identical(other.trimType, trimType) || other.trimType == trimType)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.basePriceUsd, basePriceUsd) || other.basePriceUsd == basePriceUsd)&&(identical(other.doors, doors) || other.doors == doors)&&(identical(other.seats, seats) || other.seats == seats)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,name,trimLevel,trimType,engine,basePriceUsd,doors,seats,isCurrent,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'VehicleTrim(id: $id, modelId: $modelId, name: $name, trimLevel: $trimLevel, trimType: $trimType, engine: $engine, basePriceUsd: $basePriceUsd, doors: $doors, seats: $seats, isCurrent: $isCurrent, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$VehicleTrimCopyWith<$Res> implements $VehicleTrimCopyWith<$Res> {
  factory _$VehicleTrimCopyWith(_VehicleTrim value, $Res Function(_VehicleTrim) _then) = __$VehicleTrimCopyWithImpl;
@override @useResult
$Res call({
 int id,@JsonKey(name: 'model_id') int modelId, String name,@JsonKey(name: 'trim_level') String? trimLevel,@JsonKey(name: 'trim_type') String? trimType, String? engine,@JsonKey(name: 'base_price_usd') double? basePriceUsd, int? doors, int? seats,@JsonKey(name: 'is_current') bool isCurrent,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class __$VehicleTrimCopyWithImpl<$Res>
    implements _$VehicleTrimCopyWith<$Res> {
  __$VehicleTrimCopyWithImpl(this._self, this._then);

  final _VehicleTrim _self;
  final $Res Function(_VehicleTrim) _then;

/// Create a copy of VehicleTrim
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? modelId = null,Object? name = null,Object? trimLevel = freezed,Object? trimType = freezed,Object? engine = freezed,Object? basePriceUsd = freezed,Object? doors = freezed,Object? seats = freezed,Object? isCurrent = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_VehicleTrim(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,trimLevel: freezed == trimLevel ? _self.trimLevel : trimLevel // ignore: cast_nullable_to_non_nullable
as String?,trimType: freezed == trimType ? _self.trimType : trimType // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,basePriceUsd: freezed == basePriceUsd ? _self.basePriceUsd : basePriceUsd // ignore: cast_nullable_to_non_nullable
as double?,doors: freezed == doors ? _self.doors : doors // ignore: cast_nullable_to_non_nullable
as int?,seats: freezed == seats ? _self.seats : seats // ignore: cast_nullable_to_non_nullable
as int?,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
