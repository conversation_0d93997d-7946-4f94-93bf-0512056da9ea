// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parts_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PartModel _$PartModelFromJson(Map<String, dynamic> json) => _PartModel(
  id: json['id'] as String,
  name: json['name'] as String,
  nameAr: json['nameAr'] as String?,
  description: json['description'] as String?,
  descriptionAr: json['descriptionAr'] as String?,
  categoryId: json['categoryId'] as String,
  partNumber: json['partNumber'] as String?,
  brand: json['brand'] as String?,
  model: json['model'] as String?,
  priceLyd: (json['priceLyd'] as num).toDouble(),
  isAvailable: json['isAvailable'] as bool? ?? true,
  stockQuantity: (json['stockQuantity'] as num?)?.toInt() ?? 0,
  imageUrl: json['imageUrl'] as String?,
  additionalImages: (json['additionalImages'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  sellerAuthId: json['sellerAuthId'] as String?,
  condition: json['condition'] as String?,
  warranty: json['warranty'] as String?,
  location: json['location'] as String?,
  specifications: json['specifications'] as Map<String, dynamic>?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
);

Map<String, dynamic> _$PartModelToJson(_PartModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'nameAr': instance.nameAr,
      'description': instance.description,
      'descriptionAr': instance.descriptionAr,
      'categoryId': instance.categoryId,
      'partNumber': instance.partNumber,
      'brand': instance.brand,
      'model': instance.model,
      'priceLyd': instance.priceLyd,
      'isAvailable': instance.isAvailable,
      'stockQuantity': instance.stockQuantity,
      'imageUrl': instance.imageUrl,
      'additionalImages': instance.additionalImages,
      'sellerAuthId': instance.sellerAuthId,
      'condition': instance.condition,
      'warranty': instance.warranty,
      'location': instance.location,
      'specifications': instance.specifications,
      'isDeleted': instance.isDeleted,
    };

_PartCompatibilityModel _$PartCompatibilityModelFromJson(
  Map<String, dynamic> json,
) => _PartCompatibilityModel(
  id: json['id'] as String,
  partId: json['partId'] as String,
  makeId: json['makeId'] as String,
  modelId: json['modelId'] as String?,
  yearFrom: (json['yearFrom'] as num?)?.toInt(),
  yearTo: (json['yearTo'] as num?)?.toInt(),
  engine: json['engine'] as String?,
  trim: json['trim'] as String?,
  isCompatible: json['isCompatible'] as bool? ?? true,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$PartCompatibilityModelToJson(
  _PartCompatibilityModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'partId': instance.partId,
  'makeId': instance.makeId,
  'modelId': instance.modelId,
  'yearFrom': instance.yearFrom,
  'yearTo': instance.yearTo,
  'engine': instance.engine,
  'trim': instance.trim,
  'isCompatible': instance.isCompatible,
};

_PartAttributeModel _$PartAttributeModelFromJson(Map<String, dynamic> json) =>
    _PartAttributeModel(
      id: json['id'] as String,
      partId: json['partId'] as String,
      attributeName: json['attributeName'] as String,
      attributeValue: json['attributeValue'] as String,
      dataType: json['dataType'] as String?,
      unit: json['unit'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PartAttributeModelToJson(_PartAttributeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'partId': instance.partId,
      'attributeName': instance.attributeName,
      'attributeValue': instance.attributeValue,
      'dataType': instance.dataType,
      'unit': instance.unit,
    };

_CompletePartModel _$CompletePartModelFromJson(Map<String, dynamic> json) =>
    _CompletePartModel(
      part: PartModel.fromJson(json['part'] as Map<String, dynamic>),
      compatibility: (json['compatibility'] as List<dynamic>?)
          ?.map(
            (e) => PartCompatibilityModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      attributes: (json['attributes'] as List<dynamic>?)
          ?.map((e) => PartAttributeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      categoryName: json['categoryName'] as String?,
      categoryNameAr: json['categoryNameAr'] as String?,
    );

Map<String, dynamic> _$CompletePartModelToJson(_CompletePartModel instance) =>
    <String, dynamic>{
      'part': instance.part,
      'compatibility': instance.compatibility,
      'attributes': instance.attributes,
      'categoryName': instance.categoryName,
      'categoryNameAr': instance.categoryNameAr,
    };
