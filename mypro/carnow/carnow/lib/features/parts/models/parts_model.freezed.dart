// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'parts_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PartModel {

 String get id; String get name; String? get nameAr; String? get description; String? get descriptionAr; String get categoryId; String? get partNumber; String? get brand; String? get model; double get priceLyd; bool get isAvailable; int get stockQuantity; String? get imageUrl; List<String>? get additionalImages; String? get sellerAuthId; String? get condition; String? get warranty; String? get location; Map<String, dynamic>? get specifications;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt; bool get isDeleted;
/// Create a copy of PartModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PartModelCopyWith<PartModel> get copyWith => _$PartModelCopyWithImpl<PartModel>(this as PartModel, _$identity);

  /// Serializes this PartModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PartModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.partNumber, partNumber) || other.partNumber == partNumber)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.model, model) || other.model == model)&&(identical(other.priceLyd, priceLyd) || other.priceLyd == priceLyd)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.stockQuantity, stockQuantity) || other.stockQuantity == stockQuantity)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&const DeepCollectionEquality().equals(other.additionalImages, additionalImages)&&(identical(other.sellerAuthId, sellerAuthId) || other.sellerAuthId == sellerAuthId)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.warranty, warranty) || other.warranty == warranty)&&(identical(other.location, location) || other.location == location)&&const DeepCollectionEquality().equals(other.specifications, specifications)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,nameAr,description,descriptionAr,categoryId,partNumber,brand,model,priceLyd,isAvailable,stockQuantity,imageUrl,const DeepCollectionEquality().hash(additionalImages),sellerAuthId,condition,warranty,location,const DeepCollectionEquality().hash(specifications),createdAt,updatedAt,isDeleted]);

@override
String toString() {
  return 'PartModel(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, categoryId: $categoryId, partNumber: $partNumber, brand: $brand, model: $model, priceLyd: $priceLyd, isAvailable: $isAvailable, stockQuantity: $stockQuantity, imageUrl: $imageUrl, additionalImages: $additionalImages, sellerAuthId: $sellerAuthId, condition: $condition, warranty: $warranty, location: $location, specifications: $specifications, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $PartModelCopyWith<$Res>  {
  factory $PartModelCopyWith(PartModel value, $Res Function(PartModel) _then) = _$PartModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? nameAr, String? description, String? descriptionAr, String categoryId, String? partNumber, String? brand, String? model, double priceLyd, bool isAvailable, int stockQuantity, String? imageUrl, List<String>? additionalImages, String? sellerAuthId, String? condition, String? warranty, String? location, Map<String, dynamic>? specifications,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt, bool isDeleted
});




}
/// @nodoc
class _$PartModelCopyWithImpl<$Res>
    implements $PartModelCopyWith<$Res> {
  _$PartModelCopyWithImpl(this._self, this._then);

  final PartModel _self;
  final $Res Function(PartModel) _then;

/// Create a copy of PartModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? description = freezed,Object? descriptionAr = freezed,Object? categoryId = null,Object? partNumber = freezed,Object? brand = freezed,Object? model = freezed,Object? priceLyd = null,Object? isAvailable = null,Object? stockQuantity = null,Object? imageUrl = freezed,Object? additionalImages = freezed,Object? sellerAuthId = freezed,Object? condition = freezed,Object? warranty = freezed,Object? location = freezed,Object? specifications = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,categoryId: null == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String,partNumber: freezed == partNumber ? _self.partNumber : partNumber // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,priceLyd: null == priceLyd ? _self.priceLyd : priceLyd // ignore: cast_nullable_to_non_nullable
as double,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,stockQuantity: null == stockQuantity ? _self.stockQuantity : stockQuantity // ignore: cast_nullable_to_non_nullable
as int,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,additionalImages: freezed == additionalImages ? _self.additionalImages : additionalImages // ignore: cast_nullable_to_non_nullable
as List<String>?,sellerAuthId: freezed == sellerAuthId ? _self.sellerAuthId : sellerAuthId // ignore: cast_nullable_to_non_nullable
as String?,condition: freezed == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String?,warranty: freezed == warranty ? _self.warranty : warranty // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,specifications: freezed == specifications ? _self.specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PartModel].
extension PartModelPatterns on PartModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PartModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PartModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PartModel value)  $default,){
final _that = this;
switch (_that) {
case _PartModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PartModel value)?  $default,){
final _that = this;
switch (_that) {
case _PartModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? description,  String? descriptionAr,  String categoryId,  String? partNumber,  String? brand,  String? model,  double priceLyd,  bool isAvailable,  int stockQuantity,  String? imageUrl,  List<String>? additionalImages,  String? sellerAuthId,  String? condition,  String? warranty,  String? location,  Map<String, dynamic>? specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt,  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PartModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.categoryId,_that.partNumber,_that.brand,_that.model,_that.priceLyd,_that.isAvailable,_that.stockQuantity,_that.imageUrl,_that.additionalImages,_that.sellerAuthId,_that.condition,_that.warranty,_that.location,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? description,  String? descriptionAr,  String categoryId,  String? partNumber,  String? brand,  String? model,  double priceLyd,  bool isAvailable,  int stockQuantity,  String? imageUrl,  List<String>? additionalImages,  String? sellerAuthId,  String? condition,  String? warranty,  String? location,  Map<String, dynamic>? specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt,  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _PartModel():
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.categoryId,_that.partNumber,_that.brand,_that.model,_that.priceLyd,_that.isAvailable,_that.stockQuantity,_that.imageUrl,_that.additionalImages,_that.sellerAuthId,_that.condition,_that.warranty,_that.location,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? nameAr,  String? description,  String? descriptionAr,  String categoryId,  String? partNumber,  String? brand,  String? model,  double priceLyd,  bool isAvailable,  int stockQuantity,  String? imageUrl,  List<String>? additionalImages,  String? sellerAuthId,  String? condition,  String? warranty,  String? location,  Map<String, dynamic>? specifications, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt,  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _PartModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.categoryId,_that.partNumber,_that.brand,_that.model,_that.priceLyd,_that.isAvailable,_that.stockQuantity,_that.imageUrl,_that.additionalImages,_that.sellerAuthId,_that.condition,_that.warranty,_that.location,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PartModel implements PartModel {
  const _PartModel({required this.id, required this.name, this.nameAr, this.description, this.descriptionAr, required this.categoryId, this.partNumber, this.brand, this.model, required this.priceLyd, this.isAvailable = true, this.stockQuantity = 0, this.imageUrl, final  List<String>? additionalImages, this.sellerAuthId, this.condition, this.warranty, this.location, final  Map<String, dynamic>? specifications, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt, this.isDeleted = false}): _additionalImages = additionalImages,_specifications = specifications;
  factory _PartModel.fromJson(Map<String, dynamic> json) => _$PartModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String? nameAr;
@override final  String? description;
@override final  String? descriptionAr;
@override final  String categoryId;
@override final  String? partNumber;
@override final  String? brand;
@override final  String? model;
@override final  double priceLyd;
@override@JsonKey() final  bool isAvailable;
@override@JsonKey() final  int stockQuantity;
@override final  String? imageUrl;
 final  List<String>? _additionalImages;
@override List<String>? get additionalImages {
  final value = _additionalImages;
  if (value == null) return null;
  if (_additionalImages is EqualUnmodifiableListView) return _additionalImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? sellerAuthId;
@override final  String? condition;
@override final  String? warranty;
@override final  String? location;
 final  Map<String, dynamic>? _specifications;
@override Map<String, dynamic>? get specifications {
  final value = _specifications;
  if (value == null) return null;
  if (_specifications is EqualUnmodifiableMapView) return _specifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;
@override@JsonKey() final  bool isDeleted;

/// Create a copy of PartModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PartModelCopyWith<_PartModel> get copyWith => __$PartModelCopyWithImpl<_PartModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PartModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PartModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.partNumber, partNumber) || other.partNumber == partNumber)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.model, model) || other.model == model)&&(identical(other.priceLyd, priceLyd) || other.priceLyd == priceLyd)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.stockQuantity, stockQuantity) || other.stockQuantity == stockQuantity)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&const DeepCollectionEquality().equals(other._additionalImages, _additionalImages)&&(identical(other.sellerAuthId, sellerAuthId) || other.sellerAuthId == sellerAuthId)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.warranty, warranty) || other.warranty == warranty)&&(identical(other.location, location) || other.location == location)&&const DeepCollectionEquality().equals(other._specifications, _specifications)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,nameAr,description,descriptionAr,categoryId,partNumber,brand,model,priceLyd,isAvailable,stockQuantity,imageUrl,const DeepCollectionEquality().hash(_additionalImages),sellerAuthId,condition,warranty,location,const DeepCollectionEquality().hash(_specifications),createdAt,updatedAt,isDeleted]);

@override
String toString() {
  return 'PartModel(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, categoryId: $categoryId, partNumber: $partNumber, brand: $brand, model: $model, priceLyd: $priceLyd, isAvailable: $isAvailable, stockQuantity: $stockQuantity, imageUrl: $imageUrl, additionalImages: $additionalImages, sellerAuthId: $sellerAuthId, condition: $condition, warranty: $warranty, location: $location, specifications: $specifications, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$PartModelCopyWith<$Res> implements $PartModelCopyWith<$Res> {
  factory _$PartModelCopyWith(_PartModel value, $Res Function(_PartModel) _then) = __$PartModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? nameAr, String? description, String? descriptionAr, String categoryId, String? partNumber, String? brand, String? model, double priceLyd, bool isAvailable, int stockQuantity, String? imageUrl, List<String>? additionalImages, String? sellerAuthId, String? condition, String? warranty, String? location, Map<String, dynamic>? specifications,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt, bool isDeleted
});




}
/// @nodoc
class __$PartModelCopyWithImpl<$Res>
    implements _$PartModelCopyWith<$Res> {
  __$PartModelCopyWithImpl(this._self, this._then);

  final _PartModel _self;
  final $Res Function(_PartModel) _then;

/// Create a copy of PartModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? description = freezed,Object? descriptionAr = freezed,Object? categoryId = null,Object? partNumber = freezed,Object? brand = freezed,Object? model = freezed,Object? priceLyd = null,Object? isAvailable = null,Object? stockQuantity = null,Object? imageUrl = freezed,Object? additionalImages = freezed,Object? sellerAuthId = freezed,Object? condition = freezed,Object? warranty = freezed,Object? location = freezed,Object? specifications = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_PartModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,categoryId: null == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String,partNumber: freezed == partNumber ? _self.partNumber : partNumber // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,priceLyd: null == priceLyd ? _self.priceLyd : priceLyd // ignore: cast_nullable_to_non_nullable
as double,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,stockQuantity: null == stockQuantity ? _self.stockQuantity : stockQuantity // ignore: cast_nullable_to_non_nullable
as int,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,additionalImages: freezed == additionalImages ? _self._additionalImages : additionalImages // ignore: cast_nullable_to_non_nullable
as List<String>?,sellerAuthId: freezed == sellerAuthId ? _self.sellerAuthId : sellerAuthId // ignore: cast_nullable_to_non_nullable
as String?,condition: freezed == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String?,warranty: freezed == warranty ? _self.warranty : warranty // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,specifications: freezed == specifications ? _self._specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$PartCompatibilityModel {

 String get id; String get partId; String get makeId; String? get modelId; int? get yearFrom; int? get yearTo; String? get engine; String? get trim; bool get isCompatible;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of PartCompatibilityModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PartCompatibilityModelCopyWith<PartCompatibilityModel> get copyWith => _$PartCompatibilityModelCopyWithImpl<PartCompatibilityModel>(this as PartCompatibilityModel, _$identity);

  /// Serializes this PartCompatibilityModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PartCompatibilityModel&&(identical(other.id, id) || other.id == id)&&(identical(other.partId, partId) || other.partId == partId)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.isCompatible, isCompatible) || other.isCompatible == isCompatible)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,partId,makeId,modelId,yearFrom,yearTo,engine,trim,isCompatible,createdAt,updatedAt);

@override
String toString() {
  return 'PartCompatibilityModel(id: $id, partId: $partId, makeId: $makeId, modelId: $modelId, yearFrom: $yearFrom, yearTo: $yearTo, engine: $engine, trim: $trim, isCompatible: $isCompatible, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $PartCompatibilityModelCopyWith<$Res>  {
  factory $PartCompatibilityModelCopyWith(PartCompatibilityModel value, $Res Function(PartCompatibilityModel) _then) = _$PartCompatibilityModelCopyWithImpl;
@useResult
$Res call({
 String id, String partId, String makeId, String? modelId, int? yearFrom, int? yearTo, String? engine, String? trim, bool isCompatible,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$PartCompatibilityModelCopyWithImpl<$Res>
    implements $PartCompatibilityModelCopyWith<$Res> {
  _$PartCompatibilityModelCopyWithImpl(this._self, this._then);

  final PartCompatibilityModel _self;
  final $Res Function(PartCompatibilityModel) _then;

/// Create a copy of PartCompatibilityModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? partId = null,Object? makeId = null,Object? modelId = freezed,Object? yearFrom = freezed,Object? yearTo = freezed,Object? engine = freezed,Object? trim = freezed,Object? isCompatible = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,partId: null == partId ? _self.partId : partId // ignore: cast_nullable_to_non_nullable
as String,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as String,modelId: freezed == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as String?,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,isCompatible: null == isCompatible ? _self.isCompatible : isCompatible // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [PartCompatibilityModel].
extension PartCompatibilityModelPatterns on PartCompatibilityModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PartCompatibilityModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PartCompatibilityModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PartCompatibilityModel value)  $default,){
final _that = this;
switch (_that) {
case _PartCompatibilityModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PartCompatibilityModel value)?  $default,){
final _that = this;
switch (_that) {
case _PartCompatibilityModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String partId,  String makeId,  String? modelId,  int? yearFrom,  int? yearTo,  String? engine,  String? trim,  bool isCompatible, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PartCompatibilityModel() when $default != null:
return $default(_that.id,_that.partId,_that.makeId,_that.modelId,_that.yearFrom,_that.yearTo,_that.engine,_that.trim,_that.isCompatible,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String partId,  String makeId,  String? modelId,  int? yearFrom,  int? yearTo,  String? engine,  String? trim,  bool isCompatible, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _PartCompatibilityModel():
return $default(_that.id,_that.partId,_that.makeId,_that.modelId,_that.yearFrom,_that.yearTo,_that.engine,_that.trim,_that.isCompatible,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String partId,  String makeId,  String? modelId,  int? yearFrom,  int? yearTo,  String? engine,  String? trim,  bool isCompatible, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _PartCompatibilityModel() when $default != null:
return $default(_that.id,_that.partId,_that.makeId,_that.modelId,_that.yearFrom,_that.yearTo,_that.engine,_that.trim,_that.isCompatible,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PartCompatibilityModel implements PartCompatibilityModel {
  const _PartCompatibilityModel({required this.id, required this.partId, required this.makeId, this.modelId, this.yearFrom, this.yearTo, this.engine, this.trim, this.isCompatible = true, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _PartCompatibilityModel.fromJson(Map<String, dynamic> json) => _$PartCompatibilityModelFromJson(json);

@override final  String id;
@override final  String partId;
@override final  String makeId;
@override final  String? modelId;
@override final  int? yearFrom;
@override final  int? yearTo;
@override final  String? engine;
@override final  String? trim;
@override@JsonKey() final  bool isCompatible;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of PartCompatibilityModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PartCompatibilityModelCopyWith<_PartCompatibilityModel> get copyWith => __$PartCompatibilityModelCopyWithImpl<_PartCompatibilityModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PartCompatibilityModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PartCompatibilityModel&&(identical(other.id, id) || other.id == id)&&(identical(other.partId, partId) || other.partId == partId)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.isCompatible, isCompatible) || other.isCompatible == isCompatible)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,partId,makeId,modelId,yearFrom,yearTo,engine,trim,isCompatible,createdAt,updatedAt);

@override
String toString() {
  return 'PartCompatibilityModel(id: $id, partId: $partId, makeId: $makeId, modelId: $modelId, yearFrom: $yearFrom, yearTo: $yearTo, engine: $engine, trim: $trim, isCompatible: $isCompatible, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$PartCompatibilityModelCopyWith<$Res> implements $PartCompatibilityModelCopyWith<$Res> {
  factory _$PartCompatibilityModelCopyWith(_PartCompatibilityModel value, $Res Function(_PartCompatibilityModel) _then) = __$PartCompatibilityModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String partId, String makeId, String? modelId, int? yearFrom, int? yearTo, String? engine, String? trim, bool isCompatible,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$PartCompatibilityModelCopyWithImpl<$Res>
    implements _$PartCompatibilityModelCopyWith<$Res> {
  __$PartCompatibilityModelCopyWithImpl(this._self, this._then);

  final _PartCompatibilityModel _self;
  final $Res Function(_PartCompatibilityModel) _then;

/// Create a copy of PartCompatibilityModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? partId = null,Object? makeId = null,Object? modelId = freezed,Object? yearFrom = freezed,Object? yearTo = freezed,Object? engine = freezed,Object? trim = freezed,Object? isCompatible = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_PartCompatibilityModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,partId: null == partId ? _self.partId : partId // ignore: cast_nullable_to_non_nullable
as String,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as String,modelId: freezed == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as String?,yearFrom: freezed == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int?,yearTo: freezed == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,isCompatible: null == isCompatible ? _self.isCompatible : isCompatible // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$PartAttributeModel {

 String get id; String get partId; String get attributeName; String get attributeValue; String? get dataType; String? get unit;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of PartAttributeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PartAttributeModelCopyWith<PartAttributeModel> get copyWith => _$PartAttributeModelCopyWithImpl<PartAttributeModel>(this as PartAttributeModel, _$identity);

  /// Serializes this PartAttributeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PartAttributeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.partId, partId) || other.partId == partId)&&(identical(other.attributeName, attributeName) || other.attributeName == attributeName)&&(identical(other.attributeValue, attributeValue) || other.attributeValue == attributeValue)&&(identical(other.dataType, dataType) || other.dataType == dataType)&&(identical(other.unit, unit) || other.unit == unit)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,partId,attributeName,attributeValue,dataType,unit,createdAt,updatedAt);

@override
String toString() {
  return 'PartAttributeModel(id: $id, partId: $partId, attributeName: $attributeName, attributeValue: $attributeValue, dataType: $dataType, unit: $unit, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $PartAttributeModelCopyWith<$Res>  {
  factory $PartAttributeModelCopyWith(PartAttributeModel value, $Res Function(PartAttributeModel) _then) = _$PartAttributeModelCopyWithImpl;
@useResult
$Res call({
 String id, String partId, String attributeName, String attributeValue, String? dataType, String? unit,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$PartAttributeModelCopyWithImpl<$Res>
    implements $PartAttributeModelCopyWith<$Res> {
  _$PartAttributeModelCopyWithImpl(this._self, this._then);

  final PartAttributeModel _self;
  final $Res Function(PartAttributeModel) _then;

/// Create a copy of PartAttributeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? partId = null,Object? attributeName = null,Object? attributeValue = null,Object? dataType = freezed,Object? unit = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,partId: null == partId ? _self.partId : partId // ignore: cast_nullable_to_non_nullable
as String,attributeName: null == attributeName ? _self.attributeName : attributeName // ignore: cast_nullable_to_non_nullable
as String,attributeValue: null == attributeValue ? _self.attributeValue : attributeValue // ignore: cast_nullable_to_non_nullable
as String,dataType: freezed == dataType ? _self.dataType : dataType // ignore: cast_nullable_to_non_nullable
as String?,unit: freezed == unit ? _self.unit : unit // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [PartAttributeModel].
extension PartAttributeModelPatterns on PartAttributeModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PartAttributeModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PartAttributeModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PartAttributeModel value)  $default,){
final _that = this;
switch (_that) {
case _PartAttributeModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PartAttributeModel value)?  $default,){
final _that = this;
switch (_that) {
case _PartAttributeModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String partId,  String attributeName,  String attributeValue,  String? dataType,  String? unit, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PartAttributeModel() when $default != null:
return $default(_that.id,_that.partId,_that.attributeName,_that.attributeValue,_that.dataType,_that.unit,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String partId,  String attributeName,  String attributeValue,  String? dataType,  String? unit, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _PartAttributeModel():
return $default(_that.id,_that.partId,_that.attributeName,_that.attributeValue,_that.dataType,_that.unit,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String partId,  String attributeName,  String attributeValue,  String? dataType,  String? unit, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _PartAttributeModel() when $default != null:
return $default(_that.id,_that.partId,_that.attributeName,_that.attributeValue,_that.dataType,_that.unit,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PartAttributeModel implements PartAttributeModel {
  const _PartAttributeModel({required this.id, required this.partId, required this.attributeName, required this.attributeValue, this.dataType, this.unit, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _PartAttributeModel.fromJson(Map<String, dynamic> json) => _$PartAttributeModelFromJson(json);

@override final  String id;
@override final  String partId;
@override final  String attributeName;
@override final  String attributeValue;
@override final  String? dataType;
@override final  String? unit;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of PartAttributeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PartAttributeModelCopyWith<_PartAttributeModel> get copyWith => __$PartAttributeModelCopyWithImpl<_PartAttributeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PartAttributeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PartAttributeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.partId, partId) || other.partId == partId)&&(identical(other.attributeName, attributeName) || other.attributeName == attributeName)&&(identical(other.attributeValue, attributeValue) || other.attributeValue == attributeValue)&&(identical(other.dataType, dataType) || other.dataType == dataType)&&(identical(other.unit, unit) || other.unit == unit)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,partId,attributeName,attributeValue,dataType,unit,createdAt,updatedAt);

@override
String toString() {
  return 'PartAttributeModel(id: $id, partId: $partId, attributeName: $attributeName, attributeValue: $attributeValue, dataType: $dataType, unit: $unit, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$PartAttributeModelCopyWith<$Res> implements $PartAttributeModelCopyWith<$Res> {
  factory _$PartAttributeModelCopyWith(_PartAttributeModel value, $Res Function(_PartAttributeModel) _then) = __$PartAttributeModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String partId, String attributeName, String attributeValue, String? dataType, String? unit,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$PartAttributeModelCopyWithImpl<$Res>
    implements _$PartAttributeModelCopyWith<$Res> {
  __$PartAttributeModelCopyWithImpl(this._self, this._then);

  final _PartAttributeModel _self;
  final $Res Function(_PartAttributeModel) _then;

/// Create a copy of PartAttributeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? partId = null,Object? attributeName = null,Object? attributeValue = null,Object? dataType = freezed,Object? unit = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_PartAttributeModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,partId: null == partId ? _self.partId : partId // ignore: cast_nullable_to_non_nullable
as String,attributeName: null == attributeName ? _self.attributeName : attributeName // ignore: cast_nullable_to_non_nullable
as String,attributeValue: null == attributeValue ? _self.attributeValue : attributeValue // ignore: cast_nullable_to_non_nullable
as String,dataType: freezed == dataType ? _self.dataType : dataType // ignore: cast_nullable_to_non_nullable
as String?,unit: freezed == unit ? _self.unit : unit // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CompletePartModel {

 PartModel get part; List<PartCompatibilityModel>? get compatibility; List<PartAttributeModel>? get attributes; String? get categoryName; String? get categoryNameAr;
/// Create a copy of CompletePartModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CompletePartModelCopyWith<CompletePartModel> get copyWith => _$CompletePartModelCopyWithImpl<CompletePartModel>(this as CompletePartModel, _$identity);

  /// Serializes this CompletePartModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CompletePartModel&&(identical(other.part, part) || other.part == part)&&const DeepCollectionEquality().equals(other.compatibility, compatibility)&&const DeepCollectionEquality().equals(other.attributes, attributes)&&(identical(other.categoryName, categoryName) || other.categoryName == categoryName)&&(identical(other.categoryNameAr, categoryNameAr) || other.categoryNameAr == categoryNameAr));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,part,const DeepCollectionEquality().hash(compatibility),const DeepCollectionEquality().hash(attributes),categoryName,categoryNameAr);

@override
String toString() {
  return 'CompletePartModel(part: $part, compatibility: $compatibility, attributes: $attributes, categoryName: $categoryName, categoryNameAr: $categoryNameAr)';
}


}

/// @nodoc
abstract mixin class $CompletePartModelCopyWith<$Res>  {
  factory $CompletePartModelCopyWith(CompletePartModel value, $Res Function(CompletePartModel) _then) = _$CompletePartModelCopyWithImpl;
@useResult
$Res call({
 PartModel part, List<PartCompatibilityModel>? compatibility, List<PartAttributeModel>? attributes, String? categoryName, String? categoryNameAr
});


$PartModelCopyWith<$Res> get part;

}
/// @nodoc
class _$CompletePartModelCopyWithImpl<$Res>
    implements $CompletePartModelCopyWith<$Res> {
  _$CompletePartModelCopyWithImpl(this._self, this._then);

  final CompletePartModel _self;
  final $Res Function(CompletePartModel) _then;

/// Create a copy of CompletePartModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? part = null,Object? compatibility = freezed,Object? attributes = freezed,Object? categoryName = freezed,Object? categoryNameAr = freezed,}) {
  return _then(_self.copyWith(
part: null == part ? _self.part : part // ignore: cast_nullable_to_non_nullable
as PartModel,compatibility: freezed == compatibility ? _self.compatibility : compatibility // ignore: cast_nullable_to_non_nullable
as List<PartCompatibilityModel>?,attributes: freezed == attributes ? _self.attributes : attributes // ignore: cast_nullable_to_non_nullable
as List<PartAttributeModel>?,categoryName: freezed == categoryName ? _self.categoryName : categoryName // ignore: cast_nullable_to_non_nullable
as String?,categoryNameAr: freezed == categoryNameAr ? _self.categoryNameAr : categoryNameAr // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of CompletePartModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PartModelCopyWith<$Res> get part {
  
  return $PartModelCopyWith<$Res>(_self.part, (value) {
    return _then(_self.copyWith(part: value));
  });
}
}


/// Adds pattern-matching-related methods to [CompletePartModel].
extension CompletePartModelPatterns on CompletePartModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CompletePartModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CompletePartModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CompletePartModel value)  $default,){
final _that = this;
switch (_that) {
case _CompletePartModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CompletePartModel value)?  $default,){
final _that = this;
switch (_that) {
case _CompletePartModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( PartModel part,  List<PartCompatibilityModel>? compatibility,  List<PartAttributeModel>? attributes,  String? categoryName,  String? categoryNameAr)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CompletePartModel() when $default != null:
return $default(_that.part,_that.compatibility,_that.attributes,_that.categoryName,_that.categoryNameAr);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( PartModel part,  List<PartCompatibilityModel>? compatibility,  List<PartAttributeModel>? attributes,  String? categoryName,  String? categoryNameAr)  $default,) {final _that = this;
switch (_that) {
case _CompletePartModel():
return $default(_that.part,_that.compatibility,_that.attributes,_that.categoryName,_that.categoryNameAr);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( PartModel part,  List<PartCompatibilityModel>? compatibility,  List<PartAttributeModel>? attributes,  String? categoryName,  String? categoryNameAr)?  $default,) {final _that = this;
switch (_that) {
case _CompletePartModel() when $default != null:
return $default(_that.part,_that.compatibility,_that.attributes,_that.categoryName,_that.categoryNameAr);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CompletePartModel implements CompletePartModel {
  const _CompletePartModel({required this.part, final  List<PartCompatibilityModel>? compatibility, final  List<PartAttributeModel>? attributes, this.categoryName, this.categoryNameAr}): _compatibility = compatibility,_attributes = attributes;
  factory _CompletePartModel.fromJson(Map<String, dynamic> json) => _$CompletePartModelFromJson(json);

@override final  PartModel part;
 final  List<PartCompatibilityModel>? _compatibility;
@override List<PartCompatibilityModel>? get compatibility {
  final value = _compatibility;
  if (value == null) return null;
  if (_compatibility is EqualUnmodifiableListView) return _compatibility;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<PartAttributeModel>? _attributes;
@override List<PartAttributeModel>? get attributes {
  final value = _attributes;
  if (value == null) return null;
  if (_attributes is EqualUnmodifiableListView) return _attributes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? categoryName;
@override final  String? categoryNameAr;

/// Create a copy of CompletePartModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CompletePartModelCopyWith<_CompletePartModel> get copyWith => __$CompletePartModelCopyWithImpl<_CompletePartModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CompletePartModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CompletePartModel&&(identical(other.part, part) || other.part == part)&&const DeepCollectionEquality().equals(other._compatibility, _compatibility)&&const DeepCollectionEquality().equals(other._attributes, _attributes)&&(identical(other.categoryName, categoryName) || other.categoryName == categoryName)&&(identical(other.categoryNameAr, categoryNameAr) || other.categoryNameAr == categoryNameAr));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,part,const DeepCollectionEquality().hash(_compatibility),const DeepCollectionEquality().hash(_attributes),categoryName,categoryNameAr);

@override
String toString() {
  return 'CompletePartModel(part: $part, compatibility: $compatibility, attributes: $attributes, categoryName: $categoryName, categoryNameAr: $categoryNameAr)';
}


}

/// @nodoc
abstract mixin class _$CompletePartModelCopyWith<$Res> implements $CompletePartModelCopyWith<$Res> {
  factory _$CompletePartModelCopyWith(_CompletePartModel value, $Res Function(_CompletePartModel) _then) = __$CompletePartModelCopyWithImpl;
@override @useResult
$Res call({
 PartModel part, List<PartCompatibilityModel>? compatibility, List<PartAttributeModel>? attributes, String? categoryName, String? categoryNameAr
});


@override $PartModelCopyWith<$Res> get part;

}
/// @nodoc
class __$CompletePartModelCopyWithImpl<$Res>
    implements _$CompletePartModelCopyWith<$Res> {
  __$CompletePartModelCopyWithImpl(this._self, this._then);

  final _CompletePartModel _self;
  final $Res Function(_CompletePartModel) _then;

/// Create a copy of CompletePartModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? part = null,Object? compatibility = freezed,Object? attributes = freezed,Object? categoryName = freezed,Object? categoryNameAr = freezed,}) {
  return _then(_CompletePartModel(
part: null == part ? _self.part : part // ignore: cast_nullable_to_non_nullable
as PartModel,compatibility: freezed == compatibility ? _self._compatibility : compatibility // ignore: cast_nullable_to_non_nullable
as List<PartCompatibilityModel>?,attributes: freezed == attributes ? _self._attributes : attributes // ignore: cast_nullable_to_non_nullable
as List<PartAttributeModel>?,categoryName: freezed == categoryName ? _self.categoryName : categoryName // ignore: cast_nullable_to_non_nullable
as String?,categoryNameAr: freezed == categoryNameAr ? _self.categoryNameAr : categoryNameAr // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of CompletePartModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PartModelCopyWith<$Res> get part {
  
  return $PartModelCopyWith<$Res>(_self.part, (value) {
    return _then(_self.copyWith(part: value));
  });
}
}

// dart format on
