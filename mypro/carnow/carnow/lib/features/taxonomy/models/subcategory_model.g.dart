// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subcategory_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SubCategoryModel _$SubCategoryModelFromJson(Map<String, dynamic> json) =>
    _SubCategoryModel(
      id: json['id'] as String,
      categoryId: json['category_id'] as String,
      nameAr: json['name_ar'] as String,
      nameEn: json['name_en'] as String,
      specificationSchema: (json['specification_schema'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$SubCategoryModelToJson(_SubCategoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'category_id': instance.categoryId,
      'name_ar': instance.nameAr,
      'name_en': instance.nameEn,
      'specification_schema': instance.specificationSchema,
      'created_at': instance.createdAt?.toIso8601String(),
    };
