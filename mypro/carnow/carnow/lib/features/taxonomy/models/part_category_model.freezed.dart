// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'part_category_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PartCategoryModel {

 String get id; String get subcategoryId; String get nameAr; String get nameEn; Map<String, dynamic>? get specificationSchema; DateTime? get createdAt;
/// Create a copy of PartCategoryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PartCategoryModelCopyWith<PartCategoryModel> get copyWith => _$PartCategoryModelCopyWithImpl<PartCategoryModel>(this as PartCategoryModel, _$identity);

  /// Serializes this PartCategoryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PartCategoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.subcategoryId, subcategoryId) || other.subcategoryId == subcategoryId)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&const DeepCollectionEquality().equals(other.specificationSchema, specificationSchema)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,subcategoryId,nameAr,nameEn,const DeepCollectionEquality().hash(specificationSchema),createdAt);

@override
String toString() {
  return 'PartCategoryModel(id: $id, subcategoryId: $subcategoryId, nameAr: $nameAr, nameEn: $nameEn, specificationSchema: $specificationSchema, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $PartCategoryModelCopyWith<$Res>  {
  factory $PartCategoryModelCopyWith(PartCategoryModel value, $Res Function(PartCategoryModel) _then) = _$PartCategoryModelCopyWithImpl;
@useResult
$Res call({
 String id, String subcategoryId, String nameAr, String nameEn, Map<String, dynamic>? specificationSchema, DateTime? createdAt
});




}
/// @nodoc
class _$PartCategoryModelCopyWithImpl<$Res>
    implements $PartCategoryModelCopyWith<$Res> {
  _$PartCategoryModelCopyWithImpl(this._self, this._then);

  final PartCategoryModel _self;
  final $Res Function(PartCategoryModel) _then;

/// Create a copy of PartCategoryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? subcategoryId = null,Object? nameAr = null,Object? nameEn = null,Object? specificationSchema = freezed,Object? createdAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subcategoryId: null == subcategoryId ? _self.subcategoryId : subcategoryId // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,nameEn: null == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String,specificationSchema: freezed == specificationSchema ? _self.specificationSchema : specificationSchema // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [PartCategoryModel].
extension PartCategoryModelPatterns on PartCategoryModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PartCategoryModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PartCategoryModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PartCategoryModel value)  $default,){
final _that = this;
switch (_that) {
case _PartCategoryModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PartCategoryModel value)?  $default,){
final _that = this;
switch (_that) {
case _PartCategoryModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String subcategoryId,  String nameAr,  String nameEn,  Map<String, dynamic>? specificationSchema,  DateTime? createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PartCategoryModel() when $default != null:
return $default(_that.id,_that.subcategoryId,_that.nameAr,_that.nameEn,_that.specificationSchema,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String subcategoryId,  String nameAr,  String nameEn,  Map<String, dynamic>? specificationSchema,  DateTime? createdAt)  $default,) {final _that = this;
switch (_that) {
case _PartCategoryModel():
return $default(_that.id,_that.subcategoryId,_that.nameAr,_that.nameEn,_that.specificationSchema,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String subcategoryId,  String nameAr,  String nameEn,  Map<String, dynamic>? specificationSchema,  DateTime? createdAt)?  $default,) {final _that = this;
switch (_that) {
case _PartCategoryModel() when $default != null:
return $default(_that.id,_that.subcategoryId,_that.nameAr,_that.nameEn,_that.specificationSchema,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _PartCategoryModel implements PartCategoryModel {
  const _PartCategoryModel({required this.id, required this.subcategoryId, required this.nameAr, required this.nameEn, final  Map<String, dynamic>? specificationSchema, this.createdAt}): _specificationSchema = specificationSchema;
  factory _PartCategoryModel.fromJson(Map<String, dynamic> json) => _$PartCategoryModelFromJson(json);

@override final  String id;
@override final  String subcategoryId;
@override final  String nameAr;
@override final  String nameEn;
 final  Map<String, dynamic>? _specificationSchema;
@override Map<String, dynamic>? get specificationSchema {
  final value = _specificationSchema;
  if (value == null) return null;
  if (_specificationSchema is EqualUnmodifiableMapView) return _specificationSchema;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  DateTime? createdAt;

/// Create a copy of PartCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PartCategoryModelCopyWith<_PartCategoryModel> get copyWith => __$PartCategoryModelCopyWithImpl<_PartCategoryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PartCategoryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PartCategoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.subcategoryId, subcategoryId) || other.subcategoryId == subcategoryId)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&const DeepCollectionEquality().equals(other._specificationSchema, _specificationSchema)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,subcategoryId,nameAr,nameEn,const DeepCollectionEquality().hash(_specificationSchema),createdAt);

@override
String toString() {
  return 'PartCategoryModel(id: $id, subcategoryId: $subcategoryId, nameAr: $nameAr, nameEn: $nameEn, specificationSchema: $specificationSchema, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$PartCategoryModelCopyWith<$Res> implements $PartCategoryModelCopyWith<$Res> {
  factory _$PartCategoryModelCopyWith(_PartCategoryModel value, $Res Function(_PartCategoryModel) _then) = __$PartCategoryModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String subcategoryId, String nameAr, String nameEn, Map<String, dynamic>? specificationSchema, DateTime? createdAt
});




}
/// @nodoc
class __$PartCategoryModelCopyWithImpl<$Res>
    implements _$PartCategoryModelCopyWith<$Res> {
  __$PartCategoryModelCopyWithImpl(this._self, this._then);

  final _PartCategoryModel _self;
  final $Res Function(_PartCategoryModel) _then;

/// Create a copy of PartCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? subcategoryId = null,Object? nameAr = null,Object? nameEn = null,Object? specificationSchema = freezed,Object? createdAt = freezed,}) {
  return _then(_PartCategoryModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,subcategoryId: null == subcategoryId ? _self.subcategoryId : subcategoryId // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,nameEn: null == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String,specificationSchema: freezed == specificationSchema ? _self._specificationSchema : specificationSchema // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
