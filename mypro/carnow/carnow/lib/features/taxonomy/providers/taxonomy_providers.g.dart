// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'taxonomy_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allSectionsHash() => r'32b757a0976d07f1d40f01545d63fe54392194ba';

/// مزود لجلب جميع الأقسام
///
/// Copied from [allSections].
@ProviderFor(allSections)
final allSectionsProvider =
    AutoDisposeFutureProvider<List<SectionModel>>.internal(
      allSections,
      name: r'allSectionsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$allSectionsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllSectionsRef = AutoDisposeFutureProviderRef<List<SectionModel>>;
String _$allCategoriesHash() => r'e9e4356b5f5458ffdc61518b3422d1e60bd924bb';

/// مزود لجلب جميع الفئات
///
/// Copied from [allCategories].
@ProviderFor(allCategories)
final allCategoriesProvider =
    AutoDisposeFutureProvider<List<CategoryModel>>.internal(
      allCategories,
      name: r'allCategoriesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$allCategoriesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllCategoriesRef = AutoDisposeFutureProviderRef<List<CategoryModel>>;
String _$sectionByIdHash() => r'168f46ecdb3f22794421597db8ebd0a30259765b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود لجلب قسم محدد بواسطة المعرف
///
/// Copied from [sectionById].
@ProviderFor(sectionById)
const sectionByIdProvider = SectionByIdFamily();

/// مزود لجلب قسم محدد بواسطة المعرف
///
/// Copied from [sectionById].
class SectionByIdFamily extends Family<AsyncValue<SectionModel?>> {
  /// مزود لجلب قسم محدد بواسطة المعرف
  ///
  /// Copied from [sectionById].
  const SectionByIdFamily();

  /// مزود لجلب قسم محدد بواسطة المعرف
  ///
  /// Copied from [sectionById].
  SectionByIdProvider call(String sectionId) {
    return SectionByIdProvider(sectionId);
  }

  @override
  SectionByIdProvider getProviderOverride(
    covariant SectionByIdProvider provider,
  ) {
    return call(provider.sectionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sectionByIdProvider';
}

/// مزود لجلب قسم محدد بواسطة المعرف
///
/// Copied from [sectionById].
class SectionByIdProvider extends AutoDisposeFutureProvider<SectionModel?> {
  /// مزود لجلب قسم محدد بواسطة المعرف
  ///
  /// Copied from [sectionById].
  SectionByIdProvider(String sectionId)
    : this._internal(
        (ref) => sectionById(ref as SectionByIdRef, sectionId),
        from: sectionByIdProvider,
        name: r'sectionByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sectionByIdHash,
        dependencies: SectionByIdFamily._dependencies,
        allTransitiveDependencies: SectionByIdFamily._allTransitiveDependencies,
        sectionId: sectionId,
      );

  SectionByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sectionId,
  }) : super.internal();

  final String sectionId;

  @override
  Override overrideWith(
    FutureOr<SectionModel?> Function(SectionByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SectionByIdProvider._internal(
        (ref) => create(ref as SectionByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sectionId: sectionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SectionModel?> createElement() {
    return _SectionByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SectionByIdProvider && other.sectionId == sectionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sectionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SectionByIdRef on AutoDisposeFutureProviderRef<SectionModel?> {
  /// The parameter `sectionId` of this provider.
  String get sectionId;
}

class _SectionByIdProviderElement
    extends AutoDisposeFutureProviderElement<SectionModel?>
    with SectionByIdRef {
  _SectionByIdProviderElement(super.provider);

  @override
  String get sectionId => (origin as SectionByIdProvider).sectionId;
}

String _$categoryByIdHash() => r'e24d66e84708d373a654a58ed6210406b8aef060';

/// مزود لجلب فئة محددة بواسطة المعرف
///
/// Copied from [categoryById].
@ProviderFor(categoryById)
const categoryByIdProvider = CategoryByIdFamily();

/// مزود لجلب فئة محددة بواسطة المعرف
///
/// Copied from [categoryById].
class CategoryByIdFamily extends Family<AsyncValue<CategoryModel?>> {
  /// مزود لجلب فئة محددة بواسطة المعرف
  ///
  /// Copied from [categoryById].
  const CategoryByIdFamily();

  /// مزود لجلب فئة محددة بواسطة المعرف
  ///
  /// Copied from [categoryById].
  CategoryByIdProvider call(String categoryId) {
    return CategoryByIdProvider(categoryId);
  }

  @override
  CategoryByIdProvider getProviderOverride(
    covariant CategoryByIdProvider provider,
  ) {
    return call(provider.categoryId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryByIdProvider';
}

/// مزود لجلب فئة محددة بواسطة المعرف
///
/// Copied from [categoryById].
class CategoryByIdProvider extends AutoDisposeFutureProvider<CategoryModel?> {
  /// مزود لجلب فئة محددة بواسطة المعرف
  ///
  /// Copied from [categoryById].
  CategoryByIdProvider(String categoryId)
    : this._internal(
        (ref) => categoryById(ref as CategoryByIdRef, categoryId),
        from: categoryByIdProvider,
        name: r'categoryByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$categoryByIdHash,
        dependencies: CategoryByIdFamily._dependencies,
        allTransitiveDependencies:
            CategoryByIdFamily._allTransitiveDependencies,
        categoryId: categoryId,
      );

  CategoryByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<CategoryModel?> Function(CategoryByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryByIdProvider._internal(
        (ref) => create(ref as CategoryByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CategoryModel?> createElement() {
    return _CategoryByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryByIdProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryByIdRef on AutoDisposeFutureProviderRef<CategoryModel?> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _CategoryByIdProviderElement
    extends AutoDisposeFutureProviderElement<CategoryModel?>
    with CategoryByIdRef {
  _CategoryByIdProviderElement(super.provider);

  @override
  String get categoryId => (origin as CategoryByIdProvider).categoryId;
}

String _$categoriesBySectionHash() =>
    r'1d22a4d516878f235343fe9cb0f8c5277c89ba8c';

/// مزود لجلب الفئات حسب القسم
///
/// Copied from [categoriesBySection].
@ProviderFor(categoriesBySection)
const categoriesBySectionProvider = CategoriesBySectionFamily();

/// مزود لجلب الفئات حسب القسم
///
/// Copied from [categoriesBySection].
class CategoriesBySectionFamily
    extends Family<AsyncValue<List<CategoryModel>>> {
  /// مزود لجلب الفئات حسب القسم
  ///
  /// Copied from [categoriesBySection].
  const CategoriesBySectionFamily();

  /// مزود لجلب الفئات حسب القسم
  ///
  /// Copied from [categoriesBySection].
  CategoriesBySectionProvider call(String sectionId) {
    return CategoriesBySectionProvider(sectionId);
  }

  @override
  CategoriesBySectionProvider getProviderOverride(
    covariant CategoriesBySectionProvider provider,
  ) {
    return call(provider.sectionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoriesBySectionProvider';
}

/// مزود لجلب الفئات حسب القسم
///
/// Copied from [categoriesBySection].
class CategoriesBySectionProvider
    extends AutoDisposeFutureProvider<List<CategoryModel>> {
  /// مزود لجلب الفئات حسب القسم
  ///
  /// Copied from [categoriesBySection].
  CategoriesBySectionProvider(String sectionId)
    : this._internal(
        (ref) => categoriesBySection(ref as CategoriesBySectionRef, sectionId),
        from: categoriesBySectionProvider,
        name: r'categoriesBySectionProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$categoriesBySectionHash,
        dependencies: CategoriesBySectionFamily._dependencies,
        allTransitiveDependencies:
            CategoriesBySectionFamily._allTransitiveDependencies,
        sectionId: sectionId,
      );

  CategoriesBySectionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sectionId,
  }) : super.internal();

  final String sectionId;

  @override
  Override overrideWith(
    FutureOr<List<CategoryModel>> Function(CategoriesBySectionRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoriesBySectionProvider._internal(
        (ref) => create(ref as CategoriesBySectionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sectionId: sectionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CategoryModel>> createElement() {
    return _CategoriesBySectionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoriesBySectionProvider && other.sectionId == sectionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sectionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoriesBySectionRef
    on AutoDisposeFutureProviderRef<List<CategoryModel>> {
  /// The parameter `sectionId` of this provider.
  String get sectionId;
}

class _CategoriesBySectionProviderElement
    extends AutoDisposeFutureProviderElement<List<CategoryModel>>
    with CategoriesBySectionRef {
  _CategoriesBySectionProviderElement(super.provider);

  @override
  String get sectionId => (origin as CategoriesBySectionProvider).sectionId;
}

String _$subCategoriesByCategoryHash() =>
    r'4bb8befc76d9c7c129e1e8ac229f722e3cc78e4d';

/// مزود لجلب الفئات الفرعية حسب الفئة
///
/// Copied from [subCategoriesByCategory].
@ProviderFor(subCategoriesByCategory)
const subCategoriesByCategoryProvider = SubCategoriesByCategoryFamily();

/// مزود لجلب الفئات الفرعية حسب الفئة
///
/// Copied from [subCategoriesByCategory].
class SubCategoriesByCategoryFamily
    extends Family<AsyncValue<List<SubCategoryModel>>> {
  /// مزود لجلب الفئات الفرعية حسب الفئة
  ///
  /// Copied from [subCategoriesByCategory].
  const SubCategoriesByCategoryFamily();

  /// مزود لجلب الفئات الفرعية حسب الفئة
  ///
  /// Copied from [subCategoriesByCategory].
  SubCategoriesByCategoryProvider call(String categoryId) {
    return SubCategoriesByCategoryProvider(categoryId);
  }

  @override
  SubCategoriesByCategoryProvider getProviderOverride(
    covariant SubCategoriesByCategoryProvider provider,
  ) {
    return call(provider.categoryId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'subCategoriesByCategoryProvider';
}

/// مزود لجلب الفئات الفرعية حسب الفئة
///
/// Copied from [subCategoriesByCategory].
class SubCategoriesByCategoryProvider
    extends AutoDisposeFutureProvider<List<SubCategoryModel>> {
  /// مزود لجلب الفئات الفرعية حسب الفئة
  ///
  /// Copied from [subCategoriesByCategory].
  SubCategoriesByCategoryProvider(String categoryId)
    : this._internal(
        (ref) => subCategoriesByCategory(
          ref as SubCategoriesByCategoryRef,
          categoryId,
        ),
        from: subCategoriesByCategoryProvider,
        name: r'subCategoriesByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$subCategoriesByCategoryHash,
        dependencies: SubCategoriesByCategoryFamily._dependencies,
        allTransitiveDependencies:
            SubCategoriesByCategoryFamily._allTransitiveDependencies,
        categoryId: categoryId,
      );

  SubCategoriesByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<List<SubCategoryModel>> Function(
      SubCategoriesByCategoryRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SubCategoriesByCategoryProvider._internal(
        (ref) => create(ref as SubCategoriesByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<SubCategoryModel>> createElement() {
    return _SubCategoriesByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SubCategoriesByCategoryProvider &&
        other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SubCategoriesByCategoryRef
    on AutoDisposeFutureProviderRef<List<SubCategoryModel>> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _SubCategoriesByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<SubCategoryModel>>
    with SubCategoriesByCategoryRef {
  _SubCategoriesByCategoryProviderElement(super.provider);

  @override
  String get categoryId =>
      (origin as SubCategoriesByCategoryProvider).categoryId;
}

String _$selectedTaxonomyHash() => r'e1eb70f8149496a33e65177677e355bbafd4240f';

/// Holds the currently selected section, category, and subcategory.
/// Uses Dart record type (Section, Category, SubCategory)
///
/// Copied from [SelectedTaxonomy].
@ProviderFor(SelectedTaxonomy)
final selectedTaxonomyProvider =
    AutoDisposeNotifierProvider<
      SelectedTaxonomy,
      (SectionModel?, CategoryModel?, SubCategoryModel?)
    >.internal(
      SelectedTaxonomy.new,
      name: r'selectedTaxonomyProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$selectedTaxonomyHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SelectedTaxonomy =
    AutoDisposeNotifier<(SectionModel?, CategoryModel?, SubCategoryModel?)>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
