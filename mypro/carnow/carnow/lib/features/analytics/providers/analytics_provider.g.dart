// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerAnalyticsHash() => r'c60fa8e0455bcd6e7fd20d4e743a90d53c2e55bb';

/// Provider for seller analytics data
///
/// Copied from [sellerAnalytics].
@ProviderFor(sellerAnalytics)
final sellerAnalyticsProvider =
    AutoDisposeFutureProvider<SellerAnalyticsModel>.internal(
      sellerAnalytics,
      name: r'sellerAnalyticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerAnalyticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerAnalyticsRef = AutoDisposeFutureProviderRef<SellerAnalyticsModel>;
String _$inventoryAnalyticsHash() =>
    r'ef10c9789bcc3a9678eb644b290cdce9706da308';

/// Provider for inventory analytics
///
/// Copied from [inventoryAnalytics].
@ProviderFor(inventoryAnalytics)
final inventoryAnalyticsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      inventoryAnalytics,
      name: r'inventoryAnalyticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryAnalyticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InventoryAnalyticsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
