// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'specialized_screen_analytics_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$specializedScreenAnalyticsHash() =>
    r'963de961fc31b3b056d467b88f09416c2c2aec39';

/// See also [specializedScreenAnalytics].
@ProviderFor(specializedScreenAnalytics)
final specializedScreenAnalyticsProvider =
    AutoDisposeFutureProvider<List<SpecializedScreenAnalytics>>.internal(
      specializedScreenAnalytics,
      name: r'specializedScreenAnalyticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$specializedScreenAnalyticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SpecializedScreenAnalyticsRef =
    AutoDisposeFutureProviderRef<List<SpecializedScreenAnalytics>>;
String _$specializedScreenSummaryHash() =>
    r'08465404f875fd60e507abe96e52da5228a8ba16';

/// See also [specializedScreenSummary].
@ProviderFor(specializedScreenSummary)
final specializedScreenSummaryProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      specializedScreenSummary,
      name: r'specializedScreenSummaryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$specializedScreenSummaryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SpecializedScreenSummaryRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
