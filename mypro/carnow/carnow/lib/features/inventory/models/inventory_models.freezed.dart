// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'inventory_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$InventoryItemModel {

 String get id; String get productName; String get sku; int get currentStock; int get minStockLevel; int get maxStockLevel; double get unitCost; double get sellingPrice; String get category; InventoryStatus get status; DateTime get lastUpdated; String? get imageUrl; String? get location;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of InventoryItemModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InventoryItemModelCopyWith<InventoryItemModel> get copyWith => _$InventoryItemModelCopyWithImpl<InventoryItemModel>(this as InventoryItemModel, _$identity);

  /// Serializes this InventoryItemModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InventoryItemModel&&(identical(other.id, id) || other.id == id)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.currentStock, currentStock) || other.currentStock == currentStock)&&(identical(other.minStockLevel, minStockLevel) || other.minStockLevel == minStockLevel)&&(identical(other.maxStockLevel, maxStockLevel) || other.maxStockLevel == maxStockLevel)&&(identical(other.unitCost, unitCost) || other.unitCost == unitCost)&&(identical(other.sellingPrice, sellingPrice) || other.sellingPrice == sellingPrice)&&(identical(other.category, category) || other.category == category)&&(identical(other.status, status) || other.status == status)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.location, location) || other.location == location)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productName,sku,currentStock,minStockLevel,maxStockLevel,unitCost,sellingPrice,category,status,lastUpdated,imageUrl,location,createdAt,updatedAt);

@override
String toString() {
  return 'InventoryItemModel(id: $id, productName: $productName, sku: $sku, currentStock: $currentStock, minStockLevel: $minStockLevel, maxStockLevel: $maxStockLevel, unitCost: $unitCost, sellingPrice: $sellingPrice, category: $category, status: $status, lastUpdated: $lastUpdated, imageUrl: $imageUrl, location: $location, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $InventoryItemModelCopyWith<$Res>  {
  factory $InventoryItemModelCopyWith(InventoryItemModel value, $Res Function(InventoryItemModel) _then) = _$InventoryItemModelCopyWithImpl;
@useResult
$Res call({
 String id, String productName, String sku, int currentStock, int minStockLevel, int maxStockLevel, double unitCost, double sellingPrice, String category, InventoryStatus status, DateTime lastUpdated, String? imageUrl, String? location,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$InventoryItemModelCopyWithImpl<$Res>
    implements $InventoryItemModelCopyWith<$Res> {
  _$InventoryItemModelCopyWithImpl(this._self, this._then);

  final InventoryItemModel _self;
  final $Res Function(InventoryItemModel) _then;

/// Create a copy of InventoryItemModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? productName = null,Object? sku = null,Object? currentStock = null,Object? minStockLevel = null,Object? maxStockLevel = null,Object? unitCost = null,Object? sellingPrice = null,Object? category = null,Object? status = null,Object? lastUpdated = null,Object? imageUrl = freezed,Object? location = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,sku: null == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String,currentStock: null == currentStock ? _self.currentStock : currentStock // ignore: cast_nullable_to_non_nullable
as int,minStockLevel: null == minStockLevel ? _self.minStockLevel : minStockLevel // ignore: cast_nullable_to_non_nullable
as int,maxStockLevel: null == maxStockLevel ? _self.maxStockLevel : maxStockLevel // ignore: cast_nullable_to_non_nullable
as int,unitCost: null == unitCost ? _self.unitCost : unitCost // ignore: cast_nullable_to_non_nullable
as double,sellingPrice: null == sellingPrice ? _self.sellingPrice : sellingPrice // ignore: cast_nullable_to_non_nullable
as double,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InventoryStatus,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [InventoryItemModel].
extension InventoryItemModelPatterns on InventoryItemModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _InventoryItemModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _InventoryItemModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _InventoryItemModel value)  $default,){
final _that = this;
switch (_that) {
case _InventoryItemModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _InventoryItemModel value)?  $default,){
final _that = this;
switch (_that) {
case _InventoryItemModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String productName,  String sku,  int currentStock,  int minStockLevel,  int maxStockLevel,  double unitCost,  double sellingPrice,  String category,  InventoryStatus status,  DateTime lastUpdated,  String? imageUrl,  String? location, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _InventoryItemModel() when $default != null:
return $default(_that.id,_that.productName,_that.sku,_that.currentStock,_that.minStockLevel,_that.maxStockLevel,_that.unitCost,_that.sellingPrice,_that.category,_that.status,_that.lastUpdated,_that.imageUrl,_that.location,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String productName,  String sku,  int currentStock,  int minStockLevel,  int maxStockLevel,  double unitCost,  double sellingPrice,  String category,  InventoryStatus status,  DateTime lastUpdated,  String? imageUrl,  String? location, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _InventoryItemModel():
return $default(_that.id,_that.productName,_that.sku,_that.currentStock,_that.minStockLevel,_that.maxStockLevel,_that.unitCost,_that.sellingPrice,_that.category,_that.status,_that.lastUpdated,_that.imageUrl,_that.location,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String productName,  String sku,  int currentStock,  int minStockLevel,  int maxStockLevel,  double unitCost,  double sellingPrice,  String category,  InventoryStatus status,  DateTime lastUpdated,  String? imageUrl,  String? location, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _InventoryItemModel() when $default != null:
return $default(_that.id,_that.productName,_that.sku,_that.currentStock,_that.minStockLevel,_that.maxStockLevel,_that.unitCost,_that.sellingPrice,_that.category,_that.status,_that.lastUpdated,_that.imageUrl,_that.location,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _InventoryItemModel implements InventoryItemModel {
  const _InventoryItemModel({required this.id, required this.productName, required this.sku, required this.currentStock, required this.minStockLevel, required this.maxStockLevel, required this.unitCost, required this.sellingPrice, required this.category, required this.status, required this.lastUpdated, this.imageUrl, this.location, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _InventoryItemModel.fromJson(Map<String, dynamic> json) => _$InventoryItemModelFromJson(json);

@override final  String id;
@override final  String productName;
@override final  String sku;
@override final  int currentStock;
@override final  int minStockLevel;
@override final  int maxStockLevel;
@override final  double unitCost;
@override final  double sellingPrice;
@override final  String category;
@override final  InventoryStatus status;
@override final  DateTime lastUpdated;
@override final  String? imageUrl;
@override final  String? location;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of InventoryItemModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InventoryItemModelCopyWith<_InventoryItemModel> get copyWith => __$InventoryItemModelCopyWithImpl<_InventoryItemModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InventoryItemModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InventoryItemModel&&(identical(other.id, id) || other.id == id)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.currentStock, currentStock) || other.currentStock == currentStock)&&(identical(other.minStockLevel, minStockLevel) || other.minStockLevel == minStockLevel)&&(identical(other.maxStockLevel, maxStockLevel) || other.maxStockLevel == maxStockLevel)&&(identical(other.unitCost, unitCost) || other.unitCost == unitCost)&&(identical(other.sellingPrice, sellingPrice) || other.sellingPrice == sellingPrice)&&(identical(other.category, category) || other.category == category)&&(identical(other.status, status) || other.status == status)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.location, location) || other.location == location)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productName,sku,currentStock,minStockLevel,maxStockLevel,unitCost,sellingPrice,category,status,lastUpdated,imageUrl,location,createdAt,updatedAt);

@override
String toString() {
  return 'InventoryItemModel(id: $id, productName: $productName, sku: $sku, currentStock: $currentStock, minStockLevel: $minStockLevel, maxStockLevel: $maxStockLevel, unitCost: $unitCost, sellingPrice: $sellingPrice, category: $category, status: $status, lastUpdated: $lastUpdated, imageUrl: $imageUrl, location: $location, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$InventoryItemModelCopyWith<$Res> implements $InventoryItemModelCopyWith<$Res> {
  factory _$InventoryItemModelCopyWith(_InventoryItemModel value, $Res Function(_InventoryItemModel) _then) = __$InventoryItemModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String productName, String sku, int currentStock, int minStockLevel, int maxStockLevel, double unitCost, double sellingPrice, String category, InventoryStatus status, DateTime lastUpdated, String? imageUrl, String? location,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$InventoryItemModelCopyWithImpl<$Res>
    implements _$InventoryItemModelCopyWith<$Res> {
  __$InventoryItemModelCopyWithImpl(this._self, this._then);

  final _InventoryItemModel _self;
  final $Res Function(_InventoryItemModel) _then;

/// Create a copy of InventoryItemModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? productName = null,Object? sku = null,Object? currentStock = null,Object? minStockLevel = null,Object? maxStockLevel = null,Object? unitCost = null,Object? sellingPrice = null,Object? category = null,Object? status = null,Object? lastUpdated = null,Object? imageUrl = freezed,Object? location = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_InventoryItemModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,sku: null == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String,currentStock: null == currentStock ? _self.currentStock : currentStock // ignore: cast_nullable_to_non_nullable
as int,minStockLevel: null == minStockLevel ? _self.minStockLevel : minStockLevel // ignore: cast_nullable_to_non_nullable
as int,maxStockLevel: null == maxStockLevel ? _self.maxStockLevel : maxStockLevel // ignore: cast_nullable_to_non_nullable
as int,unitCost: null == unitCost ? _self.unitCost : unitCost // ignore: cast_nullable_to_non_nullable
as double,sellingPrice: null == sellingPrice ? _self.sellingPrice : sellingPrice // ignore: cast_nullable_to_non_nullable
as double,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InventoryStatus,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,location: freezed == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$InventorySummary {

 int get totalProducts; double get totalValue; int get lowStockCount; int get outOfStockCount; int get inStockCount; int get discontinuedCount;
/// Create a copy of InventorySummary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InventorySummaryCopyWith<InventorySummary> get copyWith => _$InventorySummaryCopyWithImpl<InventorySummary>(this as InventorySummary, _$identity);

  /// Serializes this InventorySummary to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InventorySummary&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalValue, totalValue) || other.totalValue == totalValue)&&(identical(other.lowStockCount, lowStockCount) || other.lowStockCount == lowStockCount)&&(identical(other.outOfStockCount, outOfStockCount) || other.outOfStockCount == outOfStockCount)&&(identical(other.inStockCount, inStockCount) || other.inStockCount == inStockCount)&&(identical(other.discontinuedCount, discontinuedCount) || other.discontinuedCount == discontinuedCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalProducts,totalValue,lowStockCount,outOfStockCount,inStockCount,discontinuedCount);

@override
String toString() {
  return 'InventorySummary(totalProducts: $totalProducts, totalValue: $totalValue, lowStockCount: $lowStockCount, outOfStockCount: $outOfStockCount, inStockCount: $inStockCount, discontinuedCount: $discontinuedCount)';
}


}

/// @nodoc
abstract mixin class $InventorySummaryCopyWith<$Res>  {
  factory $InventorySummaryCopyWith(InventorySummary value, $Res Function(InventorySummary) _then) = _$InventorySummaryCopyWithImpl;
@useResult
$Res call({
 int totalProducts, double totalValue, int lowStockCount, int outOfStockCount, int inStockCount, int discontinuedCount
});




}
/// @nodoc
class _$InventorySummaryCopyWithImpl<$Res>
    implements $InventorySummaryCopyWith<$Res> {
  _$InventorySummaryCopyWithImpl(this._self, this._then);

  final InventorySummary _self;
  final $Res Function(InventorySummary) _then;

/// Create a copy of InventorySummary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalProducts = null,Object? totalValue = null,Object? lowStockCount = null,Object? outOfStockCount = null,Object? inStockCount = null,Object? discontinuedCount = null,}) {
  return _then(_self.copyWith(
totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalValue: null == totalValue ? _self.totalValue : totalValue // ignore: cast_nullable_to_non_nullable
as double,lowStockCount: null == lowStockCount ? _self.lowStockCount : lowStockCount // ignore: cast_nullable_to_non_nullable
as int,outOfStockCount: null == outOfStockCount ? _self.outOfStockCount : outOfStockCount // ignore: cast_nullable_to_non_nullable
as int,inStockCount: null == inStockCount ? _self.inStockCount : inStockCount // ignore: cast_nullable_to_non_nullable
as int,discontinuedCount: null == discontinuedCount ? _self.discontinuedCount : discontinuedCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [InventorySummary].
extension InventorySummaryPatterns on InventorySummary {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _InventorySummary value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _InventorySummary() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _InventorySummary value)  $default,){
final _that = this;
switch (_that) {
case _InventorySummary():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _InventorySummary value)?  $default,){
final _that = this;
switch (_that) {
case _InventorySummary() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalProducts,  double totalValue,  int lowStockCount,  int outOfStockCount,  int inStockCount,  int discontinuedCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _InventorySummary() when $default != null:
return $default(_that.totalProducts,_that.totalValue,_that.lowStockCount,_that.outOfStockCount,_that.inStockCount,_that.discontinuedCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalProducts,  double totalValue,  int lowStockCount,  int outOfStockCount,  int inStockCount,  int discontinuedCount)  $default,) {final _that = this;
switch (_that) {
case _InventorySummary():
return $default(_that.totalProducts,_that.totalValue,_that.lowStockCount,_that.outOfStockCount,_that.inStockCount,_that.discontinuedCount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalProducts,  double totalValue,  int lowStockCount,  int outOfStockCount,  int inStockCount,  int discontinuedCount)?  $default,) {final _that = this;
switch (_that) {
case _InventorySummary() when $default != null:
return $default(_that.totalProducts,_that.totalValue,_that.lowStockCount,_that.outOfStockCount,_that.inStockCount,_that.discontinuedCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _InventorySummary implements InventorySummary {
  const _InventorySummary({required this.totalProducts, required this.totalValue, required this.lowStockCount, required this.outOfStockCount, required this.inStockCount, required this.discontinuedCount});
  factory _InventorySummary.fromJson(Map<String, dynamic> json) => _$InventorySummaryFromJson(json);

@override final  int totalProducts;
@override final  double totalValue;
@override final  int lowStockCount;
@override final  int outOfStockCount;
@override final  int inStockCount;
@override final  int discontinuedCount;

/// Create a copy of InventorySummary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InventorySummaryCopyWith<_InventorySummary> get copyWith => __$InventorySummaryCopyWithImpl<_InventorySummary>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InventorySummaryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InventorySummary&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalValue, totalValue) || other.totalValue == totalValue)&&(identical(other.lowStockCount, lowStockCount) || other.lowStockCount == lowStockCount)&&(identical(other.outOfStockCount, outOfStockCount) || other.outOfStockCount == outOfStockCount)&&(identical(other.inStockCount, inStockCount) || other.inStockCount == inStockCount)&&(identical(other.discontinuedCount, discontinuedCount) || other.discontinuedCount == discontinuedCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalProducts,totalValue,lowStockCount,outOfStockCount,inStockCount,discontinuedCount);

@override
String toString() {
  return 'InventorySummary(totalProducts: $totalProducts, totalValue: $totalValue, lowStockCount: $lowStockCount, outOfStockCount: $outOfStockCount, inStockCount: $inStockCount, discontinuedCount: $discontinuedCount)';
}


}

/// @nodoc
abstract mixin class _$InventorySummaryCopyWith<$Res> implements $InventorySummaryCopyWith<$Res> {
  factory _$InventorySummaryCopyWith(_InventorySummary value, $Res Function(_InventorySummary) _then) = __$InventorySummaryCopyWithImpl;
@override @useResult
$Res call({
 int totalProducts, double totalValue, int lowStockCount, int outOfStockCount, int inStockCount, int discontinuedCount
});




}
/// @nodoc
class __$InventorySummaryCopyWithImpl<$Res>
    implements _$InventorySummaryCopyWith<$Res> {
  __$InventorySummaryCopyWithImpl(this._self, this._then);

  final _InventorySummary _self;
  final $Res Function(_InventorySummary) _then;

/// Create a copy of InventorySummary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalProducts = null,Object? totalValue = null,Object? lowStockCount = null,Object? outOfStockCount = null,Object? inStockCount = null,Object? discontinuedCount = null,}) {
  return _then(_InventorySummary(
totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalValue: null == totalValue ? _self.totalValue : totalValue // ignore: cast_nullable_to_non_nullable
as double,lowStockCount: null == lowStockCount ? _self.lowStockCount : lowStockCount // ignore: cast_nullable_to_non_nullable
as int,outOfStockCount: null == outOfStockCount ? _self.outOfStockCount : outOfStockCount // ignore: cast_nullable_to_non_nullable
as int,inStockCount: null == inStockCount ? _self.inStockCount : inStockCount // ignore: cast_nullable_to_non_nullable
as int,discontinuedCount: null == discontinuedCount ? _self.discontinuedCount : discontinuedCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
