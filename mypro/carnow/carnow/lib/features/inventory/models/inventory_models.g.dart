// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inventory_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_InventoryItemModel _$InventoryItemModelFromJson(Map<String, dynamic> json) =>
    _InventoryItemModel(
      id: json['id'] as String,
      productName: json['product_name'] as String,
      sku: json['sku'] as String,
      currentStock: (json['current_stock'] as num).toInt(),
      minStockLevel: (json['min_stock_level'] as num).toInt(),
      maxStockLevel: (json['max_stock_level'] as num).toInt(),
      unitCost: (json['unit_cost'] as num).toDouble(),
      sellingPrice: (json['selling_price'] as num).toDouble(),
      category: json['category'] as String,
      status: $enumDecode(_$InventoryStatusEnumMap, json['status']),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
      imageUrl: json['image_url'] as String?,
      location: json['location'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$InventoryItemModelToJson(_InventoryItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'product_name': instance.productName,
      'sku': instance.sku,
      'current_stock': instance.currentStock,
      'min_stock_level': instance.minStockLevel,
      'max_stock_level': instance.maxStockLevel,
      'unit_cost': instance.unitCost,
      'selling_price': instance.sellingPrice,
      'category': instance.category,
      'status': _$InventoryStatusEnumMap[instance.status]!,
      'last_updated': instance.lastUpdated.toIso8601String(),
      'image_url': instance.imageUrl,
      'location': instance.location,
    };

const _$InventoryStatusEnumMap = {
  InventoryStatus.inStock: 'in_stock',
  InventoryStatus.lowStock: 'low_stock',
  InventoryStatus.outOfStock: 'out_of_stock',
  InventoryStatus.discontinued: 'discontinued',
};

_InventorySummary _$InventorySummaryFromJson(Map<String, dynamic> json) =>
    _InventorySummary(
      totalProducts: (json['totalProducts'] as num).toInt(),
      totalValue: (json['totalValue'] as num).toDouble(),
      lowStockCount: (json['lowStockCount'] as num).toInt(),
      outOfStockCount: (json['outOfStockCount'] as num).toInt(),
      inStockCount: (json['inStockCount'] as num).toInt(),
      discontinuedCount: (json['discontinuedCount'] as num).toInt(),
    );

Map<String, dynamic> _$InventorySummaryToJson(_InventorySummary instance) =>
    <String, dynamic>{
      'totalProducts': instance.totalProducts,
      'totalValue': instance.totalValue,
      'lowStockCount': instance.lowStockCount,
      'outOfStockCount': instance.outOfStockCount,
      'inStockCount': instance.inStockCount,
      'discontinuedCount': instance.discontinuedCount,
    };
