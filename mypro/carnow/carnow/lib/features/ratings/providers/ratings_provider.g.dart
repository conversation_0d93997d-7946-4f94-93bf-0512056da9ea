// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ratings_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productRatingStatsHash() =>
    r'0e5a4f6a415b583071ee3333d726e1a83221cec0';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [productRatingStats].
@ProviderFor(productRatingStats)
const productRatingStatsProvider = ProductRatingStatsFamily();

/// See also [productRatingStats].
class ProductRatingStatsFamily
    extends Family<AsyncValue<Map<String, dynamic>>> {
  /// See also [productRatingStats].
  const ProductRatingStatsFamily();

  /// See also [productRatingStats].
  ProductRatingStatsProvider call(int productId) {
    return ProductRatingStatsProvider(productId);
  }

  @override
  ProductRatingStatsProvider getProviderOverride(
    covariant ProductRatingStatsProvider provider,
  ) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productRatingStatsProvider';
}

/// See also [productRatingStats].
class ProductRatingStatsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// See also [productRatingStats].
  ProductRatingStatsProvider(int productId)
    : this._internal(
        (ref) => productRatingStats(ref as ProductRatingStatsRef, productId),
        from: productRatingStatsProvider,
        name: r'productRatingStatsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productRatingStatsHash,
        dependencies: ProductRatingStatsFamily._dependencies,
        allTransitiveDependencies:
            ProductRatingStatsFamily._allTransitiveDependencies,
        productId: productId,
      );

  ProductRatingStatsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final int productId;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(ProductRatingStatsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductRatingStatsProvider._internal(
        (ref) => create(ref as ProductRatingStatsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _ProductRatingStatsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductRatingStatsProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductRatingStatsRef
    on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `productId` of this provider.
  int get productId;
}

class _ProductRatingStatsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with ProductRatingStatsRef {
  _ProductRatingStatsProviderElement(super.provider);

  @override
  int get productId => (origin as ProductRatingStatsProvider).productId;
}

String _$userRatingHash() => r'4c3a1f4422c89fabf5f813c18af1c123b52782e6';

/// See also [userRating].
@ProviderFor(userRating)
const userRatingProvider = UserRatingFamily();

/// See also [userRating].
class UserRatingFamily extends Family<AsyncValue<RatingModel?>> {
  /// See also [userRating].
  const UserRatingFamily();

  /// See also [userRating].
  UserRatingProvider call(int productId) {
    return UserRatingProvider(productId);
  }

  @override
  UserRatingProvider getProviderOverride(
    covariant UserRatingProvider provider,
  ) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userRatingProvider';
}

/// See also [userRating].
class UserRatingProvider extends AutoDisposeFutureProvider<RatingModel?> {
  /// See also [userRating].
  UserRatingProvider(int productId)
    : this._internal(
        (ref) => userRating(ref as UserRatingRef, productId),
        from: userRatingProvider,
        name: r'userRatingProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userRatingHash,
        dependencies: UserRatingFamily._dependencies,
        allTransitiveDependencies: UserRatingFamily._allTransitiveDependencies,
        productId: productId,
      );

  UserRatingProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final int productId;

  @override
  Override overrideWith(
    FutureOr<RatingModel?> Function(UserRatingRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserRatingProvider._internal(
        (ref) => create(ref as UserRatingRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<RatingModel?> createElement() {
    return _UserRatingProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserRatingProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserRatingRef on AutoDisposeFutureProviderRef<RatingModel?> {
  /// The parameter `productId` of this provider.
  int get productId;
}

class _UserRatingProviderElement
    extends AutoDisposeFutureProviderElement<RatingModel?>
    with UserRatingRef {
  _UserRatingProviderElement(super.provider);

  @override
  int get productId => (origin as UserRatingProvider).productId;
}

String _$paginatedRatingsHash() => r'afe33eac07f317ebe2611a3d434b1f6aaa93ab61';

/// See also [paginatedRatings].
@ProviderFor(paginatedRatings)
const paginatedRatingsProvider = PaginatedRatingsFamily();

/// See also [paginatedRatings].
class PaginatedRatingsFamily extends Family<AsyncValue<List<RatingModel>>> {
  /// See also [paginatedRatings].
  const PaginatedRatingsFamily();

  /// See also [paginatedRatings].
  PaginatedRatingsProvider call(int productId, {int page = 0, int limit = 10}) {
    return PaginatedRatingsProvider(productId, page: page, limit: limit);
  }

  @override
  PaginatedRatingsProvider getProviderOverride(
    covariant PaginatedRatingsProvider provider,
  ) {
    return call(provider.productId, page: provider.page, limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'paginatedRatingsProvider';
}

/// See also [paginatedRatings].
class PaginatedRatingsProvider
    extends AutoDisposeFutureProvider<List<RatingModel>> {
  /// See also [paginatedRatings].
  PaginatedRatingsProvider(int productId, {int page = 0, int limit = 10})
    : this._internal(
        (ref) => paginatedRatings(
          ref as PaginatedRatingsRef,
          productId,
          page: page,
          limit: limit,
        ),
        from: paginatedRatingsProvider,
        name: r'paginatedRatingsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$paginatedRatingsHash,
        dependencies: PaginatedRatingsFamily._dependencies,
        allTransitiveDependencies:
            PaginatedRatingsFamily._allTransitiveDependencies,
        productId: productId,
        page: page,
        limit: limit,
      );

  PaginatedRatingsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
    required this.page,
    required this.limit,
  }) : super.internal();

  final int productId;
  final int page;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<RatingModel>> Function(PaginatedRatingsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PaginatedRatingsProvider._internal(
        (ref) => create(ref as PaginatedRatingsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<RatingModel>> createElement() {
    return _PaginatedRatingsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PaginatedRatingsProvider &&
        other.productId == productId &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PaginatedRatingsRef on AutoDisposeFutureProviderRef<List<RatingModel>> {
  /// The parameter `productId` of this provider.
  int get productId;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _PaginatedRatingsProviderElement
    extends AutoDisposeFutureProviderElement<List<RatingModel>>
    with PaginatedRatingsRef {
  _PaginatedRatingsProviderElement(super.provider);

  @override
  int get productId => (origin as PaginatedRatingsProvider).productId;
  @override
  int get page => (origin as PaginatedRatingsProvider).page;
  @override
  int get limit => (origin as PaginatedRatingsProvider).limit;
}

String _$ratingsNotifierHash() => r'9cc5e96a5eef04fc1b11cb721f989d8a9229d4c2';

abstract class _$RatingsNotifier
    extends BuildlessAutoDisposeAsyncNotifier<List<RatingModel>> {
  late final int productId;

  FutureOr<List<RatingModel>> build(int productId);
}

/// See also [RatingsNotifier].
@ProviderFor(RatingsNotifier)
const ratingsNotifierProvider = RatingsNotifierFamily();

/// See also [RatingsNotifier].
class RatingsNotifierFamily extends Family<AsyncValue<List<RatingModel>>> {
  /// See also [RatingsNotifier].
  const RatingsNotifierFamily();

  /// See also [RatingsNotifier].
  RatingsNotifierProvider call(int productId) {
    return RatingsNotifierProvider(productId);
  }

  @override
  RatingsNotifierProvider getProviderOverride(
    covariant RatingsNotifierProvider provider,
  ) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'ratingsNotifierProvider';
}

/// See also [RatingsNotifier].
class RatingsNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          RatingsNotifier,
          List<RatingModel>
        > {
  /// See also [RatingsNotifier].
  RatingsNotifierProvider(int productId)
    : this._internal(
        () => RatingsNotifier()..productId = productId,
        from: ratingsNotifierProvider,
        name: r'ratingsNotifierProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$ratingsNotifierHash,
        dependencies: RatingsNotifierFamily._dependencies,
        allTransitiveDependencies:
            RatingsNotifierFamily._allTransitiveDependencies,
        productId: productId,
      );

  RatingsNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final int productId;

  @override
  FutureOr<List<RatingModel>> runNotifierBuild(
    covariant RatingsNotifier notifier,
  ) {
    return notifier.build(productId);
  }

  @override
  Override overrideWith(RatingsNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: RatingsNotifierProvider._internal(
        () => create()..productId = productId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<RatingsNotifier, List<RatingModel>>
  createElement() {
    return _RatingsNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RatingsNotifierProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RatingsNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<List<RatingModel>> {
  /// The parameter `productId` of this provider.
  int get productId;
}

class _RatingsNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          RatingsNotifier,
          List<RatingModel>
        >
    with RatingsNotifierRef {
  _RatingsNotifierProviderElement(super.provider);

  @override
  int get productId => (origin as RatingsNotifierProvider).productId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
