// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_conversation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatConversation _$ChatConversationFromJson(Map<String, dynamic> json) =>
    ChatConversation(
      id: json['id'] as String,
      participants: (json['participants'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      productId: json['product_id'] as String?,
      productName: json['product_name'] as String?,
      productImageUrl: json['product_image_url'] as String?,
      lastMessage: json['last_message'] as Map<String, dynamic>?,
      unreadCount: (json['unread_count'] as num?)?.toInt() ?? 0,
      status: json['status'] as String? ?? 'active',
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      isDeleted: json['is_deleted'] as bool? ?? false,
    );

Map<String, dynamic> _$ChatConversationToJson(ChatConversation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'participants': instance.participants,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'product_id': instance.productId,
      'product_name': instance.productName,
      'product_image_url': instance.productImageUrl,
      'last_message': instance.lastMessage,
      'unread_count': instance.unreadCount,
      'status': instance.status,
      'metadata': instance.metadata,
      'is_deleted': instance.isDeleted,
    };
