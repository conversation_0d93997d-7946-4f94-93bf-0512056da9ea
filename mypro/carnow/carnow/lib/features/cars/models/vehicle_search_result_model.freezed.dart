// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_search_result_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleSearchResultModel {

 String get makeName; String get modelName; int get year;
/// Create a copy of VehicleSearchResultModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleSearchResultModelCopyWith<VehicleSearchResultModel> get copyWith => _$VehicleSearchResultModelCopyWithImpl<VehicleSearchResultModel>(this as VehicleSearchResultModel, _$identity);

  /// Serializes this VehicleSearchResultModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleSearchResultModel&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.year, year) || other.year == year));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,makeName,modelName,year);

@override
String toString() {
  return 'VehicleSearchResultModel(makeName: $makeName, modelName: $modelName, year: $year)';
}


}

/// @nodoc
abstract mixin class $VehicleSearchResultModelCopyWith<$Res>  {
  factory $VehicleSearchResultModelCopyWith(VehicleSearchResultModel value, $Res Function(VehicleSearchResultModel) _then) = _$VehicleSearchResultModelCopyWithImpl;
@useResult
$Res call({
 String makeName, String modelName, int year
});




}
/// @nodoc
class _$VehicleSearchResultModelCopyWithImpl<$Res>
    implements $VehicleSearchResultModelCopyWith<$Res> {
  _$VehicleSearchResultModelCopyWithImpl(this._self, this._then);

  final VehicleSearchResultModel _self;
  final $Res Function(VehicleSearchResultModel) _then;

/// Create a copy of VehicleSearchResultModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? makeName = null,Object? modelName = null,Object? year = null,}) {
  return _then(_self.copyWith(
makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleSearchResultModel].
extension VehicleSearchResultModelPatterns on VehicleSearchResultModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleSearchResultModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleSearchResultModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleSearchResultModel value)  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchResultModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleSearchResultModel value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchResultModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String makeName,  String modelName,  int year)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleSearchResultModel() when $default != null:
return $default(_that.makeName,_that.modelName,_that.year);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String makeName,  String modelName,  int year)  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchResultModel():
return $default(_that.makeName,_that.modelName,_that.year);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String makeName,  String modelName,  int year)?  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchResultModel() when $default != null:
return $default(_that.makeName,_that.modelName,_that.year);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleSearchResultModel implements VehicleSearchResultModel {
  const _VehicleSearchResultModel({required this.makeName, required this.modelName, required this.year});
  factory _VehicleSearchResultModel.fromJson(Map<String, dynamic> json) => _$VehicleSearchResultModelFromJson(json);

@override final  String makeName;
@override final  String modelName;
@override final  int year;

/// Create a copy of VehicleSearchResultModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleSearchResultModelCopyWith<_VehicleSearchResultModel> get copyWith => __$VehicleSearchResultModelCopyWithImpl<_VehicleSearchResultModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleSearchResultModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleSearchResultModel&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.year, year) || other.year == year));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,makeName,modelName,year);

@override
String toString() {
  return 'VehicleSearchResultModel(makeName: $makeName, modelName: $modelName, year: $year)';
}


}

/// @nodoc
abstract mixin class _$VehicleSearchResultModelCopyWith<$Res> implements $VehicleSearchResultModelCopyWith<$Res> {
  factory _$VehicleSearchResultModelCopyWith(_VehicleSearchResultModel value, $Res Function(_VehicleSearchResultModel) _then) = __$VehicleSearchResultModelCopyWithImpl;
@override @useResult
$Res call({
 String makeName, String modelName, int year
});




}
/// @nodoc
class __$VehicleSearchResultModelCopyWithImpl<$Res>
    implements _$VehicleSearchResultModelCopyWith<$Res> {
  __$VehicleSearchResultModelCopyWithImpl(this._self, this._then);

  final _VehicleSearchResultModel _self;
  final $Res Function(_VehicleSearchResultModel) _then;

/// Create a copy of VehicleSearchResultModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? makeName = null,Object? modelName = null,Object? year = null,}) {
  return _then(_VehicleSearchResultModel(
makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
