// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_specifications_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleSpecificationsModel {

// معلومات أساسية
 String get makeName; String get modelName; int get year; String get vehicleType; String get generationInfo;// مواصفات المحرك
 String get engineDisplacement; int get engineCylinders; int get enginePowerHp; int get engineTorqueNm; String get fuelType; String get engineCode;// مواصفات ناقل الحركة
 String get transmissionType; int get transmissionSpeeds; String get drivetrain;// مواصفات الهيكل
 String get bodyStyle; int get doorsCount; int get seatingCapacity; int get wheelbaseMm;// مواصفات الفرامل والعجلات
 String get frontBrakeType; String get rearBrakeType; String get wheelSize; String get tireSize;// معلومات قطع الغيار
 String get partsCompatibilityCode; List<String> get commonPartsCategories; Map<String, String> get maintenanceIntervals;// معلومات إضافية
 String get marketAvailability; bool get isPopularModel;
/// Create a copy of VehicleSpecificationsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleSpecificationsModelCopyWith<VehicleSpecificationsModel> get copyWith => _$VehicleSpecificationsModelCopyWithImpl<VehicleSpecificationsModel>(this as VehicleSpecificationsModel, _$identity);

  /// Serializes this VehicleSpecificationsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleSpecificationsModel&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.year, year) || other.year == year)&&(identical(other.vehicleType, vehicleType) || other.vehicleType == vehicleType)&&(identical(other.generationInfo, generationInfo) || other.generationInfo == generationInfo)&&(identical(other.engineDisplacement, engineDisplacement) || other.engineDisplacement == engineDisplacement)&&(identical(other.engineCylinders, engineCylinders) || other.engineCylinders == engineCylinders)&&(identical(other.enginePowerHp, enginePowerHp) || other.enginePowerHp == enginePowerHp)&&(identical(other.engineTorqueNm, engineTorqueNm) || other.engineTorqueNm == engineTorqueNm)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.engineCode, engineCode) || other.engineCode == engineCode)&&(identical(other.transmissionType, transmissionType) || other.transmissionType == transmissionType)&&(identical(other.transmissionSpeeds, transmissionSpeeds) || other.transmissionSpeeds == transmissionSpeeds)&&(identical(other.drivetrain, drivetrain) || other.drivetrain == drivetrain)&&(identical(other.bodyStyle, bodyStyle) || other.bodyStyle == bodyStyle)&&(identical(other.doorsCount, doorsCount) || other.doorsCount == doorsCount)&&(identical(other.seatingCapacity, seatingCapacity) || other.seatingCapacity == seatingCapacity)&&(identical(other.wheelbaseMm, wheelbaseMm) || other.wheelbaseMm == wheelbaseMm)&&(identical(other.frontBrakeType, frontBrakeType) || other.frontBrakeType == frontBrakeType)&&(identical(other.rearBrakeType, rearBrakeType) || other.rearBrakeType == rearBrakeType)&&(identical(other.wheelSize, wheelSize) || other.wheelSize == wheelSize)&&(identical(other.tireSize, tireSize) || other.tireSize == tireSize)&&(identical(other.partsCompatibilityCode, partsCompatibilityCode) || other.partsCompatibilityCode == partsCompatibilityCode)&&const DeepCollectionEquality().equals(other.commonPartsCategories, commonPartsCategories)&&const DeepCollectionEquality().equals(other.maintenanceIntervals, maintenanceIntervals)&&(identical(other.marketAvailability, marketAvailability) || other.marketAvailability == marketAvailability)&&(identical(other.isPopularModel, isPopularModel) || other.isPopularModel == isPopularModel));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,makeName,modelName,year,vehicleType,generationInfo,engineDisplacement,engineCylinders,enginePowerHp,engineTorqueNm,fuelType,engineCode,transmissionType,transmissionSpeeds,drivetrain,bodyStyle,doorsCount,seatingCapacity,wheelbaseMm,frontBrakeType,rearBrakeType,wheelSize,tireSize,partsCompatibilityCode,const DeepCollectionEquality().hash(commonPartsCategories),const DeepCollectionEquality().hash(maintenanceIntervals),marketAvailability,isPopularModel]);

@override
String toString() {
  return 'VehicleSpecificationsModel(makeName: $makeName, modelName: $modelName, year: $year, vehicleType: $vehicleType, generationInfo: $generationInfo, engineDisplacement: $engineDisplacement, engineCylinders: $engineCylinders, enginePowerHp: $enginePowerHp, engineTorqueNm: $engineTorqueNm, fuelType: $fuelType, engineCode: $engineCode, transmissionType: $transmissionType, transmissionSpeeds: $transmissionSpeeds, drivetrain: $drivetrain, bodyStyle: $bodyStyle, doorsCount: $doorsCount, seatingCapacity: $seatingCapacity, wheelbaseMm: $wheelbaseMm, frontBrakeType: $frontBrakeType, rearBrakeType: $rearBrakeType, wheelSize: $wheelSize, tireSize: $tireSize, partsCompatibilityCode: $partsCompatibilityCode, commonPartsCategories: $commonPartsCategories, maintenanceIntervals: $maintenanceIntervals, marketAvailability: $marketAvailability, isPopularModel: $isPopularModel)';
}


}

/// @nodoc
abstract mixin class $VehicleSpecificationsModelCopyWith<$Res>  {
  factory $VehicleSpecificationsModelCopyWith(VehicleSpecificationsModel value, $Res Function(VehicleSpecificationsModel) _then) = _$VehicleSpecificationsModelCopyWithImpl;
@useResult
$Res call({
 String makeName, String modelName, int year, String vehicleType, String generationInfo, String engineDisplacement, int engineCylinders, int enginePowerHp, int engineTorqueNm, String fuelType, String engineCode, String transmissionType, int transmissionSpeeds, String drivetrain, String bodyStyle, int doorsCount, int seatingCapacity, int wheelbaseMm, String frontBrakeType, String rearBrakeType, String wheelSize, String tireSize, String partsCompatibilityCode, List<String> commonPartsCategories, Map<String, String> maintenanceIntervals, String marketAvailability, bool isPopularModel
});




}
/// @nodoc
class _$VehicleSpecificationsModelCopyWithImpl<$Res>
    implements $VehicleSpecificationsModelCopyWith<$Res> {
  _$VehicleSpecificationsModelCopyWithImpl(this._self, this._then);

  final VehicleSpecificationsModel _self;
  final $Res Function(VehicleSpecificationsModel) _then;

/// Create a copy of VehicleSpecificationsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? makeName = null,Object? modelName = null,Object? year = null,Object? vehicleType = null,Object? generationInfo = null,Object? engineDisplacement = null,Object? engineCylinders = null,Object? enginePowerHp = null,Object? engineTorqueNm = null,Object? fuelType = null,Object? engineCode = null,Object? transmissionType = null,Object? transmissionSpeeds = null,Object? drivetrain = null,Object? bodyStyle = null,Object? doorsCount = null,Object? seatingCapacity = null,Object? wheelbaseMm = null,Object? frontBrakeType = null,Object? rearBrakeType = null,Object? wheelSize = null,Object? tireSize = null,Object? partsCompatibilityCode = null,Object? commonPartsCategories = null,Object? maintenanceIntervals = null,Object? marketAvailability = null,Object? isPopularModel = null,}) {
  return _then(_self.copyWith(
makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,vehicleType: null == vehicleType ? _self.vehicleType : vehicleType // ignore: cast_nullable_to_non_nullable
as String,generationInfo: null == generationInfo ? _self.generationInfo : generationInfo // ignore: cast_nullable_to_non_nullable
as String,engineDisplacement: null == engineDisplacement ? _self.engineDisplacement : engineDisplacement // ignore: cast_nullable_to_non_nullable
as String,engineCylinders: null == engineCylinders ? _self.engineCylinders : engineCylinders // ignore: cast_nullable_to_non_nullable
as int,enginePowerHp: null == enginePowerHp ? _self.enginePowerHp : enginePowerHp // ignore: cast_nullable_to_non_nullable
as int,engineTorqueNm: null == engineTorqueNm ? _self.engineTorqueNm : engineTorqueNm // ignore: cast_nullable_to_non_nullable
as int,fuelType: null == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String,engineCode: null == engineCode ? _self.engineCode : engineCode // ignore: cast_nullable_to_non_nullable
as String,transmissionType: null == transmissionType ? _self.transmissionType : transmissionType // ignore: cast_nullable_to_non_nullable
as String,transmissionSpeeds: null == transmissionSpeeds ? _self.transmissionSpeeds : transmissionSpeeds // ignore: cast_nullable_to_non_nullable
as int,drivetrain: null == drivetrain ? _self.drivetrain : drivetrain // ignore: cast_nullable_to_non_nullable
as String,bodyStyle: null == bodyStyle ? _self.bodyStyle : bodyStyle // ignore: cast_nullable_to_non_nullable
as String,doorsCount: null == doorsCount ? _self.doorsCount : doorsCount // ignore: cast_nullable_to_non_nullable
as int,seatingCapacity: null == seatingCapacity ? _self.seatingCapacity : seatingCapacity // ignore: cast_nullable_to_non_nullable
as int,wheelbaseMm: null == wheelbaseMm ? _self.wheelbaseMm : wheelbaseMm // ignore: cast_nullable_to_non_nullable
as int,frontBrakeType: null == frontBrakeType ? _self.frontBrakeType : frontBrakeType // ignore: cast_nullable_to_non_nullable
as String,rearBrakeType: null == rearBrakeType ? _self.rearBrakeType : rearBrakeType // ignore: cast_nullable_to_non_nullable
as String,wheelSize: null == wheelSize ? _self.wheelSize : wheelSize // ignore: cast_nullable_to_non_nullable
as String,tireSize: null == tireSize ? _self.tireSize : tireSize // ignore: cast_nullable_to_non_nullable
as String,partsCompatibilityCode: null == partsCompatibilityCode ? _self.partsCompatibilityCode : partsCompatibilityCode // ignore: cast_nullable_to_non_nullable
as String,commonPartsCategories: null == commonPartsCategories ? _self.commonPartsCategories : commonPartsCategories // ignore: cast_nullable_to_non_nullable
as List<String>,maintenanceIntervals: null == maintenanceIntervals ? _self.maintenanceIntervals : maintenanceIntervals // ignore: cast_nullable_to_non_nullable
as Map<String, String>,marketAvailability: null == marketAvailability ? _self.marketAvailability : marketAvailability // ignore: cast_nullable_to_non_nullable
as String,isPopularModel: null == isPopularModel ? _self.isPopularModel : isPopularModel // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleSpecificationsModel].
extension VehicleSpecificationsModelPatterns on VehicleSpecificationsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleSpecificationsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleSpecificationsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleSpecificationsModel value)  $default,){
final _that = this;
switch (_that) {
case _VehicleSpecificationsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleSpecificationsModel value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleSpecificationsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String makeName,  String modelName,  int year,  String vehicleType,  String generationInfo,  String engineDisplacement,  int engineCylinders,  int enginePowerHp,  int engineTorqueNm,  String fuelType,  String engineCode,  String transmissionType,  int transmissionSpeeds,  String drivetrain,  String bodyStyle,  int doorsCount,  int seatingCapacity,  int wheelbaseMm,  String frontBrakeType,  String rearBrakeType,  String wheelSize,  String tireSize,  String partsCompatibilityCode,  List<String> commonPartsCategories,  Map<String, String> maintenanceIntervals,  String marketAvailability,  bool isPopularModel)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleSpecificationsModel() when $default != null:
return $default(_that.makeName,_that.modelName,_that.year,_that.vehicleType,_that.generationInfo,_that.engineDisplacement,_that.engineCylinders,_that.enginePowerHp,_that.engineTorqueNm,_that.fuelType,_that.engineCode,_that.transmissionType,_that.transmissionSpeeds,_that.drivetrain,_that.bodyStyle,_that.doorsCount,_that.seatingCapacity,_that.wheelbaseMm,_that.frontBrakeType,_that.rearBrakeType,_that.wheelSize,_that.tireSize,_that.partsCompatibilityCode,_that.commonPartsCategories,_that.maintenanceIntervals,_that.marketAvailability,_that.isPopularModel);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String makeName,  String modelName,  int year,  String vehicleType,  String generationInfo,  String engineDisplacement,  int engineCylinders,  int enginePowerHp,  int engineTorqueNm,  String fuelType,  String engineCode,  String transmissionType,  int transmissionSpeeds,  String drivetrain,  String bodyStyle,  int doorsCount,  int seatingCapacity,  int wheelbaseMm,  String frontBrakeType,  String rearBrakeType,  String wheelSize,  String tireSize,  String partsCompatibilityCode,  List<String> commonPartsCategories,  Map<String, String> maintenanceIntervals,  String marketAvailability,  bool isPopularModel)  $default,) {final _that = this;
switch (_that) {
case _VehicleSpecificationsModel():
return $default(_that.makeName,_that.modelName,_that.year,_that.vehicleType,_that.generationInfo,_that.engineDisplacement,_that.engineCylinders,_that.enginePowerHp,_that.engineTorqueNm,_that.fuelType,_that.engineCode,_that.transmissionType,_that.transmissionSpeeds,_that.drivetrain,_that.bodyStyle,_that.doorsCount,_that.seatingCapacity,_that.wheelbaseMm,_that.frontBrakeType,_that.rearBrakeType,_that.wheelSize,_that.tireSize,_that.partsCompatibilityCode,_that.commonPartsCategories,_that.maintenanceIntervals,_that.marketAvailability,_that.isPopularModel);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String makeName,  String modelName,  int year,  String vehicleType,  String generationInfo,  String engineDisplacement,  int engineCylinders,  int enginePowerHp,  int engineTorqueNm,  String fuelType,  String engineCode,  String transmissionType,  int transmissionSpeeds,  String drivetrain,  String bodyStyle,  int doorsCount,  int seatingCapacity,  int wheelbaseMm,  String frontBrakeType,  String rearBrakeType,  String wheelSize,  String tireSize,  String partsCompatibilityCode,  List<String> commonPartsCategories,  Map<String, String> maintenanceIntervals,  String marketAvailability,  bool isPopularModel)?  $default,) {final _that = this;
switch (_that) {
case _VehicleSpecificationsModel() when $default != null:
return $default(_that.makeName,_that.modelName,_that.year,_that.vehicleType,_that.generationInfo,_that.engineDisplacement,_that.engineCylinders,_that.enginePowerHp,_that.engineTorqueNm,_that.fuelType,_that.engineCode,_that.transmissionType,_that.transmissionSpeeds,_that.drivetrain,_that.bodyStyle,_that.doorsCount,_that.seatingCapacity,_that.wheelbaseMm,_that.frontBrakeType,_that.rearBrakeType,_that.wheelSize,_that.tireSize,_that.partsCompatibilityCode,_that.commonPartsCategories,_that.maintenanceIntervals,_that.marketAvailability,_that.isPopularModel);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleSpecificationsModel implements VehicleSpecificationsModel {
  const _VehicleSpecificationsModel({required this.makeName, required this.modelName, required this.year, required this.vehicleType, required this.generationInfo, required this.engineDisplacement, required this.engineCylinders, required this.enginePowerHp, required this.engineTorqueNm, required this.fuelType, required this.engineCode, required this.transmissionType, required this.transmissionSpeeds, required this.drivetrain, required this.bodyStyle, required this.doorsCount, required this.seatingCapacity, required this.wheelbaseMm, required this.frontBrakeType, required this.rearBrakeType, required this.wheelSize, required this.tireSize, required this.partsCompatibilityCode, required final  List<String> commonPartsCategories, required final  Map<String, String> maintenanceIntervals, required this.marketAvailability, required this.isPopularModel}): _commonPartsCategories = commonPartsCategories,_maintenanceIntervals = maintenanceIntervals;
  factory _VehicleSpecificationsModel.fromJson(Map<String, dynamic> json) => _$VehicleSpecificationsModelFromJson(json);

// معلومات أساسية
@override final  String makeName;
@override final  String modelName;
@override final  int year;
@override final  String vehicleType;
@override final  String generationInfo;
// مواصفات المحرك
@override final  String engineDisplacement;
@override final  int engineCylinders;
@override final  int enginePowerHp;
@override final  int engineTorqueNm;
@override final  String fuelType;
@override final  String engineCode;
// مواصفات ناقل الحركة
@override final  String transmissionType;
@override final  int transmissionSpeeds;
@override final  String drivetrain;
// مواصفات الهيكل
@override final  String bodyStyle;
@override final  int doorsCount;
@override final  int seatingCapacity;
@override final  int wheelbaseMm;
// مواصفات الفرامل والعجلات
@override final  String frontBrakeType;
@override final  String rearBrakeType;
@override final  String wheelSize;
@override final  String tireSize;
// معلومات قطع الغيار
@override final  String partsCompatibilityCode;
 final  List<String> _commonPartsCategories;
@override List<String> get commonPartsCategories {
  if (_commonPartsCategories is EqualUnmodifiableListView) return _commonPartsCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_commonPartsCategories);
}

 final  Map<String, String> _maintenanceIntervals;
@override Map<String, String> get maintenanceIntervals {
  if (_maintenanceIntervals is EqualUnmodifiableMapView) return _maintenanceIntervals;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_maintenanceIntervals);
}

// معلومات إضافية
@override final  String marketAvailability;
@override final  bool isPopularModel;

/// Create a copy of VehicleSpecificationsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleSpecificationsModelCopyWith<_VehicleSpecificationsModel> get copyWith => __$VehicleSpecificationsModelCopyWithImpl<_VehicleSpecificationsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleSpecificationsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleSpecificationsModel&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.year, year) || other.year == year)&&(identical(other.vehicleType, vehicleType) || other.vehicleType == vehicleType)&&(identical(other.generationInfo, generationInfo) || other.generationInfo == generationInfo)&&(identical(other.engineDisplacement, engineDisplacement) || other.engineDisplacement == engineDisplacement)&&(identical(other.engineCylinders, engineCylinders) || other.engineCylinders == engineCylinders)&&(identical(other.enginePowerHp, enginePowerHp) || other.enginePowerHp == enginePowerHp)&&(identical(other.engineTorqueNm, engineTorqueNm) || other.engineTorqueNm == engineTorqueNm)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.engineCode, engineCode) || other.engineCode == engineCode)&&(identical(other.transmissionType, transmissionType) || other.transmissionType == transmissionType)&&(identical(other.transmissionSpeeds, transmissionSpeeds) || other.transmissionSpeeds == transmissionSpeeds)&&(identical(other.drivetrain, drivetrain) || other.drivetrain == drivetrain)&&(identical(other.bodyStyle, bodyStyle) || other.bodyStyle == bodyStyle)&&(identical(other.doorsCount, doorsCount) || other.doorsCount == doorsCount)&&(identical(other.seatingCapacity, seatingCapacity) || other.seatingCapacity == seatingCapacity)&&(identical(other.wheelbaseMm, wheelbaseMm) || other.wheelbaseMm == wheelbaseMm)&&(identical(other.frontBrakeType, frontBrakeType) || other.frontBrakeType == frontBrakeType)&&(identical(other.rearBrakeType, rearBrakeType) || other.rearBrakeType == rearBrakeType)&&(identical(other.wheelSize, wheelSize) || other.wheelSize == wheelSize)&&(identical(other.tireSize, tireSize) || other.tireSize == tireSize)&&(identical(other.partsCompatibilityCode, partsCompatibilityCode) || other.partsCompatibilityCode == partsCompatibilityCode)&&const DeepCollectionEquality().equals(other._commonPartsCategories, _commonPartsCategories)&&const DeepCollectionEquality().equals(other._maintenanceIntervals, _maintenanceIntervals)&&(identical(other.marketAvailability, marketAvailability) || other.marketAvailability == marketAvailability)&&(identical(other.isPopularModel, isPopularModel) || other.isPopularModel == isPopularModel));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,makeName,modelName,year,vehicleType,generationInfo,engineDisplacement,engineCylinders,enginePowerHp,engineTorqueNm,fuelType,engineCode,transmissionType,transmissionSpeeds,drivetrain,bodyStyle,doorsCount,seatingCapacity,wheelbaseMm,frontBrakeType,rearBrakeType,wheelSize,tireSize,partsCompatibilityCode,const DeepCollectionEquality().hash(_commonPartsCategories),const DeepCollectionEquality().hash(_maintenanceIntervals),marketAvailability,isPopularModel]);

@override
String toString() {
  return 'VehicleSpecificationsModel(makeName: $makeName, modelName: $modelName, year: $year, vehicleType: $vehicleType, generationInfo: $generationInfo, engineDisplacement: $engineDisplacement, engineCylinders: $engineCylinders, enginePowerHp: $enginePowerHp, engineTorqueNm: $engineTorqueNm, fuelType: $fuelType, engineCode: $engineCode, transmissionType: $transmissionType, transmissionSpeeds: $transmissionSpeeds, drivetrain: $drivetrain, bodyStyle: $bodyStyle, doorsCount: $doorsCount, seatingCapacity: $seatingCapacity, wheelbaseMm: $wheelbaseMm, frontBrakeType: $frontBrakeType, rearBrakeType: $rearBrakeType, wheelSize: $wheelSize, tireSize: $tireSize, partsCompatibilityCode: $partsCompatibilityCode, commonPartsCategories: $commonPartsCategories, maintenanceIntervals: $maintenanceIntervals, marketAvailability: $marketAvailability, isPopularModel: $isPopularModel)';
}


}

/// @nodoc
abstract mixin class _$VehicleSpecificationsModelCopyWith<$Res> implements $VehicleSpecificationsModelCopyWith<$Res> {
  factory _$VehicleSpecificationsModelCopyWith(_VehicleSpecificationsModel value, $Res Function(_VehicleSpecificationsModel) _then) = __$VehicleSpecificationsModelCopyWithImpl;
@override @useResult
$Res call({
 String makeName, String modelName, int year, String vehicleType, String generationInfo, String engineDisplacement, int engineCylinders, int enginePowerHp, int engineTorqueNm, String fuelType, String engineCode, String transmissionType, int transmissionSpeeds, String drivetrain, String bodyStyle, int doorsCount, int seatingCapacity, int wheelbaseMm, String frontBrakeType, String rearBrakeType, String wheelSize, String tireSize, String partsCompatibilityCode, List<String> commonPartsCategories, Map<String, String> maintenanceIntervals, String marketAvailability, bool isPopularModel
});




}
/// @nodoc
class __$VehicleSpecificationsModelCopyWithImpl<$Res>
    implements _$VehicleSpecificationsModelCopyWith<$Res> {
  __$VehicleSpecificationsModelCopyWithImpl(this._self, this._then);

  final _VehicleSpecificationsModel _self;
  final $Res Function(_VehicleSpecificationsModel) _then;

/// Create a copy of VehicleSpecificationsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? makeName = null,Object? modelName = null,Object? year = null,Object? vehicleType = null,Object? generationInfo = null,Object? engineDisplacement = null,Object? engineCylinders = null,Object? enginePowerHp = null,Object? engineTorqueNm = null,Object? fuelType = null,Object? engineCode = null,Object? transmissionType = null,Object? transmissionSpeeds = null,Object? drivetrain = null,Object? bodyStyle = null,Object? doorsCount = null,Object? seatingCapacity = null,Object? wheelbaseMm = null,Object? frontBrakeType = null,Object? rearBrakeType = null,Object? wheelSize = null,Object? tireSize = null,Object? partsCompatibilityCode = null,Object? commonPartsCategories = null,Object? maintenanceIntervals = null,Object? marketAvailability = null,Object? isPopularModel = null,}) {
  return _then(_VehicleSpecificationsModel(
makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,vehicleType: null == vehicleType ? _self.vehicleType : vehicleType // ignore: cast_nullable_to_non_nullable
as String,generationInfo: null == generationInfo ? _self.generationInfo : generationInfo // ignore: cast_nullable_to_non_nullable
as String,engineDisplacement: null == engineDisplacement ? _self.engineDisplacement : engineDisplacement // ignore: cast_nullable_to_non_nullable
as String,engineCylinders: null == engineCylinders ? _self.engineCylinders : engineCylinders // ignore: cast_nullable_to_non_nullable
as int,enginePowerHp: null == enginePowerHp ? _self.enginePowerHp : enginePowerHp // ignore: cast_nullable_to_non_nullable
as int,engineTorqueNm: null == engineTorqueNm ? _self.engineTorqueNm : engineTorqueNm // ignore: cast_nullable_to_non_nullable
as int,fuelType: null == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String,engineCode: null == engineCode ? _self.engineCode : engineCode // ignore: cast_nullable_to_non_nullable
as String,transmissionType: null == transmissionType ? _self.transmissionType : transmissionType // ignore: cast_nullable_to_non_nullable
as String,transmissionSpeeds: null == transmissionSpeeds ? _self.transmissionSpeeds : transmissionSpeeds // ignore: cast_nullable_to_non_nullable
as int,drivetrain: null == drivetrain ? _self.drivetrain : drivetrain // ignore: cast_nullable_to_non_nullable
as String,bodyStyle: null == bodyStyle ? _self.bodyStyle : bodyStyle // ignore: cast_nullable_to_non_nullable
as String,doorsCount: null == doorsCount ? _self.doorsCount : doorsCount // ignore: cast_nullable_to_non_nullable
as int,seatingCapacity: null == seatingCapacity ? _self.seatingCapacity : seatingCapacity // ignore: cast_nullable_to_non_nullable
as int,wheelbaseMm: null == wheelbaseMm ? _self.wheelbaseMm : wheelbaseMm // ignore: cast_nullable_to_non_nullable
as int,frontBrakeType: null == frontBrakeType ? _self.frontBrakeType : frontBrakeType // ignore: cast_nullable_to_non_nullable
as String,rearBrakeType: null == rearBrakeType ? _self.rearBrakeType : rearBrakeType // ignore: cast_nullable_to_non_nullable
as String,wheelSize: null == wheelSize ? _self.wheelSize : wheelSize // ignore: cast_nullable_to_non_nullable
as String,tireSize: null == tireSize ? _self.tireSize : tireSize // ignore: cast_nullable_to_non_nullable
as String,partsCompatibilityCode: null == partsCompatibilityCode ? _self.partsCompatibilityCode : partsCompatibilityCode // ignore: cast_nullable_to_non_nullable
as String,commonPartsCategories: null == commonPartsCategories ? _self._commonPartsCategories : commonPartsCategories // ignore: cast_nullable_to_non_nullable
as List<String>,maintenanceIntervals: null == maintenanceIntervals ? _self._maintenanceIntervals : maintenanceIntervals // ignore: cast_nullable_to_non_nullable
as Map<String, String>,marketAvailability: null == marketAvailability ? _self.marketAvailability : marketAvailability // ignore: cast_nullable_to_non_nullable
as String,isPopularModel: null == isPopularModel ? _self.isPopularModel : isPopularModel // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$CompatiblePartsModel {

 String get vehicleInfo; String get compatibilityCode; String get engineSpecs; String get transmissionSpecs; String get brakeSpecs; String get wheelSpecs; Map<String, String> get maintenanceInfo; List<String> get partsCategories; List<String> get generationCompatibility;
/// Create a copy of CompatiblePartsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CompatiblePartsModelCopyWith<CompatiblePartsModel> get copyWith => _$CompatiblePartsModelCopyWithImpl<CompatiblePartsModel>(this as CompatiblePartsModel, _$identity);

  /// Serializes this CompatiblePartsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CompatiblePartsModel&&(identical(other.vehicleInfo, vehicleInfo) || other.vehicleInfo == vehicleInfo)&&(identical(other.compatibilityCode, compatibilityCode) || other.compatibilityCode == compatibilityCode)&&(identical(other.engineSpecs, engineSpecs) || other.engineSpecs == engineSpecs)&&(identical(other.transmissionSpecs, transmissionSpecs) || other.transmissionSpecs == transmissionSpecs)&&(identical(other.brakeSpecs, brakeSpecs) || other.brakeSpecs == brakeSpecs)&&(identical(other.wheelSpecs, wheelSpecs) || other.wheelSpecs == wheelSpecs)&&const DeepCollectionEquality().equals(other.maintenanceInfo, maintenanceInfo)&&const DeepCollectionEquality().equals(other.partsCategories, partsCategories)&&const DeepCollectionEquality().equals(other.generationCompatibility, generationCompatibility));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,vehicleInfo,compatibilityCode,engineSpecs,transmissionSpecs,brakeSpecs,wheelSpecs,const DeepCollectionEquality().hash(maintenanceInfo),const DeepCollectionEquality().hash(partsCategories),const DeepCollectionEquality().hash(generationCompatibility));

@override
String toString() {
  return 'CompatiblePartsModel(vehicleInfo: $vehicleInfo, compatibilityCode: $compatibilityCode, engineSpecs: $engineSpecs, transmissionSpecs: $transmissionSpecs, brakeSpecs: $brakeSpecs, wheelSpecs: $wheelSpecs, maintenanceInfo: $maintenanceInfo, partsCategories: $partsCategories, generationCompatibility: $generationCompatibility)';
}


}

/// @nodoc
abstract mixin class $CompatiblePartsModelCopyWith<$Res>  {
  factory $CompatiblePartsModelCopyWith(CompatiblePartsModel value, $Res Function(CompatiblePartsModel) _then) = _$CompatiblePartsModelCopyWithImpl;
@useResult
$Res call({
 String vehicleInfo, String compatibilityCode, String engineSpecs, String transmissionSpecs, String brakeSpecs, String wheelSpecs, Map<String, String> maintenanceInfo, List<String> partsCategories, List<String> generationCompatibility
});




}
/// @nodoc
class _$CompatiblePartsModelCopyWithImpl<$Res>
    implements $CompatiblePartsModelCopyWith<$Res> {
  _$CompatiblePartsModelCopyWithImpl(this._self, this._then);

  final CompatiblePartsModel _self;
  final $Res Function(CompatiblePartsModel) _then;

/// Create a copy of CompatiblePartsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? vehicleInfo = null,Object? compatibilityCode = null,Object? engineSpecs = null,Object? transmissionSpecs = null,Object? brakeSpecs = null,Object? wheelSpecs = null,Object? maintenanceInfo = null,Object? partsCategories = null,Object? generationCompatibility = null,}) {
  return _then(_self.copyWith(
vehicleInfo: null == vehicleInfo ? _self.vehicleInfo : vehicleInfo // ignore: cast_nullable_to_non_nullable
as String,compatibilityCode: null == compatibilityCode ? _self.compatibilityCode : compatibilityCode // ignore: cast_nullable_to_non_nullable
as String,engineSpecs: null == engineSpecs ? _self.engineSpecs : engineSpecs // ignore: cast_nullable_to_non_nullable
as String,transmissionSpecs: null == transmissionSpecs ? _self.transmissionSpecs : transmissionSpecs // ignore: cast_nullable_to_non_nullable
as String,brakeSpecs: null == brakeSpecs ? _self.brakeSpecs : brakeSpecs // ignore: cast_nullable_to_non_nullable
as String,wheelSpecs: null == wheelSpecs ? _self.wheelSpecs : wheelSpecs // ignore: cast_nullable_to_non_nullable
as String,maintenanceInfo: null == maintenanceInfo ? _self.maintenanceInfo : maintenanceInfo // ignore: cast_nullable_to_non_nullable
as Map<String, String>,partsCategories: null == partsCategories ? _self.partsCategories : partsCategories // ignore: cast_nullable_to_non_nullable
as List<String>,generationCompatibility: null == generationCompatibility ? _self.generationCompatibility : generationCompatibility // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [CompatiblePartsModel].
extension CompatiblePartsModelPatterns on CompatiblePartsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CompatiblePartsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CompatiblePartsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CompatiblePartsModel value)  $default,){
final _that = this;
switch (_that) {
case _CompatiblePartsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CompatiblePartsModel value)?  $default,){
final _that = this;
switch (_that) {
case _CompatiblePartsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String vehicleInfo,  String compatibilityCode,  String engineSpecs,  String transmissionSpecs,  String brakeSpecs,  String wheelSpecs,  Map<String, String> maintenanceInfo,  List<String> partsCategories,  List<String> generationCompatibility)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CompatiblePartsModel() when $default != null:
return $default(_that.vehicleInfo,_that.compatibilityCode,_that.engineSpecs,_that.transmissionSpecs,_that.brakeSpecs,_that.wheelSpecs,_that.maintenanceInfo,_that.partsCategories,_that.generationCompatibility);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String vehicleInfo,  String compatibilityCode,  String engineSpecs,  String transmissionSpecs,  String brakeSpecs,  String wheelSpecs,  Map<String, String> maintenanceInfo,  List<String> partsCategories,  List<String> generationCompatibility)  $default,) {final _that = this;
switch (_that) {
case _CompatiblePartsModel():
return $default(_that.vehicleInfo,_that.compatibilityCode,_that.engineSpecs,_that.transmissionSpecs,_that.brakeSpecs,_that.wheelSpecs,_that.maintenanceInfo,_that.partsCategories,_that.generationCompatibility);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String vehicleInfo,  String compatibilityCode,  String engineSpecs,  String transmissionSpecs,  String brakeSpecs,  String wheelSpecs,  Map<String, String> maintenanceInfo,  List<String> partsCategories,  List<String> generationCompatibility)?  $default,) {final _that = this;
switch (_that) {
case _CompatiblePartsModel() when $default != null:
return $default(_that.vehicleInfo,_that.compatibilityCode,_that.engineSpecs,_that.transmissionSpecs,_that.brakeSpecs,_that.wheelSpecs,_that.maintenanceInfo,_that.partsCategories,_that.generationCompatibility);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CompatiblePartsModel implements CompatiblePartsModel {
  const _CompatiblePartsModel({required this.vehicleInfo, required this.compatibilityCode, required this.engineSpecs, required this.transmissionSpecs, required this.brakeSpecs, required this.wheelSpecs, required final  Map<String, String> maintenanceInfo, required final  List<String> partsCategories, required final  List<String> generationCompatibility}): _maintenanceInfo = maintenanceInfo,_partsCategories = partsCategories,_generationCompatibility = generationCompatibility;
  factory _CompatiblePartsModel.fromJson(Map<String, dynamic> json) => _$CompatiblePartsModelFromJson(json);

@override final  String vehicleInfo;
@override final  String compatibilityCode;
@override final  String engineSpecs;
@override final  String transmissionSpecs;
@override final  String brakeSpecs;
@override final  String wheelSpecs;
 final  Map<String, String> _maintenanceInfo;
@override Map<String, String> get maintenanceInfo {
  if (_maintenanceInfo is EqualUnmodifiableMapView) return _maintenanceInfo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_maintenanceInfo);
}

 final  List<String> _partsCategories;
@override List<String> get partsCategories {
  if (_partsCategories is EqualUnmodifiableListView) return _partsCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_partsCategories);
}

 final  List<String> _generationCompatibility;
@override List<String> get generationCompatibility {
  if (_generationCompatibility is EqualUnmodifiableListView) return _generationCompatibility;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_generationCompatibility);
}


/// Create a copy of CompatiblePartsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CompatiblePartsModelCopyWith<_CompatiblePartsModel> get copyWith => __$CompatiblePartsModelCopyWithImpl<_CompatiblePartsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CompatiblePartsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CompatiblePartsModel&&(identical(other.vehicleInfo, vehicleInfo) || other.vehicleInfo == vehicleInfo)&&(identical(other.compatibilityCode, compatibilityCode) || other.compatibilityCode == compatibilityCode)&&(identical(other.engineSpecs, engineSpecs) || other.engineSpecs == engineSpecs)&&(identical(other.transmissionSpecs, transmissionSpecs) || other.transmissionSpecs == transmissionSpecs)&&(identical(other.brakeSpecs, brakeSpecs) || other.brakeSpecs == brakeSpecs)&&(identical(other.wheelSpecs, wheelSpecs) || other.wheelSpecs == wheelSpecs)&&const DeepCollectionEquality().equals(other._maintenanceInfo, _maintenanceInfo)&&const DeepCollectionEquality().equals(other._partsCategories, _partsCategories)&&const DeepCollectionEquality().equals(other._generationCompatibility, _generationCompatibility));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,vehicleInfo,compatibilityCode,engineSpecs,transmissionSpecs,brakeSpecs,wheelSpecs,const DeepCollectionEquality().hash(_maintenanceInfo),const DeepCollectionEquality().hash(_partsCategories),const DeepCollectionEquality().hash(_generationCompatibility));

@override
String toString() {
  return 'CompatiblePartsModel(vehicleInfo: $vehicleInfo, compatibilityCode: $compatibilityCode, engineSpecs: $engineSpecs, transmissionSpecs: $transmissionSpecs, brakeSpecs: $brakeSpecs, wheelSpecs: $wheelSpecs, maintenanceInfo: $maintenanceInfo, partsCategories: $partsCategories, generationCompatibility: $generationCompatibility)';
}


}

/// @nodoc
abstract mixin class _$CompatiblePartsModelCopyWith<$Res> implements $CompatiblePartsModelCopyWith<$Res> {
  factory _$CompatiblePartsModelCopyWith(_CompatiblePartsModel value, $Res Function(_CompatiblePartsModel) _then) = __$CompatiblePartsModelCopyWithImpl;
@override @useResult
$Res call({
 String vehicleInfo, String compatibilityCode, String engineSpecs, String transmissionSpecs, String brakeSpecs, String wheelSpecs, Map<String, String> maintenanceInfo, List<String> partsCategories, List<String> generationCompatibility
});




}
/// @nodoc
class __$CompatiblePartsModelCopyWithImpl<$Res>
    implements _$CompatiblePartsModelCopyWith<$Res> {
  __$CompatiblePartsModelCopyWithImpl(this._self, this._then);

  final _CompatiblePartsModel _self;
  final $Res Function(_CompatiblePartsModel) _then;

/// Create a copy of CompatiblePartsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? vehicleInfo = null,Object? compatibilityCode = null,Object? engineSpecs = null,Object? transmissionSpecs = null,Object? brakeSpecs = null,Object? wheelSpecs = null,Object? maintenanceInfo = null,Object? partsCategories = null,Object? generationCompatibility = null,}) {
  return _then(_CompatiblePartsModel(
vehicleInfo: null == vehicleInfo ? _self.vehicleInfo : vehicleInfo // ignore: cast_nullable_to_non_nullable
as String,compatibilityCode: null == compatibilityCode ? _self.compatibilityCode : compatibilityCode // ignore: cast_nullable_to_non_nullable
as String,engineSpecs: null == engineSpecs ? _self.engineSpecs : engineSpecs // ignore: cast_nullable_to_non_nullable
as String,transmissionSpecs: null == transmissionSpecs ? _self.transmissionSpecs : transmissionSpecs // ignore: cast_nullable_to_non_nullable
as String,brakeSpecs: null == brakeSpecs ? _self.brakeSpecs : brakeSpecs // ignore: cast_nullable_to_non_nullable
as String,wheelSpecs: null == wheelSpecs ? _self.wheelSpecs : wheelSpecs // ignore: cast_nullable_to_non_nullable
as String,maintenanceInfo: null == maintenanceInfo ? _self._maintenanceInfo : maintenanceInfo // ignore: cast_nullable_to_non_nullable
as Map<String, String>,partsCategories: null == partsCategories ? _self._partsCategories : partsCategories // ignore: cast_nullable_to_non_nullable
as List<String>,generationCompatibility: null == generationCompatibility ? _self._generationCompatibility : generationCompatibility // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$VehicleForProductModel {

 String get makeName; String get modelName; String get yearRange; String get engineInfo; String get transmissionInfo; String get compatibilityCode; int get marketPopularity; String get partsAvailability; String get maintenanceFrequency;
/// Create a copy of VehicleForProductModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleForProductModelCopyWith<VehicleForProductModel> get copyWith => _$VehicleForProductModelCopyWithImpl<VehicleForProductModel>(this as VehicleForProductModel, _$identity);

  /// Serializes this VehicleForProductModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleForProductModel&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.yearRange, yearRange) || other.yearRange == yearRange)&&(identical(other.engineInfo, engineInfo) || other.engineInfo == engineInfo)&&(identical(other.transmissionInfo, transmissionInfo) || other.transmissionInfo == transmissionInfo)&&(identical(other.compatibilityCode, compatibilityCode) || other.compatibilityCode == compatibilityCode)&&(identical(other.marketPopularity, marketPopularity) || other.marketPopularity == marketPopularity)&&(identical(other.partsAvailability, partsAvailability) || other.partsAvailability == partsAvailability)&&(identical(other.maintenanceFrequency, maintenanceFrequency) || other.maintenanceFrequency == maintenanceFrequency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,makeName,modelName,yearRange,engineInfo,transmissionInfo,compatibilityCode,marketPopularity,partsAvailability,maintenanceFrequency);

@override
String toString() {
  return 'VehicleForProductModel(makeName: $makeName, modelName: $modelName, yearRange: $yearRange, engineInfo: $engineInfo, transmissionInfo: $transmissionInfo, compatibilityCode: $compatibilityCode, marketPopularity: $marketPopularity, partsAvailability: $partsAvailability, maintenanceFrequency: $maintenanceFrequency)';
}


}

/// @nodoc
abstract mixin class $VehicleForProductModelCopyWith<$Res>  {
  factory $VehicleForProductModelCopyWith(VehicleForProductModel value, $Res Function(VehicleForProductModel) _then) = _$VehicleForProductModelCopyWithImpl;
@useResult
$Res call({
 String makeName, String modelName, String yearRange, String engineInfo, String transmissionInfo, String compatibilityCode, int marketPopularity, String partsAvailability, String maintenanceFrequency
});




}
/// @nodoc
class _$VehicleForProductModelCopyWithImpl<$Res>
    implements $VehicleForProductModelCopyWith<$Res> {
  _$VehicleForProductModelCopyWithImpl(this._self, this._then);

  final VehicleForProductModel _self;
  final $Res Function(VehicleForProductModel) _then;

/// Create a copy of VehicleForProductModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? makeName = null,Object? modelName = null,Object? yearRange = null,Object? engineInfo = null,Object? transmissionInfo = null,Object? compatibilityCode = null,Object? marketPopularity = null,Object? partsAvailability = null,Object? maintenanceFrequency = null,}) {
  return _then(_self.copyWith(
makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,yearRange: null == yearRange ? _self.yearRange : yearRange // ignore: cast_nullable_to_non_nullable
as String,engineInfo: null == engineInfo ? _self.engineInfo : engineInfo // ignore: cast_nullable_to_non_nullable
as String,transmissionInfo: null == transmissionInfo ? _self.transmissionInfo : transmissionInfo // ignore: cast_nullable_to_non_nullable
as String,compatibilityCode: null == compatibilityCode ? _self.compatibilityCode : compatibilityCode // ignore: cast_nullable_to_non_nullable
as String,marketPopularity: null == marketPopularity ? _self.marketPopularity : marketPopularity // ignore: cast_nullable_to_non_nullable
as int,partsAvailability: null == partsAvailability ? _self.partsAvailability : partsAvailability // ignore: cast_nullable_to_non_nullable
as String,maintenanceFrequency: null == maintenanceFrequency ? _self.maintenanceFrequency : maintenanceFrequency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleForProductModel].
extension VehicleForProductModelPatterns on VehicleForProductModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleForProductModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleForProductModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleForProductModel value)  $default,){
final _that = this;
switch (_that) {
case _VehicleForProductModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleForProductModel value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleForProductModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String makeName,  String modelName,  String yearRange,  String engineInfo,  String transmissionInfo,  String compatibilityCode,  int marketPopularity,  String partsAvailability,  String maintenanceFrequency)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleForProductModel() when $default != null:
return $default(_that.makeName,_that.modelName,_that.yearRange,_that.engineInfo,_that.transmissionInfo,_that.compatibilityCode,_that.marketPopularity,_that.partsAvailability,_that.maintenanceFrequency);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String makeName,  String modelName,  String yearRange,  String engineInfo,  String transmissionInfo,  String compatibilityCode,  int marketPopularity,  String partsAvailability,  String maintenanceFrequency)  $default,) {final _that = this;
switch (_that) {
case _VehicleForProductModel():
return $default(_that.makeName,_that.modelName,_that.yearRange,_that.engineInfo,_that.transmissionInfo,_that.compatibilityCode,_that.marketPopularity,_that.partsAvailability,_that.maintenanceFrequency);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String makeName,  String modelName,  String yearRange,  String engineInfo,  String transmissionInfo,  String compatibilityCode,  int marketPopularity,  String partsAvailability,  String maintenanceFrequency)?  $default,) {final _that = this;
switch (_that) {
case _VehicleForProductModel() when $default != null:
return $default(_that.makeName,_that.modelName,_that.yearRange,_that.engineInfo,_that.transmissionInfo,_that.compatibilityCode,_that.marketPopularity,_that.partsAvailability,_that.maintenanceFrequency);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleForProductModel implements VehicleForProductModel {
  const _VehicleForProductModel({required this.makeName, required this.modelName, required this.yearRange, required this.engineInfo, required this.transmissionInfo, required this.compatibilityCode, required this.marketPopularity, required this.partsAvailability, required this.maintenanceFrequency});
  factory _VehicleForProductModel.fromJson(Map<String, dynamic> json) => _$VehicleForProductModelFromJson(json);

@override final  String makeName;
@override final  String modelName;
@override final  String yearRange;
@override final  String engineInfo;
@override final  String transmissionInfo;
@override final  String compatibilityCode;
@override final  int marketPopularity;
@override final  String partsAvailability;
@override final  String maintenanceFrequency;

/// Create a copy of VehicleForProductModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleForProductModelCopyWith<_VehicleForProductModel> get copyWith => __$VehicleForProductModelCopyWithImpl<_VehicleForProductModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleForProductModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleForProductModel&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.yearRange, yearRange) || other.yearRange == yearRange)&&(identical(other.engineInfo, engineInfo) || other.engineInfo == engineInfo)&&(identical(other.transmissionInfo, transmissionInfo) || other.transmissionInfo == transmissionInfo)&&(identical(other.compatibilityCode, compatibilityCode) || other.compatibilityCode == compatibilityCode)&&(identical(other.marketPopularity, marketPopularity) || other.marketPopularity == marketPopularity)&&(identical(other.partsAvailability, partsAvailability) || other.partsAvailability == partsAvailability)&&(identical(other.maintenanceFrequency, maintenanceFrequency) || other.maintenanceFrequency == maintenanceFrequency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,makeName,modelName,yearRange,engineInfo,transmissionInfo,compatibilityCode,marketPopularity,partsAvailability,maintenanceFrequency);

@override
String toString() {
  return 'VehicleForProductModel(makeName: $makeName, modelName: $modelName, yearRange: $yearRange, engineInfo: $engineInfo, transmissionInfo: $transmissionInfo, compatibilityCode: $compatibilityCode, marketPopularity: $marketPopularity, partsAvailability: $partsAvailability, maintenanceFrequency: $maintenanceFrequency)';
}


}

/// @nodoc
abstract mixin class _$VehicleForProductModelCopyWith<$Res> implements $VehicleForProductModelCopyWith<$Res> {
  factory _$VehicleForProductModelCopyWith(_VehicleForProductModel value, $Res Function(_VehicleForProductModel) _then) = __$VehicleForProductModelCopyWithImpl;
@override @useResult
$Res call({
 String makeName, String modelName, String yearRange, String engineInfo, String transmissionInfo, String compatibilityCode, int marketPopularity, String partsAvailability, String maintenanceFrequency
});




}
/// @nodoc
class __$VehicleForProductModelCopyWithImpl<$Res>
    implements _$VehicleForProductModelCopyWith<$Res> {
  __$VehicleForProductModelCopyWithImpl(this._self, this._then);

  final _VehicleForProductModel _self;
  final $Res Function(_VehicleForProductModel) _then;

/// Create a copy of VehicleForProductModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? makeName = null,Object? modelName = null,Object? yearRange = null,Object? engineInfo = null,Object? transmissionInfo = null,Object? compatibilityCode = null,Object? marketPopularity = null,Object? partsAvailability = null,Object? maintenanceFrequency = null,}) {
  return _then(_VehicleForProductModel(
makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,yearRange: null == yearRange ? _self.yearRange : yearRange // ignore: cast_nullable_to_non_nullable
as String,engineInfo: null == engineInfo ? _self.engineInfo : engineInfo // ignore: cast_nullable_to_non_nullable
as String,transmissionInfo: null == transmissionInfo ? _self.transmissionInfo : transmissionInfo // ignore: cast_nullable_to_non_nullable
as String,compatibilityCode: null == compatibilityCode ? _self.compatibilityCode : compatibilityCode // ignore: cast_nullable_to_non_nullable
as String,marketPopularity: null == marketPopularity ? _self.marketPopularity : marketPopularity // ignore: cast_nullable_to_non_nullable
as int,partsAvailability: null == partsAvailability ? _self.partsAvailability : partsAvailability // ignore: cast_nullable_to_non_nullable
as String,maintenanceFrequency: null == maintenanceFrequency ? _self.maintenanceFrequency : maintenanceFrequency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$VehicleSearchStepModel {

 int get makeId; String get makeName; String get makeNameAr; int get modelsCount; int get yearsCount;
/// Create a copy of VehicleSearchStepModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleSearchStepModelCopyWith<VehicleSearchStepModel> get copyWith => _$VehicleSearchStepModelCopyWithImpl<VehicleSearchStepModel>(this as VehicleSearchStepModel, _$identity);

  /// Serializes this VehicleSearchStepModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleSearchStepModel&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.makeNameAr, makeNameAr) || other.makeNameAr == makeNameAr)&&(identical(other.modelsCount, modelsCount) || other.modelsCount == modelsCount)&&(identical(other.yearsCount, yearsCount) || other.yearsCount == yearsCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,makeId,makeName,makeNameAr,modelsCount,yearsCount);

@override
String toString() {
  return 'VehicleSearchStepModel(makeId: $makeId, makeName: $makeName, makeNameAr: $makeNameAr, modelsCount: $modelsCount, yearsCount: $yearsCount)';
}


}

/// @nodoc
abstract mixin class $VehicleSearchStepModelCopyWith<$Res>  {
  factory $VehicleSearchStepModelCopyWith(VehicleSearchStepModel value, $Res Function(VehicleSearchStepModel) _then) = _$VehicleSearchStepModelCopyWithImpl;
@useResult
$Res call({
 int makeId, String makeName, String makeNameAr, int modelsCount, int yearsCount
});




}
/// @nodoc
class _$VehicleSearchStepModelCopyWithImpl<$Res>
    implements $VehicleSearchStepModelCopyWith<$Res> {
  _$VehicleSearchStepModelCopyWithImpl(this._self, this._then);

  final VehicleSearchStepModel _self;
  final $Res Function(VehicleSearchStepModel) _then;

/// Create a copy of VehicleSearchStepModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? makeId = null,Object? makeName = null,Object? makeNameAr = null,Object? modelsCount = null,Object? yearsCount = null,}) {
  return _then(_self.copyWith(
makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as int,makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,makeNameAr: null == makeNameAr ? _self.makeNameAr : makeNameAr // ignore: cast_nullable_to_non_nullable
as String,modelsCount: null == modelsCount ? _self.modelsCount : modelsCount // ignore: cast_nullable_to_non_nullable
as int,yearsCount: null == yearsCount ? _self.yearsCount : yearsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleSearchStepModel].
extension VehicleSearchStepModelPatterns on VehicleSearchStepModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleSearchStepModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleSearchStepModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleSearchStepModel value)  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchStepModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleSearchStepModel value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchStepModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int makeId,  String makeName,  String makeNameAr,  int modelsCount,  int yearsCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleSearchStepModel() when $default != null:
return $default(_that.makeId,_that.makeName,_that.makeNameAr,_that.modelsCount,_that.yearsCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int makeId,  String makeName,  String makeNameAr,  int modelsCount,  int yearsCount)  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchStepModel():
return $default(_that.makeId,_that.makeName,_that.makeNameAr,_that.modelsCount,_that.yearsCount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int makeId,  String makeName,  String makeNameAr,  int modelsCount,  int yearsCount)?  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchStepModel() when $default != null:
return $default(_that.makeId,_that.makeName,_that.makeNameAr,_that.modelsCount,_that.yearsCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleSearchStepModel implements VehicleSearchStepModel {
  const _VehicleSearchStepModel({required this.makeId, required this.makeName, required this.makeNameAr, required this.modelsCount, required this.yearsCount});
  factory _VehicleSearchStepModel.fromJson(Map<String, dynamic> json) => _$VehicleSearchStepModelFromJson(json);

@override final  int makeId;
@override final  String makeName;
@override final  String makeNameAr;
@override final  int modelsCount;
@override final  int yearsCount;

/// Create a copy of VehicleSearchStepModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleSearchStepModelCopyWith<_VehicleSearchStepModel> get copyWith => __$VehicleSearchStepModelCopyWithImpl<_VehicleSearchStepModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleSearchStepModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleSearchStepModel&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.makeNameAr, makeNameAr) || other.makeNameAr == makeNameAr)&&(identical(other.modelsCount, modelsCount) || other.modelsCount == modelsCount)&&(identical(other.yearsCount, yearsCount) || other.yearsCount == yearsCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,makeId,makeName,makeNameAr,modelsCount,yearsCount);

@override
String toString() {
  return 'VehicleSearchStepModel(makeId: $makeId, makeName: $makeName, makeNameAr: $makeNameAr, modelsCount: $modelsCount, yearsCount: $yearsCount)';
}


}

/// @nodoc
abstract mixin class _$VehicleSearchStepModelCopyWith<$Res> implements $VehicleSearchStepModelCopyWith<$Res> {
  factory _$VehicleSearchStepModelCopyWith(_VehicleSearchStepModel value, $Res Function(_VehicleSearchStepModel) _then) = __$VehicleSearchStepModelCopyWithImpl;
@override @useResult
$Res call({
 int makeId, String makeName, String makeNameAr, int modelsCount, int yearsCount
});




}
/// @nodoc
class __$VehicleSearchStepModelCopyWithImpl<$Res>
    implements _$VehicleSearchStepModelCopyWith<$Res> {
  __$VehicleSearchStepModelCopyWithImpl(this._self, this._then);

  final _VehicleSearchStepModel _self;
  final $Res Function(_VehicleSearchStepModel) _then;

/// Create a copy of VehicleSearchStepModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? makeId = null,Object? makeName = null,Object? makeNameAr = null,Object? modelsCount = null,Object? yearsCount = null,}) {
  return _then(_VehicleSearchStepModel(
makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as int,makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,makeNameAr: null == makeNameAr ? _self.makeNameAr : makeNameAr // ignore: cast_nullable_to_non_nullable
as String,modelsCount: null == modelsCount ? _self.modelsCount : modelsCount // ignore: cast_nullable_to_non_nullable
as int,yearsCount: null == yearsCount ? _self.yearsCount : yearsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$ModelsByMakeModel {

 int get modelId; String get modelName; String get modelNameAr; String get vehicleType; int get yearsCount; int get yearFrom; int get yearTo; bool get hasSpecifications;
/// Create a copy of ModelsByMakeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ModelsByMakeModelCopyWith<ModelsByMakeModel> get copyWith => _$ModelsByMakeModelCopyWithImpl<ModelsByMakeModel>(this as ModelsByMakeModel, _$identity);

  /// Serializes this ModelsByMakeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModelsByMakeModel&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.modelNameAr, modelNameAr) || other.modelNameAr == modelNameAr)&&(identical(other.vehicleType, vehicleType) || other.vehicleType == vehicleType)&&(identical(other.yearsCount, yearsCount) || other.yearsCount == yearsCount)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.hasSpecifications, hasSpecifications) || other.hasSpecifications == hasSpecifications));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,modelId,modelName,modelNameAr,vehicleType,yearsCount,yearFrom,yearTo,hasSpecifications);

@override
String toString() {
  return 'ModelsByMakeModel(modelId: $modelId, modelName: $modelName, modelNameAr: $modelNameAr, vehicleType: $vehicleType, yearsCount: $yearsCount, yearFrom: $yearFrom, yearTo: $yearTo, hasSpecifications: $hasSpecifications)';
}


}

/// @nodoc
abstract mixin class $ModelsByMakeModelCopyWith<$Res>  {
  factory $ModelsByMakeModelCopyWith(ModelsByMakeModel value, $Res Function(ModelsByMakeModel) _then) = _$ModelsByMakeModelCopyWithImpl;
@useResult
$Res call({
 int modelId, String modelName, String modelNameAr, String vehicleType, int yearsCount, int yearFrom, int yearTo, bool hasSpecifications
});




}
/// @nodoc
class _$ModelsByMakeModelCopyWithImpl<$Res>
    implements $ModelsByMakeModelCopyWith<$Res> {
  _$ModelsByMakeModelCopyWithImpl(this._self, this._then);

  final ModelsByMakeModel _self;
  final $Res Function(ModelsByMakeModel) _then;

/// Create a copy of ModelsByMakeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? modelId = null,Object? modelName = null,Object? modelNameAr = null,Object? vehicleType = null,Object? yearsCount = null,Object? yearFrom = null,Object? yearTo = null,Object? hasSpecifications = null,}) {
  return _then(_self.copyWith(
modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,modelNameAr: null == modelNameAr ? _self.modelNameAr : modelNameAr // ignore: cast_nullable_to_non_nullable
as String,vehicleType: null == vehicleType ? _self.vehicleType : vehicleType // ignore: cast_nullable_to_non_nullable
as String,yearsCount: null == yearsCount ? _self.yearsCount : yearsCount // ignore: cast_nullable_to_non_nullable
as int,yearFrom: null == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int,yearTo: null == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int,hasSpecifications: null == hasSpecifications ? _self.hasSpecifications : hasSpecifications // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ModelsByMakeModel].
extension ModelsByMakeModelPatterns on ModelsByMakeModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ModelsByMakeModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ModelsByMakeModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ModelsByMakeModel value)  $default,){
final _that = this;
switch (_that) {
case _ModelsByMakeModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ModelsByMakeModel value)?  $default,){
final _that = this;
switch (_that) {
case _ModelsByMakeModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int modelId,  String modelName,  String modelNameAr,  String vehicleType,  int yearsCount,  int yearFrom,  int yearTo,  bool hasSpecifications)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ModelsByMakeModel() when $default != null:
return $default(_that.modelId,_that.modelName,_that.modelNameAr,_that.vehicleType,_that.yearsCount,_that.yearFrom,_that.yearTo,_that.hasSpecifications);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int modelId,  String modelName,  String modelNameAr,  String vehicleType,  int yearsCount,  int yearFrom,  int yearTo,  bool hasSpecifications)  $default,) {final _that = this;
switch (_that) {
case _ModelsByMakeModel():
return $default(_that.modelId,_that.modelName,_that.modelNameAr,_that.vehicleType,_that.yearsCount,_that.yearFrom,_that.yearTo,_that.hasSpecifications);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int modelId,  String modelName,  String modelNameAr,  String vehicleType,  int yearsCount,  int yearFrom,  int yearTo,  bool hasSpecifications)?  $default,) {final _that = this;
switch (_that) {
case _ModelsByMakeModel() when $default != null:
return $default(_that.modelId,_that.modelName,_that.modelNameAr,_that.vehicleType,_that.yearsCount,_that.yearFrom,_that.yearTo,_that.hasSpecifications);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ModelsByMakeModel implements ModelsByMakeModel {
  const _ModelsByMakeModel({required this.modelId, required this.modelName, required this.modelNameAr, required this.vehicleType, required this.yearsCount, required this.yearFrom, required this.yearTo, required this.hasSpecifications});
  factory _ModelsByMakeModel.fromJson(Map<String, dynamic> json) => _$ModelsByMakeModelFromJson(json);

@override final  int modelId;
@override final  String modelName;
@override final  String modelNameAr;
@override final  String vehicleType;
@override final  int yearsCount;
@override final  int yearFrom;
@override final  int yearTo;
@override final  bool hasSpecifications;

/// Create a copy of ModelsByMakeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModelsByMakeModelCopyWith<_ModelsByMakeModel> get copyWith => __$ModelsByMakeModelCopyWithImpl<_ModelsByMakeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ModelsByMakeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModelsByMakeModel&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.modelNameAr, modelNameAr) || other.modelNameAr == modelNameAr)&&(identical(other.vehicleType, vehicleType) || other.vehicleType == vehicleType)&&(identical(other.yearsCount, yearsCount) || other.yearsCount == yearsCount)&&(identical(other.yearFrom, yearFrom) || other.yearFrom == yearFrom)&&(identical(other.yearTo, yearTo) || other.yearTo == yearTo)&&(identical(other.hasSpecifications, hasSpecifications) || other.hasSpecifications == hasSpecifications));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,modelId,modelName,modelNameAr,vehicleType,yearsCount,yearFrom,yearTo,hasSpecifications);

@override
String toString() {
  return 'ModelsByMakeModel(modelId: $modelId, modelName: $modelName, modelNameAr: $modelNameAr, vehicleType: $vehicleType, yearsCount: $yearsCount, yearFrom: $yearFrom, yearTo: $yearTo, hasSpecifications: $hasSpecifications)';
}


}

/// @nodoc
abstract mixin class _$ModelsByMakeModelCopyWith<$Res> implements $ModelsByMakeModelCopyWith<$Res> {
  factory _$ModelsByMakeModelCopyWith(_ModelsByMakeModel value, $Res Function(_ModelsByMakeModel) _then) = __$ModelsByMakeModelCopyWithImpl;
@override @useResult
$Res call({
 int modelId, String modelName, String modelNameAr, String vehicleType, int yearsCount, int yearFrom, int yearTo, bool hasSpecifications
});




}
/// @nodoc
class __$ModelsByMakeModelCopyWithImpl<$Res>
    implements _$ModelsByMakeModelCopyWith<$Res> {
  __$ModelsByMakeModelCopyWithImpl(this._self, this._then);

  final _ModelsByMakeModel _self;
  final $Res Function(_ModelsByMakeModel) _then;

/// Create a copy of ModelsByMakeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? modelId = null,Object? modelName = null,Object? modelNameAr = null,Object? vehicleType = null,Object? yearsCount = null,Object? yearFrom = null,Object? yearTo = null,Object? hasSpecifications = null,}) {
  return _then(_ModelsByMakeModel(
modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,modelNameAr: null == modelNameAr ? _self.modelNameAr : modelNameAr // ignore: cast_nullable_to_non_nullable
as String,vehicleType: null == vehicleType ? _self.vehicleType : vehicleType // ignore: cast_nullable_to_non_nullable
as String,yearsCount: null == yearsCount ? _self.yearsCount : yearsCount // ignore: cast_nullable_to_non_nullable
as int,yearFrom: null == yearFrom ? _self.yearFrom : yearFrom // ignore: cast_nullable_to_non_nullable
as int,yearTo: null == yearTo ? _self.yearTo : yearTo // ignore: cast_nullable_to_non_nullable
as int,hasSpecifications: null == hasSpecifications ? _self.hasSpecifications : hasSpecifications // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$YearsByModelModel {

 int get year; bool get isPopular; bool get hasSpecifications; String get generationInfo;
/// Create a copy of YearsByModelModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$YearsByModelModelCopyWith<YearsByModelModel> get copyWith => _$YearsByModelModelCopyWithImpl<YearsByModelModel>(this as YearsByModelModel, _$identity);

  /// Serializes this YearsByModelModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is YearsByModelModel&&(identical(other.year, year) || other.year == year)&&(identical(other.isPopular, isPopular) || other.isPopular == isPopular)&&(identical(other.hasSpecifications, hasSpecifications) || other.hasSpecifications == hasSpecifications)&&(identical(other.generationInfo, generationInfo) || other.generationInfo == generationInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,year,isPopular,hasSpecifications,generationInfo);

@override
String toString() {
  return 'YearsByModelModel(year: $year, isPopular: $isPopular, hasSpecifications: $hasSpecifications, generationInfo: $generationInfo)';
}


}

/// @nodoc
abstract mixin class $YearsByModelModelCopyWith<$Res>  {
  factory $YearsByModelModelCopyWith(YearsByModelModel value, $Res Function(YearsByModelModel) _then) = _$YearsByModelModelCopyWithImpl;
@useResult
$Res call({
 int year, bool isPopular, bool hasSpecifications, String generationInfo
});




}
/// @nodoc
class _$YearsByModelModelCopyWithImpl<$Res>
    implements $YearsByModelModelCopyWith<$Res> {
  _$YearsByModelModelCopyWithImpl(this._self, this._then);

  final YearsByModelModel _self;
  final $Res Function(YearsByModelModel) _then;

/// Create a copy of YearsByModelModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? year = null,Object? isPopular = null,Object? hasSpecifications = null,Object? generationInfo = null,}) {
  return _then(_self.copyWith(
year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,isPopular: null == isPopular ? _self.isPopular : isPopular // ignore: cast_nullable_to_non_nullable
as bool,hasSpecifications: null == hasSpecifications ? _self.hasSpecifications : hasSpecifications // ignore: cast_nullable_to_non_nullable
as bool,generationInfo: null == generationInfo ? _self.generationInfo : generationInfo // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [YearsByModelModel].
extension YearsByModelModelPatterns on YearsByModelModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _YearsByModelModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _YearsByModelModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _YearsByModelModel value)  $default,){
final _that = this;
switch (_that) {
case _YearsByModelModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _YearsByModelModel value)?  $default,){
final _that = this;
switch (_that) {
case _YearsByModelModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int year,  bool isPopular,  bool hasSpecifications,  String generationInfo)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _YearsByModelModel() when $default != null:
return $default(_that.year,_that.isPopular,_that.hasSpecifications,_that.generationInfo);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int year,  bool isPopular,  bool hasSpecifications,  String generationInfo)  $default,) {final _that = this;
switch (_that) {
case _YearsByModelModel():
return $default(_that.year,_that.isPopular,_that.hasSpecifications,_that.generationInfo);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int year,  bool isPopular,  bool hasSpecifications,  String generationInfo)?  $default,) {final _that = this;
switch (_that) {
case _YearsByModelModel() when $default != null:
return $default(_that.year,_that.isPopular,_that.hasSpecifications,_that.generationInfo);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _YearsByModelModel implements YearsByModelModel {
  const _YearsByModelModel({required this.year, required this.isPopular, required this.hasSpecifications, required this.generationInfo});
  factory _YearsByModelModel.fromJson(Map<String, dynamic> json) => _$YearsByModelModelFromJson(json);

@override final  int year;
@override final  bool isPopular;
@override final  bool hasSpecifications;
@override final  String generationInfo;

/// Create a copy of YearsByModelModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$YearsByModelModelCopyWith<_YearsByModelModel> get copyWith => __$YearsByModelModelCopyWithImpl<_YearsByModelModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$YearsByModelModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _YearsByModelModel&&(identical(other.year, year) || other.year == year)&&(identical(other.isPopular, isPopular) || other.isPopular == isPopular)&&(identical(other.hasSpecifications, hasSpecifications) || other.hasSpecifications == hasSpecifications)&&(identical(other.generationInfo, generationInfo) || other.generationInfo == generationInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,year,isPopular,hasSpecifications,generationInfo);

@override
String toString() {
  return 'YearsByModelModel(year: $year, isPopular: $isPopular, hasSpecifications: $hasSpecifications, generationInfo: $generationInfo)';
}


}

/// @nodoc
abstract mixin class _$YearsByModelModelCopyWith<$Res> implements $YearsByModelModelCopyWith<$Res> {
  factory _$YearsByModelModelCopyWith(_YearsByModelModel value, $Res Function(_YearsByModelModel) _then) = __$YearsByModelModelCopyWithImpl;
@override @useResult
$Res call({
 int year, bool isPopular, bool hasSpecifications, String generationInfo
});




}
/// @nodoc
class __$YearsByModelModelCopyWithImpl<$Res>
    implements _$YearsByModelModelCopyWith<$Res> {
  __$YearsByModelModelCopyWithImpl(this._self, this._then);

  final _YearsByModelModel _self;
  final $Res Function(_YearsByModelModel) _then;

/// Create a copy of YearsByModelModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? year = null,Object? isPopular = null,Object? hasSpecifications = null,Object? generationInfo = null,}) {
  return _then(_YearsByModelModel(
year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,isPopular: null == isPopular ? _self.isPopular : isPopular // ignore: cast_nullable_to_non_nullable
as bool,hasSpecifications: null == hasSpecifications ? _self.hasSpecifications : hasSpecifications // ignore: cast_nullable_to_non_nullable
as bool,generationInfo: null == generationInfo ? _self.generationInfo : generationInfo // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$VehicleSearchStateData {

 VehicleSearchStepModel? get selectedMake; ModelsByMakeModel? get selectedModel; YearsByModelModel? get selectedYear; int get currentStep;
/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleSearchStateDataCopyWith<VehicleSearchStateData> get copyWith => _$VehicleSearchStateDataCopyWithImpl<VehicleSearchStateData>(this as VehicleSearchStateData, _$identity);

  /// Serializes this VehicleSearchStateData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleSearchStateData&&(identical(other.selectedMake, selectedMake) || other.selectedMake == selectedMake)&&(identical(other.selectedModel, selectedModel) || other.selectedModel == selectedModel)&&(identical(other.selectedYear, selectedYear) || other.selectedYear == selectedYear)&&(identical(other.currentStep, currentStep) || other.currentStep == currentStep));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,selectedMake,selectedModel,selectedYear,currentStep);

@override
String toString() {
  return 'VehicleSearchStateData(selectedMake: $selectedMake, selectedModel: $selectedModel, selectedYear: $selectedYear, currentStep: $currentStep)';
}


}

/// @nodoc
abstract mixin class $VehicleSearchStateDataCopyWith<$Res>  {
  factory $VehicleSearchStateDataCopyWith(VehicleSearchStateData value, $Res Function(VehicleSearchStateData) _then) = _$VehicleSearchStateDataCopyWithImpl;
@useResult
$Res call({
 VehicleSearchStepModel? selectedMake, ModelsByMakeModel? selectedModel, YearsByModelModel? selectedYear, int currentStep
});


$VehicleSearchStepModelCopyWith<$Res>? get selectedMake;$ModelsByMakeModelCopyWith<$Res>? get selectedModel;$YearsByModelModelCopyWith<$Res>? get selectedYear;

}
/// @nodoc
class _$VehicleSearchStateDataCopyWithImpl<$Res>
    implements $VehicleSearchStateDataCopyWith<$Res> {
  _$VehicleSearchStateDataCopyWithImpl(this._self, this._then);

  final VehicleSearchStateData _self;
  final $Res Function(VehicleSearchStateData) _then;

/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? selectedMake = freezed,Object? selectedModel = freezed,Object? selectedYear = freezed,Object? currentStep = null,}) {
  return _then(_self.copyWith(
selectedMake: freezed == selectedMake ? _self.selectedMake : selectedMake // ignore: cast_nullable_to_non_nullable
as VehicleSearchStepModel?,selectedModel: freezed == selectedModel ? _self.selectedModel : selectedModel // ignore: cast_nullable_to_non_nullable
as ModelsByMakeModel?,selectedYear: freezed == selectedYear ? _self.selectedYear : selectedYear // ignore: cast_nullable_to_non_nullable
as YearsByModelModel?,currentStep: null == currentStep ? _self.currentStep : currentStep // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleSearchStepModelCopyWith<$Res>? get selectedMake {
    if (_self.selectedMake == null) {
    return null;
  }

  return $VehicleSearchStepModelCopyWith<$Res>(_self.selectedMake!, (value) {
    return _then(_self.copyWith(selectedMake: value));
  });
}/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ModelsByMakeModelCopyWith<$Res>? get selectedModel {
    if (_self.selectedModel == null) {
    return null;
  }

  return $ModelsByMakeModelCopyWith<$Res>(_self.selectedModel!, (value) {
    return _then(_self.copyWith(selectedModel: value));
  });
}/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$YearsByModelModelCopyWith<$Res>? get selectedYear {
    if (_self.selectedYear == null) {
    return null;
  }

  return $YearsByModelModelCopyWith<$Res>(_self.selectedYear!, (value) {
    return _then(_self.copyWith(selectedYear: value));
  });
}
}


/// Adds pattern-matching-related methods to [VehicleSearchStateData].
extension VehicleSearchStateDataPatterns on VehicleSearchStateData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleSearchStateData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleSearchStateData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleSearchStateData value)  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchStateData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleSearchStateData value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleSearchStateData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( VehicleSearchStepModel? selectedMake,  ModelsByMakeModel? selectedModel,  YearsByModelModel? selectedYear,  int currentStep)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleSearchStateData() when $default != null:
return $default(_that.selectedMake,_that.selectedModel,_that.selectedYear,_that.currentStep);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( VehicleSearchStepModel? selectedMake,  ModelsByMakeModel? selectedModel,  YearsByModelModel? selectedYear,  int currentStep)  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchStateData():
return $default(_that.selectedMake,_that.selectedModel,_that.selectedYear,_that.currentStep);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( VehicleSearchStepModel? selectedMake,  ModelsByMakeModel? selectedModel,  YearsByModelModel? selectedYear,  int currentStep)?  $default,) {final _that = this;
switch (_that) {
case _VehicleSearchStateData() when $default != null:
return $default(_that.selectedMake,_that.selectedModel,_that.selectedYear,_that.currentStep);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleSearchStateData implements VehicleSearchStateData {
  const _VehicleSearchStateData({this.selectedMake, this.selectedModel, this.selectedYear, this.currentStep = 1});
  factory _VehicleSearchStateData.fromJson(Map<String, dynamic> json) => _$VehicleSearchStateDataFromJson(json);

@override final  VehicleSearchStepModel? selectedMake;
@override final  ModelsByMakeModel? selectedModel;
@override final  YearsByModelModel? selectedYear;
@override@JsonKey() final  int currentStep;

/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleSearchStateDataCopyWith<_VehicleSearchStateData> get copyWith => __$VehicleSearchStateDataCopyWithImpl<_VehicleSearchStateData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleSearchStateDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleSearchStateData&&(identical(other.selectedMake, selectedMake) || other.selectedMake == selectedMake)&&(identical(other.selectedModel, selectedModel) || other.selectedModel == selectedModel)&&(identical(other.selectedYear, selectedYear) || other.selectedYear == selectedYear)&&(identical(other.currentStep, currentStep) || other.currentStep == currentStep));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,selectedMake,selectedModel,selectedYear,currentStep);

@override
String toString() {
  return 'VehicleSearchStateData(selectedMake: $selectedMake, selectedModel: $selectedModel, selectedYear: $selectedYear, currentStep: $currentStep)';
}


}

/// @nodoc
abstract mixin class _$VehicleSearchStateDataCopyWith<$Res> implements $VehicleSearchStateDataCopyWith<$Res> {
  factory _$VehicleSearchStateDataCopyWith(_VehicleSearchStateData value, $Res Function(_VehicleSearchStateData) _then) = __$VehicleSearchStateDataCopyWithImpl;
@override @useResult
$Res call({
 VehicleSearchStepModel? selectedMake, ModelsByMakeModel? selectedModel, YearsByModelModel? selectedYear, int currentStep
});


@override $VehicleSearchStepModelCopyWith<$Res>? get selectedMake;@override $ModelsByMakeModelCopyWith<$Res>? get selectedModel;@override $YearsByModelModelCopyWith<$Res>? get selectedYear;

}
/// @nodoc
class __$VehicleSearchStateDataCopyWithImpl<$Res>
    implements _$VehicleSearchStateDataCopyWith<$Res> {
  __$VehicleSearchStateDataCopyWithImpl(this._self, this._then);

  final _VehicleSearchStateData _self;
  final $Res Function(_VehicleSearchStateData) _then;

/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? selectedMake = freezed,Object? selectedModel = freezed,Object? selectedYear = freezed,Object? currentStep = null,}) {
  return _then(_VehicleSearchStateData(
selectedMake: freezed == selectedMake ? _self.selectedMake : selectedMake // ignore: cast_nullable_to_non_nullable
as VehicleSearchStepModel?,selectedModel: freezed == selectedModel ? _self.selectedModel : selectedModel // ignore: cast_nullable_to_non_nullable
as ModelsByMakeModel?,selectedYear: freezed == selectedYear ? _self.selectedYear : selectedYear // ignore: cast_nullable_to_non_nullable
as YearsByModelModel?,currentStep: null == currentStep ? _self.currentStep : currentStep // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleSearchStepModelCopyWith<$Res>? get selectedMake {
    if (_self.selectedMake == null) {
    return null;
  }

  return $VehicleSearchStepModelCopyWith<$Res>(_self.selectedMake!, (value) {
    return _then(_self.copyWith(selectedMake: value));
  });
}/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ModelsByMakeModelCopyWith<$Res>? get selectedModel {
    if (_self.selectedModel == null) {
    return null;
  }

  return $ModelsByMakeModelCopyWith<$Res>(_self.selectedModel!, (value) {
    return _then(_self.copyWith(selectedModel: value));
  });
}/// Create a copy of VehicleSearchStateData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$YearsByModelModelCopyWith<$Res>? get selectedYear {
    if (_self.selectedYear == null) {
    return null;
  }

  return $YearsByModelModelCopyWith<$Res>(_self.selectedYear!, (value) {
    return _then(_self.copyWith(selectedYear: value));
  });
}
}

// dart format on
