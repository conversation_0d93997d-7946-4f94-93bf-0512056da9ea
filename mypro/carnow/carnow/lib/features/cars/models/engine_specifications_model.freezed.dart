// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'engine_specifications_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EngineSpecificationsModel {

 String get id; String get name; String get type; double get displacement; int get horsepower; int get torque; String get fuelType; int get cylinders; String get configuration; String? get aspiration; double? get compressionRatio; String? get boreStroke; String? get valvetrain; String? get fuelSystem; String? get coolingSystem;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of EngineSpecificationsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EngineSpecificationsModelCopyWith<EngineSpecificationsModel> get copyWith => _$EngineSpecificationsModelCopyWithImpl<EngineSpecificationsModel>(this as EngineSpecificationsModel, _$identity);

  /// Serializes this EngineSpecificationsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EngineSpecificationsModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.displacement, displacement) || other.displacement == displacement)&&(identical(other.horsepower, horsepower) || other.horsepower == horsepower)&&(identical(other.torque, torque) || other.torque == torque)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.cylinders, cylinders) || other.cylinders == cylinders)&&(identical(other.configuration, configuration) || other.configuration == configuration)&&(identical(other.aspiration, aspiration) || other.aspiration == aspiration)&&(identical(other.compressionRatio, compressionRatio) || other.compressionRatio == compressionRatio)&&(identical(other.boreStroke, boreStroke) || other.boreStroke == boreStroke)&&(identical(other.valvetrain, valvetrain) || other.valvetrain == valvetrain)&&(identical(other.fuelSystem, fuelSystem) || other.fuelSystem == fuelSystem)&&(identical(other.coolingSystem, coolingSystem) || other.coolingSystem == coolingSystem)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,displacement,horsepower,torque,fuelType,cylinders,configuration,aspiration,compressionRatio,boreStroke,valvetrain,fuelSystem,coolingSystem,createdAt,updatedAt);

@override
String toString() {
  return 'EngineSpecificationsModel(id: $id, name: $name, type: $type, displacement: $displacement, horsepower: $horsepower, torque: $torque, fuelType: $fuelType, cylinders: $cylinders, configuration: $configuration, aspiration: $aspiration, compressionRatio: $compressionRatio, boreStroke: $boreStroke, valvetrain: $valvetrain, fuelSystem: $fuelSystem, coolingSystem: $coolingSystem, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $EngineSpecificationsModelCopyWith<$Res>  {
  factory $EngineSpecificationsModelCopyWith(EngineSpecificationsModel value, $Res Function(EngineSpecificationsModel) _then) = _$EngineSpecificationsModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String type, double displacement, int horsepower, int torque, String fuelType, int cylinders, String configuration, String? aspiration, double? compressionRatio, String? boreStroke, String? valvetrain, String? fuelSystem, String? coolingSystem,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$EngineSpecificationsModelCopyWithImpl<$Res>
    implements $EngineSpecificationsModelCopyWith<$Res> {
  _$EngineSpecificationsModelCopyWithImpl(this._self, this._then);

  final EngineSpecificationsModel _self;
  final $Res Function(EngineSpecificationsModel) _then;

/// Create a copy of EngineSpecificationsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? type = null,Object? displacement = null,Object? horsepower = null,Object? torque = null,Object? fuelType = null,Object? cylinders = null,Object? configuration = null,Object? aspiration = freezed,Object? compressionRatio = freezed,Object? boreStroke = freezed,Object? valvetrain = freezed,Object? fuelSystem = freezed,Object? coolingSystem = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,displacement: null == displacement ? _self.displacement : displacement // ignore: cast_nullable_to_non_nullable
as double,horsepower: null == horsepower ? _self.horsepower : horsepower // ignore: cast_nullable_to_non_nullable
as int,torque: null == torque ? _self.torque : torque // ignore: cast_nullable_to_non_nullable
as int,fuelType: null == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String,cylinders: null == cylinders ? _self.cylinders : cylinders // ignore: cast_nullable_to_non_nullable
as int,configuration: null == configuration ? _self.configuration : configuration // ignore: cast_nullable_to_non_nullable
as String,aspiration: freezed == aspiration ? _self.aspiration : aspiration // ignore: cast_nullable_to_non_nullable
as String?,compressionRatio: freezed == compressionRatio ? _self.compressionRatio : compressionRatio // ignore: cast_nullable_to_non_nullable
as double?,boreStroke: freezed == boreStroke ? _self.boreStroke : boreStroke // ignore: cast_nullable_to_non_nullable
as String?,valvetrain: freezed == valvetrain ? _self.valvetrain : valvetrain // ignore: cast_nullable_to_non_nullable
as String?,fuelSystem: freezed == fuelSystem ? _self.fuelSystem : fuelSystem // ignore: cast_nullable_to_non_nullable
as String?,coolingSystem: freezed == coolingSystem ? _self.coolingSystem : coolingSystem // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [EngineSpecificationsModel].
extension EngineSpecificationsModelPatterns on EngineSpecificationsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EngineSpecificationsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EngineSpecificationsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EngineSpecificationsModel value)  $default,){
final _that = this;
switch (_that) {
case _EngineSpecificationsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EngineSpecificationsModel value)?  $default,){
final _that = this;
switch (_that) {
case _EngineSpecificationsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String type,  double displacement,  int horsepower,  int torque,  String fuelType,  int cylinders,  String configuration,  String? aspiration,  double? compressionRatio,  String? boreStroke,  String? valvetrain,  String? fuelSystem,  String? coolingSystem, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EngineSpecificationsModel() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.displacement,_that.horsepower,_that.torque,_that.fuelType,_that.cylinders,_that.configuration,_that.aspiration,_that.compressionRatio,_that.boreStroke,_that.valvetrain,_that.fuelSystem,_that.coolingSystem,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String type,  double displacement,  int horsepower,  int torque,  String fuelType,  int cylinders,  String configuration,  String? aspiration,  double? compressionRatio,  String? boreStroke,  String? valvetrain,  String? fuelSystem,  String? coolingSystem, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _EngineSpecificationsModel():
return $default(_that.id,_that.name,_that.type,_that.displacement,_that.horsepower,_that.torque,_that.fuelType,_that.cylinders,_that.configuration,_that.aspiration,_that.compressionRatio,_that.boreStroke,_that.valvetrain,_that.fuelSystem,_that.coolingSystem,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String type,  double displacement,  int horsepower,  int torque,  String fuelType,  int cylinders,  String configuration,  String? aspiration,  double? compressionRatio,  String? boreStroke,  String? valvetrain,  String? fuelSystem,  String? coolingSystem, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _EngineSpecificationsModel() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.displacement,_that.horsepower,_that.torque,_that.fuelType,_that.cylinders,_that.configuration,_that.aspiration,_that.compressionRatio,_that.boreStroke,_that.valvetrain,_that.fuelSystem,_that.coolingSystem,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _EngineSpecificationsModel implements EngineSpecificationsModel {
  const _EngineSpecificationsModel({required this.id, required this.name, required this.type, required this.displacement, required this.horsepower, required this.torque, required this.fuelType, required this.cylinders, required this.configuration, this.aspiration, this.compressionRatio, this.boreStroke, this.valvetrain, this.fuelSystem, this.coolingSystem, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _EngineSpecificationsModel.fromJson(Map<String, dynamic> json) => _$EngineSpecificationsModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String type;
@override final  double displacement;
@override final  int horsepower;
@override final  int torque;
@override final  String fuelType;
@override final  int cylinders;
@override final  String configuration;
@override final  String? aspiration;
@override final  double? compressionRatio;
@override final  String? boreStroke;
@override final  String? valvetrain;
@override final  String? fuelSystem;
@override final  String? coolingSystem;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of EngineSpecificationsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EngineSpecificationsModelCopyWith<_EngineSpecificationsModel> get copyWith => __$EngineSpecificationsModelCopyWithImpl<_EngineSpecificationsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EngineSpecificationsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EngineSpecificationsModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.displacement, displacement) || other.displacement == displacement)&&(identical(other.horsepower, horsepower) || other.horsepower == horsepower)&&(identical(other.torque, torque) || other.torque == torque)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.cylinders, cylinders) || other.cylinders == cylinders)&&(identical(other.configuration, configuration) || other.configuration == configuration)&&(identical(other.aspiration, aspiration) || other.aspiration == aspiration)&&(identical(other.compressionRatio, compressionRatio) || other.compressionRatio == compressionRatio)&&(identical(other.boreStroke, boreStroke) || other.boreStroke == boreStroke)&&(identical(other.valvetrain, valvetrain) || other.valvetrain == valvetrain)&&(identical(other.fuelSystem, fuelSystem) || other.fuelSystem == fuelSystem)&&(identical(other.coolingSystem, coolingSystem) || other.coolingSystem == coolingSystem)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,displacement,horsepower,torque,fuelType,cylinders,configuration,aspiration,compressionRatio,boreStroke,valvetrain,fuelSystem,coolingSystem,createdAt,updatedAt);

@override
String toString() {
  return 'EngineSpecificationsModel(id: $id, name: $name, type: $type, displacement: $displacement, horsepower: $horsepower, torque: $torque, fuelType: $fuelType, cylinders: $cylinders, configuration: $configuration, aspiration: $aspiration, compressionRatio: $compressionRatio, boreStroke: $boreStroke, valvetrain: $valvetrain, fuelSystem: $fuelSystem, coolingSystem: $coolingSystem, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$EngineSpecificationsModelCopyWith<$Res> implements $EngineSpecificationsModelCopyWith<$Res> {
  factory _$EngineSpecificationsModelCopyWith(_EngineSpecificationsModel value, $Res Function(_EngineSpecificationsModel) _then) = __$EngineSpecificationsModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String type, double displacement, int horsepower, int torque, String fuelType, int cylinders, String configuration, String? aspiration, double? compressionRatio, String? boreStroke, String? valvetrain, String? fuelSystem, String? coolingSystem,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$EngineSpecificationsModelCopyWithImpl<$Res>
    implements _$EngineSpecificationsModelCopyWith<$Res> {
  __$EngineSpecificationsModelCopyWithImpl(this._self, this._then);

  final _EngineSpecificationsModel _self;
  final $Res Function(_EngineSpecificationsModel) _then;

/// Create a copy of EngineSpecificationsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? type = null,Object? displacement = null,Object? horsepower = null,Object? torque = null,Object? fuelType = null,Object? cylinders = null,Object? configuration = null,Object? aspiration = freezed,Object? compressionRatio = freezed,Object? boreStroke = freezed,Object? valvetrain = freezed,Object? fuelSystem = freezed,Object? coolingSystem = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_EngineSpecificationsModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,displacement: null == displacement ? _self.displacement : displacement // ignore: cast_nullable_to_non_nullable
as double,horsepower: null == horsepower ? _self.horsepower : horsepower // ignore: cast_nullable_to_non_nullable
as int,torque: null == torque ? _self.torque : torque // ignore: cast_nullable_to_non_nullable
as int,fuelType: null == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String,cylinders: null == cylinders ? _self.cylinders : cylinders // ignore: cast_nullable_to_non_nullable
as int,configuration: null == configuration ? _self.configuration : configuration // ignore: cast_nullable_to_non_nullable
as String,aspiration: freezed == aspiration ? _self.aspiration : aspiration // ignore: cast_nullable_to_non_nullable
as String?,compressionRatio: freezed == compressionRatio ? _self.compressionRatio : compressionRatio // ignore: cast_nullable_to_non_nullable
as double?,boreStroke: freezed == boreStroke ? _self.boreStroke : boreStroke // ignore: cast_nullable_to_non_nullable
as String?,valvetrain: freezed == valvetrain ? _self.valvetrain : valvetrain // ignore: cast_nullable_to_non_nullable
as String?,fuelSystem: freezed == fuelSystem ? _self.fuelSystem : fuelSystem // ignore: cast_nullable_to_non_nullable
as String?,coolingSystem: freezed == coolingSystem ? _self.coolingSystem : coolingSystem // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$EnginePerformanceModel {

 String get id; String get engineId; double get acceleration0To100; double get topSpeed; double get fuelConsumptionCity; double get fuelConsumptionHighway; double get fuelConsumptionCombined; double? get co2Emissions; String? get performanceNotes;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of EnginePerformanceModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EnginePerformanceModelCopyWith<EnginePerformanceModel> get copyWith => _$EnginePerformanceModelCopyWithImpl<EnginePerformanceModel>(this as EnginePerformanceModel, _$identity);

  /// Serializes this EnginePerformanceModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EnginePerformanceModel&&(identical(other.id, id) || other.id == id)&&(identical(other.engineId, engineId) || other.engineId == engineId)&&(identical(other.acceleration0To100, acceleration0To100) || other.acceleration0To100 == acceleration0To100)&&(identical(other.topSpeed, topSpeed) || other.topSpeed == topSpeed)&&(identical(other.fuelConsumptionCity, fuelConsumptionCity) || other.fuelConsumptionCity == fuelConsumptionCity)&&(identical(other.fuelConsumptionHighway, fuelConsumptionHighway) || other.fuelConsumptionHighway == fuelConsumptionHighway)&&(identical(other.fuelConsumptionCombined, fuelConsumptionCombined) || other.fuelConsumptionCombined == fuelConsumptionCombined)&&(identical(other.co2Emissions, co2Emissions) || other.co2Emissions == co2Emissions)&&(identical(other.performanceNotes, performanceNotes) || other.performanceNotes == performanceNotes)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,engineId,acceleration0To100,topSpeed,fuelConsumptionCity,fuelConsumptionHighway,fuelConsumptionCombined,co2Emissions,performanceNotes,createdAt,updatedAt);

@override
String toString() {
  return 'EnginePerformanceModel(id: $id, engineId: $engineId, acceleration0To100: $acceleration0To100, topSpeed: $topSpeed, fuelConsumptionCity: $fuelConsumptionCity, fuelConsumptionHighway: $fuelConsumptionHighway, fuelConsumptionCombined: $fuelConsumptionCombined, co2Emissions: $co2Emissions, performanceNotes: $performanceNotes, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $EnginePerformanceModelCopyWith<$Res>  {
  factory $EnginePerformanceModelCopyWith(EnginePerformanceModel value, $Res Function(EnginePerformanceModel) _then) = _$EnginePerformanceModelCopyWithImpl;
@useResult
$Res call({
 String id, String engineId, double acceleration0To100, double topSpeed, double fuelConsumptionCity, double fuelConsumptionHighway, double fuelConsumptionCombined, double? co2Emissions, String? performanceNotes,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$EnginePerformanceModelCopyWithImpl<$Res>
    implements $EnginePerformanceModelCopyWith<$Res> {
  _$EnginePerformanceModelCopyWithImpl(this._self, this._then);

  final EnginePerformanceModel _self;
  final $Res Function(EnginePerformanceModel) _then;

/// Create a copy of EnginePerformanceModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? engineId = null,Object? acceleration0To100 = null,Object? topSpeed = null,Object? fuelConsumptionCity = null,Object? fuelConsumptionHighway = null,Object? fuelConsumptionCombined = null,Object? co2Emissions = freezed,Object? performanceNotes = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,engineId: null == engineId ? _self.engineId : engineId // ignore: cast_nullable_to_non_nullable
as String,acceleration0To100: null == acceleration0To100 ? _self.acceleration0To100 : acceleration0To100 // ignore: cast_nullable_to_non_nullable
as double,topSpeed: null == topSpeed ? _self.topSpeed : topSpeed // ignore: cast_nullable_to_non_nullable
as double,fuelConsumptionCity: null == fuelConsumptionCity ? _self.fuelConsumptionCity : fuelConsumptionCity // ignore: cast_nullable_to_non_nullable
as double,fuelConsumptionHighway: null == fuelConsumptionHighway ? _self.fuelConsumptionHighway : fuelConsumptionHighway // ignore: cast_nullable_to_non_nullable
as double,fuelConsumptionCombined: null == fuelConsumptionCombined ? _self.fuelConsumptionCombined : fuelConsumptionCombined // ignore: cast_nullable_to_non_nullable
as double,co2Emissions: freezed == co2Emissions ? _self.co2Emissions : co2Emissions // ignore: cast_nullable_to_non_nullable
as double?,performanceNotes: freezed == performanceNotes ? _self.performanceNotes : performanceNotes // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [EnginePerformanceModel].
extension EnginePerformanceModelPatterns on EnginePerformanceModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EnginePerformanceModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EnginePerformanceModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EnginePerformanceModel value)  $default,){
final _that = this;
switch (_that) {
case _EnginePerformanceModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EnginePerformanceModel value)?  $default,){
final _that = this;
switch (_that) {
case _EnginePerformanceModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String engineId,  double acceleration0To100,  double topSpeed,  double fuelConsumptionCity,  double fuelConsumptionHighway,  double fuelConsumptionCombined,  double? co2Emissions,  String? performanceNotes, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EnginePerformanceModel() when $default != null:
return $default(_that.id,_that.engineId,_that.acceleration0To100,_that.topSpeed,_that.fuelConsumptionCity,_that.fuelConsumptionHighway,_that.fuelConsumptionCombined,_that.co2Emissions,_that.performanceNotes,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String engineId,  double acceleration0To100,  double topSpeed,  double fuelConsumptionCity,  double fuelConsumptionHighway,  double fuelConsumptionCombined,  double? co2Emissions,  String? performanceNotes, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _EnginePerformanceModel():
return $default(_that.id,_that.engineId,_that.acceleration0To100,_that.topSpeed,_that.fuelConsumptionCity,_that.fuelConsumptionHighway,_that.fuelConsumptionCombined,_that.co2Emissions,_that.performanceNotes,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String engineId,  double acceleration0To100,  double topSpeed,  double fuelConsumptionCity,  double fuelConsumptionHighway,  double fuelConsumptionCombined,  double? co2Emissions,  String? performanceNotes, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _EnginePerformanceModel() when $default != null:
return $default(_that.id,_that.engineId,_that.acceleration0To100,_that.topSpeed,_that.fuelConsumptionCity,_that.fuelConsumptionHighway,_that.fuelConsumptionCombined,_that.co2Emissions,_that.performanceNotes,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _EnginePerformanceModel implements EnginePerformanceModel {
  const _EnginePerformanceModel({required this.id, required this.engineId, required this.acceleration0To100, required this.topSpeed, required this.fuelConsumptionCity, required this.fuelConsumptionHighway, required this.fuelConsumptionCombined, this.co2Emissions, this.performanceNotes, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _EnginePerformanceModel.fromJson(Map<String, dynamic> json) => _$EnginePerformanceModelFromJson(json);

@override final  String id;
@override final  String engineId;
@override final  double acceleration0To100;
@override final  double topSpeed;
@override final  double fuelConsumptionCity;
@override final  double fuelConsumptionHighway;
@override final  double fuelConsumptionCombined;
@override final  double? co2Emissions;
@override final  String? performanceNotes;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of EnginePerformanceModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EnginePerformanceModelCopyWith<_EnginePerformanceModel> get copyWith => __$EnginePerformanceModelCopyWithImpl<_EnginePerformanceModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EnginePerformanceModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EnginePerformanceModel&&(identical(other.id, id) || other.id == id)&&(identical(other.engineId, engineId) || other.engineId == engineId)&&(identical(other.acceleration0To100, acceleration0To100) || other.acceleration0To100 == acceleration0To100)&&(identical(other.topSpeed, topSpeed) || other.topSpeed == topSpeed)&&(identical(other.fuelConsumptionCity, fuelConsumptionCity) || other.fuelConsumptionCity == fuelConsumptionCity)&&(identical(other.fuelConsumptionHighway, fuelConsumptionHighway) || other.fuelConsumptionHighway == fuelConsumptionHighway)&&(identical(other.fuelConsumptionCombined, fuelConsumptionCombined) || other.fuelConsumptionCombined == fuelConsumptionCombined)&&(identical(other.co2Emissions, co2Emissions) || other.co2Emissions == co2Emissions)&&(identical(other.performanceNotes, performanceNotes) || other.performanceNotes == performanceNotes)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,engineId,acceleration0To100,topSpeed,fuelConsumptionCity,fuelConsumptionHighway,fuelConsumptionCombined,co2Emissions,performanceNotes,createdAt,updatedAt);

@override
String toString() {
  return 'EnginePerformanceModel(id: $id, engineId: $engineId, acceleration0To100: $acceleration0To100, topSpeed: $topSpeed, fuelConsumptionCity: $fuelConsumptionCity, fuelConsumptionHighway: $fuelConsumptionHighway, fuelConsumptionCombined: $fuelConsumptionCombined, co2Emissions: $co2Emissions, performanceNotes: $performanceNotes, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$EnginePerformanceModelCopyWith<$Res> implements $EnginePerformanceModelCopyWith<$Res> {
  factory _$EnginePerformanceModelCopyWith(_EnginePerformanceModel value, $Res Function(_EnginePerformanceModel) _then) = __$EnginePerformanceModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String engineId, double acceleration0To100, double topSpeed, double fuelConsumptionCity, double fuelConsumptionHighway, double fuelConsumptionCombined, double? co2Emissions, String? performanceNotes,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$EnginePerformanceModelCopyWithImpl<$Res>
    implements _$EnginePerformanceModelCopyWith<$Res> {
  __$EnginePerformanceModelCopyWithImpl(this._self, this._then);

  final _EnginePerformanceModel _self;
  final $Res Function(_EnginePerformanceModel) _then;

/// Create a copy of EnginePerformanceModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? engineId = null,Object? acceleration0To100 = null,Object? topSpeed = null,Object? fuelConsumptionCity = null,Object? fuelConsumptionHighway = null,Object? fuelConsumptionCombined = null,Object? co2Emissions = freezed,Object? performanceNotes = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_EnginePerformanceModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,engineId: null == engineId ? _self.engineId : engineId // ignore: cast_nullable_to_non_nullable
as String,acceleration0To100: null == acceleration0To100 ? _self.acceleration0To100 : acceleration0To100 // ignore: cast_nullable_to_non_nullable
as double,topSpeed: null == topSpeed ? _self.topSpeed : topSpeed // ignore: cast_nullable_to_non_nullable
as double,fuelConsumptionCity: null == fuelConsumptionCity ? _self.fuelConsumptionCity : fuelConsumptionCity // ignore: cast_nullable_to_non_nullable
as double,fuelConsumptionHighway: null == fuelConsumptionHighway ? _self.fuelConsumptionHighway : fuelConsumptionHighway // ignore: cast_nullable_to_non_nullable
as double,fuelConsumptionCombined: null == fuelConsumptionCombined ? _self.fuelConsumptionCombined : fuelConsumptionCombined // ignore: cast_nullable_to_non_nullable
as double,co2Emissions: freezed == co2Emissions ? _self.co2Emissions : co2Emissions // ignore: cast_nullable_to_non_nullable
as double?,performanceNotes: freezed == performanceNotes ? _self.performanceNotes : performanceNotes // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CompleteEngineModel {

 EngineSpecificationsModel get specifications; EnginePerformanceModel? get performance; List<String> get compatibleVehicles;
/// Create a copy of CompleteEngineModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CompleteEngineModelCopyWith<CompleteEngineModel> get copyWith => _$CompleteEngineModelCopyWithImpl<CompleteEngineModel>(this as CompleteEngineModel, _$identity);

  /// Serializes this CompleteEngineModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CompleteEngineModel&&(identical(other.specifications, specifications) || other.specifications == specifications)&&(identical(other.performance, performance) || other.performance == performance)&&const DeepCollectionEquality().equals(other.compatibleVehicles, compatibleVehicles));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,specifications,performance,const DeepCollectionEquality().hash(compatibleVehicles));

@override
String toString() {
  return 'CompleteEngineModel(specifications: $specifications, performance: $performance, compatibleVehicles: $compatibleVehicles)';
}


}

/// @nodoc
abstract mixin class $CompleteEngineModelCopyWith<$Res>  {
  factory $CompleteEngineModelCopyWith(CompleteEngineModel value, $Res Function(CompleteEngineModel) _then) = _$CompleteEngineModelCopyWithImpl;
@useResult
$Res call({
 EngineSpecificationsModel specifications, EnginePerformanceModel? performance, List<String> compatibleVehicles
});


$EngineSpecificationsModelCopyWith<$Res> get specifications;$EnginePerformanceModelCopyWith<$Res>? get performance;

}
/// @nodoc
class _$CompleteEngineModelCopyWithImpl<$Res>
    implements $CompleteEngineModelCopyWith<$Res> {
  _$CompleteEngineModelCopyWithImpl(this._self, this._then);

  final CompleteEngineModel _self;
  final $Res Function(CompleteEngineModel) _then;

/// Create a copy of CompleteEngineModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? specifications = null,Object? performance = freezed,Object? compatibleVehicles = null,}) {
  return _then(_self.copyWith(
specifications: null == specifications ? _self.specifications : specifications // ignore: cast_nullable_to_non_nullable
as EngineSpecificationsModel,performance: freezed == performance ? _self.performance : performance // ignore: cast_nullable_to_non_nullable
as EnginePerformanceModel?,compatibleVehicles: null == compatibleVehicles ? _self.compatibleVehicles : compatibleVehicles // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}
/// Create a copy of CompleteEngineModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EngineSpecificationsModelCopyWith<$Res> get specifications {
  
  return $EngineSpecificationsModelCopyWith<$Res>(_self.specifications, (value) {
    return _then(_self.copyWith(specifications: value));
  });
}/// Create a copy of CompleteEngineModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EnginePerformanceModelCopyWith<$Res>? get performance {
    if (_self.performance == null) {
    return null;
  }

  return $EnginePerformanceModelCopyWith<$Res>(_self.performance!, (value) {
    return _then(_self.copyWith(performance: value));
  });
}
}


/// Adds pattern-matching-related methods to [CompleteEngineModel].
extension CompleteEngineModelPatterns on CompleteEngineModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CompleteEngineModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CompleteEngineModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CompleteEngineModel value)  $default,){
final _that = this;
switch (_that) {
case _CompleteEngineModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CompleteEngineModel value)?  $default,){
final _that = this;
switch (_that) {
case _CompleteEngineModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( EngineSpecificationsModel specifications,  EnginePerformanceModel? performance,  List<String> compatibleVehicles)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CompleteEngineModel() when $default != null:
return $default(_that.specifications,_that.performance,_that.compatibleVehicles);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( EngineSpecificationsModel specifications,  EnginePerformanceModel? performance,  List<String> compatibleVehicles)  $default,) {final _that = this;
switch (_that) {
case _CompleteEngineModel():
return $default(_that.specifications,_that.performance,_that.compatibleVehicles);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( EngineSpecificationsModel specifications,  EnginePerformanceModel? performance,  List<String> compatibleVehicles)?  $default,) {final _that = this;
switch (_that) {
case _CompleteEngineModel() when $default != null:
return $default(_that.specifications,_that.performance,_that.compatibleVehicles);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CompleteEngineModel implements CompleteEngineModel {
  const _CompleteEngineModel({required this.specifications, this.performance, final  List<String> compatibleVehicles = const []}): _compatibleVehicles = compatibleVehicles;
  factory _CompleteEngineModel.fromJson(Map<String, dynamic> json) => _$CompleteEngineModelFromJson(json);

@override final  EngineSpecificationsModel specifications;
@override final  EnginePerformanceModel? performance;
 final  List<String> _compatibleVehicles;
@override@JsonKey() List<String> get compatibleVehicles {
  if (_compatibleVehicles is EqualUnmodifiableListView) return _compatibleVehicles;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_compatibleVehicles);
}


/// Create a copy of CompleteEngineModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CompleteEngineModelCopyWith<_CompleteEngineModel> get copyWith => __$CompleteEngineModelCopyWithImpl<_CompleteEngineModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CompleteEngineModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CompleteEngineModel&&(identical(other.specifications, specifications) || other.specifications == specifications)&&(identical(other.performance, performance) || other.performance == performance)&&const DeepCollectionEquality().equals(other._compatibleVehicles, _compatibleVehicles));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,specifications,performance,const DeepCollectionEquality().hash(_compatibleVehicles));

@override
String toString() {
  return 'CompleteEngineModel(specifications: $specifications, performance: $performance, compatibleVehicles: $compatibleVehicles)';
}


}

/// @nodoc
abstract mixin class _$CompleteEngineModelCopyWith<$Res> implements $CompleteEngineModelCopyWith<$Res> {
  factory _$CompleteEngineModelCopyWith(_CompleteEngineModel value, $Res Function(_CompleteEngineModel) _then) = __$CompleteEngineModelCopyWithImpl;
@override @useResult
$Res call({
 EngineSpecificationsModel specifications, EnginePerformanceModel? performance, List<String> compatibleVehicles
});


@override $EngineSpecificationsModelCopyWith<$Res> get specifications;@override $EnginePerformanceModelCopyWith<$Res>? get performance;

}
/// @nodoc
class __$CompleteEngineModelCopyWithImpl<$Res>
    implements _$CompleteEngineModelCopyWith<$Res> {
  __$CompleteEngineModelCopyWithImpl(this._self, this._then);

  final _CompleteEngineModel _self;
  final $Res Function(_CompleteEngineModel) _then;

/// Create a copy of CompleteEngineModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? specifications = null,Object? performance = freezed,Object? compatibleVehicles = null,}) {
  return _then(_CompleteEngineModel(
specifications: null == specifications ? _self.specifications : specifications // ignore: cast_nullable_to_non_nullable
as EngineSpecificationsModel,performance: freezed == performance ? _self.performance : performance // ignore: cast_nullable_to_non_nullable
as EnginePerformanceModel?,compatibleVehicles: null == compatibleVehicles ? _self._compatibleVehicles : compatibleVehicles // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

/// Create a copy of CompleteEngineModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EngineSpecificationsModelCopyWith<$Res> get specifications {
  
  return $EngineSpecificationsModelCopyWith<$Res>(_self.specifications, (value) {
    return _then(_self.copyWith(specifications: value));
  });
}/// Create a copy of CompleteEngineModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EnginePerformanceModelCopyWith<$Res>? get performance {
    if (_self.performance == null) {
    return null;
  }

  return $EnginePerformanceModelCopyWith<$Res>(_self.performance!, (value) {
    return _then(_self.copyWith(performance: value));
  });
}
}

// dart format on
