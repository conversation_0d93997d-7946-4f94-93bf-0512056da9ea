// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'car_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CarModel {

 int? get id; String? get userId; int? get makeId; String? get make; int? get modelId; String? get model; int? get year; int? get trimId; String? get trim; String? get engine; String? get transmission; String? get fuelType; int? get mileage; String? get bodyType; String? get vin; String? get imageUrl; List<String> get images; List<String> get colors; Map<String, dynamic> get specifications; DateTime? get createdAt; DateTime? get updatedAt; bool get isDeleted;
/// Create a copy of CarModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CarModelCopyWith<CarModel> get copyWith => _$CarModelCopyWithImpl<CarModel>(this as CarModel, _$identity);

  /// Serializes this CarModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CarModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.make, make) || other.make == make)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.model, model) || other.model == model)&&(identical(other.year, year) || other.year == year)&&(identical(other.trimId, trimId) || other.trimId == trimId)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.transmission, transmission) || other.transmission == transmission)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.bodyType, bodyType) || other.bodyType == bodyType)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&const DeepCollectionEquality().equals(other.images, images)&&const DeepCollectionEquality().equals(other.colors, colors)&&const DeepCollectionEquality().equals(other.specifications, specifications)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,makeId,make,modelId,model,year,trimId,trim,engine,transmission,fuelType,mileage,bodyType,vin,imageUrl,const DeepCollectionEquality().hash(images),const DeepCollectionEquality().hash(colors),const DeepCollectionEquality().hash(specifications),createdAt,updatedAt,isDeleted]);

@override
String toString() {
  return 'CarModel(id: $id, userId: $userId, makeId: $makeId, make: $make, modelId: $modelId, model: $model, year: $year, trimId: $trimId, trim: $trim, engine: $engine, transmission: $transmission, fuelType: $fuelType, mileage: $mileage, bodyType: $bodyType, vin: $vin, imageUrl: $imageUrl, images: $images, colors: $colors, specifications: $specifications, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $CarModelCopyWith<$Res>  {
  factory $CarModelCopyWith(CarModel value, $Res Function(CarModel) _then) = _$CarModelCopyWithImpl;
@useResult
$Res call({
 int? id, String? userId, int? makeId, String? make, int? modelId, String? model, int? year, int? trimId, String? trim, String? engine, String? transmission, String? fuelType, int? mileage, String? bodyType, String? vin, String? imageUrl, List<String> images, List<String> colors, Map<String, dynamic> specifications, DateTime? createdAt, DateTime? updatedAt, bool isDeleted
});




}
/// @nodoc
class _$CarModelCopyWithImpl<$Res>
    implements $CarModelCopyWith<$Res> {
  _$CarModelCopyWithImpl(this._self, this._then);

  final CarModel _self;
  final $Res Function(CarModel) _then;

/// Create a copy of CarModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? userId = freezed,Object? makeId = freezed,Object? make = freezed,Object? modelId = freezed,Object? model = freezed,Object? year = freezed,Object? trimId = freezed,Object? trim = freezed,Object? engine = freezed,Object? transmission = freezed,Object? fuelType = freezed,Object? mileage = freezed,Object? bodyType = freezed,Object? vin = freezed,Object? imageUrl = freezed,Object? images = null,Object? colors = null,Object? specifications = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,makeId: freezed == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as int?,make: freezed == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String?,modelId: freezed == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,year: freezed == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int?,trimId: freezed == trimId ? _self.trimId : trimId // ignore: cast_nullable_to_non_nullable
as int?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,transmission: freezed == transmission ? _self.transmission : transmission // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,bodyType: freezed == bodyType ? _self.bodyType : bodyType // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,images: null == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>,colors: null == colors ? _self.colors : colors // ignore: cast_nullable_to_non_nullable
as List<String>,specifications: null == specifications ? _self.specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [CarModel].
extension CarModelPatterns on CarModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CarModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CarModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CarModel value)  $default,){
final _that = this;
switch (_that) {
case _CarModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CarModel value)?  $default,){
final _that = this;
switch (_that) {
case _CarModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  String? userId,  int? makeId,  String? make,  int? modelId,  String? model,  int? year,  int? trimId,  String? trim,  String? engine,  String? transmission,  String? fuelType,  int? mileage,  String? bodyType,  String? vin,  String? imageUrl,  List<String> images,  List<String> colors,  Map<String, dynamic> specifications,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CarModel() when $default != null:
return $default(_that.id,_that.userId,_that.makeId,_that.make,_that.modelId,_that.model,_that.year,_that.trimId,_that.trim,_that.engine,_that.transmission,_that.fuelType,_that.mileage,_that.bodyType,_that.vin,_that.imageUrl,_that.images,_that.colors,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  String? userId,  int? makeId,  String? make,  int? modelId,  String? model,  int? year,  int? trimId,  String? trim,  String? engine,  String? transmission,  String? fuelType,  int? mileage,  String? bodyType,  String? vin,  String? imageUrl,  List<String> images,  List<String> colors,  Map<String, dynamic> specifications,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _CarModel():
return $default(_that.id,_that.userId,_that.makeId,_that.make,_that.modelId,_that.model,_that.year,_that.trimId,_that.trim,_that.engine,_that.transmission,_that.fuelType,_that.mileage,_that.bodyType,_that.vin,_that.imageUrl,_that.images,_that.colors,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  String? userId,  int? makeId,  String? make,  int? modelId,  String? model,  int? year,  int? trimId,  String? trim,  String? engine,  String? transmission,  String? fuelType,  int? mileage,  String? bodyType,  String? vin,  String? imageUrl,  List<String> images,  List<String> colors,  Map<String, dynamic> specifications,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _CarModel() when $default != null:
return $default(_that.id,_that.userId,_that.makeId,_that.make,_that.modelId,_that.model,_that.year,_that.trimId,_that.trim,_that.engine,_that.transmission,_that.fuelType,_that.mileage,_that.bodyType,_that.vin,_that.imageUrl,_that.images,_that.colors,_that.specifications,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _CarModel implements CarModel {
  const _CarModel({this.id, this.userId, this.makeId, this.make, this.modelId, this.model, this.year, this.trimId, this.trim, this.engine, this.transmission, this.fuelType, this.mileage, this.bodyType, this.vin, this.imageUrl, final  List<String> images = const [], final  List<String> colors = const [], final  Map<String, dynamic> specifications = const {}, this.createdAt, this.updatedAt, this.isDeleted = false}): _images = images,_colors = colors,_specifications = specifications;
  factory _CarModel.fromJson(Map<String, dynamic> json) => _$CarModelFromJson(json);

@override final  int? id;
@override final  String? userId;
@override final  int? makeId;
@override final  String? make;
@override final  int? modelId;
@override final  String? model;
@override final  int? year;
@override final  int? trimId;
@override final  String? trim;
@override final  String? engine;
@override final  String? transmission;
@override final  String? fuelType;
@override final  int? mileage;
@override final  String? bodyType;
@override final  String? vin;
@override final  String? imageUrl;
 final  List<String> _images;
@override@JsonKey() List<String> get images {
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_images);
}

 final  List<String> _colors;
@override@JsonKey() List<String> get colors {
  if (_colors is EqualUnmodifiableListView) return _colors;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_colors);
}

 final  Map<String, dynamic> _specifications;
@override@JsonKey() Map<String, dynamic> get specifications {
  if (_specifications is EqualUnmodifiableMapView) return _specifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_specifications);
}

@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey() final  bool isDeleted;

/// Create a copy of CarModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CarModelCopyWith<_CarModel> get copyWith => __$CarModelCopyWithImpl<_CarModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CarModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CarModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.make, make) || other.make == make)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.model, model) || other.model == model)&&(identical(other.year, year) || other.year == year)&&(identical(other.trimId, trimId) || other.trimId == trimId)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.transmission, transmission) || other.transmission == transmission)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.bodyType, bodyType) || other.bodyType == bodyType)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&const DeepCollectionEquality().equals(other._images, _images)&&const DeepCollectionEquality().equals(other._colors, _colors)&&const DeepCollectionEquality().equals(other._specifications, _specifications)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,makeId,make,modelId,model,year,trimId,trim,engine,transmission,fuelType,mileage,bodyType,vin,imageUrl,const DeepCollectionEquality().hash(_images),const DeepCollectionEquality().hash(_colors),const DeepCollectionEquality().hash(_specifications),createdAt,updatedAt,isDeleted]);

@override
String toString() {
  return 'CarModel(id: $id, userId: $userId, makeId: $makeId, make: $make, modelId: $modelId, model: $model, year: $year, trimId: $trimId, trim: $trim, engine: $engine, transmission: $transmission, fuelType: $fuelType, mileage: $mileage, bodyType: $bodyType, vin: $vin, imageUrl: $imageUrl, images: $images, colors: $colors, specifications: $specifications, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$CarModelCopyWith<$Res> implements $CarModelCopyWith<$Res> {
  factory _$CarModelCopyWith(_CarModel value, $Res Function(_CarModel) _then) = __$CarModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? userId, int? makeId, String? make, int? modelId, String? model, int? year, int? trimId, String? trim, String? engine, String? transmission, String? fuelType, int? mileage, String? bodyType, String? vin, String? imageUrl, List<String> images, List<String> colors, Map<String, dynamic> specifications, DateTime? createdAt, DateTime? updatedAt, bool isDeleted
});




}
/// @nodoc
class __$CarModelCopyWithImpl<$Res>
    implements _$CarModelCopyWith<$Res> {
  __$CarModelCopyWithImpl(this._self, this._then);

  final _CarModel _self;
  final $Res Function(_CarModel) _then;

/// Create a copy of CarModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? userId = freezed,Object? makeId = freezed,Object? make = freezed,Object? modelId = freezed,Object? model = freezed,Object? year = freezed,Object? trimId = freezed,Object? trim = freezed,Object? engine = freezed,Object? transmission = freezed,Object? fuelType = freezed,Object? mileage = freezed,Object? bodyType = freezed,Object? vin = freezed,Object? imageUrl = freezed,Object? images = null,Object? colors = null,Object? specifications = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_CarModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,makeId: freezed == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as int?,make: freezed == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String?,modelId: freezed == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,year: freezed == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int?,trimId: freezed == trimId ? _self.trimId : trimId // ignore: cast_nullable_to_non_nullable
as int?,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,transmission: freezed == transmission ? _self.transmission : transmission // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,bodyType: freezed == bodyType ? _self.bodyType : bodyType // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,images: null == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>,colors: null == colors ? _self._colors : colors // ignore: cast_nullable_to_non_nullable
as List<String>,specifications: null == specifications ? _self._specifications : specifications // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
