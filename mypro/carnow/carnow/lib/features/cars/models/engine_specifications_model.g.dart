// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engine_specifications_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EngineSpecificationsModel _$EngineSpecificationsModelFromJson(
  Map<String, dynamic> json,
) => _EngineSpecificationsModel(
  id: json['id'] as String,
  name: json['name'] as String,
  type: json['type'] as String,
  displacement: (json['displacement'] as num).toDouble(),
  horsepower: (json['horsepower'] as num).toInt(),
  torque: (json['torque'] as num).toInt(),
  fuelType: json['fuel_type'] as String,
  cylinders: (json['cylinders'] as num).toInt(),
  configuration: json['configuration'] as String,
  aspiration: json['aspiration'] as String?,
  compressionRatio: (json['compression_ratio'] as num?)?.toDouble(),
  boreStroke: json['bore_stroke'] as String?,
  valvetrain: json['valvetrain'] as String?,
  fuelSystem: json['fuel_system'] as String?,
  coolingSystem: json['cooling_system'] as String?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$EngineSpecificationsModelToJson(
  _EngineSpecificationsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'type': instance.type,
  'displacement': instance.displacement,
  'horsepower': instance.horsepower,
  'torque': instance.torque,
  'fuel_type': instance.fuelType,
  'cylinders': instance.cylinders,
  'configuration': instance.configuration,
  'aspiration': instance.aspiration,
  'compression_ratio': instance.compressionRatio,
  'bore_stroke': instance.boreStroke,
  'valvetrain': instance.valvetrain,
  'fuel_system': instance.fuelSystem,
  'cooling_system': instance.coolingSystem,
};

_EnginePerformanceModel _$EnginePerformanceModelFromJson(
  Map<String, dynamic> json,
) => _EnginePerformanceModel(
  id: json['id'] as String,
  engineId: json['engine_id'] as String,
  acceleration0To100: (json['acceleration0_to100'] as num).toDouble(),
  topSpeed: (json['top_speed'] as num).toDouble(),
  fuelConsumptionCity: (json['fuel_consumption_city'] as num).toDouble(),
  fuelConsumptionHighway: (json['fuel_consumption_highway'] as num).toDouble(),
  fuelConsumptionCombined: (json['fuel_consumption_combined'] as num)
      .toDouble(),
  co2Emissions: (json['co2_emissions'] as num?)?.toDouble(),
  performanceNotes: json['performance_notes'] as String?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$EnginePerformanceModelToJson(
  _EnginePerformanceModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'engine_id': instance.engineId,
  'acceleration0_to100': instance.acceleration0To100,
  'top_speed': instance.topSpeed,
  'fuel_consumption_city': instance.fuelConsumptionCity,
  'fuel_consumption_highway': instance.fuelConsumptionHighway,
  'fuel_consumption_combined': instance.fuelConsumptionCombined,
  'co2_emissions': instance.co2Emissions,
  'performance_notes': instance.performanceNotes,
};

_CompleteEngineModel _$CompleteEngineModelFromJson(Map<String, dynamic> json) =>
    _CompleteEngineModel(
      specifications: EngineSpecificationsModel.fromJson(
        json['specifications'] as Map<String, dynamic>,
      ),
      performance: json['performance'] == null
          ? null
          : EnginePerformanceModel.fromJson(
              json['performance'] as Map<String, dynamic>,
            ),
      compatibleVehicles:
          (json['compatibleVehicles'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$CompleteEngineModelToJson(
  _CompleteEngineModel instance,
) => <String, dynamic>{
  'specifications': instance.specifications,
  'performance': instance.performance,
  'compatibleVehicles': instance.compatibleVehicles,
};
