// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_specifications_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleSpecificationsModel _$VehicleSpecificationsModelFromJson(
  Map<String, dynamic> json,
) => _VehicleSpecificationsModel(
  makeName: json['makeName'] as String,
  modelName: json['modelName'] as String,
  year: (json['year'] as num).toInt(),
  vehicleType: json['vehicleType'] as String,
  generationInfo: json['generationInfo'] as String,
  engineDisplacement: json['engineDisplacement'] as String,
  engineCylinders: (json['engineCylinders'] as num).toInt(),
  enginePowerHp: (json['enginePowerHp'] as num).toInt(),
  engineTorqueNm: (json['engineTorqueNm'] as num).toInt(),
  fuelType: json['fuelType'] as String,
  engineCode: json['engineCode'] as String,
  transmissionType: json['transmissionType'] as String,
  transmissionSpeeds: (json['transmissionSpeeds'] as num).toInt(),
  drivetrain: json['drivetrain'] as String,
  bodyStyle: json['bodyStyle'] as String,
  doorsCount: (json['doorsCount'] as num).toInt(),
  seatingCapacity: (json['seatingCapacity'] as num).toInt(),
  wheelbaseMm: (json['wheelbaseMm'] as num).toInt(),
  frontBrakeType: json['frontBrakeType'] as String,
  rearBrakeType: json['rearBrakeType'] as String,
  wheelSize: json['wheelSize'] as String,
  tireSize: json['tireSize'] as String,
  partsCompatibilityCode: json['partsCompatibilityCode'] as String,
  commonPartsCategories: (json['commonPartsCategories'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  maintenanceIntervals: Map<String, String>.from(
    json['maintenanceIntervals'] as Map,
  ),
  marketAvailability: json['marketAvailability'] as String,
  isPopularModel: json['isPopularModel'] as bool,
);

Map<String, dynamic> _$VehicleSpecificationsModelToJson(
  _VehicleSpecificationsModel instance,
) => <String, dynamic>{
  'makeName': instance.makeName,
  'modelName': instance.modelName,
  'year': instance.year,
  'vehicleType': instance.vehicleType,
  'generationInfo': instance.generationInfo,
  'engineDisplacement': instance.engineDisplacement,
  'engineCylinders': instance.engineCylinders,
  'enginePowerHp': instance.enginePowerHp,
  'engineTorqueNm': instance.engineTorqueNm,
  'fuelType': instance.fuelType,
  'engineCode': instance.engineCode,
  'transmissionType': instance.transmissionType,
  'transmissionSpeeds': instance.transmissionSpeeds,
  'drivetrain': instance.drivetrain,
  'bodyStyle': instance.bodyStyle,
  'doorsCount': instance.doorsCount,
  'seatingCapacity': instance.seatingCapacity,
  'wheelbaseMm': instance.wheelbaseMm,
  'frontBrakeType': instance.frontBrakeType,
  'rearBrakeType': instance.rearBrakeType,
  'wheelSize': instance.wheelSize,
  'tireSize': instance.tireSize,
  'partsCompatibilityCode': instance.partsCompatibilityCode,
  'commonPartsCategories': instance.commonPartsCategories,
  'maintenanceIntervals': instance.maintenanceIntervals,
  'marketAvailability': instance.marketAvailability,
  'isPopularModel': instance.isPopularModel,
};

_CompatiblePartsModel _$CompatiblePartsModelFromJson(
  Map<String, dynamic> json,
) => _CompatiblePartsModel(
  vehicleInfo: json['vehicleInfo'] as String,
  compatibilityCode: json['compatibilityCode'] as String,
  engineSpecs: json['engineSpecs'] as String,
  transmissionSpecs: json['transmissionSpecs'] as String,
  brakeSpecs: json['brakeSpecs'] as String,
  wheelSpecs: json['wheelSpecs'] as String,
  maintenanceInfo: Map<String, String>.from(json['maintenanceInfo'] as Map),
  partsCategories: (json['partsCategories'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  generationCompatibility: (json['generationCompatibility'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$CompatiblePartsModelToJson(
  _CompatiblePartsModel instance,
) => <String, dynamic>{
  'vehicleInfo': instance.vehicleInfo,
  'compatibilityCode': instance.compatibilityCode,
  'engineSpecs': instance.engineSpecs,
  'transmissionSpecs': instance.transmissionSpecs,
  'brakeSpecs': instance.brakeSpecs,
  'wheelSpecs': instance.wheelSpecs,
  'maintenanceInfo': instance.maintenanceInfo,
  'partsCategories': instance.partsCategories,
  'generationCompatibility': instance.generationCompatibility,
};

_VehicleForProductModel _$VehicleForProductModelFromJson(
  Map<String, dynamic> json,
) => _VehicleForProductModel(
  makeName: json['makeName'] as String,
  modelName: json['modelName'] as String,
  yearRange: json['yearRange'] as String,
  engineInfo: json['engineInfo'] as String,
  transmissionInfo: json['transmissionInfo'] as String,
  compatibilityCode: json['compatibilityCode'] as String,
  marketPopularity: (json['marketPopularity'] as num).toInt(),
  partsAvailability: json['partsAvailability'] as String,
  maintenanceFrequency: json['maintenanceFrequency'] as String,
);

Map<String, dynamic> _$VehicleForProductModelToJson(
  _VehicleForProductModel instance,
) => <String, dynamic>{
  'makeName': instance.makeName,
  'modelName': instance.modelName,
  'yearRange': instance.yearRange,
  'engineInfo': instance.engineInfo,
  'transmissionInfo': instance.transmissionInfo,
  'compatibilityCode': instance.compatibilityCode,
  'marketPopularity': instance.marketPopularity,
  'partsAvailability': instance.partsAvailability,
  'maintenanceFrequency': instance.maintenanceFrequency,
};

_VehicleSearchStepModel _$VehicleSearchStepModelFromJson(
  Map<String, dynamic> json,
) => _VehicleSearchStepModel(
  makeId: (json['makeId'] as num).toInt(),
  makeName: json['makeName'] as String,
  makeNameAr: json['makeNameAr'] as String,
  modelsCount: (json['modelsCount'] as num).toInt(),
  yearsCount: (json['yearsCount'] as num).toInt(),
);

Map<String, dynamic> _$VehicleSearchStepModelToJson(
  _VehicleSearchStepModel instance,
) => <String, dynamic>{
  'makeId': instance.makeId,
  'makeName': instance.makeName,
  'makeNameAr': instance.makeNameAr,
  'modelsCount': instance.modelsCount,
  'yearsCount': instance.yearsCount,
};

_ModelsByMakeModel _$ModelsByMakeModelFromJson(Map<String, dynamic> json) =>
    _ModelsByMakeModel(
      modelId: (json['modelId'] as num).toInt(),
      modelName: json['modelName'] as String,
      modelNameAr: json['modelNameAr'] as String,
      vehicleType: json['vehicleType'] as String,
      yearsCount: (json['yearsCount'] as num).toInt(),
      yearFrom: (json['yearFrom'] as num).toInt(),
      yearTo: (json['yearTo'] as num).toInt(),
      hasSpecifications: json['hasSpecifications'] as bool,
    );

Map<String, dynamic> _$ModelsByMakeModelToJson(_ModelsByMakeModel instance) =>
    <String, dynamic>{
      'modelId': instance.modelId,
      'modelName': instance.modelName,
      'modelNameAr': instance.modelNameAr,
      'vehicleType': instance.vehicleType,
      'yearsCount': instance.yearsCount,
      'yearFrom': instance.yearFrom,
      'yearTo': instance.yearTo,
      'hasSpecifications': instance.hasSpecifications,
    };

_YearsByModelModel _$YearsByModelModelFromJson(Map<String, dynamic> json) =>
    _YearsByModelModel(
      year: (json['year'] as num).toInt(),
      isPopular: json['isPopular'] as bool,
      hasSpecifications: json['hasSpecifications'] as bool,
      generationInfo: json['generationInfo'] as String,
    );

Map<String, dynamic> _$YearsByModelModelToJson(_YearsByModelModel instance) =>
    <String, dynamic>{
      'year': instance.year,
      'isPopular': instance.isPopular,
      'hasSpecifications': instance.hasSpecifications,
      'generationInfo': instance.generationInfo,
    };

_VehicleSearchStateData _$VehicleSearchStateDataFromJson(
  Map<String, dynamic> json,
) => _VehicleSearchStateData(
  selectedMake: json['selectedMake'] == null
      ? null
      : VehicleSearchStepModel.fromJson(
          json['selectedMake'] as Map<String, dynamic>,
        ),
  selectedModel: json['selectedModel'] == null
      ? null
      : ModelsByMakeModel.fromJson(
          json['selectedModel'] as Map<String, dynamic>,
        ),
  selectedYear: json['selectedYear'] == null
      ? null
      : YearsByModelModel.fromJson(
          json['selectedYear'] as Map<String, dynamic>,
        ),
  currentStep: (json['currentStep'] as num?)?.toInt() ?? 1,
);

Map<String, dynamic> _$VehicleSearchStateDataToJson(
  _VehicleSearchStateData instance,
) => <String, dynamic>{
  'selectedMake': instance.selectedMake,
  'selectedModel': instance.selectedModel,
  'selectedYear': instance.selectedYear,
  'currentStep': instance.currentStep,
};
