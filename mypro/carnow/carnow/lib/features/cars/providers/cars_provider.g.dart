// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cars_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userCarsHash() => r'ffb3b697a0f33a8956e9ad8a2c9f6d069e3dee17';

/// مزود لسيارات المستخدم - CLEAN: Uses HTTP API only
///
/// Copied from [userCars].
@ProviderFor(userCars)
final userCarsProvider = AutoDisposeFutureProvider<List<Car>>.internal(
  userCars,
  name: r'userCarsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userCarsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserCarsRef = AutoDisposeFutureProviderRef<List<Car>>;
String _$carsNotifierHash() => r'2d16ce48d4dc353ac7d5a7bb6bc5c13866caa247';

/// إضافة سيارة جديدة
///
/// Copied from [CarsNotifier].
@ProviderFor(CarsNotifier)
final carsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CarsNotifier, List<Car>>.internal(
      CarsNotifier.new,
      name: r'carsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$carsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CarsNotifier = AutoDisposeAsyncNotifier<List<Car>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
